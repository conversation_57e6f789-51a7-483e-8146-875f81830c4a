<?php

namespace App\Console\Commands;

use App\Services\PermissionTemplateService;
use App\Models\AdvancedRole;
use Illuminate\Console\Command;

/**
 * Manage Permission Templates Command
 *
 * Artisan command for managing permission templates and bulk role operations.
 */
class ManagePermissionTemplates extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permissions:templates
                            {action : Action to perform (list, create, create-all, export)}
                            {template? : Template name for create/export actions}
                            {--role= : Role name for export action}
                            {--output= : Output file for export}';

    /**
     * The console command description.
     */
    protected $description = 'Manage permission templates and bulk role operations';

    /**
     * Permission template service
     */
    protected PermissionTemplateService $templateService;

    /**
     * Create a new command instance.
     */
    public function __construct(PermissionTemplateService $templateService)
    {
        parent::__construct();
        $this->templateService = $templateService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        return match ($action) {
            'list' => $this->listTemplates(),
            'create' => $this->createFromTemplate(),
            'create-all' => $this->createAllTemplates(),
            'export' => $this->exportRole(),
            default => $this->invalidAction($action),
        };
    }

    /**
     * List available templates
     */
    protected function listTemplates(): int
    {
        $this->info('📋 Available Permission Templates');
        $this->newLine();

        $templates = $this->templateService->getAvailableTemplates();

        $tableData = [];
        foreach ($templates as $name => $template) {
            $permissionCount = is_array($template['permissions']) ? count($template['permissions']) : 'All';
            $tableData[] = [
                $name,
                $template['display_name'],
                $template['category'],
                $permissionCount,
                $template['description'],
            ];
        }

        $this->table(
            ['Template Name', 'Display Name', 'Category', 'Permissions', 'Description'],
            $tableData
        );

        $this->newLine();
        $this->info('💡 Use "permissions:templates create <template-name>" to create a role from template');

        return Command::SUCCESS;
    }

    /**
     * Create role from template
     */
    protected function createFromTemplate(): int
    {
        $templateName = $this->argument('template');

        if (!$templateName) {
            $this->error('❌ Template name is required for create action');
            $this->line('Use: php artisan permissions:templates create <template-name>');
            return Command::FAILURE;
        }

        $template = $this->templateService->getTemplate($templateName);
        if (!$template) {
            $this->error("❌ Template '{$templateName}' not found");
            $this->listAvailableTemplates();
            return Command::FAILURE;
        }

        // Check if role already exists
        if (AdvancedRole::findByName($template['name'])) {
            $this->warn("⚠️  Role '{$template['name']}' already exists");
            if (!$this->confirm('Do you want to continue anyway? (This will create a duplicate)')) {
                return Command::FAILURE;
            }

            // Modify name to avoid conflict
            $template['name'] .= '_' . time();
        }

        $this->info("🚀 Creating role from template: {$templateName}");
        $this->newLine();

        try {
            $role = $this->templateService->createRoleFromTemplate($templateName, [
                'name' => $template['name']
            ]);

            $this->info("✅ Successfully created role: {$role->display_name}");
            $this->line("   • Name: {$role->name}");
            $this->line("   • Category: {$role->category}");
            $this->line("   • Permissions: {$role->permissions()->count()}");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("❌ Failed to create role: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Create all template roles
     */
    protected function createAllTemplates(): int
    {
        $this->info('🚀 Creating roles from all templates...');
        $this->newLine();

        $result = $this->templateService->createAllTemplateRoles();

        if (!empty($result['created'])) {
            $createdCount = count($result['created']);
            $this->info("✅ Successfully created {$createdCount} roles:");
            foreach ($result['created'] as $role) {
                $this->line("   • {$role->display_name} ({$role->name})");
            }
            $this->newLine();
        }

        if (!empty($result['errors'])) {
            $errorCount = count($result['errors']);
            $this->error("❌ Encountered {$errorCount} errors:");
            foreach ($result['errors'] as $error) {
                $this->line("   • {$error}");
            }
            $this->newLine();
        }

        if (empty($result['created']) && empty($result['errors'])) {
            $this->info('ℹ️  All template roles already exist');
        }

        return Command::SUCCESS;
    }

    /**
     * Export role as template
     */
    protected function exportRole(): int
    {
        $roleName = $this->option('role');

        if (!$roleName) {
            $this->error('❌ Role name is required for export action');
            $this->line('Use: php artisan permissions:templates export --role=<role-name>');
            return Command::FAILURE;
        }

        $role = AdvancedRole::findByName($roleName);
        if (!$role) {
            $this->error("❌ Role '{$roleName}' not found");
            return Command::FAILURE;
        }

        $this->info("📤 Exporting role: {$role->display_name}");

        try {
            $template = $this->templateService->exportRoleAsTemplate($role);

            $output = $this->option('output');
            if ($output) {
                file_put_contents($output, json_encode($template, JSON_PRETTY_PRINT));
                $this->info("✅ Template exported to: {$output}");
            } else {
                $this->line(json_encode($template, JSON_PRETTY_PRINT));
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("❌ Failed to export role: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Handle invalid action
     */
    protected function invalidAction(string $action): int
    {
        $this->error("❌ Invalid action: {$action}");
        $this->newLine();
        $this->info('Available actions:');
        $this->line('  • list        - List all available templates');
        $this->line('  • create      - Create role from template');
        $this->line('  • create-all  - Create roles from all templates');
        $this->line('  • export      - Export existing role as template');

        return Command::FAILURE;
    }

    /**
     * List available templates for error messages
     */
    protected function listAvailableTemplates(): void
    {
        $this->newLine();
        $this->info('Available templates:');
        $templates = $this->templateService->getAvailableTemplates();
        foreach ($templates as $name => $template) {
            $this->line("  • {$name} - {$template['display_name']}");
        }
    }
}
