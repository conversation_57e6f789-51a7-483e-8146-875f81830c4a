@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $account->account_name }}</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">{{ $account->account_name }}</h1>
                    <p class="page-header-text">@lang('Account details and transaction history')</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Accounts')
                        </a>
                        <a class="btn btn-success" href="{{ route('admin.forex.accounts.fund', $account->id) }}">
                            <i class="bi-plus-circle me-1"></i> @lang('Fund Account')
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Account Overview -->
        <div class="row">
            <div class="col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Overview')</h4>
                        <span class="badge bg-soft-{{ $account->is_active ? 'success' : 'danger' }} text-{{ $account->is_active ? 'success' : 'danger' }}">
                            {{ $account->is_active ? __('Active') : __('Inactive') }}
                        </span>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-5">@lang('Account Type'):</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-soft-primary text-primary">{{ $account->account_type }}</span>
                            </dd>

                            <dt class="col-sm-5">@lang('Currency'):</dt>
                            <dd class="col-sm-7">{{ $account->currency_code }}</dd>

                            <dt class="col-sm-5">@lang('Available Balance'):</dt>
                            <dd class="col-sm-7">
                                <span class="fw-semibold text-success">
                                    {{ $account->formatted_balance }}
                                </span>
                                <small class="d-block text-muted">@lang('Can be used for new transactions')</small>
                            </dd>

                            @if($account->pending_balance > 0)
                                <dt class="col-sm-5">@lang('Pending Balance'):</dt>
                                <dd class="col-sm-7">
                                    <span class="fw-semibold text-warning">
                                        {{ number_format($account->pending_balance, 2) }} {{ $account->currency_code }}
                                    </span>
                                    <small class="d-block text-muted">@lang('Reserved for pending bookings')</small>
                                </dd>
                            @endif

                            <dt class="col-sm-5">@lang('Total Balance'):</dt>
                            <dd class="col-sm-7">
                                <span class="fw-bold text-primary">
                                    {{ $account->formatted_total_balance }}
                                </span>
                                <small class="d-block text-muted">@lang('Available + Pending')</small>
                            </dd>

                            <dt class="col-sm-5">@lang('Created'):</dt>
                            <dd class="col-sm-7">{{ $account->created_at->format('M d, Y') }}</dd>

                            <dt class="col-sm-5">@lang('Last Updated'):</dt>
                            <dd class="col-sm-7">{{ $account->updated_at->diffForHumans() }}</dd>
                        </dl>

                        @if($account->description)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Description')</h6>
                                <p class="text-muted">{{ $account->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-lg-8 mb-3 mb-lg-5">
                <!-- Transaction History -->
                <div class="card">
                    <div class="card-header card-header-content-md-between">
                        <div class="mb-2 mb-md-0">
                            <h4 class="card-header-title">@lang('Recent Transactions')</h4>
                            <a href="{{ route('admin.forex.accounts.transactions', $account->id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi-list-ul me-1"></i> @lang('View All Transactions')
                            </a>
                        </div>
                        <div class="d-grid d-sm-flex gap-2">
                            <div class="input-group input-group-merge navbar-input-group">
                                <div class="input-group-prepend input-group-text">
                                    <i class="bi-search"></i>
                                </div>
                                <input type="search" id="datatableSearch" class="search form-control"
                                       placeholder="@lang('Search transactions')" aria-label="@lang('Search transactions')">
                            </div>
                        </div>
                    </div>

                    @if($account->transactions->count() > 0)
                        <div class="table-responsive">
                            <table id="datatable" class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                                   data-hs-datatables-options='{
                                     "columnDefs": [{
                                        "targets": [0],
                                        "orderable": false
                                      }],
                                     "order": [],
                                     "info": {
                                       "totalQty": "#datatableWithPaginationInfoTotalQty"
                                     },
                                     "search": "#datatableSearch",
                                     "entries": "#datatableEntries",
                                     "pageLength": 15,
                                     "isResponsive": false,
                                     "isShowPaging": false,
                                     "pagination": "datatablePagination"
                                   }'>
                                <thead class="thead-light">
                                <tr>
                                    <th>@lang('Date')</th>
                                    <th>@lang('Type')</th>
                                    <th>@lang('Amount')</th>
                                    <th>@lang('Balance After')</th>
                                    <th>@lang('Description')</th>
                                    <th>@lang('Reference')</th>
                                    <th>@lang('Created By')</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($account->transactions as $transaction)
                                    <tr>
                                        <td>
                                            <span class="d-block h6 mb-0">{{ $transaction->created_at->format('M d, Y') }}</span>
                                            <small class="text-muted">{{ $transaction->created_at->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $transaction->type_class }}">
                                                {{ ucfirst(str_replace('_', ' ', $transaction->transaction_type)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-semibold">
                                                {{ $transaction->formatted_amount }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-semibold">
                                                {{ $transaction->formatted_balance_after }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="d-block">{{ $transaction->description }}</span>
                                            @if($transaction->forexBooking)
                                                <small class="text-muted">
                                                    <a href="{{ route('admin.forex.bookings.show', $transaction->forexBooking->id) }}">
                                                        {{ $transaction->forexBooking->booking_reference }}
                                                    </a>
                                                </small>
                                            @endif
                                            @if($transaction->metadata && isset($transaction->metadata['transfer_reference']))
                                                <small class="text-muted d-block">
                                                    <i class="bi-arrow-left-right me-1"></i>Transfer: {{ $transaction->metadata['transfer_reference'] }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <code>{{ $transaction->transaction_reference }}</code>
                                        </td>
                                        <td>
                                            {{ $transaction->createdBy->name ?? 'System' }}
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Footer -->
                        <div class="card-footer">
                            <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                                <div class="col-sm mb-2 mb-sm-0">
                                    <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                                        <span class="me-2">@lang('Showing:')</span>
                                        <!-- Select -->
                                        <div class="tom-select-custom">
                                            <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto"
                                                    autocomplete="off"
                                                    data-hs-tom-select-options='{
                                                        "searchInDropdown": false,
                                                        "hideSearch": true
                                                      }'>
                                                <option value="10">10</option>
                                                <option value="15" selected>15</option>
                                                <option value="20">20</option>
                                            </select>
                                        </div>
                                        <!-- End Select -->
                                        <span class="text-secondary me-2">@lang('of')</span>
                                        <!-- Dynamic Data -->
                                        <span id="datatableWithPaginationInfoTotalQty"></span>
                                    </div>
                                </div>
                                <!-- End Col -->

                                <div class="col-sm-auto">
                                    <div class="d-flex justify-content-center justify-content-sm-end">
                                        <!-- Pagination -->
                                        <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                                    </div>
                                </div>
                                <!-- End Col -->
                            </div>
                            <!-- End Row -->
                        </div>
                        <!-- End Footer -->
                    @else
                        <div class="card-body">
                            <div class="text-center p-4">
                                <img class="dataTables-image mb-3" src="{{ asset('assets/admin/img/oc-error.svg') }}"
                                     alt="Image Description" data-hs-theme-appearance="default">
                                <img class="dataTables-image mb-3" src="{{ asset('assets/admin/img/oc-error-light.svg') }}"
                                     alt="Image Description" data-hs-theme-appearance="dark">
                                <p class="mb-0">@lang('No transactions found for this account')</p>
                                <a href="{{ route('admin.forex.accounts.fund', $account->id) }}" class="btn btn-primary mt-3">
                                    <i class="bi-plus-circle me-1"></i> @lang('Fund Account')
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
                <!-- End Transaction History -->
            </div>
        </div>
        <!-- End Account Overview -->
    </div>
@endsection

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
@endpush

@push('js-lib')
    <script src="{{ asset('assets/admin/js/tom-select.complete.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/hs.datatables.js') }}"></script>
@endpush

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            HSCore.components.HSTomSelect.init('.js-select');
            HSCore.components.HSDatatables.init($('#datatable'));
        });
    </script>
@endpush
