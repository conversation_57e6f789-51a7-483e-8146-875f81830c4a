<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Models\ForexEmailLog;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;
use Illuminate\Support\Facades\DB;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING EMAIL COMMUNICATIONS TAB ===\n\n";

// Check initial counts
$initialQueueCount = DB::table('jobs')->count();
$initialEmailLogCount = ForexEmailLog::count();

echo "Initial queue jobs: {$initialQueueCount}\n";
echo "Initial email logs: {$initialEmailLogCount}\n\n";

// Get services
$walletService = new ForexWalletService();
$bookingService = new ForexBookingService($walletService);

// Get USD account
$usdAccount = ForexAccount::byType('USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

// Create a test booking
$bookingData = [
    'client_type' => 'external',
    'client_name' => 'Email Communications Test',
    'client_email' => '<EMAIL>',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 20.00,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test account for email communications',
];

try {
    echo "Creating booking...\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    
    echo "✅ Booking created: {$booking->booking_reference}\n";
    echo "✅ Email sent flag: " . ($booking->email_sent ? 'Yes' : 'No') . "\n\n";
    
    // Check email logs
    $emailLogs = $booking->emailLogs;
    echo "Email logs created: {$emailLogs->count()}\n";
    
    foreach ($emailLogs as $log) {
        echo "- Type: {$log->email_type}\n";
        echo "  Status: {$log->status}\n";
        echo "  To: {$log->email_to}\n";
        echo "  Subject: {$log->email_subject}\n";
        echo "  Created: {$log->created_at}\n\n";
    }
    
    // Test payment reminder
    echo "Sending payment reminder...\n";
    $bookingService->sendForexPaymentReminderEmail($booking, 'Test reminder for email communications tab');
    
    // Refresh and check logs again
    $booking->refresh();
    $emailLogs = $booking->emailLogs;
    echo "Total email logs after reminder: {$emailLogs->count()}\n";
    
    // Check queue
    $finalQueueCount = DB::table('jobs')->count();
    echo "Final queue jobs: {$finalQueueCount}\n";
    echo "Jobs added: " . ($finalQueueCount - $initialQueueCount) . "\n\n";
    
    echo "🎉 SUCCESS! Email communications tab should now show:\n";
    echo "- Booking confirmation email\n";
    echo "- Payment reminder email\n";
    echo "- All with proper status tracking\n\n";
    
    echo "To view in admin panel:\n";
    echo "1. Go to /admin/forex/bookings/{$booking->id}\n";
    echo "2. Check the 'Email Communications' section\n";
    echo "3. Process queue: php artisan queue:work --once\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
