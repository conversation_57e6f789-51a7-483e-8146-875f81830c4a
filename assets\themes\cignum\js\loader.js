(function(){/*

    Copyright The Closure Library Authors.
    SPDX-License-Identifier: Apache-2.0
   */
   'use strict';var l;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}function n(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}}function ba(a){if(!(a instanceof Array)){a=n(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a}function ca(a,b,c){a instanceof String&&(a=String(a));for(var d=a.length,e=0;e<d;e++){var g=a[e];if(b.call(c,g,e,a))return{U:e,Z:g}}return{U:-1,Z:void 0}}
   var da="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};function ea(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var q=ea(this);
   function r(a,b){if(b)a:{var c=q;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&da(c,a,{configurable:!0,writable:!0,value:b})}}r("Array.prototype.find",function(a){return a?a:function(b,c){return ca(this,b,c).Z}});
   function t(a,b,c){if(null==a)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""}r("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=t(this,b,"endsWith");void 0===c&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;0<e&&0<c;)if(d[--c]!=b[--e])return!1;return 0>=e}});
   r("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=t(this,b,"startsWith"),e=d.length,g=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var f=0;f<g&&c<e;)if(d[c++]!=b[f++])return!1;return f>=g}});r("String.prototype.repeat",function(a){return a?a:function(b){var c=t(this,null,"repeat");if(0>b||1342177279<b)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}});
   r("String.prototype.trimLeft",function(a){function b(){return this.replace(/^[\s\xa0]+/,"")}return a||b});r("String.prototype.trimStart",function(a){return a||String.prototype.trimLeft});
   r("Promise",function(a){function b(f){this.g=0;this.i=void 0;this.h=[];this.s=!1;var h=this.l();try{f(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.g=null}function d(f){return f instanceof b?f:new b(function(h){h(f)})}if(a)return a;c.prototype.h=function(f){if(null==this.g){this.g=[];var h=this;this.i(function(){h.m()})}this.g.push(f)};var e=q.setTimeout;c.prototype.i=function(f){e(f,0)};c.prototype.m=function(){for(;this.g&&this.g.length;){var f=this.g;this.g=[];for(var h=0;h<f.length;++h){var k=
   f[h];f[h]=null;try{k()}catch(m){this.l(m)}}}this.g=null};c.prototype.l=function(f){this.i(function(){throw f;})};b.prototype.l=function(){function f(m){return function(p){k||(k=!0,m.call(h,p))}}var h=this,k=!1;return{resolve:f(this.K),reject:f(this.m)}};b.prototype.K=function(f){if(f===this)this.m(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof b)this.$(f);else{a:switch(typeof f){case "object":var h=null!=f;break a;case "function":h=!0;break a;default:h=!1}h?this.J(f):this.o(f)}};
   b.prototype.J=function(f){var h=void 0;try{h=f.then}catch(k){this.m(k);return}"function"==typeof h?this.aa(h,f):this.o(f)};b.prototype.m=function(f){this.v(2,f)};b.prototype.o=function(f){this.v(1,f)};b.prototype.v=function(f,h){if(0!=this.g)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.g);this.g=f;this.i=h;2===this.g&&this.M();this.F()};b.prototype.M=function(){var f=this;e(function(){if(f.G()){var h=q.console;"undefined"!==typeof h&&h.error(f.i)}},1)};b.prototype.G=
   function(){if(this.s)return!1;var f=q.CustomEvent,h=q.Event,k=q.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof f?f=new f("unhandledrejection",{cancelable:!0}):"function"===typeof h?f=new h("unhandledrejection",{cancelable:!0}):(f=q.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.i;return k(f)};b.prototype.F=function(){if(null!=this.h){for(var f=0;f<this.h.length;++f)g.h(this.h[f]);this.h=null}};var g=new c;b.prototype.$=
   function(f){var h=this.l();f.L(h.resolve,h.reject)};b.prototype.aa=function(f,h){var k=this.l();try{f.call(h,k.resolve,k.reject)}catch(m){k.reject(m)}};b.prototype.then=function(f,h){function k(B,J){return"function"==typeof B?function(va){try{m(B(va))}catch(wa){p(wa)}}:J}var m,p,x=new b(function(B,J){m=B;p=J});this.L(k(f,m),k(h,p));return x};b.prototype.catch=function(f){return this.then(void 0,f)};b.prototype.L=function(f,h){function k(){switch(m.g){case 1:f(m.i);break;case 2:h(m.i);break;default:throw Error("Unexpected state: "+
   m.g);}}var m=this;null==this.h?g.h(k):this.h.push(k);this.s=!0};b.resolve=d;b.reject=function(f){return new b(function(h,k){k(f)})};b.race=function(f){return new b(function(h,k){for(var m=n(f),p=m.next();!p.done;p=m.next())d(p.value).L(h,k)})};b.all=function(f){var h=n(f),k=h.next();return k.done?d([]):new b(function(m,p){function x(va){return function(wa){B[va]=wa;J--;0==J&&m(B)}}var B=[],J=0;do B.push(void 0),J++,d(k.value).L(x(B.length-1),p),k=h.next();while(!k.done)})};return b});
   r("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});r("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b||Object.is(g,b))return!0}return!1}});r("String.prototype.includes",function(a){return a?a:function(b,c){return-1!==t(this,b,"includes").indexOf(b,c||0)}});
   r("Array.prototype.copyWithin",function(a){function b(c){c=Number(c);return Infinity===c||-Infinity===c?c:c|0}return a?a:function(c,d,e){var g=this.length;c=b(c);d=b(d);e=void 0===e?g:b(e);c=0>c?Math.max(g+c,0):Math.min(c,g);d=0>d?Math.max(g+d,0):Math.min(d,g);e=0>e?Math.max(g+e,0):Math.min(e,g);if(c<d)for(;d<e;)d in this?this[c++]=this[d++]:(delete this[c++],d++);else for(e=Math.min(e,g+d-c),c+=e-d;e>d;)--e in this?this[--c]=this[e]:delete this[--c];return this}});
   r("Symbol",function(a){function b(e){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c("jscomp_symbol_"+(e||"")+"_"+d++,e)}function c(e,g){this.g=e;da(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.g};var d=0;return b});
   r("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=q[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&da(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});r("Symbol.asyncIterator",function(a){return a?a:Symbol("Symbol.asyncIterator")});
   function fa(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}function ha(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var g=c++;return{value:b(g,a[g]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}r("Array.prototype.entries",function(a){return a?a:function(){return ha(this,function(b,c){return[b,c]})}});
   r("Array.prototype.fill",function(a){return a?a:function(b,c,d){var e=this.length||0;0>c&&(c=Math.max(0,e+c));if(null==d||d>e)d=e;d=Number(d);0>d&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});r("Array.prototype.findIndex",function(a){return a?a:function(b,c){return ca(this,b,c).U}});
   r("Array.prototype.flat",function(a){return a?a:function(b){b=void 0===b?1:b;for(var c=[],d=0;d<this.length;d++){var e=this[d];Array.isArray(e)&&0<b?(e=Array.prototype.flat.call(e,b-1),c.push.apply(c,e)):c.push(e)}return c}});r("Array.prototype.flatMap",function(a){return a?a:function(b,c){for(var d=[],e=0;e<this.length;e++){var g=b.call(c,this[e],e,this);Array.isArray(g)?d.push.apply(d,g):d.push(g)}return d}});
   r("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],g="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof g){b=g.call(b);for(var f=0;!(g=b.next()).done;)e.push(c.call(d,g.value,f++))}else for(g=b.length,f=0;f<g;f++)e.push(c.call(d,b[f],f));return e}});r("Array.prototype.keys",function(a){return a?a:function(){return ha(this,function(b){return b})}});r("Array.of",function(a){return a?a:function(b){return Array.from(arguments)}});
   r("Array.prototype.values",function(a){return a?a:function(){return ha(this,function(b,c){return c})}});var ia;if("function"==typeof Object.setPrototypeOf)ia=Object.setPrototypeOf;else{var ja;a:{var ka={a:!0},la={};try{la.__proto__=ka;ja=la.a;break a}catch(a){}ja=!1}ia=ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var ma=ia;r("globalThis",function(a){return a||q});function u(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
   r("WeakMap",function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=n(k);for(var m;!(m=k.next()).done;)m=m.value,this.set(m[0],m[1])}}function c(){}function d(k){var m=typeof k;return"object"===m&&null!==k||"function"===m}function e(k){if(!u(k,f)){var m=new c;da(k,f,{value:m})}}function g(k){var m=Object[k];m&&(Object[k]=function(p){if(p instanceof c)return p;Object.isExtensible(p)&&e(p);return m(p)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),m=Object.seal({}),
   p=new a([[k,2],[m,3]]);if(2!=p.get(k)||3!=p.get(m))return!1;p.delete(k);p.set(m,4);return!p.has(k)&&4==p.get(m)}catch(x){return!1}}())return a;var f="$jscomp_hidden_"+Math.random();g("freeze");g("preventExtensions");g("seal");var h=0;b.prototype.set=function(k,m){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!u(k,f))throw Error("WeakMap key fail: "+k);k[f][this.g]=m;return this};b.prototype.get=function(k){return d(k)&&u(k,f)?k[f][this.g]:void 0};b.prototype.has=function(k){return d(k)&&u(k,
   f)&&u(k[f],this.g)};b.prototype.delete=function(k){return d(k)&&u(k,f)&&u(k[f],this.g)?delete k[f][this.g]:!1};return b});
   r("Map",function(a){function b(){var h={};return h.B=h.next=h.head=h}function c(h,k){var m=h.g;return fa(function(){if(m){for(;m.head!=h.g;)m=m.B;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})}function d(h,k){var m=k&&typeof k;"object"==m||"function"==m?g.has(k)?m=g.get(k):(m=""+ ++f,g.set(k,m)):m="p_"+k;var p=h.h[m];if(p&&u(h.h,m))for(h=0;h<p.length;h++){var x=p[h];if(k!==k&&x.key!==x.key||k===x.key)return{id:m,list:p,index:h,u:x}}return{id:m,list:p,
   index:-1,u:void 0}}function e(h){this.h={};this.g=b();this.size=0;if(h){h=n(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(n([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var m=k.entries(),p=m.next();if(p.done||p.value[0]!=h||"s"!=p.value[1])return!1;p=m.next();return p.done||4!=p.value[0].x||
   "t"!=p.value[1]||!m.next().done?!1:!0}catch(x){return!1}}())return a;var g=new WeakMap;e.prototype.set=function(h,k){h=0===h?0:h;var m=d(this,h);m.list||(m.list=this.h[m.id]=[]);m.u?m.u.value=k:(m.u={next:this.g,B:this.g.B,head:this.g,key:h,value:k},m.list.push(m.u),this.g.B.next=m.u,this.g.B=m.u,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.u&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.h[h.id],h.u.B.next=h.u.next,h.u.next.B=h.u.B,h.u.head=null,this.size--,
   !0):!1};e.prototype.clear=function(){this.h={};this.g=this.g.B=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).u};e.prototype.get=function(h){return(h=d(this,h).u)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,function(h){return h.value})};e.prototype.forEach=function(h,k){for(var m=this.entries(),p;!(p=m.next()).done;)p=p.value,
   h.call(k,p[1],p[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var f=0;return e});r("Math.acosh",function(a){return a?a:function(b){b=Number(b);return Math.log(b+Math.sqrt(b*b-1))}});r("Math.asinh",function(a){return a?a:function(b){b=Number(b);if(0===b)return b;var c=Math.log(Math.abs(b)+Math.sqrt(b*b+1));return 0>b?-c:c}});
   r("Math.log1p",function(a){return a?a:function(b){b=Number(b);if(.25>b&&-.25<b){for(var c=b,d=1,e=b,g=0,f=1;g!=e;)c*=b,f*=-1,e=(g=e)+f*c/++d;return e}return Math.log(1+b)}});r("Math.atanh",function(a){if(a)return a;var b=Math.log1p;return function(c){c=Number(c);return(b(c)-b(-c))/2}});r("Math.cbrt",function(a){return a?a:function(b){if(0===b)return b;b=Number(b);var c=Math.pow(Math.abs(b),1/3);return 0>b?-c:c}});
   r("Math.clz32",function(a){return a?a:function(b){b=Number(b)>>>0;if(0===b)return 32;var c=0;0===(b&4294901760)&&(b<<=16,c+=16);0===(b&4278190080)&&(b<<=8,c+=8);0===(b&4026531840)&&(b<<=4,c+=4);0===(b&3221225472)&&(b<<=2,c+=2);0===(b&2147483648)&&c++;return c}});r("Math.cosh",function(a){if(a)return a;var b=Math.exp;return function(c){c=Number(c);return(b(c)+b(-c))/2}});
   r("Math.expm1",function(a){return a?a:function(b){b=Number(b);if(.25>b&&-.25<b){for(var c=b,d=1,e=b,g=0;g!=e;)c*=b/++d,e=(g=e)+c;return e}return Math.exp(b)-1}});r("Math.fround",function(a){if(a)return a;if("function"!==typeof Float32Array)return function(c){return c};var b=new Float32Array(1);return function(c){b[0]=c;return b[0]}});
   r("Math.hypot",function(a){return a?a:function(b){if(2>arguments.length)return arguments.length?Math.abs(arguments[0]):0;var c,d,e;for(c=e=0;c<arguments.length;c++)e=Math.max(e,Math.abs(arguments[c]));if(1E100<e||1E-100>e){if(!e)return e;for(c=d=0;c<arguments.length;c++){var g=Number(arguments[c])/e;d+=g*g}return Math.sqrt(d)*e}for(c=d=0;c<arguments.length;c++)g=Number(arguments[c]),d+=g*g;return Math.sqrt(d)}});
   r("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}});r("Math.log10",function(a){return a?a:function(b){return Math.log(b)/Math.LN10}});r("Math.log2",function(a){return a?a:function(b){return Math.log(b)/Math.LN2}});r("Math.sign",function(a){return a?a:function(b){b=Number(b);return 0===b||isNaN(b)?b:0<b?1:-1}});
   r("Math.sinh",function(a){if(a)return a;var b=Math.exp;return function(c){c=Number(c);return 0===c?c:(b(c)-b(-c))/2}});r("Math.tanh",function(a){return a?a:function(b){b=Number(b);if(0===b)return b;var c=Math.exp(-2*Math.abs(b));c=(1-c)/(1+c);return 0>b?-c:c}});r("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||Infinity===b||-Infinity===b||0===b)return b;var c=Math.floor(Math.abs(b));return 0>b?-c:c}});r("Number.EPSILON",function(){return Math.pow(2,-52)});
   r("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});r("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});r("Number.isFinite",function(a){return a?a:function(b){return"number"!==typeof b?!1:!isNaN(b)&&Infinity!==b&&-Infinity!==b}});r("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});r("Number.isNaN",function(a){return a?a:function(b){return"number"===typeof b&&isNaN(b)}});
   r("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});r("Number.parseFloat",function(a){return a||parseFloat});r("Number.parseInt",function(a){return a||parseInt});var na="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)u(d,e)&&(a[e]=d[e])}return a};r("Object.assign",function(a){return a||na});
   r("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)u(b,d)&&c.push([d,b[d]]);return c}});r("Object.fromEntries",function(a){return a?a:function(b){var c={};if(!(Symbol.iterator in b))throw new TypeError(""+b+" is not iterable");b=b[Symbol.iterator].call(b);for(var d=b.next();!d.done;d=b.next()){d=d.value;if(Object(d)!==d)throw new TypeError("iterable for fromEntries should yield objects");c[d[0]]=d[1]}return c}});r("Reflect",function(a){return a?a:{}});
   r("Object.getOwnPropertySymbols",function(a){return a?a:function(){return[]}});r("Reflect.ownKeys",function(a){return a?a:function(b){var c=[],d=Object.getOwnPropertyNames(b);b=Object.getOwnPropertySymbols(b);for(var e=0;e<d.length;e++)("jscomp_symbol_"==d[e].substring(0,14)?b:c).push(d[e]);return c.concat(b)}});r("Object.getOwnPropertyDescriptors",function(a){return a?a:function(b){for(var c={},d=Reflect.ownKeys(b),e=0;e<d.length;e++)c[d[e]]=Object.getOwnPropertyDescriptor(b,d[e]);return c}});
   r("Object.setPrototypeOf",function(a){return a||ma});r("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)u(b,d)&&c.push(b[d]);return c}});r("Promise.allSettled",function(a){function b(d){return{status:"fulfilled",value:d}}function c(d){return{status:"rejected",reason:d}}return a?a:function(d){var e=this;d=Array.from(d,function(g){return e.resolve(g).then(b,c)});return e.all(d)}});
   r("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}});r("Reflect.apply",function(a){if(a)return a;var b=Function.prototype.apply;return function(c,d,e){return b.call(c,d,e)}});
   var oa="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},pa=function(){function a(){function c(){}new c;Reflect.construct(c,[],function(){});return new c instanceof c}if("undefined"!=typeof Reflect&&Reflect.construct){if(a())return Reflect.construct;var b=Reflect.construct;return function(c,d,e){c=b(c,d);e&&Reflect.setPrototypeOf(c,e.prototype);return c}}return function(c,d,e){void 0===e&&(e=c);e=oa(e.prototype||Object.prototype);return Function.prototype.apply.call(c,
   e,d)||e}}();r("Reflect.construct",function(){return pa});r("Reflect.defineProperty",function(a){return a?a:function(b,c,d){try{Object.defineProperty(b,c,d);var e=Object.getOwnPropertyDescriptor(b,c);return e?e.configurable===(d.configurable||!1)&&e.enumerable===(d.enumerable||!1)&&("value"in e?e.value===d.value&&e.writable===(d.writable||!1):e.get===d.get&&e.set===d.set):!1}catch(g){return!1}}});r("Reflect.deleteProperty",function(a){return a?a:function(b,c){if(!u(b,c))return!0;try{return delete b[c]}catch(d){return!1}}});
   r("Reflect.getOwnPropertyDescriptor",function(a){return a||Object.getOwnPropertyDescriptor});r("Reflect.getPrototypeOf",function(a){return a||Object.getPrototypeOf});function qa(a,b){for(;a;){var c=Reflect.getOwnPropertyDescriptor(a,b);if(c)return c;a=Reflect.getPrototypeOf(a)}}r("Reflect.get",function(a){return a?a:function(b,c,d){if(2>=arguments.length)return b[c];var e=qa(b,c);if(e)return e.get?e.get.call(d):e.value}});r("Reflect.has",function(a){return a?a:function(b,c){return c in b}});
   r("Reflect.isExtensible",function(a){return a?a:"function"==typeof Object.isExtensible?Object.isExtensible:function(){return!0}});r("Reflect.preventExtensions",function(a){return a?a:"function"!=typeof Object.preventExtensions?function(){return!1}:function(b){Object.preventExtensions(b);return!Object.isExtensible(b)}});
   r("Reflect.set",function(a){return a?a:function(b,c,d,e){var g=qa(b,c);return g?g.set?(g.set.call(3<arguments.length?e:b,d),!0):g.writable&&!Object.isFrozen(b)?(b[c]=d,!0):!1:Reflect.isExtensible(b)?(b[c]=d,!0):!1}});r("Reflect.setPrototypeOf",function(a){return a?a:ma?function(b,c){try{return ma(b,c),!0}catch(d){return!1}}:null});
   r("Set",function(a){function b(c){this.g=new Map;if(c){c=n(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(n([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),g=e.next();if(g.done||g.value[0]!=c||g.value[1]!=c)return!1;g=e.next();return g.done||g.value[0]==c||4!=g.value[0].x||
   g.value[1]!=g.value[0]?!1:e.next().done}catch(f){return!1}}())return a;b.prototype.add=function(c){c=0===c?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;b.prototype[Symbol.iterator]=
   b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(g){return c.call(d,g,g,e)})};return b});r("String.prototype.codePointAt",function(a){return a?a:function(b){var c=t(this,null,"codePointAt"),d=c.length;b=Number(b)||0;if(0<=b&&b<d){b|=0;var e=c.charCodeAt(b);if(55296>e||56319<e||b+1===d)return e;b=c.charCodeAt(b+1);return 56320>b||57343<b?e:1024*(e-55296)+b+9216}}});
   r("String.fromCodePoint",function(a){return a?a:function(b){for(var c="",d=0;d<arguments.length;d++){var e=Number(arguments[d]);if(0>e||1114111<e||e!==Math.floor(e))throw new RangeError("invalid_code_point "+e);65535>=e?c+=String.fromCharCode(e):(e-=65536,c+=String.fromCharCode(e>>>10&1023|55296),c+=String.fromCharCode(e&1023|56320))}return c}});
   r("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,g={next:function(){if(e)return{value:void 0,done:!0};var f=c.exec(d);if(!f)return e=!0,{value:void 0,done:!0};""===f[0]&&(c.lastIndex+=1);return{value:f,done:!1}}};g[Symbol.iterator]=function(){return g};return g}});
   function ra(a,b){a=void 0!==a?String(a):" ";return 0<b&&a?a.repeat(Math.ceil(b/a.length)).substring(0,b):""}r("String.prototype.padEnd",function(a){return a?a:function(b,c){var d=t(this,null,"padStart");return d+ra(c,b-d.length)}});r("String.prototype.padStart",function(a){return a?a:function(b,c){var d=t(this,null,"padStart");return ra(c,b-d.length)+d}});r("String.prototype.trimRight",function(a){function b(){return this.replace(/[\s\xa0]+$/,"")}return a||b});
   r("String.prototype.trimEnd",function(a){return a||String.prototype.trimRight});function v(a){return a?a:Array.prototype.copyWithin}r("Int8Array.prototype.copyWithin",v);r("Uint8Array.prototype.copyWithin",v);r("Uint8ClampedArray.prototype.copyWithin",v);r("Int16Array.prototype.copyWithin",v);r("Uint16Array.prototype.copyWithin",v);r("Int32Array.prototype.copyWithin",v);r("Uint32Array.prototype.copyWithin",v);r("Float32Array.prototype.copyWithin",v);r("Float64Array.prototype.copyWithin",v);
   function w(a){return a?a:Array.prototype.fill}r("Int8Array.prototype.fill",w);r("Uint8Array.prototype.fill",w);r("Uint8ClampedArray.prototype.fill",w);r("Int16Array.prototype.fill",w);r("Uint16Array.prototype.fill",w);r("Int32Array.prototype.fill",w);r("Uint32Array.prototype.fill",w);r("Float32Array.prototype.fill",w);r("Float64Array.prototype.fill",w);
   r("WeakSet",function(a){function b(c){this.g=new WeakMap;if(c){c=n(c);for(var d;!(d=c.next()).done;)this.add(d.value)}}if(function(){if(!a||!Object.seal)return!1;try{var c=Object.seal({}),d=Object.seal({}),e=new a([c]);if(!e.has(c)||e.has(d))return!1;e.delete(c);e.add(d);return!e.has(c)&&e.has(d)}catch(g){return!1}}())return a;b.prototype.add=function(c){this.g.set(c,!0);return this};b.prototype.has=function(c){return this.g.has(c)};b.prototype.delete=function(c){return this.g.delete(c)};return b});
   var y=this||self,sa=/^[\w+/_-]+[=]{0,2}$/,ta=null;function ua(a){return(a=a.querySelector&&a.querySelector("script[nonce]"))&&(a=a.nonce||a.getAttribute("nonce"))&&sa.test(a)?a:""}function z(a){a=a.split(".");for(var b=y,c=0;c<a.length;c++)if(b=b[a[c]],null==b)return null;return b}function A(){}function xa(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function ya(a,b,c){return a.call.apply(a.bind,arguments)}
   function za(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function C(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?C=ya:C=za;return C.apply(null,arguments)}
   function D(a,b){a=a.split(".");var c=y;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}function E(a,b){function c(){}c.prototype=b.prototype;a.Ma=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.qa=function(d,e,g){for(var f=Array(arguments.length-2),h=2;h<arguments.length;h++)f[h-2]=arguments[h];return b.prototype[e].apply(d,f)}}function Aa(a){return a};function F(a){if(Error.captureStackTrace)Error.captureStackTrace(this,F);else{var b=Error().stack;b&&(this.stack=b)}a&&(this.message=String(a))}E(F,Error);F.prototype.name="CustomError";function G(a,b){this.g=a===Ba&&b||"";this.h=Ca}G.prototype.V=!0;G.prototype.T=function(){return this.g};function Da(a){return a instanceof G&&a.constructor===G&&a.h===Ca?a.g:"type_error:Const"}function H(a){return new G(Ba,a)}var Ca={},Ba={};var I={j:{}};
   I.j.O={oa:{"gstatic.com":{loader:H("https://www.gstatic.com/charts/%{version}/loader.js"),debug:H("https://www.gstatic.com/charts/debug/%{version}/js/jsapi_debug_%{package}_module.js"),debug_i18n:H("https://www.gstatic.com/charts/debug/%{version}/i18n/jsapi_debug_i18n_%{package}_module__%{language}.js"),compiled:H("https://www.gstatic.com/charts/%{version}/js/jsapi_compiled_%{package}_module.js"),compiled_i18n:H("https://www.gstatic.com/charts/%{version}/i18n/jsapi_compiled_i18n_%{package}_module__%{language}.js"),css:H("https://www.gstatic.com/charts/%{version}/css/%{subdir}/%{filename}"),
   css2:H("https://www.gstatic.com/charts/%{version}/css/%{subdir1}/%{subdir2}/%{filename}"),third_party:H("https://www.gstatic.com/charts/%{version}/third_party/%{subdir}/%{filename}"),third_party2:H("https://www.gstatic.com/charts/%{version}/third_party/%{subdir1}/%{subdir2}/%{filename}"),third_party_gen:H("https://www.gstatic.com/charts/%{version}/third_party/%{subdir}/%{filename}")},"gstatic.cn":{loader:H("https://www.gstatic.cn/charts/%{version}/loader.js"),debug:H("https://www.gstatic.cn/charts/debug/%{version}/js/jsapi_debug_%{package}_module.js"),
   debug_i18n:H("https://www.gstatic.cn/charts/debug/%{version}/i18n/jsapi_debug_i18n_%{package}_module__%{language}.js"),compiled:H("https://www.gstatic.cn/charts/%{version}/js/jsapi_compiled_%{package}_module.js"),compiled_i18n:H("https://www.gstatic.cn/charts/%{version}/i18n/jsapi_compiled_i18n_%{package}_module__%{language}.js"),css:H("https://www.gstatic.cn/charts/%{version}/css/%{subdir}/%{filename}"),css2:H("https://www.gstatic.cn/charts/%{version}/css/%{subdir1}/%{subdir2}/%{filename}"),third_party:H("https://www.gstatic.cn/charts/%{version}/third_party/%{subdir}/%{filename}"),
   third_party2:H("https://www.gstatic.cn/charts/%{version}/third_party/%{subdir1}/%{subdir2}/%{filename}"),third_party_gen:H("https://www.gstatic.cn/charts/%{version}/third_party/%{subdir}/%{filename}")}},ga:["default"],ua:{"default":[],graphics:["default"],ui:["graphics"],ui_base:["graphics"],flashui:["ui"],fw:["ui"],geo:["ui"],annotatedtimeline:["annotationchart"],annotationchart:["ui","controls","corechart","table"],areachart:"browserchart",bar:["fw","dygraph","webfontloader"],barchart:"browserchart",
   browserchart:["ui"],bubbles:["fw","d3"],calendar:["fw"],charteditor:"ui corechart imagechart annotatedtimeline gauge geochart motionchart orgchart table".split(" "),charteditor_base:"ui_base corechart imagechart annotatedtimeline gauge geochart motionchart orgchart table_base".split(" "),circles:["fw","d3"],clusterchart:["corechart","d3"],columnchart:"browserchart",controls:["ui"],controls_base:["ui_base"],corechart:["ui"],gantt:["fw","dygraph"],gauge:["ui"],geochart:["geo"],geomap:["flashui","geo"],
   geomap_base:["ui_base"],helloworld:["fw"],imagechart:["ui"],imageareachart:"imagechart",imagebarchart:"imagechart",imagelinechart:"imagechart",imagepiechart:"imagechart",imagesparkline:"imagechart",line:["fw","dygraph","webfontloader"],linechart:"browserchart",map:["geo"],matrix:["vegachart"],motionchart:["flashui"],orgchart:["ui"],overtimecharts:["ui","corechart"],piechart:"browserchart",sankey:["fw","d3","d3.sankey"],scatter:["fw","dygraph","webfontloader"],scatterchart:"browserchart",sunburst:["fw",
   "d3"],streamgraph:["fw","d3"],table:["ui"],table_base:["ui_base"],timeline:["fw","ui","dygraph"],treemap:["ui"],vegachart:["graphics"],wordtree:["ui"]},Oa:{d3:{subdir1:"d3",subdir2:"v5",filename:"d3.js"},"d3.sankey":{subdir1:"d3_sankey",subdir2:"v4",filename:"d3.sankey.js"},webfontloader:{subdir:"webfontloader",filename:"webfont.js"}},Na:{dygraph:{subdir:"dygraphs",filename:"dygraph-tickers-combined.js"}},sa:{"default":[{subdir:"core",filename:"tooltip.css"}],annotationchart:[{subdir:"annotationchart",
   filename:"annotationchart.css"}],charteditor:[{subdir:"charteditor",filename:"charteditor.css"}],charteditor_base:[{subdir:"charteditor_base",filename:"charteditor_base.css"}],controls:[{subdir:"controls",filename:"controls.css"}],imagesparkline:[{subdir:"imagechart",filename:"imagesparkline.css"}],orgchart:[{subdir:"orgchart",filename:"orgchart.css"}],table:[{subdir:"table",filename:"table.css"},{subdir:"util",filename:"format.css"}],table_base:[{subdir:"util",filename:"format.css"},{subdir:"table",
   filename:"table_base.css"}],ui:[{subdir:"util",filename:"util.css"}],ui_base:[{subdir:"util",filename:"util_base.css"}]}};I.j.ba={ia:{"chrome-frame":{versions:{"1.0.0":{uncompressed:"CFInstall.js",compressed:"CFInstall.min.js"},"1.0.1":{uncompressed:"CFInstall.js",compressed:"CFInstall.min.js"},"1.0.2":{uncompressed:"CFInstall.js",compressed:"CFInstall.min.js"}},aliases:{1:"1.0.2","1.0":"1.0.2"}},swfobject:{versions:{"2.1":{uncompressed:"swfobject_src.js",compressed:"swfobject.js"},"2.2":{uncompressed:"swfobject_src.js",compressed:"swfobject.js"}},aliases:{2:"2.2"}},"ext-core":{versions:{"3.1.0":{uncompressed:"ext-core-debug.js",
   compressed:"ext-core.js"},"3.0.0":{uncompressed:"ext-core-debug.js",compressed:"ext-core.js"}},aliases:{3:"3.1.0","3.0":"3.0.0","3.1":"3.1.0"}},scriptaculous:{versions:{"1.8.3":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"},"1.9.0":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"},"1.8.1":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"},"1.8.2":{uncompressed:"scriptaculous.js",compressed:"scriptaculous.js"}},aliases:{1:"1.9.0","1.8":"1.8.3","1.9":"1.9.0"}},
   webfont:{versions:{"1.0.12":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.13":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.14":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.15":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.10":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.11":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.27":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.28":{uncompressed:"webfont_debug.js",
   compressed:"webfont.js"},"1.0.29":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.23":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.24":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.25":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.26":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.21":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.22":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.3":{uncompressed:"webfont_debug.js",
   compressed:"webfont.js"},"1.0.4":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.5":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.6":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.9":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.16":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.17":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.0":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.18":{uncompressed:"webfont_debug.js",
   compressed:"webfont.js"},"1.0.1":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.19":{uncompressed:"webfont_debug.js",compressed:"webfont.js"},"1.0.2":{uncompressed:"webfont_debug.js",compressed:"webfont.js"}},aliases:{1:"1.0.29","1.0":"1.0.29"}},jqueryui:{versions:{"1.8.17":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.16":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.15":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.14":{uncompressed:"jquery-ui.js",
   compressed:"jquery-ui.min.js"},"1.8.4":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.13":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.5":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.12":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.6":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.11":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.7":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},
   "1.8.10":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.8":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.9":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.6.0":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.0":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.5.2":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.0":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.1":{uncompressed:"jquery-ui.js",
   compressed:"jquery-ui.min.js"},"1.5.3":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.1":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.2":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.8.2":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"},"1.7.3":{uncompressed:"jquery-ui.js",compressed:"jquery-ui.min.js"}},aliases:{1:"1.8.17","1.5":"1.5.3","1.6":"1.6.0","1.7":"1.7.3","1.8":"1.8.17","1.8.3":"1.8.4"}},mootools:{versions:{"1.3.0":{uncompressed:"mootools.js",
   compressed:"mootools-yui-compressed.js"},"1.2.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.1.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.4.0":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.3.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.4.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},
   "1.3.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.3":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.4.2":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.4":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.2.5":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"},"1.1.1":{uncompressed:"mootools.js",compressed:"mootools-yui-compressed.js"}},aliases:{1:"1.1.2","1.1":"1.1.2","1.2":"1.2.5",
   "1.3":"1.3.2","1.4":"1.4.2","1.11":"1.1.1"}},yui:{versions:{"2.8.0r4":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.9.0":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.8.1":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.6.0":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"},"2.7.0":{uncompressed:"build/yuiloader/yuiloader.js",
   compressed:"build/yuiloader/yuiloader-min.js"},"3.3.0":{uncompressed:"build/yui/yui.js",compressed:"build/yui/yui-min.js"},"2.8.2r1":{uncompressed:"build/yuiloader/yuiloader.js",compressed:"build/yuiloader/yuiloader-min.js"}},aliases:{2:"2.9.0","2.6":"2.6.0","2.7":"2.7.0","2.8":"2.8.2r1","2.8.0":"2.8.0r4","2.8.2":"2.8.2r1","2.9":"2.9.0",3:"3.3.0","3.3":"3.3.0"}},prototype:{versions:{"1.6.1.0":{uncompressed:"prototype.js",compressed:"prototype.js"},"1.6.0.2":{uncompressed:"prototype.js",compressed:"prototype.js"},
   "1.7.0.0":{uncompressed:"prototype.js",compressed:"prototype.js"},"1.6.0.3":{uncompressed:"prototype.js",compressed:"prototype.js"}},aliases:{1:"1.7.0.0","1.6":"1.6.1.0","1.6.0":"1.6.0.3","1.6.1":"1.6.1.0","1.7":"1.7.0.0","1.7.0":"1.7.0.0"}},jquery:{versions:{"1.2.3":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.2.6":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.3.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.3.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},
   "1.3.2":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.2":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.3":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.4.4":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.5.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.5.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.5.2":{uncompressed:"jquery.js",
   compressed:"jquery.min.js"},"1.6.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.2":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.3":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.6.4":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.7.0":{uncompressed:"jquery.js",compressed:"jquery.min.js"},"1.7.1":{uncompressed:"jquery.js",compressed:"jquery.min.js"}},aliases:{1:"1.7.1","1.2":"1.2.6","1.3":"1.3.2",
   "1.4":"1.4.4","1.5":"1.5.2","1.6":"1.6.4","1.7":"1.7.1"}},dojo:{versions:{"1.3.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.4.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.3.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.5.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.4.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},
   "1.3.2":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.2.3":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.6.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.5.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.7.0":{uncompressed:"dojo/dojo.js.uncompressed.js",compressed:"dojo/dojo.js"},"1.6.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},
   "1.4.3":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.7.1":{uncompressed:"dojo/dojo.js.uncompressed.js",compressed:"dojo/dojo.js"},"1.7.2":{uncompressed:"dojo/dojo.js.uncompressed.js",compressed:"dojo/dojo.js"},"1.2.0":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"},"1.1.1":{uncompressed:"dojo/dojo.xd.js.uncompressed.js",compressed:"dojo/dojo.xd.js"}},aliases:{1:"1.6.1","1.1":"1.1.1","1.2":"1.2.3","1.3":"1.3.2","1.4":"1.4.3","1.5":"1.5.1",
   "1.6":"1.6.1","1.7":"1.7.2"}}}};I.j.ea={af:!0,am:!0,az:!0,ar:!0,arb:"ar",bg:!0,bn:!0,ca:!0,cs:!0,cmn:"zh",da:!0,de:!0,el:!0,en:!0,en_gb:!0,es:!0,es_419:!0,et:!0,eu:!0,fa:!0,fi:!0,fil:!0,fr:!0,fr_ca:!0,gl:!0,ka:!0,gu:!0,he:"iw",hi:!0,hr:!0,hu:!0,hy:!0,id:!0,"in":"id",is:!0,it:!0,iw:!0,ja:!0,ji:"yi",jv:!1,jw:"jv",km:!0,kn:!0,ko:!0,lo:!0,lt:!0,lv:!0,ml:!0,mn:!0,mo:"ro",mr:!0,ms:!0,nb:"no",ne:!0,nl:!0,no:!0,pl:!0,pt:"pt_br",pt_br:!0,pt_pt:!0,ro:!0,ru:!0,si:!0,sk:!0,sl:!0,sr:!0,sv:!0,sw:!0,swh:"sw",ta:!0,te:!0,th:!0,tl:"fil",tr:!0,uk:!0,
   ur:!0,vi:!0,yi:!1,zh:"zh_cn",zh_cn:!0,zh_hk:!0,zh_tw:!0,zsm:"ms",zu:!0};var Ea=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,g=0;g<d;g++)g in e&&b.call(c,e[g],g,a)},Fa=Array.prototype.map?function(a,b){return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,g=0;g<c;g++)g in e&&(d[g]=b.call(void 0,e[g],g,a));return d},Ga=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:
   function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function Ha(a){return Array.prototype.concat.apply([],arguments)}function Ia(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];var e=typeof d;e="object"!=e?e:d?Array.isArray(d)?"array":e:"null";if("array"==e||"object"==e&&"number"==typeof d.length){e=a.length||0;var g=d.length||0;a.length=e+g;for(var f=0;f<g;f++)a[e+f]=d[f]}else a.push(d)}};var Ja;function K(a,b){this.g=b===Ka?a:""}K.prototype.V=!0;K.prototype.T=function(){return this.g.toString()};K.prototype.toString=function(){return this.g+""};function La(a){return a instanceof K&&a.constructor===K?a.g:"type_error:TrustedResourceUrl"}
   function Ma(a,b){var c=Da(a);if(!Na.test(c))throw Error("Invalid TrustedResourceUrl format: "+c);a=c.replace(Oa,function(d,e){if(!Object.prototype.hasOwnProperty.call(b,e))throw Error('Found marker, "'+e+'", in format string, "'+c+'", but no valid label mapping found in args: '+JSON.stringify(b));d=b[e];return d instanceof G?Da(d):encodeURIComponent(String(d))});return Pa(a)}var Oa=/%{(\w+)}/g,Na=/^((https:)?\/\/[0-9a-z.:[\]-]+\/|\/[^/\\]|[^:/\\%]+\/|[^:/\\%]*[?#]|about:blank#)/i,Qa=/^([^?#]*)(\?[^#]*)?(#[\s\S]*)?/;
   function Ra(a,b,c){a=Ma(a,b);a=Qa.exec(La(a).toString());b=a[3]||"";return Pa(a[1]+Sa("?",a[2]||"",c)+Sa("#",b,void 0))}var Ka={};function Pa(a){if(void 0===Ja){var b=null;var c=y.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:Aa,createScript:Aa,createScriptURL:Aa})}catch(d){y.console&&y.console.error(d.message)}Ja=b}else Ja=b}a=(b=Ja)?b.createScriptURL(a):a;return new K(a,Ka)}
   function Sa(a,b,c){if(null==c)return b;if("string"===typeof c)return c?a+encodeURIComponent(c):"";for(var d in c)if(Object.prototype.hasOwnProperty.call(c,d)){var e=c[d];e=Array.isArray(e)?e:[e];for(var g=0;g<e.length;g++){var f=e[g];null!=f&&(b||(b=a),b+=(b.length>a.length?"&":"")+encodeURIComponent(d)+"="+encodeURIComponent(String(f)))}}return b};var Ta=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};function Ua(a,b){return a<b?-1:a>b?1:0};var L;a:{var Va=y.navigator;if(Va){var Wa=Va.userAgent;if(Wa){L=Wa;break a}}L=""}function M(a){return-1!=L.indexOf(a)};function Xa(a,b){for(var c in a)b.call(void 0,a[c],c,a)}var Ya="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Za(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var g=0;g<Ya.length;g++)c=Ya[g],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function $a(a){var b;(b=a.ownerDocument&&a.ownerDocument.defaultView)&&b!=y?b=ua(b.document):(null===ta&&(ta=ua(y.document)),b=ta);b&&a.setAttribute("nonce",b)};function ab(a){var b=bb;return Object.prototype.hasOwnProperty.call(b,11)?b[11]:b[11]=a(11)};var cb=M("Opera"),db=M("Trident")||M("MSIE"),eb=M("Edge"),fb=M("Gecko")&&!(-1!=L.toLowerCase().indexOf("webkit")&&!M("Edge"))&&!(M("Trident")||M("MSIE"))&&!M("Edge"),gb=-1!=L.toLowerCase().indexOf("webkit")&&!M("Edge"),hb;
   a:{var ib="",jb=function(){var a=L;if(fb)return/rv:([^\);]+)(\)|;)/.exec(a);if(eb)return/Edge\/([\d\.]+)/.exec(a);if(db)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(gb)return/WebKit\/(\S+)/.exec(a);if(cb)return/(?:Version)[ \/]?(\S+)/.exec(a)}();jb&&(ib=jb?jb[1]:"");if(db){var kb,lb=y.document;kb=lb?lb.documentMode:void 0;if(null!=kb&&kb>parseFloat(ib)){hb=String(kb);break a}}hb=ib}var mb=hb,bb={};
   function nb(){return ab(function(){for(var a=0,b=Ta(String(mb)).split("."),c=Ta("11").split("."),d=Math.max(b.length,c.length),e=0;0==a&&e<d;e++){var g=b[e]||"",f=c[e]||"";do{g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(0==g[0].length&&0==f[0].length)break;a=Ua(0==g[1].length?0:parseInt(g[1],10),0==f[1].length?0:parseInt(f[1],10))||Ua(0==g[2].length,0==f[2].length)||Ua(g[2],f[2]);g=g[3];f=f[3]}while(0==a)}return 0<=a})};function ob(a,b){Xa(b,function(c,d){c&&"object"==typeof c&&c.V&&(c=c.T());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:pb.hasOwnProperty(d)?a.setAttribute(pb[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})}
   var pb={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};function qb(a){var b=document;a=String(a);"application/xhtml+xml"===b.contentType&&(a=a.toLowerCase());return b.createElement(a)};function rb(a,b){this.i=a;this.l=b;this.h=0;this.g=null}rb.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.i();return a};function sb(a,b){a.l(b);100>a.h&&(a.h++,b.next=a.g,a.g=b)};var tb;
   function ub(){var a=y.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!M("Presto")&&(a=function(){var e=qb("IFRAME");e.style.display="none";document.documentElement.appendChild(e);var g=e.contentWindow;e=g.document;e.open();e.close();var f="callImmediate"+Math.random(),h="file:"==g.location.protocol?"*":g.location.protocol+"//"+g.location.host;e=C(function(k){if(("*"==h||k.origin==h)&&k.data==f)this.port1.onmessage()},this);g.addEventListener("message",e,
   !1);this.port1={};this.port2={postMessage:function(){g.postMessage(f,h)}}});if("undefined"!==typeof a&&!M("Trident")&&!M("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.S;c.S=null;e()}};return function(e){d.next={S:e};d=d.next;b.port2.postMessage(0)}}return function(e){y.setTimeout(e,0)}};function vb(a){y.setTimeout(function(){throw a;},0)};function wb(){this.h=this.g=null}wb.prototype.add=function(a,b){var c=xb.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};function yb(){var a=zb,b=null;a.g&&(b=a.g,a.g=a.g.next,a.g||(a.h=null),b.next=null);return b}var xb=new rb(function(){return new Ab},function(a){return a.reset()});function Ab(){this.next=this.g=this.h=null}Ab.prototype.set=function(a,b){this.h=a;this.g=b;this.next=null};Ab.prototype.reset=function(){this.next=this.g=this.h=null};function Bb(a,b){Cb||Db();Eb||(Cb(),Eb=!0);zb.add(a,b)}var Cb;function Db(){if(y.Promise&&y.Promise.resolve){var a=y.Promise.resolve(void 0);Cb=function(){a.then(Fb)}}else Cb=function(){var b=Fb;"function"!==typeof y.setImmediate||y.Window&&y.Window.prototype&&!M("Edge")&&y.Window.prototype.setImmediate==y.setImmediate?(tb||(tb=ub()),tb(b)):y.setImmediate(b)}}var Eb=!1,zb=new wb;function Fb(){for(var a;a=yb();){try{a.h.call(a.g)}catch(b){vb(b)}sb(xb,a)}Eb=!1};function Gb(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function N(a){this.g=0;this.s=void 0;this.l=this.h=this.i=null;this.m=this.o=!1;if(a!=A)try{var b=this;a.call(void 0,function(c){O(b,2,c)},function(c){O(b,3,c)})}catch(c){O(this,3,c)}}function Hb(){this.next=this.i=this.h=this.l=this.g=null;this.m=!1}Hb.prototype.reset=function(){this.i=this.h=this.l=this.g=null;this.m=!1};var Ib=new rb(function(){return new Hb},function(a){a.reset()});function Jb(a,b,c){var d=Ib.get();d.l=a;d.h=b;d.i=c;return d}
   N.prototype.then=function(a,b,c){return Kb(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};N.prototype.$goog_Thenable=!0;N.prototype.cancel=function(a){if(0==this.g){var b=new P(a);Bb(function(){Lb(this,b)},this)}};function Lb(a,b){if(0==a.g)if(a.i){var c=a.i;if(c.h){for(var d=0,e=null,g=null,f=c.h;f&&(f.m||(d++,f.g==a&&(e=f),!(e&&1<d)));f=f.next)e||(g=f);e&&(0==c.g&&1==d?Lb(c,b):(g?(d=g,d.next==c.l&&(c.l=d),d.next=d.next.next):Mb(c),Nb(c,e,3,b)))}a.i=null}else O(a,3,b)}
   function Ob(a,b){a.h||2!=a.g&&3!=a.g||Pb(a);a.l?a.l.next=b:a.h=b;a.l=b}function Kb(a,b,c,d){var e=Jb(null,null,null);e.g=new N(function(g,f){e.l=b?function(h){try{var k=b.call(d,h);g(k)}catch(m){f(m)}}:g;e.h=c?function(h){try{var k=c.call(d,h);void 0===k&&h instanceof P?f(h):g(k)}catch(m){f(m)}}:f});e.g.i=a;Ob(a,e);return e.g}N.prototype.F=function(a){this.g=0;O(this,2,a)};N.prototype.G=function(a){this.g=0;O(this,3,a)};
   function O(a,b,c){if(0==a.g){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.g=1;a:{var d=c,e=a.F,g=a.G;if(d instanceof N){Ob(d,Jb(e||A,g||null,a));var f=!0}else if(Gb(d))d.then(e,g,a),f=!0;else{if(xa(d))try{var h=d.then;if("function"===typeof h){Qb(d,h,e,g,a);f=!0;break a}}catch(k){g.call(a,k);f=!0;break a}f=!1}}f||(a.s=c,a.g=b,a.i=null,Pb(a),3!=b||c instanceof P||Rb(a,c))}}
   function Qb(a,b,c,d,e){function g(k){h||(h=!0,d.call(e,k))}function f(k){h||(h=!0,c.call(e,k))}var h=!1;try{b.call(a,f,g)}catch(k){g(k)}}function Pb(a){a.o||(a.o=!0,Bb(a.v,a))}function Mb(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.l=null);return b}N.prototype.v=function(){for(var a;a=Mb(this);)Nb(this,a,this.g,this.s);this.o=!1};
   function Nb(a,b,c,d){if(3==c&&b.h&&!b.m)for(;a&&a.m;a=a.i)a.m=!1;if(b.g)b.g.i=null,Sb(b,c,d);else try{b.m?b.l.call(b.i):Sb(b,c,d)}catch(e){Tb.call(null,e)}sb(Ib,b)}function Sb(a,b,c){2==b?a.l.call(a.i,c):a.h&&a.h.call(a.i,c)}function Rb(a,b){a.m=!0;Bb(function(){a.m&&Tb.call(null,b)})}var Tb=vb;function P(a){F.call(this,a)}E(P,F);P.prototype.name="cancel";/*
    Portions of this code are from MochiKit, received by
    The Closure Authors under the MIT license. All other code is Copyright
    2005-2009 The Closure Authors. All Rights Reserved.
   */
   function Q(a,b){this.m=[];this.K=a;this.J=b||null;this.l=this.i=!1;this.h=void 0;this.F=this.M=this.s=!1;this.o=0;this.g=null;this.v=0}Q.prototype.cancel=function(a){if(this.i)this.h instanceof Q&&this.h.cancel();else{if(this.g){var b=this.g;delete this.g;a?b.cancel(a):(b.v--,0>=b.v&&b.cancel())}this.K?this.K.call(this.J,this):this.F=!0;this.i||(a=new Ub(this),Vb(this),R(this,!1,a))}};Q.prototype.G=function(a,b){this.s=!1;R(this,a,b)};function R(a,b,c){a.i=!0;a.h=c;a.l=!b;Wb(a)}
   function Vb(a){if(a.i){if(!a.F)throw new Xb(a);a.F=!1}}function Yb(a,b,c,d){a.m.push([b,c,d]);a.i&&Wb(a);return a}Q.prototype.then=function(a,b,c){var d,e,g=new N(function(f,h){e=f;d=h});Yb(this,e,function(f){f instanceof Ub?g.cancel():d(f)});return g.then(a,b,c)};Q.prototype.$goog_Thenable=!0;function Zb(a){return Ga(a.m,function(b){return"function"===typeof b[1]})}
   function Wb(a){if(a.o&&a.i&&Zb(a)){var b=a.o,c=$b[b];c&&(y.clearTimeout(c.g),delete $b[b]);a.o=0}a.g&&(a.g.v--,delete a.g);b=a.h;for(var d=c=!1;a.m.length&&!a.s;){var e=a.m.shift(),g=e[0],f=e[1];e=e[2];if(g=a.l?f:g)try{var h=g.call(e||a.J,b);void 0!==h&&(a.l=a.l&&(h==b||h instanceof Error),a.h=b=h);if(Gb(b)||"function"===typeof y.Promise&&b instanceof y.Promise)d=!0,a.s=!0}catch(k){b=k,a.l=!0,Zb(a)||(c=!0)}}a.h=b;d&&(h=C(a.G,a,!0),d=C(a.G,a,!1),b instanceof Q?(Yb(b,h,d),b.M=!0):b.then(h,d));c&&(b=
   new ac(b),$b[b.g]=b,a.o=b.g)}function bc(){var a=new Q;Vb(a);R(a,!0,null);return a}function Xb(){F.call(this)}E(Xb,F);Xb.prototype.message="Deferred has already fired";Xb.prototype.name="AlreadyCalledError";function Ub(){F.call(this)}E(Ub,F);Ub.prototype.message="Deferred was canceled";Ub.prototype.name="CanceledError";function ac(a){this.g=y.setTimeout(C(this.i,this),0);this.h=a}ac.prototype.i=function(){delete $b[this.g];throw this.h;};var $b={};var cc,dc=[];function ec(a,b){function c(){var e=a.shift();e=fc(e,b);a.length&&Yb(e,c,c,void 0);return e}if(!a.length)return bc();var d=dc.length;Ia(dc,a);if(d)return cc;a=dc;return cc=c()}
   function fc(a,b){var c=b||{};b=c.document||document;var d=La(a).toString(),e=qb("SCRIPT"),g={W:e,Y:void 0},f=new Q(gc,g),h=null,k=null!=c.timeout?c.timeout:5E3;0<k&&(h=window.setTimeout(function(){hc(e,!0);var m=new ic(1,"Timeout reached for loading script "+d);Vb(f);R(f,!1,m)},k),g.Y=h);e.onload=e.onreadystatechange=function(){e.readyState&&"loaded"!=e.readyState&&"complete"!=e.readyState||(hc(e,c.ra||!1,h),Vb(f),R(f,!0,null))};e.onerror=function(){hc(e,!0,h);var m=new ic(0,"Error while loading script "+
   d);Vb(f);R(f,!1,m)};g=c.attributes||{};Za(g,{type:"text/javascript",charset:"UTF-8"});ob(e,g);e.src=La(a);$a(e);jc(b).appendChild(e);return f}function jc(a){var b;return(b=(a||document).getElementsByTagName("HEAD"))&&0!=b.length?b[0]:a.documentElement}function gc(){if(this&&this.W){var a=this.W;a&&"SCRIPT"==a.tagName&&hc(a,!0,this.Y)}}
   function hc(a,b,c){null!=c&&y.clearTimeout(c);a.onload=A;a.onerror=A;a.onreadystatechange=A;b&&window.setTimeout(function(){a&&a.parentNode&&a.parentNode.removeChild(a)},0)}function ic(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);F.call(this,c);this.code=a}E(ic,F);I.j.A={};var kc=fc,mc=lc;function nc(a){return Ra(a.format,a.R,a.na||{})}function lc(a,b,c){c=c||{};a=Ra(a,b,c);var d=kc(a,{timeout:3E4,attributes:{async:!1,defer:!1}});return new Promise(function(e){Yb(d,e,null,void 0)})}I.j.A.Ia=function(a){lc=a};I.j.A.La=function(a){kc=a};I.j.A.ha=nc;I.j.A.load=mc;
   I.j.A.Aa=function(a){a=Fa(a,nc);if(0==a.length)return Promise.resolve();var b={timeout:3E4,attributes:{async:!1,defer:!1}},c=[];!db||nb()?Ea(a,function(d){c.push(kc(d,b))}):c.push(ec(a,b));return Promise.all(Fa(c,function(d){return new Promise(function(e){return Yb(d,e,null,void 0)})}))};I.j.A.Ca=function(a,b,c){return{format:a,R:b,na:c}};I.j.D={};var S={};I.j.D.va=function(a){return S[a]&&S[a].loaded};I.j.D.wa=function(a){return S[a]&&S[a].promise};I.j.D.la=function(){return new Promise(function(a){"undefined"==typeof window||"complete"===document.readyState?a():window.addEventListener?(document.addEventListener("DOMContentLoaded",a,!0),window.addEventListener("load",a,!0)):window.attachEvent?window.attachEvent("onload",a):"function"!==typeof window.onload?window.onload=a:window.onload=function(b){window.onload(b);a()}})};
   I.j.D.Ba=S;I.j.D.Ha=function(){S={}};I.j.D.Ja=function(a){S[a]||(S[a]={loaded:!1});S[a].loaded=!0};I.j.D.Ka=function(a,b){S[a]={promise:b,loaded:!1}};I.j.N={};I.j.N.P={1:"1.0","1.0":"current","1.1":"upcoming","1.2":"testing",41:"pre-45",42:"pre-45",43:"pre-45",44:"pre-45",46:"46.1","46.1":"46.2",48:"48.1",current:"49",upcoming:"50",testing:"50"};function oc(a,b){this.h={};this.g=[];this.i=0;var c=arguments.length;if(1<c){if(c%2)throw Error("Uneven number of arguments");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else if(a)if(a instanceof oc)for(c=a.H(),d=0;d<c.length;d++)this.set(c[d],a.get(c[d]));else for(d in a)this.set(d,a[d])}l=oc.prototype;l.I=function(){pc(this);for(var a=[],b=0;b<this.g.length;b++)a.push(this.h[this.g[b]]);return a};l.H=function(){pc(this);return this.g.concat()};
   function pc(a){if(a.i!=a.g.length){for(var b=0,c=0;b<a.g.length;){var d=a.g[b];T(a.h,d)&&(a.g[c++]=d);b++}a.g.length=c}if(a.i!=a.g.length){var e={};for(c=b=0;b<a.g.length;)d=a.g[b],T(e,d)||(a.g[c++]=d,e[d]=1),b++;a.g.length=c}}l.get=function(a,b){return T(this.h,a)?this.h[a]:b};l.set=function(a,b){T(this.h,a)||(this.i++,this.g.push(a));this.h[a]=b};l.forEach=function(a,b){for(var c=this.H(),d=0;d<c.length;d++){var e=c[d],g=this.get(e);a.call(b,g,e,this)}};
   function T(a,b){return Object.prototype.hasOwnProperty.call(a,b)};var qc=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^\\/?#]*)@)?([^\\/?#]*?)(?::([0-9]+))?(?=[\\/?#]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/;function rc(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var g=a[c].substring(0,d);e=a[c].substring(d+1)}else g=a[c];b(g,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};function sc(a){this.g=this.s=this.l="";this.v=null;this.o=this.h="";this.m=!1;var b;a instanceof sc?(this.m=a.m,tc(this,a.l),this.s=a.s,this.g=a.g,uc(this,a.v),this.h=a.h,vc(this,wc(a.i)),this.o=a.o):a&&(b=String(a).match(qc))?(this.m=!1,tc(this,b[1]||"",!0),this.s=xc(b[2]||""),this.g=xc(b[3]||"",!0),uc(this,b[4]),this.h=xc(b[5]||"",!0),vc(this,b[6]||"",!0),this.o=xc(b[7]||"")):(this.m=!1,this.i=new U(null,this.m))}
   sc.prototype.toString=function(){var a=[],b=this.l;b&&a.push(yc(b,zc,!0),":");var c=this.g;if(c||"file"==b)a.push("//"),(b=this.s)&&a.push(yc(b,zc,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.v,null!=c&&a.push(":",String(c));if(c=this.h)this.g&&"/"!=c.charAt(0)&&a.push("/"),a.push(yc(c,"/"==c.charAt(0)?Ac:Bc,!0));(c=this.i.toString())&&a.push("?",c);(c=this.o)&&a.push("#",yc(c,Cc));return a.join("")};
   sc.prototype.resolve=function(a){var b=new sc(this),c=!!a.l;c?tc(b,a.l):c=!!a.s;c?b.s=a.s:c=!!a.g;c?b.g=a.g:c=null!=a.v;var d=a.h;if(c)uc(b,a.v);else if(c=!!a.h){if("/"!=d.charAt(0))if(this.g&&!this.h)d="/"+d;else{var e=b.h.lastIndexOf("/");-1!=e&&(d=b.h.substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var g=[],f=0;f<e.length;){var h=e[f++];"."==h?d&&f==e.length&&g.push(""):".."==h?((1<g.length||1==g.length&&
   ""!=g[0])&&g.pop(),d&&f==e.length&&g.push("")):(g.push(h),d=!0)}d=g.join("/")}else d=e}c?b.h=d:c=""!==a.i.toString();c?vc(b,wc(a.i)):c=!!a.o;c&&(b.o=a.o);return b};function tc(a,b,c){a.l=c?xc(b,!0):b;a.l&&(a.l=a.l.replace(/:$/,""))}function uc(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.v=b}else a.v=null}function vc(a,b,c){b instanceof U?(a.i=b,Dc(a.i,a.m)):(c||(b=yc(b,Ec)),a.i=new U(b,a.m))}
   function xc(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function yc(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,Fc),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function Fc(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var zc=/[#\/\?@]/g,Bc=/[#\?:]/g,Ac=/[#\?]/g,Ec=/[#\?@]/g,Cc=/#/g;function U(a,b){this.h=this.g=null;this.i=a||null;this.l=!!b}
   function V(a){a.g||(a.g=new oc,a.h=0,a.i&&rc(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}l=U.prototype;l.add=function(a,b){V(this);this.i=null;a=W(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};function Gc(a,b){V(a);b=W(a,b);T(a.g.h,b)&&(a.i=null,a.h-=a.g.get(b).length,a=a.g,T(a.h,b)&&(delete a.h[b],a.i--,a.g.length>2*a.i&&pc(a)))}function Hc(a,b){V(a);b=W(a,b);return T(a.g.h,b)}
   l.forEach=function(a,b){V(this);this.g.forEach(function(c,d){Ea(c,function(e){a.call(b,e,d,this)},this)},this)};l.H=function(){V(this);for(var a=this.g.I(),b=this.g.H(),c=[],d=0;d<b.length;d++)for(var e=a[d],g=0;g<e.length;g++)c.push(b[d]);return c};l.I=function(a){V(this);var b=[];if("string"===typeof a)Hc(this,a)&&(b=Ha(b,this.g.get(W(this,a))));else{a=this.g.I();for(var c=0;c<a.length;c++)b=Ha(b,a[c])}return b};
   l.set=function(a,b){V(this);this.i=null;a=W(this,a);Hc(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};l.get=function(a,b){if(!a)return b;a=this.I(a);return 0<a.length?String(a[0]):b};l.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=this.g.H(),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.I(d);for(var g=0;g<d.length;g++){var f=e;""!==d[g]&&(f+="="+encodeURIComponent(String(d[g])));a.push(f)}}return this.i=a.join("&")};
   function wc(a){var b=new U;b.i=a.i;a.g&&(b.g=new oc(a.g),b.h=a.h);return b}function W(a,b){b=String(b);a.l&&(b=b.toLowerCase());return b}function Dc(a,b){b&&!a.l&&(V(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();if(d!=e&&(Gc(this,d),Gc(this,e),0<c.length)){this.i=null;d=this.g;var g=d.set;e=W(this,e);var f=c.length;if(0<f){for(var h=Array(f),k=0;k<f;k++)h[k]=c[k];f=h}else f=[];g.call(d,e,f);this.h+=c.length}},a));a.l=b};I.j.C={};var X="",Y="",Ic,Z,Jc=null,Kc;function Lc(){Y=X="";Jc=Z=Ic=null;z("google.load")||(D("google.load",Mc),D("google.setOnLoadCallback",I.X));var a=document.getElementsByTagName("script");a=(document.currentScript||a[a.length-1]).getAttribute("src");a=new sc(a);var b=a.g;Kc=b=b.match(/^www\.gstatic\.cn/)?"gstatic.cn":"gstatic.com";Nc(a)}
   function Nc(a){a=new U(a.i.toString());var b=a.get("callback");"string"===typeof b&&(b=Oc(b),I.j.D.la().then(b));a=a.get("autoload");if("string"===typeof a)try{if(""!==a){var c=JSON.parse(a).modules;for(a=0;a<c.length;a++){var d=c[a];Mc(d.name,d.version,d)}}}catch(e){throw Error("Autoload failed with: "+e);}}
   function Pc(a){var b=a,c,d=a.match(/^testing-/);d&&(b=b.replace(/^testing-/,""));a=b;do{if(b===I.j.N.P[b])throw Error("Infinite loop in version mapping: "+b);(c=I.j.N.P[b])&&(b=c)}while(c);c=(d?"testing-":"")+b;return{version:"pre-45"==b?a:c,ma:c}}
   function Qc(a){var b=I.j.O.oa[Kc].loader,c=Pc(a);return I.j.A.load(b,{version:c.ma}).then(function(){var d=z("google.charts.loader.VersionSpecific.load")||z("google.charts.loader.publicLoad")||z("google.charts.versionSpecific.load");if(!d)throw Error("Bad version: "+a);Jc=function(e){e=d(c.version,e);if(null==e||null==e.then){var g=z("google.charts.loader.publicSetOnLoadCallback")||z("google.charts.versionSpecific.setOnLoadCallback");e=new Promise(function(f){g(f)});e.then=g}return e}})}
   function Rc(a){"string"===typeof a&&(a=[a]);Array.isArray(a)&&0!==a.length||(a=I.j.O.ga);var b=[];a.forEach(function(c){c=c.toLowerCase();b=b.concat(c.split(/[\s,]+\s*/))});return b}function Sc(a){a=a||"";for(var b=a.replace(/-/g,"_").toLowerCase();"string"===typeof b;)a=b,b=I.j.ea[b],b===a&&(b=!1);b||(a.match(/_[^_]+$/)?(a=a.replace(/_[^_]+$/,""),a=Sc(a)):a="en");return a}
   function Tc(a){a=a||"";""!==X&&X!==a&&(console.warn(" Attempting to load version '"+a+"' of Google Charts, but the previously loaded '"+(X+"' will be used instead.")),a=X);return X=a||""}function Uc(a){a=a||"";""!==Y&&Y!==a&&(console.warn(" Attempting to load Google Charts for language '"+a+"', but the previously loaded '"+(Y+"' will be used instead.")),a=Y);"en"===a&&(a="");return Y=a||""}function Vc(a){var b={},c;for(c in a)b[c]=a[c];return b}
   function Wc(a,b){b=Vc(b);b.domain=Kc;b.callback=Oc(b.callback);a=Tc(a);var c=b.language;c=Uc(Sc(c));b.language=c;if(!Ic){if(b.enableUrlSettings&&window.URLSearchParams)try{a=(new URLSearchParams(top.location.search)).get("charts-version")||a}catch(d){console.info("Failed to get charts-version from top URL",d)}Ic=Qc(a)}b.packages=Rc(b.packages);return Z=Ic.then(function(){return Jc(b)})}I.pa=function(a){return I.load(Object.assign({},a,{safeMode:!0}))};D("google.charts.safeLoad",I.pa);
   I.load=function(a){for(var b=[],c=0;c<arguments.length;++c)b[c]=arguments[c];c=0;"visualization"===b[c]&&c++;var d="current";if("string"===typeof b[c]||"number"===typeof b[c])d=String(b[c]),c++;var e={};xa(b[c])&&(e=b[c]);return Wc(d,e)};D("google.charts.load",I.load);I.X=function(a){if(!Z)throw Error("Must call google.charts.load before google.charts.setOnLoadCallback");return a?Z.then(a):Z};D("google.charts.setOnLoadCallback",I.X);
   var Xc=H("https://maps.googleapis.com/maps/api/js?jsapiRedirect=true"),Yc=H("https://maps-api-ssl.google.com/maps?jsapiRedirect=true&file=googleapi");
   function Zc(a,b,c){console.warn("Loading Maps API with the jsapi loader is deprecated.");c=c||{};a=c.key||c.client;var d=c.libraries,e=function(h){for(var k={},m=0;m<h.length;m++){var p=h[m];k[p[0]]=p[1]}return k}(c.other_params?c.other_params.split("&").map(function(h){return h.split("=")}):[]),g=Object.assign({},{key:a,za:d},e),f="2"===b?Yc:Xc;Z=new Promise(function(h){var k=Oc(c&&c.callback);I.j.A.load(f,{},g).then(k).then(h)})}var $c=H("https://www.gstatic.com/inputtools/js/ita/inputtools_3.js");
   function ad(a,b,c){xa(c)&&c.packages?(Array.isArray(c.packages)?c.packages:[c.packages]).includes("inputtools")?(console.warn('Loading "elements" with the jsapi loader is deprecated.\nPlease load '+($c+" directly.")),Z=new Promise(function(d){var e=Oc(c&&c.callback);I.j.A.load($c,{},{}).then(e).then(d)})):console.error('Loading "elements" other than "inputtools" is unsupported.'):console.error("google.load of elements was invoked without specifying packages")}var bd=H("https://ajax.googleapis.com/ajax/libs/%{module}/%{version}/%{file}");
   function cd(a,b){var c;do{if(a===b[a])throw Error("Infinite loop in version mapping for version "+a);(c=b[a])&&(a=c)}while(c);return a}
   function dd(a,b,c){var d=I.j.ba.ia[a];if(d){b=cd(b,d.aliases);d=d.versions[b];if(!d)throw Error("Unknown version, "+b+", of "+a+".");var e={module:a,version:b||"",file:d.compressed};b=La(I.j.A.ha({format:bd,R:e})).toString();console.warn("Loading modules with the jsapi loader is deprecated.\nPlease load "+(a+" directly from "+b+"."));Z=new Promise(function(g){var f=Oc(c&&c.callback);I.j.A.load(bd,e).then(f).then(g)})}else setTimeout(function(){throw Error('Module "'+a+'" is not supported.');},0)}
   function Oc(a){return function(){if("function"===typeof a)a();else if("string"===typeof a&&""!==a)try{var b=z(a);if("function"!==typeof b)throw Error("Type of '"+a+"' is "+typeof b+".");b()}catch(c){throw Error("Callback of "+a+" failed with: "+c);}}}function Mc(a){for(var b=[],c=0;c<arguments.length;++c)b[c]=arguments[c];switch(b[0]){case "maps":Zc.apply(null,ba(b));break;case "elements":ad.apply(null,ba(b));break;case "visualization":I.load.apply(I,ba(b));break;default:dd.apply(null,ba(b))}}
   D("google.loader.LoadFailure",!1);Kc?console.warn("Google Charts loader.js should only be loaded once."):Lc();I.j.C.ya=Lc;I.j.C.Da=Pc;I.j.C.Ea=Sc;I.j.C.Fa=Rc;I.j.C.Qa=Tc;I.j.C.Pa=Uc;I.j.C.Ga=Nc;I.j.C.xa=function(){return Jc};}).call(this);