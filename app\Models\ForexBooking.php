<?php

namespace App\Models;

use App\Traits\Notify;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;

class ForexBooking extends Model
{
    use HasFactory, Notify, Prunable;

    protected $fillable = [
        'booking_reference',
        'user_id',
        'client_name',
        'client_email',
        'client_phone',
        'client_type',
        'transaction_type',
        'currency',
        'amount',
        'cbn_rate',
        'parallel_rate',
        'markup_percentage',
        'customer_rate',
        'customer_total',
        'customer_payment_amount',
        'markup_amount',
        'cbn_total',
        'parallel_total',
        'difference_amount',
        'target_account_id',
        'account_details',
        'payment_method',
        'wallet_currency_id',
        'status',
        'status_notes',
        'initiated_by',
        'completed_by',
        'completed_at',
        'payment_instructions',
        'email_sent'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'cbn_rate' => 'decimal:8',
        'parallel_rate' => 'decimal:8',
        'markup_percentage' => 'decimal:2',
        'customer_rate' => 'decimal:8',
        'customer_total' => 'decimal:8',
        'customer_payment_amount' => 'decimal:8',
        'markup_amount' => 'decimal:8',
        'cbn_total' => 'decimal:8',
        'parallel_total' => 'decimal:8',
        'difference_amount' => 'decimal:8',
        'email_sent' => 'boolean',
        'completed_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function (ForexBooking $booking) {
            if (empty($booking->booking_reference)) {
                $booking->booking_reference = $booking->generateBookingReference();
            }
        });
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function targetAccount()
    {
        return $this->belongsTo(ForexAccount::class, 'target_account_id');
    }

    public function walletCurrency()
    {
        return $this->belongsTo(Currency::class, 'wallet_currency_id');
    }

    public function initiatedBy()
    {
        return $this->belongsTo(Admin::class, 'initiated_by');
    }

    public function completedBy()
    {
        return $this->belongsTo(Admin::class, 'completed_by');
    }

    public function transactions()
    {
        return $this->hasMany(ForexTransaction::class, 'forex_booking_id');
    }

    public function emailLogs()
    {
        return $this->hasMany(ForexEmailLog::class, 'forex_booking_id');
    }

    public function reservations()
    {
        return $this->hasMany(ForexBookingReservation::class, 'forex_booking_id');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeByClientType($query, $type)
    {
        return $query->where('client_type', $type);
    }

    public function scopeByTransactionType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    // Accessors
    public function getStatusClassAttribute()
    {
        return [
            'pending' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    public function getFormattedCbnTotalAttribute()
    {
        return number_format($this->cbn_total, 2) . ' NGN';
    }

    public function getFormattedParallelTotalAttribute()
    {
        return number_format($this->parallel_total, 2) . ' NGN';
    }

    public function getFormattedDifferenceAmountAttribute()
    {
        return number_format($this->difference_amount, 2) . ' NGN';
    }

    // Business Logic Methods
    public function calculateTotals($cbnBuyRate, $parallelBuyRate, $buyMarkupPercentage = 0, $cbnSellRate = null, $parallelSellRate = null, $sellMarkupPercentage = 0)
    {
        if ($this->transaction_type === 'buying') {
            // NGN to USD: Use buy rates
            $this->cbn_rate = $cbnBuyRate;
            $this->parallel_rate = $parallelBuyRate;
            $this->markup_percentage = $buyMarkupPercentage;

            // Calculate customer rate (parallel rate + markup)
            $this->customer_rate = $parallelBuyRate + ($parallelBuyRate * $buyMarkupPercentage / 100);

            // Calculate totals
            $this->cbn_total = $this->amount * $cbnBuyRate;
            $this->parallel_total = $this->amount * $parallelBuyRate;
            $this->customer_total = $this->amount * $this->customer_rate;

            // Customer pays NGN (customer_total)
            $this->customer_payment_amount = $this->customer_total;
        } else {
            // USD to NGN: Use sell rates
            $this->cbn_rate = $cbnSellRate ?? $cbnBuyRate;
            $this->parallel_rate = $parallelSellRate ?? $parallelBuyRate;
            $this->markup_percentage = $sellMarkupPercentage;

            // Calculate customer rate (parallel rate + markup)
            $this->customer_rate = $this->parallel_rate + ($this->parallel_rate * $sellMarkupPercentage / 100);

            // Calculate totals
            $this->cbn_total = $this->amount * $this->cbn_rate;
            $this->parallel_total = $this->amount * $this->parallel_rate;
            $this->customer_total = $this->amount * $this->customer_rate;

            // Customer pays USD (amount)
            $this->customer_payment_amount = $this->amount;
        }

        // Calculate amounts for accounting
        $this->markup_amount = $this->customer_total - $this->parallel_total; // Goes to CBN account
        $this->difference_amount = $this->parallel_total - $this->cbn_total; // Goes to Difference account
    }

    public function complete($adminId, $notes = null)
    {
        // Only update status - all transaction processing is handled by ForexBookingService
        $this->update([
            'status' => 'completed',
            'completed_by' => $adminId,
            'completed_at' => now(),
            'status_notes' => $notes,
        ]);

        // Note: Transaction processing is now handled by ForexBookingService
        // to avoid double processing and ensure consistency
    }

    public function cancel($adminId, $notes = null)
    {
        $this->update([
            'status' => 'cancelled',
            'completed_by' => $adminId,
            'completed_at' => now(),
            'status_notes' => $notes,
        ]);

        // Note: Pending balance release is now handled by ForexBookingService
        // to support multi-account reservations
    }

    // Multi-Account Reservation Helper Methods
    public function getTotalReservedAmount($status = 'reserved')
    {
        return $this->reservations()->where('status', $status)->sum('reserved_amount');
    }

    public function getReservationBreakdown()
    {
        return ForexBookingReservation::getReservationBreakdown($this->id);
    }

    public function hasMultipleReservations()
    {
        return $this->reservations()->count() > 1;
    }

    public function getReservedAccounts()
    {
        return $this->reservations()
            ->with('account')
            ->where('status', 'reserved')
            ->get()
            ->pluck('account');
    }

    // Note: getBookingAmount method removed - transaction processing handled by ForexBookingService

    // Note: processAccountTransactions method removed - transaction processing handled by ForexBookingService

    // Note: processExchangeTransactions method removed - transaction processing handled by ForexBookingService

    private function generateBookingReference()
    {
        return 'FXB' . date('Ymd') . strtoupper(substr($this->transaction_type, 0, 1)) . rand(1000, 9999);
    }

    /**
     * Check if this booking uses wallet payment method
     */
    public function isWalletPayment(): bool
    {
        return $this->payment_method === 'wallet';
    }

    /**
     * Get the currency code for wallet payment
     */
    public function getWalletCurrencyCode(): ?string
    {
        return $this->walletCurrency?->code;
    }

    public function prunable()
    {
        return static::where('status', 'pending')->where('created_at', '<', now()->subDays(30));
    }
}
