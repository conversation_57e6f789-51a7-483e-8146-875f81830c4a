@extends(userLayout())

@section('title',__('Api Documentation'))

@section('content')

    <div class="content {{ containerClass() }}" id="api-app">
        <div class="page-header">
            <div class="row align-items-end">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{route('merchant.dashboard')}}">@lang("Dashboard")</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Business Api')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('API Documentation Reference')</h1>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-4">
                <h3>@lang('Getting Started')</h3>
                <p>
                    @lang('This document explains how to successfully call the API with your app and get an
                    successful call. These endpoints helps you create and manage wallets.')
                </p>
                <p>
                    <x-badge text="Base Url" /><i>{{url('/api')}}</i>
                </p>
            </div>
            <div class="col-12 col-md-6 col-lg-6">
                <h2 class="section-title mt-0">@lang('Initiate Payment')</h2>
                <p>
                    @lang('To initiate the payment process follow the example code and be careful with the parameters.
                    Use this guide to make sure your chosen payment methods work for your business and to determine how you want to add payment methods.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/><i>initiate/payment</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row ">
                    <div class="col-12">
                        <p>
                            <b>currency<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">
                            @lang('Currency Code, Must be in Upper Case. e.g. USD, EUR')
                        </p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="USD"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>amount<span class="text-danger">*</span></b>
                            <x-badge text="int32" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">
                            @lang('The amount you want to transaction')
                        </p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="100"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>ipn_url<span class="text-danger">*</span></b>
                            <x-badge text="url" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Instant payment notification url')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="https://bugfinder.net/"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>callback_url<span class="text-danger">*</span></b>
                            <x-badge text="url" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('This url you should be redirect after successful payment')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="https://bugfinder.net/"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>order_id<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('This is unique id generate for transaction. Its length should be 10 to 20 characters')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="562589636985"/>
                    </div>
                </div>
                <hr/>
                <div id="accordion">
                    <div class="accordion">
                        <div class="accordion-header collapsed" role="button"
                             data-bs-toggle="collapse" data-bs-target="#panel-body-1"
                             aria-expanded="false">
                            <h4>@lang('Optional Parameters') <i class="bi bi-chevron-right d-inline"></i></h4>

                        </div>
                        <div
                            class="accordion-body collapse px-0 pt-4"
                            id="panel-body-1"
                            data-bs-parent="#accordion">
                            <div class="row">
                                <div class="col-12">
                                    <p>
                                        <b>customer_name</b>
                                        <x-badge text="string" />
                                    </p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0">
                                        @lang('The person name who make want to transaction. Name should no be greater than 20 characters')
                                    </p>
                                </div>
                                <div class="col-md-4">
                                    <input type="text" class="form-control" value="John Doe"/>
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="col-12">
                                    <p>
                                        <b>customer_email</b>
                                        <x-badge text="string" />
                                    </p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0">
                                        @lang('The person email who make want to transaction.')
                                    </p>
                                </div>
                            </div>
                            <hr/>
                            <div class="row">
                                <div class="col-12">
                                    <p>
                                        <b>description</b>
                                        <x-badge text="string" />
                                    </p>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-0">
                                        @lang('Details of your payment or transaction. Details should no be greater than 500 characters')
                                    </p>
                                </div>
                            </div>
                            <hr/>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-6">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="myTab2" role="tablist">
                            <li class="nav-item">
                                <a
                                    class="nav-link active"
                                    id="home-tab2"
                                    data-bs-toggle="tab"
                                    href="#node-js"
                                    role="tab"
                                    aria-controls="home"
                                    aria-selected="true">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="profile-tab2"
                                    data-bs-toggle="tab"
                                    href="#php"
                                    role="tab"
                                    aria-controls="profile"
                                    aria-selected="false">@lang('PHP')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="contact-tab2"
                                    data-bs-toggle="tab"
                                    href="#ruby"
                                    role="tab"
                                    aria-controls="contact"
                                    aria-selected="false">@lang('RUBY')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="node-tab2"
                                    data-bs-toggle="tab"
                                    href="#node"
                                    role="tab"
                                    aria-controls="contact"
                                    aria-selected="false">@lang('NODE JS')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="python-tab2"
                                    data-bs-toggle="tab"
                                    href="#python"
                                    role="tab"
                                    aria-controls="contact"
                                    aria-selected="false">@lang('PYTHON')</a>
                            </li>
                        </ul>
                        <a href="javascript:void(0)" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered" id="myTab3Content">
                            <div
                                class="tab-pane fade show active"
                                id="node-js"
                                role="tabpanel"
                                aria-labelledby="home-tab2"
                            >
                                       <pre>
<code id="copycurl1">
curl --location --request POST 'https://bugfinder.net/api/payment/initiate' \
--header 'ApiKey: 9a5cd8e5beb32c70400bfdcf5f576562bd09202d' \
--header 'SecretKey: f2cc7f986a81b14525807a37a6a89ec99b8a82b6' \
--form 'currency="USD"' \
--form 'amount="100"' \
--form 'ipn_url="https://bugfinder.net/"' \
--form 'callback_url="https://bugfinder.net/"' \
--form 'order_id="256985412569"' \
--form 'meta.customer_name="John Doe"' \
--form 'meta.customer_email="<EMAIL>"' \
--form 'meta.description=""'
</code>
                                    </pre>
                            </div>
                            <div class="tab-pane fade" id="php" role="tabpanel" aria-labelledby="profile-tab2">
                                       <pre>
<code id="copyphp1">
?php

$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://bugfinder.net/api/payment/initiate',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('currency' => 'USD','amount' => '100','ipn_url' => 'https://bugfinder.net/','callback_url' => 'https://bugfinder.net/','order_id' => '256985412569','meta.customer_name' => 'John Doe','meta.customer_email' => '<EMAIL>','meta.description' => ''),
  CURLOPT_HTTPHEADER => array(
    'ApiKey: 9a5cd8e5beb32c70400bfdcf5f576562bd09202d',
    'SecretKey: f2cc7f986a81b14525807a37a6a89ec99b8a82b6'
  ),
));

$response = curl_exec($curl);

curl_close($curl);
echo $response;

</code>

</pre>
                            </div>
                            <div class="tab-pane fade" id="ruby" role="tabpanel" aria-labelledby="node-tab2">
                                       <pre>
<code id="copyruby1">
require "uri"
require "net/http"

url = URI("https://bugfinder.net/api/payment/initiate")

http = Net::HTTP.new(url.host, url.port);
request = Net::HTTP::Post.new(url)
request["ApiKey"] = "9a5cd8e5beb32c70400bfdcf5f576562bd09202d"
request["SecretKey"] = "f2cc7f986a81b14525807a37a6a89ec99b8a82b6"
form_data = [['currency', 'USD'],['amount', '100'],['ipn_url', 'https://bugfinder.net/'],['callback_url', 'https://bugfinder.net/'],['order_id', '256985412569'],['meta.customer_name', 'John Doe'],['meta.customer_email', '<EMAIL>'],['meta.description', '']]
request.set_form form_data, 'multipart/form-data'
response = http.request(request)
puts response.read_body
</code>

</pre>
                            </div>
                            <div class="tab-pane fade" id="node" role="tabpanel" aria-labelledby="contact-tab3">
                                       <pre>
<code id="copynode1">
var request = require('request');
var options = {
  'method': 'POST',
  'url': 'https://bugfinder.net/payment/initiate',
  'headers': {
    'ApiKey': '9a5cd8e5beb32c70400bfdcf5f576562bd09202d',
    'SecretKey': 'f2cc7f986a81b14525807a37a6a89ec99b8a82b6'
  },
  formData: {
    'currency': 'USD',
    'amount': '100',
    'ipn_url': 'https://bugfinder.net/',
    'callback_url': 'https://bugfinder.net/',
    'order_id': '256985412569',
    'meta.customer_name': 'John Doe',
    'meta.customer_email': '<EMAIL>',
    'meta.description': ''
  }
};
request(options, function (error, response) {
  if (error) throw new Error(error);
  console.log(response.body);
});

</code>

</pre>
                            </div>
                            <div class="tab-pane fade" id="python" role="tabpanel"
                                 aria-labelledby="python-tab3">
                                       <pre>
<code id="copypython1">
import requests

url = "https://bugfinder.net/payment/initiate"

payload={'currency': 'USD',
'amount': '100',
'ipn_url': 'https://bugfinder.net/',
'callback_url': 'https://bugfinder.net/',
'order_id': '256985412569',
'meta.customer_name': 'John Doe',
'meta.customer_email': '<EMAIL>',
'meta.description': ''}
files=[

]
headers = {
  'ApiKey': '9a5cd8e5beb32c70400bfdcf5f576562bd09202d',
  'SecretKey': 'f2cc7f986a81b14525807a37a6a89ec99b8a82b6'
}

response = requests.request("POST", url, headers=headers, data=payload, files=files)

print(response.text)


</code>

</pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <!-- <h4>Bordered Tab</h4> -->

                        <ul class="nav nav-tabs" id="myTab3" role="tablist">
                            <li class="nav-item">
                                <a
                                    class="nav-link active"
                                    id="home-tab2"
                                    data-bs-toggle="tab"
                                    href="#success-code"
                                    role="tab"
                                    aria-controls="home"
                                    aria-selected="true">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="profile-tab2"
                                    data-bs-toggle="tab"
                                    href="#bad-request"
                                    role="tab"
                                    aria-controls="profile"
                                    aria-selected="false">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered" id="myTab3Content">
                            <div
                                class="tab-pane fade show active"
                                id="success-code"
                                role="tabpanel"
                                aria-labelledby="home-tab2"
                            >
                                       <pre>
<code id="success1">
{
    "status": "success",
    "data": {
        "id": "f8f2ec61-3719-4d64-b185-4951b5f71f9d",
        "currency": "USD",
        "amount": "100",
        "order_id": "256985412569",
        "ipn_url": "https://bugfinder.net/",
        "callback_url": "https://bugfinder.net/",
        "meta": {
            "customer_name": null,
            "customer_email": null,
            "description": null
        },
        "redirect_url": "https://bugfinder.net/make/payment/test/f8f2ec61-3719-4d64-b185-4951b5f71f9d"
    }
}
</code>
                                    </pre>
                            </div>
                            <div
                                class="tab-pane fade"
                                id="bad-request"
                                role="tabpanel"
                                aria-labelledby="profile-tab2"
                            >
										<pre><code>{
    "status": "error",
    "error": [
        "The currency field is required.",
        "The amount field is required.",
        "The ipn url field is required.",
        "The callback url field is required.",
        "The order id field is required.",
        "The api key field is required.",
        "The secret key field is required."
    ]
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <h2 class="section-title mt-0">@lang('Verify Payment')</h2>
                <p>
                    @lang('To ensure the payment completed, your application must verify the payment state. You will need to make request with these following API end points.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/>
                    <i>verify/payment</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>order_id<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('This is unique id what you get response.')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="562589636985"/>
                    </div>
                </div>
                <hr/>
                <div class="row g-4">
                    <div class="col-md-12">
                        <div class="alert alert-soft-info text-info" role="alert">
                            @lang('Note: status are pending, success, failed')
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="myTab2" role="tablist">
                            <li class="nav-item">
                                <a
                                    class="nav-link active"
                                    id="home2-tab2"
                                    data-bs-toggle="tab"
                                    href="#node2-js"
                                    role="tab"
                                    aria-controls="home"
                                    aria-selected="true">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="profile2-tab2"
                                    data-bs-toggle="tab"
                                    href="#php2"
                                    role="tab"
                                    aria-controls="profile"
                                    aria-selected="false">@lang('PHP')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="contact2-tab2"
                                    data-bs-toggle="tab"
                                    href="#ruby2"
                                    role="tab"
                                    aria-controls="contact"
                                    aria-selected="false">@lang('RUBY')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="node2-tab2"
                                    data-bs-toggle="tab"
                                    href="#node2"
                                    role="tab"
                                    aria-controls="contact"
                                    aria-selected="false">@lang('NODE JS')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="python2-tab2"
                                    data-bs-toggle="tab"
                                    href="#python2"
                                    role="tab"
                                    aria-controls="contact"
                                    aria-selected="false">@lang('PYTHON')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered" id="myTab3Content">
                            <div
                                class="tab-pane fade show active"
                                id="node2-js"
                                role="tabpanel"
                                aria-labelledby="home2-tab2"
                            >
                                       <pre>
<code id="copycurl2">
curl --location --request POST 'https://bugfinder.net/api/payment/verify' \
--header 'ApiKey: 9a5cd8e5beb32c70400bfdcf5f576562bd09202d' \
--header 'SecretKey: f2cc7f986a81b14525807a37a6a89ec99b8a82b6' \
--form 'order_id="1823456781"'
</code>
                                    </pre>
                            </div>
                            <div class="tab-pane fade" id="php2" role="tabpanel"
                                 aria-labelledby="profile2-tab2">
                                       <pre>
<code id="copyphp2">
?php

$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://bugfinder.net/api/payment/verify',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('order_id' => '1823456781'),
  CURLOPT_HTTPHEADER => array(
    'ApiKey: 9a5cd8e5beb32c70400bfdcf5f576562bd09202d',
    'SecretKey: f2cc7f986a81b14525807a37a6a89ec99b8a82b6'
  ),
));

$response = curl_exec($curl);

curl_close($curl);
echo $response;


</code>

</pre>
                            </div>
                            <div class="tab-pane fade" id="ruby2" role="tabpanel" aria-labelledby="node2-tab2">
                                       <pre>
<code id="copyruby2">
require "uri"
require "net/http"

url = URI("https://bugfinder.net/api/payment/verify")

http = Net::HTTP.new(url.host, url.port);
request = Net::HTTP::Post.new(url)
request["ApiKey"] = "9a5cd8e5beb32c70400bfdcf5f576562bd09202d"
request["SecretKey"] = "f2cc7f986a81b14525807a37a6a89ec99b8a82b6"
form_data = [['order_id', '1823456781']]
request.set_form form_data, 'multipart/form-data'
response = http.request(request)
puts response.read_body

</code>

</pre>
                            </div>
                            <div class="tab-pane fade" id="node2" role="tabpanel"
                                 aria-labelledby="contact2-tab3">
                                       <pre>
<code id="copynode2">
var request = require('request');
var options = {
  'method': 'POST',
  'url': 'https://bugfinder.net/api/payment/verify',
  'headers': {
    'ApiKey': '9a5cd8e5beb32c70400bfdcf5f576562bd09202d',
    'SecretKey': 'f2cc7f986a81b14525807a37a6a89ec99b8a82b6'
  },
  formData: {
    'order_id': '1823456781'
  }
};
request(options, function (error, response) {
  if (error) throw new Error(error);
  console.log(response.body);
});


</code>

</pre>
                            </div>
                            <div class="tab-pane fade" id="python2" role="tabpanel"
                                 aria-labelledby="python2-tab3">
                                       <pre>
<code id="copypython2">
import requests

url = "https://bugfinder.net/api/payment/verify"

payload={'order_id': '1823456781'}
files=[

]
headers = {
  'ApiKey': '9a5cd8e5beb32c70400bfdcf5f576562bd09202d',
  'SecretKey': 'f2cc7f986a81b14525807a37a6a89ec99b8a82b6'
}

response = requests.request("POST", url, headers=headers, data=payload, files=files)

print(response.text)


</code>

</pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">

                        <ul class="nav nav-tabs" id="myTab3" role="tablist">
                            <li class="nav-item">
                                <a
                                    class="nav-link active"
                                    id="home2-tab2"
                                    data-bs-toggle="tab"
                                    href="#success-code2"
                                    role="tab"
                                    aria-controls="home"
                                    aria-selected="true">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a
                                    class="nav-link"
                                    id="profile2-tab2"
                                    data-bs-toggle="tab"
                                    href="#bad-request2"
                                    role="tab"
                                    aria-controls="profile"
                                    aria-selected="false">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered" id="myTab3Content">
                            <div
                                class="tab-pane fade show active"
                                id="success-code2"
                                role="tabpanel"
                                aria-labelledby="home2-tab2"
                            >
                                       <pre>
<code id="success2">
{
    "status": "success",
    "data": {
        "order_id": 1823456781,
        "status": "success"
    }
}
</code>
                                    </pre>
                            </div>
                            <div
                                class="tab-pane fade"
                                id="bad-request2"
                                role="tabpanel"
                                aria-labelledby="profile2-tab2"
                            >
										<pre><code>{
    "status": "error",
    "error": [
        "The order id field is required.",
        "The api key field is required.",
        "The secret key field is required."
    ]
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Validate Account Section -->
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <h2 class="section-title mt-0">@lang('Validate Account')</h2>
                <p>
                    @lang('Validate bank account details before processing transfers. This endpoint verifies account number and bank code.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/>
                    <i>validate-account</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>accountNumber<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('10-digit bank account number to validate')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="**********"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>bankCode<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('6-digit bank code (e.g., 000017 for Wema Bank)')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="000017"/>
                    </div>
                </div>
                <hr/>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="validateTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="validate-curl-tab" data-bs-toggle="tab" href="#validate-curl" role="tab">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="validate-php-tab" data-bs-toggle="tab" href="#validate-php" role="tab">@lang('PHP')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="validate-curl" role="tabpanel">
                                <pre><code id="copyvalidatecurl">
curl --location --request POST 'https://bugfinder.net/api/validate-account' \
--header 'Authorization: Bearer YOUR_API_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "accountNumber": "**********",
    "bankCode": "000017"
}'
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="validate-php" role="tabpanel">
                                <pre><code id="copyvalidatephp">
<?php
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://bugfinder.net/api/validate-account',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => json_encode([
    'accountNumber' => '**********',
    'bankCode' => '000017'
  ]),
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer YOUR_API_TOKEN',
    'Content-Type: application/json'
  ),
));

$response = curl_exec($curl);
curl_close($curl);
echo $response;
?>
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="validateResponseTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="validate-success-tab" data-bs-toggle="tab" href="#validate-success" role="tab">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="validate-error-tab" data-bs-toggle="tab" href="#validate-error" role="tab">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="validate-success" role="tabpanel">
                                <pre><code id="validatesuccessresponse">
{
    "status": "success",
    "message": "Account validation successful",
    "data": {
        "accountNumber": "**********",
        "accountName": "JOHN DOE",
        "bankCode": "000017",
        "bankName": "Access Bank"
    }
}
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="validate-error" role="tabpanel">
                                <pre><code id="validateerrorresponse">
{
    "status": "failed",
    "message": [
        "The accountNumber field is required.",
        "The bankCode field is required."
    ]
}
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Single Transfer Section -->
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <h2 class="section-title mt-0">@lang('Single Transfer')</h2>
                <p>
                    @lang('Process a single bank transfer. This endpoint validates wallet balance, applies charges, and processes the transfer.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/>
                    <i>single-transfer</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>amount<span class="text-danger">*</span></b>
                            <x-badge text="numeric" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Transfer amount (minimum 1)')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="1000"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>currency<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Currency code (e.g., NGN, USD)')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="NGN"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>narration<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Transfer description/narration')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="Payment for services"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>destinationAccountNumber<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('10-digit destination account number')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="**********"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>destinationBankCode<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('6-digit destination bank code')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="000017"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>destinationAccountName<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Account holder name')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="John Doe"/>
                    </div>
                </div>
                <hr/>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="singleTransferTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="single-curl-tab" data-bs-toggle="tab" href="#single-curl" role="tab">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="single-php-tab" data-bs-toggle="tab" href="#single-php" role="tab">@lang('PHP')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="single-curl" role="tabpanel">
                                <pre><code id="copysinglecurl">
curl --location --request POST 'https://bugfinder.net/api/single-transfer' \
--header 'Authorization: Bearer YOUR_API_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "amount": 1000,
    "currency": "NGN",
    "narration": "Payment for services",
    "destinationAccountNumber": "**********",
    "destinationBankCode": "000017",
    "destinationAccountName": "John Doe"
}'
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="single-php" role="tabpanel">
                                <pre><code id="copysinglephp">
<?php
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://bugfinder.net/api/single-transfer',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => json_encode([
    'amount' => 1000,
    'currency' => 'NGN',
    'narration' => 'Payment for services',
    'destinationAccountNumber' => '**********',
    'destinationBankCode' => '000017',
    'destinationAccountName' => 'John Doe'
  ]),
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer YOUR_API_TOKEN',
    'Content-Type: application/json'
  ),
));

$response = curl_exec($curl);
curl_close($curl);
echo $response;
?>
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="singleResponseTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="single-success-tab" data-bs-toggle="tab" href="#single-success" role="tab">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="single-error-tab" data-bs-toggle="tab" href="#single-error" role="tab">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="single-success" role="tabpanel">
                                <pre><code id="singlesuccessresponse">
{
    "status": "success",
    "message": "Transfer processed successfully",
    "data": {
        "transaction_id": "TXN123456789",
        "amount": 1000,
        "charge": 50,
        "net_amount": 1050,
        "reference": "NUM_REF_123456"
    }
}
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="single-error" role="tabpanel">
                                <pre><code id="singleerrorresponse">
{
    "status": "failed",
    "message": [
        "The amount field is required.",
        "Insufficient wallet balance"
    ]
}
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bulk Transfer Section -->
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <h2 class="section-title mt-0">@lang('Bulk Transfer')</h2>
                <p>
                    @lang('Process multiple bank transfers in a single request. This endpoint validates total wallet balance and processes all transfers with a unique batch identifier.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/>
                    <i>bulk-transfer</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>currency<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Currency code for all transfers (e.g., NGN, USD)')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="NGN"/>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>transfers<span class="text-danger">*</span></b>
                            <x-badge text="array" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Array of transfer objects (max 100 transfers)')</p>
                    </div>
                    <div class="col-md-4">
                        <span class="text-muted">@lang('See example')</span>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>transfers[].amount<span class="text-danger">*</span></b>
                            <x-badge text="numeric" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Transfer amount for each transfer')</p>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>transfers[].narration<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Transfer description for each transfer')</p>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>transfers[].destinationAccountNumber<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('10-digit destination account number')</p>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>transfers[].destinationBankCode<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('6-digit destination bank code')</p>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>transfers[].destinationAccountName<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Account holder name')</p>
                    </div>
                </div>
                <hr/>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="bulkTransferTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="bulk-curl-tab" data-bs-toggle="tab" href="#bulk-curl" role="tab">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="bulk-php-tab" data-bs-toggle="tab" href="#bulk-php" role="tab">@lang('PHP')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="bulk-curl" role="tabpanel">
                                <pre><code id="copybulkcurl">
curl --location --request POST 'https://bugfinder.net/api/bulk-transfer' \
--header 'Authorization: Bearer YOUR_API_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "currency": "NGN",
    "transfers": [
        {
            "amount": 1000,
            "narration": "Payment 1",
            "destinationAccountNumber": "**********",
            "destinationBankCode": "000017",
            "destinationAccountName": "John Doe"
        },
        {
            "amount": 2000,
            "narration": "Payment 2",
            "destinationAccountNumber": "**********",
            "destinationBankCode": "000017",
            "destinationAccountName": "Jane Smith"
        }
    ]
}'
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="bulk-php" role="tabpanel">
                                <pre><code id="copybulkphp">
<?php
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://bugfinder.net/api/bulk-transfer',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => json_encode([
    'currency' => 'NGN',
    'transfers' => [
        [
            'amount' => 1000,
            'narration' => 'Payment 1',
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '000017',
            'destinationAccountName' => 'John Doe'
        ],
        [
            'amount' => 2000,
            'narration' => 'Payment 2',
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '000017',
            'destinationAccountName' => 'Jane Smith'
        ]
    ]
  ]),
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer YOUR_API_TOKEN',
    'Content-Type: application/json'
  ),
));

$response = curl_exec($curl);
curl_close($curl);
echo $response;
?>
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="bulkResponseTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="bulk-success-tab" data-bs-toggle="tab" href="#bulk-success" role="tab">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="bulk-error-tab" data-bs-toggle="tab" href="#bulk-error" role="tab">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="bulk-success" role="tabpanel">
                                <pre><code id="bulksuccessresponse">
{
    "status": "success",
    "message": "Bulk transfer processed successfully",
    "data": {
        "batch_id": "BULK_ABC123XYZ",
        "total_transfers": 2,
        "total_amount": 3000,
        "total_charge": 100,
        "total_net_amount": 3100,
        "batch_reference": "NUM_BATCH_123456",
        "transfers": [
            {
                "transaction_id": "TXN123456789",
                "amount": 1000,
                "charge": 50,
                "net_amount": 1050,
                "destination_account": "**********"
            },
            {
                "transaction_id": "TXN987654321",
                "amount": 2000,
                "charge": 50,
                "net_amount": 2050,
                "destination_account": "**********"
            }
        ]
    }
}
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="bulk-error" role="tabpanel">
                                <pre><code id="bulkerrorresponse">
{
    "status": "failed",
    "message": [
        "The transfers field is required.",
        "Insufficient wallet balance for bulk transfer"
    ]
}
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <h2 class="section-title mt-0">@lang('Check Transaction Status')</h2>
                <p>
                    @lang('Check the status of a payout transaction using the transaction reference. This endpoint accepts both internal transaction IDs and provider references.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/>
                    <i>payout/status</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>reference<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Transaction reference ID (internal transaction ID or provider reference)')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="TXN123456789"/>
                    </div>
                </div>
                <hr/>
                <div class="row g-4">
                    <div class="col-md-12">
                        <div class="alert alert-soft-info text-info" role="alert">
                            @lang('Note: Status values are pending, processing, success, cancelled, failed')
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="statusCheckTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="status-check-curl-tab" data-bs-toggle="tab" href="#status-check-curl" role="tab">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="status-check-php-tab" data-bs-toggle="tab" href="#status-check-php" role="tab">@lang('PHP')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="status-check-curl" role="tabpanel">
                                <pre><code id="copystatuscheckcurl">
curl --location --request POST 'https://your-domain.com/api/payout/status' \
--header 'Authorization: Bearer YOUR_API_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "reference": "TXN123456789"
}'
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="status-check-php" role="tabpanel">
                                <pre><code id="copystatuscheckphp">
<?php
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => 'https://your-domain.com/api/payout/status',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => json_encode([
      'reference' => 'TXN123456789'
  ]),
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer YOUR_API_TOKEN',
    'Content-Type: application/json'
  ),
));

$response = curl_exec($curl);
curl_close($curl);
echo $response;
?>
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="statusCheckResponseTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="status-check-success-tab" data-bs-toggle="tab" href="#status-check-success" role="tab">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="status-check-error-tab" data-bs-toggle="tab" href="#status-check-error" role="tab">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="status-check-success" role="tabpanel">
                                <pre><code id="statuschecksuccess">
{
    "status": "success",
    "message": {
        "reference": "TXN123456789",
        "status": "success",
        "amount": 100.00,
        "currency": "NGN",
        "created_at": "2025-07-10T11:00:00.000Z",
        "updated_at": "2025-07-10T12:00:00.000Z",
        "provider_reference": "NUM_REF_123456"
    }
}
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="status-check-error" role="tabpanel">
                                <pre><code id="statuscheckerror">
{
    "status": "failed",
    "message": "Transaction not found"
}
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 mt-5">
                <h2 class="section-title mt-0">@lang('Webhook Configuration')</h2>
                <p>
                    @lang('Configure webhook URLs in your merchant settings to receive real-time notifications when transaction events occur. Webhooks are sent for payout status changes (success, failed, pending) and deposit notifications.')
                </p>

                <h4>@lang('Webhook Events')</h4>
                <ul>
                    <li><strong>TRANSFER_NOTIFICATION:</strong> @lang('Sent when payout transaction status changes')</li>
                    <li><strong>FUNDING_NOTIFICATION:</strong> @lang('Sent when deposit/funding occurs')</li>
                    <li><strong>TEST_NOTIFICATION:</strong> @lang('Sent when testing webhook configuration')</li>
                </ul>

                <h4>@lang('Webhook Payload Structure')</h4>
                <div class="card">
                    <div class="card-body">
                        <pre><code id="webhookpayloadstructure">
{
  "event": "TRANSFER_NOTIFICATION",
  "timestamp": "2025-07-10T12:00:00.000Z",
  "data": {
    "transaction_id": "TXN123456789",
    "reference": "NUM_REF_123456",
    "amount": 100.00,
    "currency": "NGN",
    "status": "success",
    "created_at": "2025-07-10T11:00:00.000Z",
    "updated_at": "2025-07-10T12:00:00.000Z",
    "user_id": 123,
    "charge": 5.00,
    "net_amount": 105.00,
    "provider_data": {
      // Original provider webhook data
    }
  }
}
                        </code></pre>
                    </div>
                </div>

                <h4>@lang('Configuration Steps')</h4>
                <ol>
                    <li>@lang('Go to your merchant settings')</li>
                    <li>@lang('Add your webhook URL in the webhook configuration section')</li>
                    <li>@lang('Ensure your endpoint returns HTTP 200-299 status codes')</li>
                    <li>@lang('Implement proper error handling and idempotency using transaction_id')</li>
                    <li>@lang('Test your webhook using the test endpoint: POST /api/webhook/test')</li>
                </ol>

                <h4>@lang('Response Requirements')</h4>
                <ul>
                    <li>@lang('Return HTTP status 200-299 for successful processing')</li>
                    <li>@lang('Respond within 30 seconds')</li>
                    <li>@lang('Handle duplicate notifications gracefully (use transaction_id for deduplication)')</li>
                    <li>@lang('Use HTTPS for your webhook URL')</li>
                    <li><strong>@lang('Verify webhook signatures to ensure authenticity')</strong></li>
                </ul>

                <h4>@lang('Webhook Security')</h4>
                <p>
                    @lang('All webhooks include an HMAC-SHA256 signature in the') <code>X-Webhook-Signature</code> @lang('header. Verify this signature using your API secret key to ensure webhook authenticity.')
                </p>

                <div class="alert alert-soft-info text-info" role="alert">
                    <strong>@lang('Security Note:') </strong>
                    @lang('Always use HTTPS for webhook URLs and verify the X-Webhook-Signature header to ensure authenticity. The signature format is') <code>sha256=&lt;hex_digest&gt;</code>@lang('. Use your API secret key to verify the signature.')
                </div>

                <h4>@lang('Signature Verification Example')</h4>
                <div class="card">
                    <div class="card-body">
                        <pre><code id="webhooksignatureverification">
// PHP Example - Verify webhook signature
$payload = file_get_contents('php://input');
$signature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] ?? '';
$secretKey = 'your_api_secret_key';

$expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $secretKey);

if (hash_equals($expectedSignature, $signature)) {
    // Signature is valid - process webhook
    $data = json_decode($payload, true);
    // Handle webhook data...
    echo json_encode(['status' => 'success']);
} else {
    // Invalid signature - reject webhook
    http_response_code(401);
    echo json_encode(['error' => 'Invalid signature']);
}
                        </code></pre>
                    </div>
                </div>
            </div>
        </div>

    </div>

@endsection


@push('script')
    <script src="{{ asset('assets/user/js/highlight.min.js') }}"></script>
    <script>
        'use strict'
        hljs.highlightAll();

        $(document).on('click', '.copy-btn', function (e) {
            e.preventDefault();
            var node = $(this).parents('.card-header').siblings('.card-body').find('.active').find('code').attr('id');

            var r = document.createRange();
            r.selectNode(document.getElementById(node));
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(r);
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            Notiflix.Notify.success("Copied");
            return false;
        })
    </script>
    @if ($errors->any())
        @php
            $collection = collect($errors->all());
            $errors = $collection->unique();
        @endphp
        <script>
            "use strict";
            @foreach ($errors as $error)
            Notiflix.Notify.failure("{{ trans($error) }}");
            @endforeach
        </script>
    @endif
@endpush
