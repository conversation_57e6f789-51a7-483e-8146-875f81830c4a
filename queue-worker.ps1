# Laravel Queue Worker PowerShell Script
param(
    [string]$Action = "start",
    [int]$Workers = 2
)

$ProjectPath = "c:\Users\<USER>\Herd\currency"
$LogPath = "$ProjectPath\storage\logs\queue-worker.log"

function Start-QueueWorker {
    param([int]$WorkerNumber)
    
    Write-Host "Starting Queue Worker #$WorkerNumber..." -ForegroundColor Green
    
    $ProcessName = "queue-worker-$WorkerNumber"
    
    Start-Process -FilePath "php" -ArgumentList @(
        "artisan", 
        "queue:work", 
        "database",
        "--sleep=3",
        "--tries=3", 
        "--max-time=3600",
        "--timeout=60",
        "--name=$ProcessName"
    ) -WorkingDirectory $ProjectPath -WindowStyle Minimized
}

function Stop-QueueWorkers {
    Write-Host "Stopping all queue workers..." -ForegroundColor Yellow
    
    Get-Process | Where-Object { $_.ProcessName -eq "php" -and $_.CommandLine -like "*queue:work*" } | Stop-Process -Force
    
    Write-Host "All queue workers stopped." -ForegroundColor Red
}

function Get-QueueWorkerStatus {
    $QueueProcesses = Get-Process | Where-Object { $_.ProcessName -eq "php" -and $_.CommandLine -like "*queue:work*" }
    
    if ($QueueProcesses.Count -gt 0) {
        Write-Host "Queue Workers Running: $($QueueProcesses.Count)" -ForegroundColor Green
        $QueueProcesses | Format-Table Id, ProcessName, StartTime, CPU
    } else {
        Write-Host "No queue workers are currently running." -ForegroundColor Red
    }
}

function Restart-QueueWorkers {
    Stop-QueueWorkers
    Start-Sleep -Seconds 3
    
    for ($i = 1; $i -le $Workers; $i++) {
        Start-QueueWorker -WorkerNumber $i
        Start-Sleep -Seconds 1
    }
}

# Main script logic
switch ($Action.ToLower()) {
    "start" {
        Write-Host "Starting $Workers queue workers..." -ForegroundColor Cyan
        for ($i = 1; $i -le $Workers; $i++) {
            Start-QueueWorker -WorkerNumber $i
        }
    }
    "stop" {
        Stop-QueueWorkers
    }
    "restart" {
        Restart-QueueWorkers
    }
    "status" {
        Get-QueueWorkerStatus
    }
    default {
        Write-Host "Usage: .\queue-worker.ps1 -Action [start|stop|restart|status] -Workers [number]" -ForegroundColor Yellow
        Write-Host "Examples:" -ForegroundColor White
        Write-Host "  .\queue-worker.ps1 -Action start -Workers 2" -ForegroundColor Gray
        Write-Host "  .\queue-worker.ps1 -Action stop" -ForegroundColor Gray
        Write-Host "  .\queue-worker.ps1 -Action restart" -ForegroundColor Gray
        Write-Host "  .\queue-worker.ps1 -Action status" -ForegroundColor Gray
    }
}
