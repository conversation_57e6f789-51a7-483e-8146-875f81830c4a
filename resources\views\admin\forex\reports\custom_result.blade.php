@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.index') }}">
                                    @lang('Forex Reports')
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.custom') }}">
                                    @lang('Custom Report Builder')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $reportData['title'] }}</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">{{ $reportData['title'] }}</h1>
                    <p class="page-header-text">@lang('Report Period'): {{ $reportData['period'] }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="btn-group" role="group">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.reports.custom') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Builder')
                        </a>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="bi-printer me-1"></i> @lang('Print')
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi-download me-1"></i> @lang('Export')
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                    <i class="bi-file-earmark-excel me-2"></i> @lang('Excel')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                    <i class="bi-file-earmark-pdf me-2"></i> @lang('PDF')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                    <i class="bi-file-earmark-text me-2"></i> @lang('CSV')
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Report Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Report Summary')</h4>
                        <span class="badge bg-soft-primary text-primary">
                            {{ ucfirst($reportData['report_type']) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($reportData['summary'] as $key => $value)
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <div class="media">
                                        <div class="media-body">
                                            <span class="d-block h4 mb-0">
                                                @if(is_array($value))
                                                    @if($key === 'cbn_rate_range')
                                                        ₦{{ number_format($value['avg'], 2) }}
                                                        <small class="text-muted">(avg)</small>
                                                    @elseif($key === 'revenue_by_type')
                                                        ${{ number_format(array_sum($value), 2) }}
                                                    @else
                                                        {{ json_encode($value) }}
                                                    @endif
                                                @elseif(is_numeric($value))
                                                    @if(str_contains($key, 'amount') || str_contains($key, 'revenue') || str_contains($key, 'total'))
                                                        @if(str_contains($key, 'usd'))
                                                            ${{ number_format($value, 2) }}
                                                        @elseif(str_contains($key, 'ngn'))
                                                            ₦{{ number_format($value, 2) }}
                                                        @else
                                                            ${{ number_format($value, 2) }}
                                                        @endif
                                                    @elseif(str_contains($key, 'percentage') || str_contains($key, 'rate'))
                                                        {{ number_format($value, 2) }}%
                                                    @else
                                                        {{ number_format($value) }}
                                                    @endif
                                                @else
                                                    {{ $value }}
                                                @endif
                                            </span>
                                            <span class="d-block text-muted">
                                                {{ ucwords(str_replace('_', ' ', $key)) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Data -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Detailed Data')</h4>
                        <div class="card-header-content">
                            <span class="text-muted">{{ $reportData['bookings']->count() }} @lang('records')</span>
                        </div>
                    </div>
                    <div class="card-body">
                        @if($reportData['bookings']->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                                    <thead class="thead-light">
                                        <tr>
                                            @if(in_array('booking_reference', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['booking_reference'] ?? 'Booking Reference' }}</th>
                                            @endif
                                            @if(in_array('client_name', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['client_name'] ?? 'Client Name' }}</th>
                                            @endif
                                            @if(in_array('amount', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['amount'] ?? 'Amount' }}</th>
                                            @endif
                                            @if(in_array('cbn_rate', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['cbn_rate'] ?? 'CBN Rate' }}</th>
                                            @endif
                                            @if(in_array('markup_percentage', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['markup_percentage'] ?? 'Markup %' }}</th>
                                            @endif
                                            @if(in_array('parallel_rate', $reportConfig['selected_fields'] ?? []) && $reportData['report_type'] !== 'cbn_focused')
                                                <th>{{ $reportConfig['field_labels']['parallel_rate'] ?? 'Parallel Rate' }}</th>
                                            @endif
                                            @if(in_array('customer_rate', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['customer_rate'] ?? 'Final Rate' }}</th>
                                            @endif
                                            @if(in_array('customer_total', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['customer_total'] ?? 'Total Amount' }}</th>
                                            @endif
                                            @if(in_array('status', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['status'] ?? 'Status' }}</th>
                                            @endif
                                            @if(in_array('created_at', $reportConfig['selected_fields'] ?? []))
                                                <th>{{ $reportConfig['field_labels']['created_at'] ?? 'Date' }}</th>
                                            @endif
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reportData['bookings'] as $booking)
                                            <tr>
                                                @if(in_array('booking_reference', $reportConfig['selected_fields'] ?? []))
                                                    <td>
                                                        <a href="{{ route('admin.forex.bookings.show', $booking->id) }}" class="text-decoration-none">
                                                            {{ $booking->booking_reference }}
                                                        </a>
                                                    </td>
                                                @endif
                                                @if(in_array('client_name', $reportConfig['selected_fields'] ?? []))
                                                    <td>{{ $booking->client_name }}</td>
                                                @endif
                                                @if(in_array('amount', $reportConfig['selected_fields'] ?? []))
                                                    <td>
                                                        @if($booking->currency === 'USD')
                                                            ${{ number_format($booking->amount, 2) }}
                                                        @else
                                                            ₦{{ number_format($booking->amount, 2) }}
                                                        @endif
                                                    </td>
                                                @endif
                                                @if(in_array('cbn_rate', $reportConfig['selected_fields'] ?? []))
                                                    <td>₦{{ number_format($booking->cbn_rate, 2) }}</td>
                                                @endif
                                                @if(in_array('markup_percentage', $reportConfig['selected_fields'] ?? []))
                                                    <td>{{ number_format($booking->markup_percentage, 2) }}%</td>
                                                @endif
                                                @if(in_array('parallel_rate', $reportConfig['selected_fields'] ?? []) && $reportData['report_type'] !== 'cbn_focused')
                                                    <td>₦{{ number_format($booking->parallel_rate, 2) }}</td>
                                                @endif
                                                @if(in_array('customer_rate', $reportConfig['selected_fields'] ?? []))
                                                    <td>₦{{ number_format($booking->customer_rate, 2) }}</td>
                                                @endif
                                                @if(in_array('customer_total', $reportConfig['selected_fields'] ?? []))
                                                    <td>₦{{ number_format($booking->customer_total, 2) }}</td>
                                                @endif
                                                @if(in_array('status', $reportConfig['selected_fields'] ?? []))
                                                    <td>
                                                        @if($booking->status === 'completed')
                                                            <span class="badge bg-soft-success text-success">@lang('Completed')</span>
                                                        @elseif($booking->status === 'pending')
                                                            <span class="badge bg-soft-warning text-warning">@lang('Pending')</span>
                                                        @elseif($booking->status === 'cancelled')
                                                            <span class="badge bg-soft-danger text-danger">@lang('Cancelled')</span>
                                                        @else
                                                            <span class="badge bg-soft-secondary text-secondary">{{ ucfirst($booking->status) }}</span>
                                                        @endif
                                                    </td>
                                                @endif
                                                @if(in_array('created_at', $reportConfig['selected_fields'] ?? []))
                                                    <td>{{ $booking->created_at->format('M d, Y H:i') }}</td>
                                                @endif
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <div class="mb-3">
                                    <i class="bi-inbox display-4 text-muted"></i>
                                </div>
                                <h5 class="text-muted">@lang('No data found')</h5>
                                <p class="text-muted">@lang('No records match your selected criteria for the specified period.')</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        
        function exportReport(format) {
            const form = $('<form>', {
                'method': 'POST',
                'action': '{{ route("admin.forex.reports.generate") }}',
                'target': '_blank'
            });
            
            // Add CSRF token
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_token',
                'value': '{{ csrf_token() }}'
            }));
            
            // Add all report config
            @foreach($reportConfig as $key => $value)
                @if(is_array($value))
                    @foreach($value as $subKey => $subValue)
                        form.append($('<input>', {
                            'type': 'hidden',
                            'name': '{{ $key }}[{{ $subKey }}]',
                            'value': '{{ $subValue }}'
                        }));
                    @endforeach
                @else
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': '{{ $key }}',
                        'value': '{{ $value }}'
                    }));
                @endif
            @endforeach
            
            // Set export format
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'format',
                'value': format
            }));
            
            $('body').append(form);
            form.submit();
            form.remove();
        }
    </script>
@endpush
