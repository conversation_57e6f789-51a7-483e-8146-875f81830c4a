@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">@lang('Operational Costs')</h1>
                    <p class="page-header-text">@lang('Record and manage operational expenses on-the-fly')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-primary" href="{{ route('admin.forex.costs.create') }}">
                        <i class="bi-plus me-1"></i> @lang('Record New Cost')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('This Week')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ number_format($weeklySummary['total_costs'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">NGN</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-warning text-warning">
                                    <i class="bi-calendar-week"></i> @lang('Weekly')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            {{ $weeklySummary['cost_count'] ?? 0 }} @lang('entries')
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('This Month')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ number_format($monthlySummary['total_costs'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">NGN</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-info text-info">
                                    <i class="bi-calendar-month"></i> @lang('Monthly')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            {{ $monthlySummary['cost_count'] ?? 0 }} @lang('entries')
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Categories')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $categories->count() }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Active')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi-tags"></i> @lang('Categories')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            @lang('Cost categories')
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Quick Actions')</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.forex.costs.create') }}" class="btn btn-sm btn-primary">
                                <i class="bi-plus-circle me-1"></i> @lang('Add Cost')
                            </a>
                            <a href="{{ route('admin.forex.costs.summary') }}" class="btn btn-sm btn-outline-secondary">
                                <i class="bi-graph-up me-1"></i> @lang('View Summary')
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Stats Cards -->

        <!-- Card -->
        <div class="card">
            <!-- Header -->
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <div class="input-group input-group-merge navbar-input-group">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input type="search" id="datatableSearch" class="search form-control"
                               placeholder="@lang('Search operational costs')" aria-label="@lang('Search operational costs')">
                    </div>
                </div>

                <div class="d-grid d-sm-flex gap-2">
                    <select class="js-select form-select" name="category" data-hs-tom-select-options='{"placeholder": "@lang('All categories')", "searchInDropdown": false}'>
                        <option value="">@lang('All categories')</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}">{{ $category }}</option>
                        @endforeach
                    </select>

                    <select class="js-select form-select" name="currency" data-hs-tom-select-options='{"placeholder": "@lang('All currencies')", "searchInDropdown": false}'>
                        <option value="">@lang('All currencies')</option>
                        <option value="NGN">NGN</option>
                        <option value="USD">USD</option>
                    </select>

                    <!-- Unfold -->
                    <div class="hs-unfold">
                        <a class="js-hs-unfold-invoker btn btn-white" href="javascript:;"
                           data-hs-unfold-options='{
                             "target": "#usersExportDropdown",
                             "type": "css-animation"
                           }'>
                            <i class="bi-download me-1"></i> @lang('Export')
                        </a>

                        <div id="usersExportDropdown"
                             class="hs-unfold-content dropdown-unfold dropdown-menu dropdown-menu-sm-end">
                            <span class="dropdown-header">@lang('Options')</span>
                            <a id="export-copy" class="dropdown-item" href="javascript:;">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-layout.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="default">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-layout-light.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="dark">
                                @lang('Copy')
                            </a>
                            <a id="export-print" class="dropdown-item" href="javascript:;">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-print.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="default">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-print-light.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="dark">
                                @lang('Print')
                            </a>
                            <div class="dropdown-divider"></div>
                            <span class="dropdown-header">@lang('Download options')</span>
                            <a id="export-excel" class="dropdown-item" href="javascript:;">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-excel.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="default">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-excel-light.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="dark">
                                @lang('Excel')
                            </a>
                            <a id="export-csv" class="dropdown-item" href="javascript:;">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-csv.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="default">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-csv-light.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="dark">
                                @lang('CSV')
                            </a>
                            <a id="export-pdf" class="dropdown-item" href="javascript:;">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-pdf.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="default">
                                <img class="avatar avatar-xss avatar-4x3 me-2"
                                     src="{{ asset('assets/admin/img/oc-pdf-light.svg') }}" alt="Image Description"
                                     data-hs-theme-appearance="dark">
                                @lang('PDF')
                            </a>
                        </div>
                    </div>
                    <!-- End Unfold -->
                </div>
            </div>
            <!-- End Header -->

            <!-- Table -->
            <div class="table-responsive datatable-custom">
                <table id="datatable"
                       class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                         "columnDefs": [{
                            "targets": [0, 6],
                            "orderable": false
                          }],
                         "order": [],
                         "info": {
                           "totalQty": "#datatableWithPaginationInfoTotalQty"
                         },
                         "search": "#datatableSearch",
                         "entries": "#datatableEntries",
                         "pageLength": 15,
                         "isResponsive": false,
                         "isShowPaging": false,
                         "pagination": "datatablePagination"
                       }'>
                    <thead class="thead-light">
                    <tr>
                        <th class="table-column-pe-0">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="datatableCheckAll">
                                <label class="form-check-label" for="datatableCheckAll"></label>
                            </div>
                        </th>
                        <th>@lang('Cost Details')</th>
                        <th>@lang('Amount')</th>
                        <th>@lang('Category')</th>
                        <th>@lang('Date')</th>
                        <th>@lang('Recorded By')</th>
                        <th>@lang('Attachments')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <!-- End Table -->

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing:')</span>
                            <!-- Select -->
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto"
                                        autocomplete="off"
                                        data-hs-tom-select-options='{
                                            "searchInDropdown": false,
                                            "hideSearch": true
                                          }'>
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="20">20</option>
                                </select>
                            </div>
                            <!-- End Select -->
                            <span class="text-secondary me-2">@lang('of')</span>
                            <!-- Dynamic Data -->
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <!-- End Col -->

                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <!-- Pagination -->
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Card -->
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="deleteModalLabel">@lang('Confirm Delete')</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    @lang('Are you sure you want to delete this operational cost record?')
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <form id="deleteForm" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">@lang('Delete')</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

<x-assets :datatable="true" :counter="true"/>

@push('script')
    <script>
        'use strict';

        $(document).on('ready', function () {
            initDataTable("{{ route('admin.forex.costs.search') }}",
                {!! json_encode(['checkbox', 'cost_info', 'amount_info', 'category', 'cost_date', 'recorded_by', 'attachments', 'action']) !!}
            );

            // Filter functionality
            $('select[name="category"], select[name="currency"]').on('change', function() {
                const datatable = HSCore.components.HSDatatables.getItem(0);
                datatable.ajax.url("{{ route('admin.forex.costs.search') }}" +
                    "?category=" + $('select[name="category"]').val() +
                    "&currency=" + $('select[name="currency"]').val()).load();
            });

            // Delete functionality
            $(document).on('click', '.delete-cost', function() {
                let costId = $(this).data('id');
                let deleteUrl = "{{ route('admin.forex.costs.destroy', ':id') }}".replace(':id', costId);
                $('#deleteForm').attr('action', deleteUrl);
                $('#deleteModal').modal('show');
            });

            // Export functionality - Note: Export buttons functionality would need to be implemented separately
            $('#export-copy').on('click', function() {
                console.log('Copy export clicked');
            });

            $('#export-excel').on('click', function() {
                console.log('Excel export clicked');
            });

            $('#export-csv').on('click', function() {
                console.log('CSV export clicked');
            });

            $('#export-pdf').on('click', function() {
                console.log('PDF export clicked');
            });

            $('#export-print').on('click', function() {
                console.log('Print export clicked');
            });
        });
    </script>
@endpush
