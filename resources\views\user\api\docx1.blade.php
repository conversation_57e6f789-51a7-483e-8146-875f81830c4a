@extends(userLayout())

@section('title',__('Api Documentation'))

@section('content')

    <div class="content {{ containerClass() }}" id="api-app">
        <div class="page-header">
            <div class="row align-items-end">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{route('user.dashboard')}}">@lang("Dashboard")</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Settings')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('API Documentation Reference')</h1>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12 mb-4">
                <h3>@lang('Getting Started')</h3>
                <p>
                    @lang('This document explains how to successfully call the API with your app and get an
                    successful call. These endpoints helps you create and manage wallets.')
                </p>
                <p>
                    <x-badge text="Base Url" /><i>{{url('/')}}/api/</i>
                </p>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <h2 class="section-title mt-0">@lang('Check Transaction Status')</h2>
                <p>
                    @lang('Check the status of a payout transaction using the transaction reference. This endpoint accepts both internal transaction IDs and provider references.')
                </p>
                <p>
                    <x-badge text="Post" type="success"/>
                    <i>payout/status</i>
                </p>
                <hr/>
                <h5 class="mb-4">@lang('Body Params')</h5>
                <div class="row">
                    <div class="col-12">
                        <p>
                            <b>reference<span class="text-danger">*</span></b>
                            <x-badge text="string" />
                        </p>
                    </div>
                    <div class="col-md-8">
                        <p class="mb-0">@lang('Transaction reference ID (internal transaction ID or provider reference)')</p>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" value="TXN123456789"/>
                    </div>
                </div>
                <hr/>
                <div class="row g-4">
                    <div class="col-md-12">
                        <div class="alert alert-soft-info text-info" role="alert">
                            @lang('Note: Status values are pending, processing, success, cancelled, failed')
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6 col-lg-6 mt-5">
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="statusTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="status-curl-tab" data-bs-toggle="tab" href="#status-curl" role="tab">@lang('cURL')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="status-php-tab" data-bs-toggle="tab" href="#status-php" role="tab">@lang('PHP')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="status-curl" role="tabpanel">
                                <pre><code id="copystatuscurl">
curl --location --request POST '{{url('/')}}/api/payout/status' \
--header 'Authorization: Bearer YOUR_API_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "reference": "TXN123456789"
}'
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="status-php" role="tabpanel">
                                <pre><code id="copystatusphp">
<?php
$curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => '{{url('/')}}/api/payout/status',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => json_encode([
      'reference' => 'TXN123456789'
  ]),
  CURLOPT_HTTPHEADER => array(
    'Authorization: Bearer YOUR_API_TOKEN',
    'Content-Type: application/json'
  ),
));

$response = curl_exec($curl);
curl_close($curl);
echo $response;
?>
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-2">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <ul class="nav nav-tabs" id="statusResponseTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="status-success-tab" data-bs-toggle="tab" href="#status-success" role="tab">@lang('200 OK')</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="status-error-tab" data-bs-toggle="tab" href="#status-error" role="tab">@lang('400 Bad Request')</a>
                            </li>
                        </ul>
                        <a href="#" class="btn btn-icon btn-primary ml-auto copy-btn">
                            <i class="bi-clipboard"></i></a>
                    </div>
                    <div class="card-body">
                        <div class="tab-content tab-bordered">
                            <div class="tab-pane fade show active" id="status-success" role="tabpanel">
                                <pre><code id="statusSuccess">
{
    "status": "success",
    "message": {
        "reference": "TXN123456789",
        "status": "success",
        "amount": 100.00,
        "currency": "NGN",
        "method": "Numero",
        "created_at": "2025-07-10T11:00:00.000Z",
        "updated_at": "2025-07-10T12:00:00.000Z",
        "provider_reference": "NUM_REF_123456"
    }
}
</code></pre>
                            </div>
                            <div class="tab-pane fade" id="status-error" role="tabpanel">
                                <pre><code>
{
    "status": "failed",
    "message": "Transaction not found"
}
</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 mt-5">
                <h2 class="section-title mt-0">@lang('Webhook Configuration')</h2>
                <p>
                    @lang('Configure webhook URLs in your settings to receive real-time notifications when transaction events occur. Webhooks are sent for payout status changes (success, failed, pending) and deposit notifications.')
                </p>

                <h4>@lang('Webhook Events')</h4>
                <ul>
                    <li><strong>TRANSFER_NOTIFICATION:</strong> @lang('Sent when payout transaction status changes')</li>
                    <li><strong>FUNDING_NOTIFICATION:</strong> @lang('Sent when deposit/funding occurs')</li>
                    <li><strong>TEST_NOTIFICATION:</strong> @lang('Sent when testing webhook configuration')</li>
                </ul>

                <h4>@lang('Webhook Payload Structure')</h4>
                <div class="card">
                    <div class="card-body">
                        <pre><code>
{
  "event": "TRANSFER_NOTIFICATION",
  "timestamp": "2025-07-10T12:00:00.000Z",
  "data": {
    "transaction_id": "TXN123456789",
    "reference": "NUM_REF_123456",
    "amount": 100.00,
    "currency": "NGN",
    "status": "success",
    "method": "Numero",
    "created_at": "2025-07-10T11:00:00.000Z",
    "updated_at": "2025-07-10T12:00:00.000Z",
    "user_id": 123,
    "charge": 5.00,
    "net_amount": 105.00,
    "provider": "numero",
    "provider_data": {
      // Original provider webhook data
    }
  }
}
                        </code></pre>
                    </div>
                </div>

                <h4>@lang('Configuration')</h4>
                <p>
                    @lang('To configure webhooks:')
                </p>
                <ol>
                    <li>@lang('Go to your account settings')</li>
                    <li>@lang('Add your webhook URL in the webhook configuration section')</li>
                    <li>@lang('Ensure your endpoint returns HTTP 200-299 status codes')</li>
                    <li>@lang('Implement proper error handling and idempotency')</li>
                </ol>

                <div class="alert alert-soft-info text-info" role="alert">
                    <strong>@lang('Security Note:') </strong>
                    @lang('Always use HTTPS for webhook URLs and validate the X-Webhook-Source header to ensure authenticity.')
                </div>
            </div>
        </div>

    </div>

@endsection


@push('script')
    <script src="{{ asset('assets/user/js/highlight.min.js') }}"></script>
    <script>
        'use strict'
        hljs.highlightAll();

        $(document).on('click', '.copy-btn', function () {
            var node = $(this).parents('.card-header').siblings('.card-body').find('.active').find('code').attr('id');

            var r = document.createRange();
            r.selectNode(document.getElementById(node));
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(r);
            document.execCommand('copy');
            window.getSelection().removeAllRanges();
            Notiflix.Notify.success("Copied");
        })
    </script>
    @if ($errors->any())
        @php
            $collection = collect($errors->all());
            $errors = $collection->unique();
        @endphp
        <script>
            "use strict";
            @foreach ($errors as $error)
            Notiflix.Notify.failure("{{ trans($error) }}");
            @endforeach
        </script>
    @endif
@endpush
