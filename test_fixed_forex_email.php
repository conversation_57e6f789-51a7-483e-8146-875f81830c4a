<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;
use Illuminate\Support\Facades\DB;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING FIXED FOREX EMAIL SYSTEM ===\n\n";

// Check initial queue count
$initialCount = DB::table('jobs')->count();
echo "Initial queue jobs: {$initialCount}\n";

// Get services
$walletService = new ForexWalletService();
$bookingService = new ForexBookingService($walletService);

// Get USD account
$usdAccount = ForexAccount::byType('USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

// Create a test booking
$bookingData = [
    'client_type' => 'external',
    'client_name' => 'Fixed Email Test Client',
    'client_email' => '<EMAIL>',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 15.00,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test account for fixed email system',
];

try {
    echo "\nCreating booking with fixed email system...\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    
    echo "✅ Booking created: {$booking->booking_reference}\n";
    echo "✅ Email sent flag: " . ($booking->email_sent ? 'Yes' : 'No') . "\n";
    
    // Check final queue count
    $finalCount = DB::table('jobs')->count();
    echo "Final queue jobs: {$finalCount}\n";
    echo "Jobs added: " . ($finalCount - $initialCount) . "\n";
    
    if ($finalCount > $initialCount) {
        echo "🎉 SUCCESS! Email job was queued!\n";
        echo "\nTo process the email:\n";
        echo "php artisan queue:work --once\n";
    } else {
        echo "❌ No job was queued. Check logs.\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
