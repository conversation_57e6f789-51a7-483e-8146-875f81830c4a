<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ForexRate;
use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ForexController extends Controller
{
    protected $bookingService;
    protected $walletService;

    public function __construct(ForexBookingService $bookingService, ForexWalletService $walletService)
    {
        $this->bookingService = $bookingService;
        $this->walletService = $walletService;
    }

    /**
     * Get current exchange rates
     */
    public function getCurrentRates()
    {
        try {
            $activeRate = ForexRate::getActiveRate();

            if (!$activeRate) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active exchange rate available',
                    'data' => null
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Exchange rates retrieved successfully',
                'data' => [
                    'cbn_rate' => $activeRate->cbn_rate,
                    'parallel_rate' => $activeRate->parallel_rate,
                    'markup_percentage' => $activeRate->markup_percentage,
                    'cbn_sell_rate' => $activeRate->cbn_sell_rate,
                    'parallel_sell_rate' => $activeRate->parallel_sell_rate,
                    'sell_markup_percentage' => $activeRate->sell_markup_percentage,
                    'last_updated' => $activeRate->updated_at->toISOString(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve exchange rates',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate exchange amounts
     */
    public function calculateExchange(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'from_currency' => 'required|in:USD,NGN',
            'to_currency' => 'required|in:USD,NGN|different:from_currency',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $activeRate = ForexRate::getActiveRate();

            if (!$activeRate) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active exchange rate available'
                ], 404);
            }

            $amount = $request->amount;
            $fromCurrency = $request->from_currency;
            $toCurrency = $request->to_currency;

            if ($fromCurrency === 'USD' && $toCurrency === 'NGN') {
                // USD to NGN: Use sell rates
                $cbnTotal = $activeRate->calculateCbnSellTotal($amount);
                $parallelTotal = $activeRate->calculateParallelSellTotal($amount);
                $difference = $activeRate->calculateSellDifference($amount);

                $result = [
                    'input_amount' => $amount,
                    'input_currency' => $fromCurrency,
                    'output_currency' => $toCurrency,
                    'cbn_sell_rate' => $activeRate->cbn_sell_rate,
                    'parallel_sell_rate' => $activeRate->parallel_sell_rate,
                    'sell_markup_percentage' => $activeRate->sell_markup_percentage,
                    'cbn_total' => $cbnTotal,
                    'parallel_total' => $parallelTotal,
                    'difference' => $difference,
                    'recommended_total' => $parallelTotal, // Using parallel sell rate as default
                ];
            } else {
                // NGN to USD: Use buy rates
                $usdAmount = $amount / $activeRate->parallel_rate;
                $cbnEquivalent = $amount / $activeRate->cbn_rate;
                $difference = $cbnEquivalent - $usdAmount;

                $result = [
                    'input_amount' => $amount,
                    'input_currency' => $fromCurrency,
                    'output_currency' => $toCurrency,
                    'cbn_rate' => $activeRate->cbn_rate,
                    'parallel_rate' => $activeRate->parallel_rate,
                    'markup_percentage' => $activeRate->markup_percentage,
                    'usd_amount' => $usdAmount,
                    'cbn_equivalent' => $cbnEquivalent,
                    'difference' => $difference,
                    'recommended_amount' => $usdAmount, // Using parallel buy rate as default
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Exchange calculation completed',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate exchange',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a forex booking
     */
    public function createBooking(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'transaction_type' => 'required|in:buying,selling',
            'currency' => 'required|in:USD,NGN',
            'amount' => 'required|numeric|min:0.01',
            'client_name' => 'required|string|max:255',
            'client_email' => 'required|email|max:255',
            'client_phone' => 'nullable|string|max:20',
            'account_details' => 'nullable|string',
            'payment_method' => 'nullable|in:account_details,wallet',
            'wallet_currency_id' => 'nullable|exists:currencies,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get appropriate target account based on transaction type and currency
            $targetAccount = $this->getTargetAccount($request->transaction_type, $request->currency);

            if (!$targetAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'No suitable account found for this transaction'
                ], 400);
            }

            // Additional validation for wallet payment
            if ($request->payment_method === 'wallet' && Auth::check()) {
                if (!$this->walletService->validateWalletPayment(
                    Auth::id(),
                    $request->payment_method,
                    $request->wallet_currency_id
                )) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Selected wallet is not available for this user.'
                    ], 400);
                }
            }

            $bookingData = $request->only([
                'transaction_type', 'currency', 'amount', 'client_name',
                'client_email', 'client_phone', 'account_details', 'payment_method',
                'wallet_currency_id'
            ]);

            $bookingData['client_type'] = Auth::check() ? 'user' : 'external';
            $bookingData['target_account_id'] = $targetAccount->id;
            $bookingData['user_id'] = Auth::id(); // If authenticated via API

            $booking = $this->bookingService->createBooking($bookingData, 1); // System admin ID

            return response()->json([
                'success' => true,
                'message' => 'Forex booking created successfully',
                'data' => [
                    'booking_reference' => $booking->booking_reference,
                    'status' => $booking->status,
                    'amount' => $booking->amount,
                    'currency' => $booking->currency,
                    'cbn_total' => $booking->cbn_total,
                    'parallel_total' => $booking->parallel_total,
                    'difference_amount' => $booking->difference_amount,
                    'created_at' => $booking->created_at->toISOString(),
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create booking',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking status
     */
    public function getBookingStatus($reference)
    {
        try {
            $booking = ForexBooking::where('booking_reference', $reference)->first();

            if (!$booking) {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Booking status retrieved successfully',
                'data' => [
                    'booking_reference' => $booking->booking_reference,
                    'status' => $booking->status,
                    'client_name' => $booking->client_name,
                    'amount' => $booking->amount,
                    'currency' => $booking->currency,
                    'transaction_type' => $booking->transaction_type,
                    'created_at' => $booking->created_at->toISOString(),
                    'completed_at' => $booking->completed_at?->toISOString(),
                    'status_notes' => $booking->status_notes,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve booking status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's booking history
     */
    public function getBookingHistory(Request $request)
    {
        try {
            $query = ForexBooking::query();

            // If user is authenticated, filter by user
            if (Auth::check()) {
                $query->where('user_id', Auth::id());
            } else {
                // For external API access, require email parameter
                $validator = Validator::make($request->all(), [
                    'client_email' => 'required|email'
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Email is required for booking history',
                        'errors' => $validator->errors()
                    ], 422);
                }

                $query->where('client_email', $request->client_email);
            }

            $bookings = $query->latest()
                ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => 'Booking history retrieved successfully',
                'data' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve booking history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get account balances (admin only)
     */
    public function getAccountBalances()
    {
        try {
            $accounts = ForexAccount::active()->get();

            $balances = $accounts->map(function ($account) {
                return [
                    'account_name' => $account->account_name,
                    'account_type' => $account->account_type,
                    'currency' => $account->currency_code,
                    'balance' => $account->balance,
                    'pending_balance' => $account->pending_balance,
                    'available_balance' => $account->available_balance,
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Account balances retrieved successfully',
                'data' => $balances
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve account balances',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get target account for booking
     */
    private function getTargetAccount($transactionType, $currency)
    {
        if ($transactionType === 'buying') {
            // For funding, use the account of the same currency
            return ForexAccount::active()
                ->where('currency_code', $currency)
                ->first();
        } else {
            // For exchange, use USD account for USD transactions, CBN for NGN
            $accountType = $currency === 'USD' ? 'USD' : 'CBN';
            return ForexAccount::active()
                ->where('account_type', $accountType)
                ->first();
        }
    }

    /**
     * Get wallet payment options for authenticated user
     */
    public function getWalletPaymentOptions(Request $request)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'transaction_type' => 'required|in:buying,selling'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $paymentOptions = $this->walletService->getPaymentMethodOptions(
                Auth::id(),
                $request->transaction_type
            );

            return response()->json([
                'success' => true,
                'data' => $paymentOptions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get wallet information: ' . $e->getMessage()
            ], 500);
        }
    }
}
