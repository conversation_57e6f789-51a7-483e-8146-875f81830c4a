<?php

/**
 * Example script demonstrating how to use the Webhook API endpoints
 * 
 * This is a demonstration script showing how to interact with the webhook management API.
 * Replace the API_TOKEN and BASE_URL with your actual values.
 */

// Configuration
$API_TOKEN = 'your_api_token_here';
$BASE_URL = 'https://your-domain.com/api';
$WEBHOOK_URL = 'https://your-webhook-endpoint.com/webhook/numero';

// Helper function to make API requests
function makeApiRequest($url, $method = 'GET', $data = null, $token = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

echo "=== Webhook API Test Script ===\n\n";

// 1. Get supported providers
echo "1. Getting supported providers...\n";
$result = makeApiRequest($BASE_URL . '/webhook/providers', 'GET', null, $API_TOKEN);
echo "Status: " . $result['status_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";

// 2. Subscribe to webhook
echo "2. Subscribing to webhook...\n";
$subscribeData = [
    'webhook_url' => $WEBHOOK_URL,
    'events' => ['transfer.success', 'transfer.failed', 'transfer.pending'],
    'provider' => 'numero'
];
$result = makeApiRequest($BASE_URL . '/webhook/subscribe', 'POST', $subscribeData, $API_TOKEN);
echo "Status: " . $result['status_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";

// Extract webhook ID for further operations (if subscription was successful)
$webhookId = null;
if ($result['status_code'] === 200 && isset($result['response']['message']['data']['webhook_id'])) {
    $webhookId = $result['response']['message']['data']['webhook_id'];
    echo "Webhook ID: " . $webhookId . "\n\n";
}

// 3. Get webhook secret
echo "3. Getting webhook secret...\n";
$result = makeApiRequest($BASE_URL . '/webhook/secret?provider=numero', 'GET', null, $API_TOKEN);
echo "Status: " . $result['status_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";

// 4. Get all webhooks
echo "4. Getting all webhooks...\n";
$result = makeApiRequest($BASE_URL . '/webhook/list?provider=numero', 'GET', null, $API_TOKEN);
echo "Status: " . $result['status_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";

// 5. Update webhook (only if we have a webhook ID)
if ($webhookId) {
    echo "5. Updating webhook...\n";
    $updateData = [
        'webhook_id' => $webhookId,
        'webhook_url' => $WEBHOOK_URL . '/updated',
        'events' => ['transfer.success', 'transfer.failed'],
        'provider' => 'numero'
    ];
    $result = makeApiRequest($BASE_URL . '/webhook/update', 'POST', $updateData, $API_TOKEN);
    echo "Status: " . $result['status_code'] . "\n";
    echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";
    
    // 6. Unsubscribe webhook
    echo "6. Unsubscribing webhook...\n";
    $unsubscribeData = [
        'webhook_id' => $webhookId,
        'provider' => 'numero'
    ];
    $result = makeApiRequest($BASE_URL . '/webhook/unsubscribe', 'POST', $unsubscribeData, $API_TOKEN);
    echo "Status: " . $result['status_code'] . "\n";
    echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";
} else {
    echo "5. Skipping update and unsubscribe - no webhook ID available\n\n";
}

echo "=== Test completed ===\n";

/**
 * Example webhook endpoint handler
 * 
 * This is how you might handle incoming webhook notifications:
 */
function handleWebhookNotification($payload, $signature, $secret) {
    // Verify the signature
    $expectedSignature = base64_encode(hash_hmac('sha256', $payload, $secret, true));
    
    if (!hash_equals($expectedSignature, $signature)) {
        http_response_code(401);
        echo json_encode(['error' => 'Invalid signature']);
        return;
    }
    
    // Parse the payload
    $data = json_decode($payload, true);
    
    // Handle different event types
    switch ($data['event'] ?? '') {
        case 'transfer.success':
            // Handle successful transfer
            echo "Transfer successful: " . ($data['data']['reference'] ?? 'unknown');
            break;
            
        case 'transfer.failed':
            // Handle failed transfer
            echo "Transfer failed: " . ($data['data']['reference'] ?? 'unknown');
            break;
            
        case 'transfer.pending':
            // Handle pending transfer
            echo "Transfer pending: " . ($data['data']['reference'] ?? 'unknown');
            break;
            
        default:
            echo "Unknown event type: " . ($data['event'] ?? 'none');
    }
    
    // Return success response
    http_response_code(200);
    echo json_encode(['status' => 'success']);
}

// Example usage:
// $payload = file_get_contents('php://input');
// $signature = $_SERVER['HTTP_X_SIGNATURE'] ?? '';
// $secret = 'your_webhook_secret';
// handleWebhookNotification($payload, $signature, $secret);
