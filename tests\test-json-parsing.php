<?php

/**
 * JSON Parsing Test Script
 * 
 * This script tests whether JSON request bodies are being properly parsed
 * by Laravel API endpoints after fixing the middleware issue.
 */

class JsonParsingTester
{
    private $baseUrl;

    public function __construct($baseUrl = 'http://currency.test')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Test JSON parsing with the authenticate endpoint
     */
    public function testAuthenticateEndpoint()
    {
        echo "Testing JSON parsing with /api/authenticate endpoint...\n";
        
        $url = $this->baseUrl . '/api/authenticate';
        $data = [
            'publicKey' => 'test_public_key_12345',
            'secretKey' => 'test_secret_key_67890'
        ];

        echo "Request URL: $url\n";
        echo "Request Data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";

        $response = $this->makeJsonRequest('POST', $url, $data);
        
        if ($response) {
            $result = json_decode($response, true);
            
            // Check if we get a validation error (which means JSO<PERSON> was parsed)
            // or if we get the expected authentication error
            if (isset($result['status'])) {
                if ($result['status'] === 'failed' && 
                    (strpos($result['message'], 'publicKey') !== false || 
                     strpos($result['message'], 'Invalid API credentials') !== false)) {
                    echo "✅ JSON parsing is working! Got expected validation/auth error.\n";
                    echo "Response: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
                    return true;
                } elseif ($result['status'] === 'success') {
                    echo "✅ JSON parsing is working! Authentication succeeded.\n";
                    echo "Response: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
                    return true;
                }
            }
            
            echo "❌ Unexpected response format:\n";
            echo $response . "\n\n";
            return false;
        } else {
            echo "❌ No response received\n\n";
            return false;
        }
    }

    /**
     * Test with a simple endpoint that should show request data
     */
    public function testBasicEndpoint()
    {
        echo "Testing JSON parsing with /api/basic endpoint...\n";
        
        $url = $this->baseUrl . '/api/basic';
        
        // This endpoint doesn't require authentication, so we can test JSON parsing
        $response = $this->makeJsonRequest('GET', $url);
        
        if ($response) {
            echo "✅ Basic endpoint responded successfully\n";
            echo "Response: " . substr($response, 0, 200) . "...\n\n";
            return true;
        } else {
            echo "❌ Basic endpoint failed\n\n";
            return false;
        }
    }

    /**
     * Test with invalid JSON to see error handling
     */
    public function testInvalidJson()
    {
        echo "Testing with malformed JSON...\n";
        
        $url = $this->baseUrl . '/api/authenticate';
        
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => '{"publicKey": "test", "secretKey":}', // Invalid JSON
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ]
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        echo "HTTP Status: $httpCode\n";
        
        if ($httpCode === 400 || $httpCode === 422) {
            echo "✅ Invalid JSON properly rejected\n";
            echo "Response: $response\n\n";
            return true;
        } else {
            echo "❌ Invalid JSON handling unexpected\n";
            echo "Response: $response\n\n";
            return false;
        }
    }

    /**
     * Test with form data to ensure normal form handling still works
     */
    public function testFormData()
    {
        echo "Testing with form data (should still work for non-API routes)...\n";
        
        $url = $this->baseUrl . '/api/authenticate';
        
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => 'publicKey=test_key&secretKey=test_secret',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json'
            ]
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        echo "HTTP Status: $httpCode\n";
        
        if ($response) {
            $result = json_decode($response, true);
            if (isset($result['status']) && $result['status'] === 'failed') {
                echo "✅ Form data parsing is working\n";
                echo "Response: " . json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
                return true;
            }
        }
        
        echo "❌ Form data parsing issue\n";
        echo "Response: $response\n\n";
        return false;
    }

    /**
     * Make a JSON request
     */
    private function makeJsonRequest($method, $url, $data = null)
    {
        $curl = curl_init();
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        if (curl_errno($curl)) {
            echo "cURL Error: " . curl_error($curl) . "\n";
            curl_close($curl);
            return false;
        }
        
        curl_close($curl);
        
        echo "HTTP Status: $httpCode\n";
        
        return $response;
    }
}

// Run the tests
echo "=== JSON Parsing Test Suite ===\n\n";

$tester = new JsonParsingTester('http://currency.test'); // Adjust URL as needed

echo "Testing JSON request parsing after middleware fix...\n\n";

// Test 1: Basic endpoint
$tester->testBasicEndpoint();

// Test 2: Authenticate endpoint with JSON
$tester->testAuthenticateEndpoint();

// Test 3: Form data (should still work)
$tester->testFormData();

// Test 4: Invalid JSON handling
$tester->testInvalidJson();

echo "=== Test Suite Completed ===\n";
echo "\nIf the authenticate endpoint shows validation errors or 'Invalid API credentials',\n";
echo "that means JSON parsing is working correctly!\n";
echo "\nThe key indicator is that you should NOT see empty array [] in request data.\n";
