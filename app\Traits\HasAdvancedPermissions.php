<?php

namespace App\Traits;

use App\Models\AdvancedPermission;
use App\Models\AdvancedRole;
use App\Models\AdvancedPermissionAudit;
use App\Services\AdvancedPermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

/**
 * Has Advanced Permissions Trait
 *
 * Provides helper methods for controllers to easily check permissions,
 * handle authorization, and manage permission-related operations.
 */
trait HasAdvancedPermissions
{
    /**
     * Advanced permission service instance
     */
    protected ?AdvancedPermissionService $permissionService = null;

    /**
     * Get permission service instance
     */
    protected function getPermissionService(): AdvancedPermissionService
    {
        if (!$this->permissionService) {
            $this->permissionService = app(\App\Services\AdvancedPermissionService::class);
        }

        return $this->permissionService;
    }

    /**
     * Check if current user has permission
     */
    protected function hasPermission(string $permission, array $context = []): bool
    {
        $user = Auth::user();
        if (!$user) {
            return false;
        }

        $result = $this->getPermissionService()->checkUserPermission($user, $permission, $context);
        return $result['granted'];
    }

    /**
     * Check multiple permissions (user needs ALL)
     */
    protected function hasAllPermissions(array $permissions, array $context = []): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check multiple permissions (user needs ANY)
     */
    protected function hasAnyPermission(array $permissions, array $context = []): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission, $context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Authorize permission or fail
     */
    protected function authorizePermission(string $permission, array $context = []): void
    {
        if (!$this->hasPermission($permission, $context)) {
            $this->handleUnauthorized($permission);
        }
    }

    /**
     * Authorize multiple permissions (ALL required) or fail
     */
    protected function authorizeAllPermissions(array $permissions, array $context = []): void
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission, $context)) {
                $this->handleUnauthorized($permission);
            }
        }
    }

    /**
     * Authorize multiple permissions (ANY required) or fail
     */
    protected function authorizeAnyPermission(array $permissions, array $context = []): void
    {
        if (!$this->hasAnyPermission($permissions, $context)) {
            $this->handleUnauthorized(implode(' or ', $permissions));
        }
    }

    /**
     * Check permission and return detailed result
     */
    protected function checkPermissionDetailed(string $permission, array $context = []): array
    {
        $user = Auth::user();
        if (!$user) {
            return [
                'granted' => false,
                'reason' => 'User not authenticated',
                'role' => null,
                'permission_model' => null,
            ];
        }

        return $this->getPermissionService()->checkUserPermission($user, $permission, $context);
    }

    /**
     * Get current user's permissions
     */
    protected function getUserPermissions(): array
    {
        $user = Auth::user();
        if (!$user) {
            return [];
        }

        return $this->getPermissionService()->getUserPermissions($user);
    }

    /**
     * Check if user has role
     */
    protected function hasRole(string $roleName): bool
    {
        $user = Auth::user();
        if (!$user || !method_exists($user, 'hasAdvancedRole')) {
            return false;
        }

        return $user->hasAdvancedRole($roleName);
    }

    /**
     * Check if user has any of the specified roles
     */
    protected function hasAnyRole(array $roleNames): bool
    {
        foreach ($roleNames as $roleName) {
            if ($this->hasRole($roleName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get current user's roles
     */
    protected function getUserRoles(): array
    {
        $user = Auth::user();
        if (!$user || !method_exists($user, 'advancedRoles')) {
            return [];
        }

        return $user->advancedRoles()->pluck('name')->toArray();
    }

    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(string $permission): void
    {
        if (request()->expectsJson()) {
            abort(403, "Access denied. Required permission: {$permission}");
        }

        abort(403, "You don't have permission to perform this action. Required: {$permission}");
    }

    /**
     * Return permission-aware JSON response
     */
    protected function permissionAwareJsonResponse(
        string $permission,
        callable $callback,
        array $context = []
    ): JsonResponse {
        if (!$this->hasPermission($permission, $context)) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => "Access denied. Required permission: {$permission}",
                'code' => 403,
            ], 403);
        }

        return $callback();
    }

    /**
     * Return permission-aware redirect response
     */
    protected function permissionAwareRedirect(
        string $permission,
        callable $callback,
        string $errorMessage = null,
        array $context = []
    ): RedirectResponse {
        if (!$this->hasPermission($permission, $context)) {
            $message = $errorMessage ?: "Access denied. Required permission: {$permission}";
            return back()->with('error', $message);
        }

        return $callback();
    }

    /**
     * Filter data based on permissions
     */
    protected function filterByPermissions(array $data, array $permissionMap): array
    {
        $filtered = [];

        foreach ($data as $key => $value) {
            $requiredPermission = $permissionMap[$key] ?? null;

            if (!$requiredPermission || $this->hasPermission($requiredPermission)) {
                $filtered[$key] = $value;
            }
        }

        return $filtered;
    }

    /**
     * Get permissions for a resource with CRUD actions
     */
    protected function getResourcePermissions(string $resource): array
    {
        return [
            'create' => "{$resource}.create",
            'read' => "{$resource}.read",
            'update' => "{$resource}.update",
            'delete' => "{$resource}.delete",
        ];
    }

    /**
     * Check CRUD permissions for a resource
     */
    protected function getResourcePermissionStatus(string $resource): array
    {
        $permissions = $this->getResourcePermissions($resource);
        $status = [];

        foreach ($permissions as $action => $permission) {
            $status[$action] = $this->hasPermission($permission);
        }

        return $status;
    }

    /**
     * Authorize CRUD action on resource
     */
    protected function authorizeCrudAction(string $resource, string $action): void
    {
        $permission = "{$resource}.{$action}";
        $this->authorizePermission($permission);
    }

    /**
     * Log custom permission event
     */
    protected function logPermissionEvent(
        string $eventType,
        string $action,
        string $reason = null,
        array $context = []
    ): void {
        $user = Auth::user();

        AdvancedPermissionAudit::create([
            'event_type' => $eventType,
            'action' => $action,
            'user_id' => $user?->id,
            'user_type' => $user ? get_class($user) : null,
            'user_identifier' => $user?->email ?? $user?->username,
            'was_granted' => true,
            'reason' => $reason,
            'context_data' => $context,
            'occurred_at' => now(),
        ]);
    }

    /**
     * Get permission constraints for display
     */
    protected function getPermissionConstraints(string $permission): array
    {
        $user = Auth::user();
        if (!$user) {
            return [];
        }

        $constraints = [];
        $activeRoles = $user->advancedUserRoles()->currentlyValid()->with('role.rolePermissions')->get();

        foreach ($activeRoles as $userRole) {
            $rolePermissions = $userRole->role->rolePermissions()
                ->whereHas('permission', function ($query) use ($permission) {
                    $query->where('name', $permission);
                })
                ->get();

            foreach ($rolePermissions as $rolePermission) {
                if ($rolePermission->constraints) {
                    $constraints[] = [
                        'role' => $userRole->role->display_name,
                        'constraints' => $rolePermission->constraints,
                        'description' => $rolePermission->getConstraintDescription(),
                    ];
                }
            }
        }

        return $constraints;
    }

    /**
     * Check if permission has constraints
     */
    protected function permissionHasConstraints(string $permission): bool
    {
        return !empty($this->getPermissionConstraints($permission));
    }

    /**
     * Validate permission context against constraints
     */
    protected function validatePermissionContext(string $permission, array $context): array
    {
        $user = Auth::user();
        if (!$user) {
            return ['valid' => false, 'errors' => ['User not authenticated']];
        }

        $result = $this->checkPermissionDetailed($permission, $context);

        if (!$result['granted']) {
            return ['valid' => false, 'errors' => [$result['reason']]];
        }

        return ['valid' => true, 'errors' => []];
    }

    /**
     * Get available actions for current user on a resource
     */
    protected function getAvailableActions(string $resource): array
    {
        $actions = ['create', 'read', 'update', 'delete'];
        $available = [];

        foreach ($actions as $action) {
            if ($this->hasPermission("{$resource}.{$action}")) {
                $available[] = $action;
            }
        }

        return $available;
    }

    /**
     * Check if user can perform any action on resource
     */
    protected function canAccessResource(string $resource): bool
    {
        return !empty($this->getAvailableActions($resource));
    }

    /**
     * Get permission-filtered menu items
     */
    protected function getPermissionFilteredMenu(array $menuItems): array
    {
        $filtered = [];

        foreach ($menuItems as $item) {
            if (isset($item['permission'])) {
                if ($this->hasPermission($item['permission'])) {
                    $filtered[] = $item;
                }
            } elseif (isset($item['permissions'])) {
                if ($this->hasAnyPermission($item['permissions'])) {
                    $filtered[] = $item;
                }
            } else {
                // No permission required
                $filtered[] = $item;
            }
        }

        return $filtered;
    }
}
