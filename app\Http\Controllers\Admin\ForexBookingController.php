<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Models\ForexRate;
use App\Models\User;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ForexBookingController extends Controller
{
    public function index()
    {
        $data['pageTitle'] = 'Forex Bookings';
        $data['stats'] = [
            'total_bookings' => ForexBooking::count(),
            'pending_bookings' => ForexBooking::pending()->count(),
            'completed_bookings' => ForexBooking::completed()->count(),
            'today_bookings' => ForexBooking::today()->count(),
        ];
        return view('admin.forex.bookings.index', $data);
    }

    public function search(Request $request)
    {
        $bookings = ForexBooking::query()
            ->with(['user', 'targetAccount', 'initiatedBy', 'completedBy'])
            ->when($request->status, function ($query) use ($request) {
                return $query->where('status', $request->status);
            })
            ->when($request->client_type, function ($query) use ($request) {
                return $query->where('client_type', $request->client_type);
            })
            ->when($request->transaction_type, function ($query) use ($request) {
                return $query->where('transaction_type', $request->transaction_type);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->latest();

        return DataTables::of($bookings)
            ->addColumn('checkbox', function ($item) {
                return '<input type="checkbox" class="form-check-input row-tic" name="check" value="' . $item->id . '">';
            })
            ->addColumn('booking_info', function ($item) {
                return '
                    <div class="d-flex flex-column">
                        <span class="fw-bold">' . $item->booking_reference . '</span>
                        <small class="text-muted">' . ucfirst($item->transaction_type) . ' - ' . $item->formatted_amount . '</small>
                    </div>
                ';
            })
            ->addColumn('client_info', function ($item) {
                $paymentBadge = $item->payment_method === 'wallet'
                    ? '<span class="badge bg-primary ms-1">Wallet</span>'
                    : '<span class="badge bg-secondary ms-1">Bank</span>';

                return '
                    <div class="d-flex flex-column">
                        <span class="fw-bold">' . $item->client_name . '</span>
                        <small class="text-muted">' . $item->client_email . '</small>
                        <div>
                            <span class="badge bg-info">' . ucfirst($item->client_type) . '</span>
                            ' . $paymentBadge . '
                        </div>
                    </div>
                ';
            })
            ->addColumn('amounts', function ($item) {
                $customerRateFormatted = number_format($item->customer_rate, 2);
                $customerTotalFormatted = number_format($item->customer_total, 2);
                $markupFormatted = number_format($item->markup_amount, 2);

                return '
                    <div class="d-flex flex-column">
                        <small class="fw-bold text-primary">Rate: ₦' . $customerRateFormatted . '/$1</small>
                        <small class="text-success">Customer Total: ₦' . $customerTotalFormatted . '</small>
                        <small class="text-info">Markup: ₦' . $markupFormatted . '</small>
                        <small class="text-muted">CBN: ' . $item->formatted_cbn_total . '</small>
                        <small class="text-muted">Difference: ' . $item->formatted_difference_amount . '</small>
                    </div>
                ';
            })
            ->addColumn('target_account', function ($item) {
                return $item->targetAccount->account_name;
            })
            ->addColumn('status', function ($item) {
                $class = $item->status_class;
                return '<span class="badge bg-' . $class . '">' . ucfirst($item->status) . '</span>';
            })
            ->addColumn('date', function ($item) {
                return $item->created_at->format('M d, Y H:i');
            })
            ->addColumn('action', function ($item) {
                $viewBtn = '<a href="' . route('admin.forex.bookings.show', $item->id) . '" class="btn btn-sm btn-info">
                    <i class="fas fa-eye"></i>
                </a>';

                $completeBtn = $item->status === 'pending' ?
                    '<button class="btn btn-sm btn-success complete-booking" data-id="' . $item->id . '">
                        <i class="fas fa-check"></i>
                    </button>' : '';

                $cancelBtn = $item->status === 'pending' ?
                    '<button class="btn btn-sm btn-danger cancel-booking" data-id="' . $item->id . '">
                        <i class="fas fa-times"></i>
                    </button>' : '';

                return '<div class="btn-group">' . $viewBtn . $completeBtn . $cancelBtn . '</div>';
            })
            ->rawColumns(['checkbox', 'booking_info', 'client_info', 'amounts', 'status', 'action'])
            ->make(true);
    }

    public function create(ForexWalletService $walletService)
    {
        $data['pageTitle'] = 'Create Forex Booking';
        $data['accounts'] = ForexAccount::active()->get();
        $data['activeRate'] = ForexRate::getActiveRate();
        $data['users'] = User::where('status', 1)->select('id', 'firstname', 'lastname', 'email')->get();
        $data['walletService'] = $walletService;
        return view('admin.forex.bookings.create', $data);
    }

    public function store(Request $request, ForexBookingService $bookingService, ForexWalletService $walletService)
    {
        $validator = Validator::make($request->all(), [
            'client_type' => 'required|in:user,merchant,external',
            'user_id' => 'required_if:client_type,user,merchant|nullable|exists:users,id',
            'client_name' => 'required|string|max:255',
            'client_email' => 'required|email|max:255',
            'client_phone' => 'nullable|string|max:20',
            'transaction_type' => 'required|in:buying,selling',
            'currency' => 'required|in:USD,NGN',
            'amount' => 'required|numeric|min:0.01',
            'target_account_id' => 'required|exists:forex_accounts,id',
            'account_details' => 'nullable|string',
            'payment_method' => 'nullable|in:account_details,wallet',
            'wallet_currency_id' => 'nullable|exists:currencies,id',
            'payment_instructions' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Validate booking data
        $validationErrors = $bookingService->validateBookingData($request->all());
        if (!empty($validationErrors)) {
            \Log::info('Booking validation errors found', ['errors' => $validationErrors]);
            // Convert array of errors to Laravel's error format
            $errorBag = [];
            foreach ($validationErrors as $index => $error) {
                $errorBag['validation_error_' . $index] = $error;
            }
            return back()->withErrors($errorBag)->withInput();
        }

        // Additional validation for wallet payment
        if ($request->payment_method === 'wallet' && $request->user_id) {
            if (!$walletService->validateWalletPayment(
                $request->user_id,
                $request->payment_method,
                $request->wallet_currency_id
            )) {
                return back()->withErrors(['payment_method' => 'Selected wallet is not available for this user.'])->withInput();
            }
        }

        try {
            $booking = $bookingService->createBooking(
                $request->only([
                    'user_id', 'client_name', 'client_email', 'client_phone',
                    'client_type', 'transaction_type', 'currency', 'amount',
                    'target_account_id', 'account_details', 'payment_method',
                    'wallet_currency_id', 'payment_instructions'
                ]),
                Auth::id()
            );

            return redirect()->route('admin.forex.bookings.show', $booking->id)
                ->with('success', 'Forex booking created successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to create booking: ' . $e->getMessage())->withInput();
        }
    }

    public function show($id)
    {
        $data['pageTitle'] = 'Booking Details';
        $data['booking'] = ForexBooking::with([
            'user', 'targetAccount', 'initiatedBy', 'completedBy',
            'transactions', 'emailLogs', 'reservations.account'
        ])->findOrFail($id);
        return view('admin.forex.bookings.show', $data);
    }

    public function complete(Request $request, $id, ForexBookingService $bookingService)
    {
        $booking = ForexBooking::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status_notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $bookingService->completeBooking($booking, Auth::id(), $request->status_notes);

            return response()->json([
                'success' => true,
                'message' => 'Booking completed successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to complete booking: ' . $e->getMessage()], 500);
        }
    }

    public function cancel(Request $request, $id, ForexBookingService $bookingService)
    {
        $booking = ForexBooking::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status_notes' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $bookingService->cancelBooking($booking, Auth::id(), $request->status_notes);

            return response()->json([
                'success' => true,
                'message' => 'Booking cancelled successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to cancel booking: ' . $e->getMessage()], 500);
        }
    }

    public function calculateRates(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $activeRate = ForexRate::getActiveRate();
        if (!$activeRate) {
            return response()->json(['error' => 'No active forex rate found.'], 400);
        }

        $amount = $request->amount;
        $cbnTotal = $activeRate->calculateCbnTotal($amount);
        $parallelTotal = $activeRate->calculateParallelTotal($amount);
        $difference = $activeRate->calculateDifference($amount);

        // Calculate customer rate with markup
        $customerRate = $activeRate->parallel_rate + ($activeRate->parallel_rate * $activeRate->markup_percentage / 100);
        $customerTotal = $amount * $customerRate;
        $markupAmount = $customerTotal - $parallelTotal;

        return response()->json([
            'cbn_rate' => $activeRate->cbn_rate,
            'parallel_rate' => $activeRate->parallel_rate,
            'markup_percentage' => $activeRate->markup_percentage,
            'customer_rate' => $customerRate,
            'cbn_total' => number_format($cbnTotal, 2),
            'parallel_total' => number_format($parallelTotal, 2),
            'customer_total' => number_format($customerTotal, 2),
            'markup_amount' => number_format($markupAmount, 2),
            'difference' => number_format($difference, 2),
        ]);
    }

    /**
     * Get wallet information for a user
     */
    public function getUserWalletInfo(Request $request, ForexWalletService $walletService)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'transaction_type' => 'required|in:buying,selling'
        ]);

        $paymentOptions = $walletService->getPaymentMethodOptions(
            $request->user_id,
            $request->transaction_type
        );

        return response()->json([
            'success' => true,
            'data' => $paymentOptions
        ]);
    }

    public function sendPaymentReminder(Request $request, $id, ForexBookingService $bookingService)
    {
        $booking = ForexBooking::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'reminder_message' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        if ($booking->status !== 'pending') {
            return response()->json(['error' => 'Can only send payment reminders for pending bookings'], 400);
        }

        try {
            $reminderMessage = $request->reminder_message ?? 'This is a payment reminder for your forex booking.';
            $bookingService->sendForexPaymentReminderEmail($booking, $reminderMessage);

            return response()->json([
                'success' => true,
                'message' => 'Payment reminder sent successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to send payment reminder: ' . $e->getMessage()], 500);
        }
    }
}
