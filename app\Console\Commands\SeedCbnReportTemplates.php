<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ForexReportService;
use App\Models\ForexReportTemplate;

class SeedCbnReportTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forex:seed-cbn-templates {--force : Force recreation of existing templates}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed CBN-focused report templates for forex reporting';

    /**
     * Execute the console command.
     */
    public function handle(ForexReportService $reportService)
    {
        $this->info('Seeding CBN-focused report templates...');

        if ($this->option('force')) {
            $this->warn('Force option enabled - existing templates will be updated');
        }

        try {
            // Create CBN-focused templates
            $reportService->createCbnFocusedTemplates();
            
            // Also create the default templates from the model
            ForexReportTemplate::createDefaultTemplates();

            $templateCount = ForexReportTemplate::count();
            
            $this->info("Successfully seeded CBN report templates!");
            $this->info("Total templates available: {$templateCount}");
            
            // Display created templates
            $this->table(
                ['ID', 'Name', 'Type', 'Public', 'Usage Count'],
                ForexReportTemplate::all()->map(function ($template) {
                    return [
                        $template->id,
                        $template->name,
                        $template->report_type,
                        $template->is_public ? 'Yes' : 'No',
                        $template->usage_count
                    ];
                })->toArray()
            );

        } catch (\Exception $e) {
            $this->error('Failed to seed CBN report templates: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
