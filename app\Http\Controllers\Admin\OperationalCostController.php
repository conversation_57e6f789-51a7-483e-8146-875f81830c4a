<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OperationalCost;
use App\Traits\Upload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

class OperationalCostController extends Controller
{
    use Upload;

    public function index()
    {
        $data['pageTitle'] = 'Operational Costs';
        $data['weeklySummary'] = OperationalCost::getWeeklySummary();
        $data['monthlySummary'] = OperationalCost::getMonthlySummary();
        $data['categories'] = OperationalCost::getAvailableCategories();
        return view('admin.forex.costs.index', $data);
    }

    public function search(Request $request)
    {
        $costs = OperationalCost::query()
            ->with('recordedBy')
            ->when($request->category, function ($query) use ($request) {
                return $query->where('category', $request->category);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('cost_date', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('cost_date', '<=', $request->date_to);
            })
            ->latest('cost_date');

        return DataTables::of($costs)
            ->addColumn('checkbox', function ($item) {
                return '<input type="checkbox" class="form-check-input row-tic" name="check" value="' . $item->id . '">';
            })
            ->addColumn('cost_info', function ($item) {
                return '
                    <div class="d-flex flex-column">
                        <span class="fw-bold">' . $item->cost_name . '</span>
                        <small class="text-muted">' . Str::limit($item->description, 50) . '</small>
                    </div>
                ';
            })
            ->addColumn('amount', function ($item) {
                return $item->formatted_amount;
            })
            ->addColumn('category', function ($item) {
                return $item->category ? '<span class="badge bg-secondary">' . $item->category . '</span>' : 'N/A';
            })
            ->addColumn('cost_date', function ($item) {
                return $item->formatted_cost_date;
            })
            ->addColumn('recorded_by', function ($item) {
                return $item->recordedBy->name ?? 'N/A';
            })
            ->addColumn('attachments', function ($item) {
                $count = $item->attachments ? count($item->attachments) : 0;
                return $count > 0 ? '<span class="badge bg-info">' . $count . ' files</span>' : 'None';
            })
            ->addColumn('action', function ($item) {
                $editBtn = '<a href="' . route('admin.forex.costs.edit', $item->id) . '" class="btn btn-sm btn-primary">
                    <i class="fas fa-edit"></i>
                </a>';

                $deleteBtn = '<button class="btn btn-sm btn-danger delete-cost" data-id="' . $item->id . '">
                    <i class="fas fa-trash"></i>
                </button>';

                return '<div class="btn-group">' . $editBtn . $deleteBtn . '</div>';
            })
            ->rawColumns(['checkbox', 'cost_info', 'category', 'attachments', 'action'])
            ->make(true);
    }

    public function create()
    {
        $data['pageTitle'] = 'Add Operational Cost';
        $data['categories'] = OperationalCost::getAvailableCategories();
        return view('admin.forex.costs.create', $data);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cost_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|in:NGN,USD',
            'category' => 'nullable|string|max:100',
            'cost_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string|max:500',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:2048',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $attachments = [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $upload = $this->fileUpload($file, config('location.operationalCost.path'), null, null, 'webp', 80);
                if ($upload['status']) {
                    $attachments[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'file_name' => $upload['file_name'],
                        'file_path' => $upload['path'],
                        'file_size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                    ];
                }
            }
        }

        OperationalCost::create([
            'cost_name' => $request->cost_name,
            'description' => $request->description,
            'amount' => $request->amount,
            'currency' => $request->currency,
            'category' => $request->category,
            'cost_date' => $request->cost_date,
            'recorded_by' => Auth::id(),
            'notes' => $request->notes,
            'attachments' => $attachments,
        ]);

        return redirect()->route('admin.forex.costs.index')
            ->with('success', 'Operational cost recorded successfully.');
    }

    public function edit($id)
    {
        $data['pageTitle'] = 'Edit Operational Cost';
        $data['cost'] = OperationalCost::findOrFail($id);
        $data['categories'] = OperationalCost::getAvailableCategories();
        return view('admin.forex.costs.edit', $data);
    }

    public function update(Request $request, $id)
    {
        $cost = OperationalCost::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'cost_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|in:NGN,USD',
            'category' => 'nullable|string|max:100',
            'cost_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string|max:500',
            'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:2048',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $attachments = $cost->attachments ?? [];
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $upload = $this->fileUpload($file, config('location.operationalCost.path'), null, null, 'webp', 80);
                if ($upload['status']) {
                    $attachments[] = [
                        'original_name' => $file->getClientOriginalName(),
                        'file_name' => $upload['file_name'],
                        'file_path' => $upload['path'],
                        'file_size' => $file->getSize(),
                        'mime_type' => $file->getMimeType(),
                    ];
                }
            }
        }

        $cost->update([
            'cost_name' => $request->cost_name,
            'description' => $request->description,
            'amount' => $request->amount,
            'currency' => $request->currency,
            'category' => $request->category,
            'cost_date' => $request->cost_date,
            'notes' => $request->notes,
            'attachments' => $attachments,
        ]);

        return redirect()->route('admin.forex.costs.index')
            ->with('success', 'Operational cost updated successfully.');
    }

    public function destroy($id)
    {
        $cost = OperationalCost::findOrFail($id);

        // Delete associated files
        if ($cost->attachments) {
            foreach ($cost->attachments as $attachment) {
                $this->fileDelete($attachment['file_path']);
            }
        }

        $cost->delete();

        return response()->json([
            'success' => true,
            'message' => 'Operational cost deleted successfully.'
        ]);
    }

    public function removeAttachment(Request $request, $id)
    {
        $cost = OperationalCost::findOrFail($id);
        $attachmentIndex = $request->attachment_index;

        $attachments = $cost->attachments ?? [];
        if (isset($attachments[$attachmentIndex])) {
            $this->fileDelete($attachments[$attachmentIndex]['file_path']);
            unset($attachments[$attachmentIndex]);
            $cost->update(['attachments' => array_values($attachments)]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Attachment removed successfully.'
        ]);
    }

    public function summary(Request $request)
    {
        $data['pageTitle'] = 'Cost Summary';

        $startDate = $request->start_date ?? now()->startOfMonth()->toDateString();
        $endDate = $request->end_date ?? now()->endOfMonth()->toDateString();

        $data['totalCosts'] = OperationalCost::getTotalCosts($startDate, $endDate);
        $data['costsByCategory'] = OperationalCost::getCostsByCategory($startDate, $endDate);
        $data['startDate'] = $startDate;
        $data['endDate'] = $endDate;

        return view('admin.forex.costs.summary', $data);
    }
}
