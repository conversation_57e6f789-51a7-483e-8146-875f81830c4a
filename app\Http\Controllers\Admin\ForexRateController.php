<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ForexRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ForexRateController extends Controller
{
    public function index()
    {
        $data['pageTitle'] = 'Forex Exchange Rates';
        $data['activeRate'] = ForexRate::getActiveRate();
        $data['totalRates'] = ForexRate::count();
        return view('admin.forex.rates.index', $data);
    }

    public function search(Request $request)
    {
        $rates = ForexRate::query()
            ->with(['createdBy', 'approvedBy'])
            ->when($request->status, function ($query) use ($request) {
                return $query->where('status', $request->status);
            })
            ->when($request->rate_type, function ($query) use ($request) {
                return $query->where('rate_type', $request->rate_type);
            })
            ->when($request->is_active !== null, function ($query) use ($request) {
                return $query->where('is_active', $request->is_active);
            })
            ->latest();

        // Debug: Log the count
        \Log::info('Forex rates count: ' . $rates->count());

        return DataTables::of($rates)
            ->addColumn('checkbox', function ($item) {
                return '<input type="checkbox" class="form-check-input row-tic" name="check" value="' . $item->id . '">';
            })
            ->addColumn('rate_info', function ($item) {
                return '
                    <div class="d-flex flex-column">
                        <span class="fw-bold">' . $item->rate_type . '</span>
                        <div class="mt-1">
                            <small class="text-muted"><strong>Buy Rates:</strong></small><br>
                            <small class="text-muted">CBN: ₦' . $item->formatted_cbn_rate . '</small><br>
                            <small class="text-muted">Parallel: ₦' . $item->formatted_parallel_rate . '</small>
                        </div>
                        <div class="mt-1">
                            <small class="text-muted"><strong>Sell Rates:</strong></small><br>
                            <small class="text-muted">CBN: ₦' . $item->formatted_cbn_sell_rate . '</small><br>
                            <small class="text-muted">Parallel: ₦' . $item->formatted_parallel_sell_rate . '</small>
                        </div>
                    </div>
                ';
            })
            ->addColumn('markup', function ($item) {
                return '
                    <div class="d-flex flex-column">
                        <small class="text-muted">Buy: ' . $item->markup_percentage . '%</small>
                        <small class="text-muted">Sell: ' . $item->sell_markup_percentage . '%</small>
                    </div>
                ';
            })
            ->addColumn('status', function ($item) {
                $class = $item->status_class;
                $active = $item->is_active ? '<span class="badge bg-success">Active</span>' : '';
                return '<span class="badge bg-' . $class . '">' . ucfirst($item->status) . '</span> ' . $active;
            })
            ->addColumn('created_info', function ($item) {
                return '
                    <div class="d-flex flex-column">
                        <span>' . ($item->createdBy->name ?? 'N/A') . '</span>
                        <small class="text-muted">' . $item->created_at->format('M d, Y H:i') . '</small>
                    </div>
                ';
            })
            ->addColumn('action', function ($item) {
                $editBtn = '<a href="' . route('admin.forex.rates.edit', $item->id) . '" class="btn btn-sm btn-primary">
                    <i class="fas fa-edit"></i>
                </a>';

                $approveBtn = $item->status === 'pending' ?
                    '<button class="btn btn-sm btn-success approve-rate" data-id="' . $item->id . '">
                        <i class="fas fa-check"></i>
                    </button>' : '';

                $rejectBtn = $item->status === 'pending' ?
                    '<button class="btn btn-sm btn-danger reject-rate" data-id="' . $item->id . '">
                        <i class="fas fa-times"></i>
                    </button>' : '';

                return '<div class="btn-group">' . $editBtn . $approveBtn . $rejectBtn . '</div>';
            })
            ->rawColumns(['checkbox', 'rate_info', 'markup', 'status', 'created_info', 'action'])
            ->make(true);
    }

    public function create()
    {
        $data['pageTitle'] = 'Create Forex Rate';
        return view('admin.forex.rates.create', $data);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rate_type' => 'required|string|max:255',
            'cbn_rate' => 'required|numeric|min:0',
            'parallel_rate' => 'required|numeric|min:0|gte:cbn_rate',
            'markup_percentage' => 'nullable|numeric|min:0|max:100',
            'cbn_sell_rate' => 'required|numeric|min:0',
            'parallel_sell_rate' => 'required|numeric|min:0|gte:cbn_sell_rate',
            'sell_markup_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check if user is super admin (ID 1) or has admin role
        $isSuperAdmin = Auth::id() == 1 || Auth::user()->hasRole('admin');

        $rate = ForexRate::create([
            'rate_type' => $request->rate_type,
            'cbn_rate' => $request->cbn_rate,
            'parallel_rate' => $request->parallel_rate,
            'markup_percentage' => $request->markup_percentage ?? 0,
            'cbn_sell_rate' => $request->cbn_sell_rate,
            'parallel_sell_rate' => $request->parallel_sell_rate,
            'sell_markup_percentage' => $request->sell_markup_percentage ?? 0,
            'is_active' => $request->has('is_active'),
            'status' => $isSuperAdmin ? 'approved' : 'pending',
            'created_by' => Auth::id(),
            'approved_by' => $isSuperAdmin ? Auth::id() : null,
            'approved_at' => $isSuperAdmin ? now() : null,
        ]);

        $message = $isSuperAdmin
            ? 'Forex rate created and approved successfully.'
            : 'Forex rate created successfully and is pending approval.';

        return redirect()->route('admin.forex.rates.index')
            ->with('success', $message);
    }

    public function edit($id)
    {
        $data['pageTitle'] = 'Edit Forex Rate';
        $data['rate'] = ForexRate::findOrFail($id);
        return view('admin.forex.rates.edit', $data);
    }

    public function update(Request $request, $id)
    {
        $rate = ForexRate::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'rate_type' => 'required|string|max:255',
            'cbn_rate' => 'required|numeric|min:0',
            'parallel_rate' => 'required|numeric|min:0|gte:cbn_rate',
            'markup_percentage' => 'nullable|numeric|min:0|max:100',
            'cbn_sell_rate' => 'required|numeric|min:0',
            'parallel_sell_rate' => 'required|numeric|min:0|gte:cbn_sell_rate',
            'sell_markup_percentage' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $rate->update([
            'rate_type' => $request->rate_type,
            'cbn_rate' => $request->cbn_rate,
            'parallel_rate' => $request->parallel_rate,
            'markup_percentage' => $request->markup_percentage ?? 0,
            'cbn_sell_rate' => $request->cbn_sell_rate,
            'parallel_sell_rate' => $request->parallel_sell_rate,
            'sell_markup_percentage' => $request->sell_markup_percentage ?? 0,
            'is_active' => $request->has('is_active'),
            'status' => 'pending', // Reset to pending when updated
        ]);

        return redirect()->route('admin.forex.rates.index')
            ->with('success', 'Forex rate updated successfully and is pending approval.');
    }

    public function approve(Request $request, $id)
    {
        $rate = ForexRate::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'approval_notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $rate->approve(Auth::id(), $request->approval_notes);

        return response()->json([
            'success' => true,
            'message' => 'Forex rate approved successfully.'
        ]);
    }

    public function reject(Request $request, $id)
    {
        $rate = ForexRate::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'approval_notes' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $rate->reject(Auth::id(), $request->approval_notes);

        return response()->json([
            'success' => true,
            'message' => 'Forex rate rejected successfully.'
        ]);
    }

    public function destroy($id)
    {
        $rate = ForexRate::findOrFail($id);

        if ($rate->is_active) {
            return back()->with('error', 'Cannot delete active forex rate.');
        }

        if ($rate->bookings()->exists()) {
            return back()->with('error', 'Cannot delete forex rate that has associated bookings.');
        }

        $rate->delete();

        return back()->with('success', 'Forex rate deleted successfully.');
    }
}
