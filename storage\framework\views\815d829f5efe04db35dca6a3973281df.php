<!-- Modal -->
<?php
    $currencies = \App\Models\Currency::where('is_active',1)->get();
?>

<div class="modal fade" id="addBalanceModal" tabindex="-1" role="dialog" aria-labelledby="addBalanceModalLabel"
     data-bs-backdrop="static"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="addBalanceModalLabel"><i
                        class="fa-light fa-square-check"></i> <?php echo app('translator')->get('Manage Balance'); ?></h3>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="" method="post" class="setBalanceRoute" id="form">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <ul class="list-group mb-4" id="showBalance">

                    </ul>

                    <div>
                        <!-- Select -->
                        <label class="form-label" for="exampleFormControlInput1"><?php echo app('translator')->get("Select Currency"); ?></label>
                        <div class="tom-select-custom">
                            <select class="js-select form-select" name="currency_id" autocomplete="off"
                                    data-hs-tom-select-options='{
                            "placeholder": "Select currency..."
                          }'>
                                <?php if(!empty($currencies)): ?>
                                    <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($currency->id); ?>"><?php echo e($currency->name); ?>

                                            - <?php echo e($currency->code); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <!-- End Select -->
                    </div>

                    <div class="mt-3">
                        <label class="form-label" for="exampleFormControlInput1"><?php echo app('translator')->get("Enter Amount"); ?></label>
                        <input type="text" class="form-control" name="amount" placeholder="<?php echo app('translator')->get("Enter Amount"); ?>"
                               value="<?php echo e(old("amount")); ?>"
                               autocomplete="off">
                        <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="invalid-feedback d-block"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="input-group input-group-sm-vertical mt-3">
                        <label class="form-control" for="editUserModalAccountTypeModalRadioEg1_2">
                            <span class="form-check">
                              <input type="radio" class="form-check-input" name="balance_operation"
                                     id="editUserModalAccountTypeModalRadioEg1_2" value="1" checked>
                              <span class="form-check-label"><i class="bi bi-plus"></i> <?php echo app('translator')->get("Add Balance"); ?></span>
                            </span>
                        </label>

                        <label class="form-control" for="editUserModalAccountTypeModalRadioEg1_1">
                        <span class="form-check">
                          <input type="radio" class="form-check-input" name="balance_operation"
                                 id="editUserModalAccountTypeModalRadioEg1_1" value="0">
                          <span class="form-check-label"><i class="bi bi-dash me"></i> <?php echo app('translator')->get("Subtract Balance"); ?></span>
                        </span>
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-white" data-bs-dismiss="modal"><?php echo app('translator')->get('Close'); ?></button>
                    <button type="submit" class="btn btn-primary" id="submitBtn"><?php echo app('translator')->get('Confirm'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- End Modal -->


<?php $__env->startPush('script'); ?>
    <script>
        $(document).ready(function () {
            $('input[name="balance_operation"]').on('click', function () {
                $('input[name="balance_operation"]').not(this).prop('checked', false);
            });
        });

        $('#submitBtn').click(function () {
            $(this).prop('disabled', true);
            $('#form').submit();
        });

    </script>

<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/admin/user_management/components/update_balance_modal.blade.php ENDPATH**/ ?>