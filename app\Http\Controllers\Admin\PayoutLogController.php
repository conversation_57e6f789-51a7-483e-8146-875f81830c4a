<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payout;
use App\Models\PayoutMethod;
use App\Models\Transaction;
use App\Traits\Notify;
use App\Traits\PayoutTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class PayoutLogController extends Controller
{
    use Notify,PayoutTrait;

    public function index()
    {
        $payoutRecord = \Cache::remember('payoutRecord', now()->addMinutes(10), function () {
            return collect(Payout::selectRaw('COUNT(id) AS totalWithdrawLog')
                ->selectRaw('COUNT(CASE WHEN status = 1 THEN id END) AS pendingWithdraw')
                ->selectRaw('(COUNT(CASE WHEN status = 1 THEN id END) / COUNT(id)) * 100 AS pendingWithdrawPercentage')
                ->selectRaw('COUNT(CASE WHEN status = 2 THEN id END) AS successWithdraw')
                ->selectRaw('(COUNT(CASE WHEN status = 2 THEN id END) / COUNT(id)) * 100 AS successWithdrawPercentage')
                ->selectRaw('COUNT(CASE WHEN status = 3 THEN id END) AS cancelWithdraw')
                ->selectRaw('(COUNT(CASE WHEN status = 3 THEN id END) / COUNT(id)) * 100 AS cancelWithdrawPercentage')
                ->selectRaw('COUNT(CASE WHEN MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) THEN id END) AS thisMonthWithdraw')
                ->selectRaw('(COUNT(CASE WHEN MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) THEN id END) / COUNT(id)) * 100 AS thisMonthWithdrawPercentage')
                ->getProfit(30)
                ->get()
                ->toArray())->collapse();
        });
        $data['type'] = validateUserType();

        $data['methods'] = PayoutMethod::query()->where('is_active', 1)
            ->orderBy('id', 'asc')
            ->get();

        return view('admin.payout.logs', $data, compact('payoutRecord'));
    }

    public function search(Request $request, $type=null)
    {
        $type = validateUserType($type);
        $basicControl = basicControl();
        $search = $request->search['value']?? null;
        $filterTransactionId = $request->filter_trx_id;
        $filterMethod = $request->filter_method;
        $filterStatus = $request->filter_status;

        $filterDate = explode('-', $request->filter_date);
        $startDate = $filterDate[0];
        $endDate = isset($filterDate[1]) ? trim($filterDate[1]) : null;

        $payout = Payout::query()
            ->with(['user:id,type,username,firstname,lastname,image,image_driver', 'method:id,name,logo,driver,is_automatic,code'])
            ->whereHas('user', function ($q) use ($type) {
                filterUsersByAllowedType($q, $type);
            })
            ->whereHas('method')
            ->orderBy('id', 'desc')
            ->where('status', '!=', 0)
            ->orderBy('id', 'desc')
            ->when(!empty($search), function ($query) use ($search) {
                return $query->where('trx_id', 'LIKE', $search)
                    ->orWhereHas('user', function ($q) use ($search) {
                        $q->where('email', 'LIKE', "%$search%")
                            ->orWhere('username', 'LIKE', "%$search%");
                    });
            })
            ->when(!empty($filterTransactionId), function ($query) use ($filterTransactionId) {
                return $query->where('trx_id', $filterTransactionId);
            })
            ->when(isset($filterStatus), function ($query) use ($filterStatus) {
                if ($filterStatus == "all") {
                    return $query->where('status', '!=', null);
                }
                return $query->where('status', $filterStatus);
            })
            ->when(isset($filterMethod), function ($query) use ($filterMethod) {
                return $query->whereHas('method', function ($subQuery) use ($filterMethod) {
                    if ($filterMethod == "all") {
                        $subQuery->where('id', '!=', null);
                    } else {
                        $subQuery->where('id', $filterMethod);
                    }
                });
            })
            ->when(!empty($request->filter_date) && $endDate == null, function ($query) use ($startDate) {
                $startDate = Carbon::createFromFormat('d/m/Y', trim($startDate));
                $query->whereDate('created_at', $startDate);
            })
            ->when(!empty($request->filter_date) && $endDate != null, function ($query) use ($startDate, $endDate) {
                $startDate = Carbon::createFromFormat('d/m/Y', trim($startDate));
                $endDate = Carbon::createFromFormat('d/m/Y', trim($endDate));
                $query->whereBetween('created_at', [$startDate, $endDate]);
            });

        return DataTables::of($payout)
            ->addColumn('no', function ($item) {
                static $counter = 0;
                $counter++;
                return $counter;
            })
            ->addColumn('name', function ($item) {
                $url = route("admin.user.edit", optional($item->user)->id);
                $type = renderUserTypeBadge($item->user->type);
                return '<a class="d-flex align-items-center me-2" href="' . $url . '">
                            <div class="flex-shrink-0"> ' . optional($item->user)->profilePicture() . ' </div>
                            <div class="flex-grow-1 ms-3">
                              <h5 class="text-hover-primary mb-0">' . optional($item->user)->name . '</h5>
                              <span class="fs-6 text-body">@' . optional($item->user)->username .$type. '</span>
                            </div>
                        </a>';
            })
            ->addColumn('trx', function ($item) {
                return $item->trx_id;
            })
            ->addColumn('method', function ($item) {
                return '<a class="d-flex align-items-center me-2 cursor-unset" href="javascript:void(0)">
                            <div class="flex-shrink-0"> ' . $item->picture() . ' </div>
                            <div class="flex-grow-1 ms-3">
                              <h5 class="text-hover-primary mb-0">' . optional($item->method)->name . '</h5>
                            </div>
                        </a>';
            })
            ->addColumn('amount', function ($item) {
                $statusClass = $item->getStatusClass();
                return "<h6 class='mb-0 $statusClass '>" . fractionNumber(getAmount($item->amount)). ' ' . $item->payout_currency_code . "</h6>";

            })
            ->addColumn('charge', function ($item) {
                return "<span class='text-danger'>".getAmount($item->charge) . ' ' . $item->payout_currency_code."</span>";
            })
            ->addColumn('net amount', function ($item) {
                return "<h6>".currencyPosition(getAmount($item->net_amount_in_base_currency))."</h6>";
            })
            ->addColumn('status', function ($item) {
                if ($item->status == 0) {
                    return '<span class="badge bg-soft-warning text-warning">' . trans('Pending') . '</span>';
                } else if ($item->status == 1) {
                    return '<span class="badge bg-soft-warning text-warning">' . trans('Pending') . '</span>';
                } else if ($item->status == 2) {
                    return '<span class="badge bg-soft-success text-success">' . trans('Successful') . '</span>';
                } else if ($item->status == 3) {
                    return '<span class="badge bg-soft-danger text-danger">' . trans('Canceled') . '</span>';
                } else if ($item->status == 6) {
                    return '<span class="badge bg-soft-danger text-danger">' . trans('Failed') . '</span>';
                }
            })
            ->addColumn('date', function ($item) {
                return dateTime($item->created_at, 'd M Y h:i A');
            })
            ->addColumn('action', function ($item) use ($basicControl) {
                $details = null;
                if ($item->information) {
                    $details = [];
                    foreach ($item->information as $k => $v) {
                        if ($v->type == "file") {
                            $details[kebab2Title($k)] = [
                                'type' => $v->type,
                                'field_name' => @$v->field_name,
                                'field_value' => getFile(config('filesystems.default'), @$v->field_value ?? @$v->field_name),
                            ];
                        } else {
                            $details[kebab2Title($k)] = [
                                'type' => @$v->type,
                                'field_name' => @$v->field_name,
                                'field_value' => @$v->field_value ?? @$v->field_name
                            ];
                        }
                    }
                }

                $icon = $item->status == 1 ? 'pencil-square' : 'eye';

                $statusColor = '';
                $statusText = '';
                if ($item->status == 0) {
                    $statusColor = 'badge bg-soft-warning text-warning';
                    $statusText = 'Pending';
                } else if ($item->status == 1) {
                    $statusColor = 'badge bg-soft-warning text-warning';
                    $statusText = 'Pending';
                } else if ($item->status == 2) {
                    $statusColor = 'badge bg-soft-success text-success';
                    $statusText = 'Success';
                } else if ($item->status == 3) {
                    $statusColor = 'badge bg-soft-danger text-danger';
                    $statusText = 'Cancelled';
                } else if ($item->status == 6) {
                    $statusColor = 'badge bg-soft-danger text-danger';
                    $statusText = 'Failed';
                }

                return "<button type='button' class='btn btn-white btn-sm edit_btn' data-bs-target='#accountInvoiceReceiptModal'
                data-id='$item->id'
                data-info='" . json_encode($details) . "'
                data-userid='" . optional($item->user)->id . "'
                data-sendername='" . optional($item->user)->name . "'
                data-transactionid='$item->trx_id'
                data-feedback='$item->feedback'
                data-lasterror='$item->last_error'
                data-responseid='$item->response_id'
                data-isautomatic='" . (optional($item->method)->is_automatic ? 'true' : 'false') . "'
                data-amount=' " . currencyPosition(getAmount($item->amount_in_base_currency)) . "'
                data-charge=' " . currencyPosition(getAmount($item->charge_in_base_currency)) . "'
                data-method='" . optional($item->method)->name . "'
                data-gatewayimage='" . getFile(optional($item->method)->driver, optional($item->method)->logo) . "'
                data-datepaid='" . dateTime($item->created_at) . "'
                data-status='$item->status'
                data-status_color='$statusColor'
                data-status_text='$statusText'
                data-username='" . optional($item->user)->username . "'
                data-action='" . route('admin.payout.action', $item->id) . "'
                data-bs-toggle='modal'
                data-bs-target='#accountInvoiceReceiptModal'>  <i class='bi-$icon me-1'></i> </button>";
            })
            ->rawColumns(['name', 'amount', 'charge', 'method', 'net amount', 'status', 'action'])
            ->make(true);

    }


    public function action(Request $request, $id)
    {
        $this->validate($request, [
            'id' => 'required|exists:payouts,id',
            'status' => ['required', Rule::in(['2', '3'])],
        ]);

        $payout = Payout::with('user', 'method')
            ->where('status',1)
            ->find($request->id);
        if (!$payout) {
            return back()->with('error','Invalid payout record.');
        }

        $payout->feedback = $request->feedback;
        $remark = 'Withdraw via ' . $payout->method?->name;

        if ($request->status == 3) {
            try {
                $this->cancelPayout($payout->id, $request->feedback);
                return back()->with('success', 'Payout Rejected');
            } catch(\Exception $e) {
                return back()->with('error', $e->getMessage());
            }
        }

        if (optional($payout->method)->is_automatic == 1) { //Automatic
            // Check if API call was already made (response_id exists)
            if (!empty($payout->response_id)) {
                // API call was already made, just update status to approved
                $payout->status = 2;
                $payout->save();
                return back()->with('success', 'Payout Confirmed - Transaction already processed');
            }

            // Check if there was a previous API error
            if (!empty($payout->last_error)) {
                // Previous API call failed, don't make another call
                return back()->with('error', 'Previous API call failed: ' . $payout->last_error . '. Use "Check Status" to verify transaction status.');
            }

            // No previous API call made, proceed with API call
            $methodClass = 'App\\Services\\Payout\\' . $payout->method->code . '\\Card';
            $data = $methodClass::payouts($payout);
            if (!$data) {
                return back()->with('error', 'Method not available or unknown error');
            }
            if ($data['status'] == 'error') {
                $apiError = $data['data'] ?? 'Unknown error';
                $payout->last_error = $apiError;
                $payout->status = 3;
                $payout->save();
                return back()->with('error', $apiError);
            }

            // No need to create transaction record here as it was already created when payout was requested
            $payout->response_id = $data['response_id'] ?? null;
            $payout->status = 2;
            $payout->save();
        }
        else { // Manual
            $payout->status = 2;
            $payout->save();

            // No need to create transaction record here as it was already created when payout was requested
            $this->userApprovedNotify($payout);
        }

        return back()->with('success', 'Payout Confirmed');
    }


    public function userApprovedNotify($payout): void
    {
        $msg = [
            'username' => optional($payout->user)->username,
            'amount' => getAmount($payout->amount),
            'currency' => $payout->payout_currency_code,
            'gateway' => optional($payout->method)->name,
            'transaction' => $payout->trx_id,
        ];
        $action = [
            "link" => '#',
            "icon" => "bi-cash"
        ];
        $fireBaseAction = "#";
        $this->userPushNotification($payout->user, 'PAYOUT_APPROVED', $msg, $action);
        $this->userFirebasePushNotification($payout->user,'PAYOUT_APPROVED', $msg, $fireBaseAction);
        $this->sendMailSms($payout->user, 'PAYOUT_APPROVED', [
            'gateway_name' => optional($payout->method)->name,
            'amount' => currencyPosition($payout->amount),
            'charge' => currencyPosition($payout->charge),
            'transaction' => $payout->trx_id,
            'feedback' => $payout->feedback,
        ]);
    }


    public function payoutIpn($code, Request $request)
    {
        //$apiResponse = json_decode($request->all());
        $apiResponse = $request->all();
        \Log::info('EEEEEEEEEEEEEEEEEEEEE', ['res' => $apiResponse]);
        $methodClass = 'App\\Services\\Payout\\' . $code . '\\Card';
        $data = $methodClass::webhook($apiResponse);
        return true;
    }

    public function checkStatus(Request $request, $id)
    {
        $this->validate($request, [
            'id' => 'required|exists:payouts,id',
        ]);

        $payout = Payout::with('user', 'method')
            ->where('status', 1)
            ->find($request->id);

        if (!$payout) {
            return back()->with('error', 'Invalid payout record or payout is not pending.');
        }

        // Check if response_id exists
        if (empty($payout->response_id)) {
            return back()->with('error', 'No transaction reference found. Cannot check status.');
        }

        // Check if method supports status checking
        if (optional($payout->method)->is_automatic != 1) {
            return back()->with('error', 'Status check not available for manual payout methods.');
        }

        try {
            $methodClass = 'App\\Services\\Payout\\' . $payout->method->code . '\\Card';

            // Check if the method has checkTransferStatus method
            if (!method_exists($methodClass, 'checkTransferStatus')) {
                return back()->with('error', 'Status check not supported for this payout method.');
            }

            $statusResponse = $methodClass::checkTransferStatus($payout->response_id, $payout->method);

            //dd($statusResponse);

            if (!$statusResponse || $statusResponse['status'] == 'error') {
                $error = $statusResponse['data'] ?? 'Failed to check transaction status';
                return back()->with('error', 'Status check failed: ' . $error);
            }

            // Process the status response
            $this->processStatusResponse($payout, $statusResponse);

            return back()->with('success', 'Status checked and payout updated accordingly.');

        } catch (\Exception $e) {
            return back()->with('error', 'Error checking status: ' . $e->getMessage());
        }
    }

    private function processStatusResponse($payout, $statusResponse)
    {
        $data = $statusResponse['data'] ?? null;

        if (!$data) {
            return;
        }

        // Handle different status responses based on the payout method
        $status = strtolower($data['status'] ?? $data['Status'] ?? '');

        switch ($status) {
            case 'successful':
            case 'completed':
            case 'success':
                if ($payout->status != 2) {
                    $payout->status = 2; // Mark as completed
                    $payout->save();

                    // Send notification to user
                    $this->userApprovedNotify($payout);
                }
                break;

            case 'failed':
            case 'declined':
            case 'rejected':
                $payout->status = 6; // Failed
                $payout->last_error = $data['message'] ?? $data['requestStateDetails'] ?? 'Transfer failed';
                $payout->save();

                // Refund user wallet including charge
                updateWallet($payout->user_id, $payout->currency_id, $payout->net_amount, 1);

                // Create transaction record for the refund
                $this->createPayoutTransaction($payout, '+', 'Payout failed - refund');
                break;

            case 'pending':
            case 'processing':
                // Keep status as pending
                $payout->status = 1;
                $payout->save();
                break;

            default:
                // Unknown status, keep as pending
                break;
        }
    }

    private function createPayoutTransaction($payout, $type, $remarks)
    {
        // This method should match the existing transaction creation logic
        // You may need to import the necessary traits or copy the logic from PayoutTrait
        try {
            $transaction = new Transaction();
            $transaction->user_id = $payout->user_id;
            $transaction->currency_id = $payout->currency_id;
            $transaction->amount = $payout->net_amount;
            $transaction->charge = 0;
            $transaction->trx_type = $type;
            $transaction->trx_id = strRandom();
            $transaction->remarks = $remarks;
            $transaction->transactional_type = get_class($payout);
            $transaction->transactional_id = $payout->id;
            $transaction->save();
        } catch (\Exception $e) {
            \Log::error('Failed to create payout transaction: ' . $e->getMessage());
        }
    }

}
