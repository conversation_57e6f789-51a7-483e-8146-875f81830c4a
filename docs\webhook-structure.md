# Webhook Structure Documentation

## Overview

This document describes the webhook payload structure that will be sent to user/merchant configured webhook URLs when transaction events occur.

## Webhook Events

### TRANSFER_NOTIFICATION
Sent when a payout transaction status changes (success, failed, pending, etc.)

### FUNDING_NOTIFICATION  
Sent when a deposit/funding transaction occurs

### TEST_NOTIFICATION
Sent when testing webhook configuration

## Webhook Payload Structure

### Standard Payload Format

```json
{
  "event": "TRANSFER_NOTIFICATION",
  "timestamp": "2025-07-10T12:00:00.000Z",
  "data": {
    "transaction_id": "TXN123456789",
    "reference": "NUM_REF_123456",
    "amount": 100.00,
    "currency": "NGN",
    "status": "success",
    "method": "Numero",
    "created_at": "2025-07-10T11:00:00.000Z",
    "updated_at": "2025-07-10T12:00:00.000Z",
    "user_id": 123,
    "charge": 5.00,
    "net_amount": 105.00,
    "provider": "numero",
    "provider_data": {
      // Original provider webhook data
    }
  }
}
```

### Field Descriptions

| Field | Type | Description |
|-------|------|-------------|
| `event` | string | Event type (TRANSFER_NOTIFICATION, FUNDING_NOTIFICATION, TEST_NOTIFICATION) |
| `timestamp` | string | ISO 8601 timestamp when webhook was sent |
| `data.transaction_id` | string | Internal transaction ID |
| `data.reference` | string | Provider reference ID |
| `data.amount` | number | Transaction amount |
| `data.currency` | string | Currency code (NGN, USD, etc.) |
| `data.status` | string | Transaction status (pending, processing, success, cancelled, failed) |
| `data.method` | string | Payment method name |
| `data.created_at` | string | Transaction creation timestamp |
| `data.updated_at` | string | Transaction last update timestamp |
| `data.user_id` | number | User/merchant ID |
| `data.charge` | number | Processing fee charged |
| `data.net_amount` | number | Total amount debited (amount + charge) |
| `data.provider` | string | Provider name (numero, paystack, etc.) |
| `data.provider_data` | object | Original provider webhook data |

### Status Values

- `pending` - Transaction is pending
- `processing` - Transaction is being processed
- `success` - Transaction completed successfully
- `cancelled` - Transaction was cancelled
- `failed` - Transaction failed

## Webhook Headers

The following headers are sent with each webhook:

```
Content-Type: application/json
User-Agent: [App Name] Webhook
X-Webhook-Source: [App Name]
```

## Response Requirements

Your webhook endpoint should:

1. Return HTTP status 200-299 for successful processing
2. Respond within 30 seconds
3. Handle duplicate notifications gracefully (use transaction_id for deduplication)

## Security Considerations

1. Validate the webhook source using the `X-Webhook-Source` header
2. Implement proper authentication/authorization for your webhook endpoint
3. Use HTTPS for your webhook URL
4. Implement idempotency using the `transaction_id` field

## Example Webhook Implementations

### PHP Example

```php
<?php
// webhook.php
$payload = json_decode(file_get_contents('php://input'), true);

if ($payload['event'] === 'TRANSFER_NOTIFICATION') {
    $transactionId = $payload['data']['transaction_id'];
    $status = $payload['data']['status'];
    
    // Process the webhook
    // Update your local records
    
    http_response_code(200);
    echo json_encode(['status' => 'success']);
}
?>
```

### Node.js Example

```javascript
const express = require('express');
const app = express();

app.use(express.json());

app.post('/webhook', (req, res) => {
    const { event, data } = req.body;
    
    if (event === 'TRANSFER_NOTIFICATION') {
        const { transaction_id, status } = data;
        
        // Process the webhook
        // Update your local records
        
        res.status(200).json({ status: 'success' });
    }
});

app.listen(3000);
```

## Testing Webhooks

Use the test webhook endpoint to verify your webhook configuration:

```bash
POST /api/webhook/test
{
    "webhook_url": "https://your-domain.com/webhook"
}
```

This will send a TEST_NOTIFICATION to verify your endpoint is working correctly.
