<?php

/**
 * Simple test script for API endpoints
 * Run this with: php tests/test-api-endpoints.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

class ApiTester
{
    private $baseUrl;
    private $token;

    public function __construct($baseUrl = 'http://localhost', $token = null)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->token = $token;
    }

    public function setToken($token)
    {
        $this->token = $token;
    }

    private function makeRequest($method, $endpoint, $data = [])
    {
        $url = $this->baseUrl . '/api' . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => $method !== 'GET' ? json_encode($data) : null,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL Error: $error");
        }

        return [
            'status_code' => $httpCode,
            'body' => json_decode($response, true),
            'raw_body' => $response
        ];
    }

    public function testTransactionStatus($reference)
    {
        echo "Testing transaction status endpoint...\n";
        
        try {
            $response = $this->makeRequest('POST', '/payout/status', [
                'reference' => $reference
            ]);

            echo "Status Code: {$response['status_code']}\n";
            echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

            return $response['status_code'] === 200;
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n\n";
            return false;
        }
    }

    public function testWebhookConfig()
    {
        echo "Testing webhook config endpoint...\n";
        
        try {
            $response = $this->makeRequest('GET', '/webhook/config');

            echo "Status Code: {$response['status_code']}\n";
            echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

            return $response['status_code'] === 200;
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n\n";
            return false;
        }
    }

    public function testWebhookTest($webhookUrl)
    {
        echo "Testing webhook test endpoint...\n";
        
        try {
            $response = $this->makeRequest('POST', '/webhook/test', [
                'webhook_url' => $webhookUrl
            ]);

            echo "Status Code: {$response['status_code']}\n";
            echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

            return $response['status_code'] === 200;
        } catch (Exception $e) {
            echo "Error: " . $e->getMessage() . "\n\n";
            return false;
        }
    }
}

// Example usage
echo "=== API Endpoint Testing ===\n\n";

$tester = new ApiTester('http://localhost'); // Adjust URL as needed

// Test without authentication (should fail for protected endpoints)
echo "1. Testing without authentication:\n";
$tester->testTransactionStatus('TEST123');

echo "2. Testing webhook config without auth:\n";
$tester->testWebhookConfig();

echo "3. Testing webhook test without auth:\n";
$tester->testWebhookTest('https://webhook.site/test');

echo "\n=== Tests completed ===\n";
echo "Note: To test with authentication, set a valid Bearer token using \$tester->setToken('your-token');\n";
