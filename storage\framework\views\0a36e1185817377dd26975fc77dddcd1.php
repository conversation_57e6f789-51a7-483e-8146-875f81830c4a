


<span class="dropdown-header d-flex justify-content-between mt-4">
    <?php echo app('translator')->get("Merchant Panel"); ?>
    
</span>


<small class="bi-three-dots nav-subtitle-replacer"></small>
<div class="nav-item">
    <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.merchants'], 3)); ?>"
       href="#navbarVerticalMerchantPanelMenu"
       role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalMerchantPanelMenu"
       aria-expanded="false" aria-controls="navbarVerticalMerchantPanelMenu">
        <i class="bi-people nav-icon"></i>
        <span class="nav-link-title"><?php echo app('translator')->get('Merchant Management'); ?></span>
    </a>
    <div id="navbarVerticalMerchantPanelMenu" class="nav-collapse collapse
                             <?php echo e(menuActive(['admin.merchants'], 2)); ?>"
         data-bs-parent="#navbarVerticalMerchantPanelMenu">

        <a class="nav-link <?php echo e(request()->is('admin/merchants') ? 'active' : ''); ?>" href="<?php echo e(route('admin.merchants')); ?>">
            <span><?php echo app('translator')->get("All Merchant"); ?></span>
            <small class="d-none"><?php echo app('translator')->get("Merchant Management > All Merchant"); ?></small>
        </a>

        <a href="<?php echo e(route('admin.merchants','active-merchants')); ?>" class="nav-link d-flex justify-content-between
                                <?php echo e(request()->is('admin/merchants/active-merchants') ? 'active' : ''); ?>">
            <span><?php echo app('translator')->get("Active Merchants"); ?></span>
            <small class="d-none"><?php echo app('translator')->get("Merchant Management > Active Merchants"); ?></small>

            <?php if($merchantCounts->active > 0): ?>
                <span class="badge bg-primary rounded-pill "><?php echo e($merchantCounts->active); ?></span>
            <?php endif; ?>
        </a>
        <a href="<?php echo e(route('admin.merchants','blocked-mercants')); ?>" class="nav-link d-flex justify-content-between
                                <?php echo e(request()->is('admin/merchants/blocked-merchants') ? 'active' : ''); ?>">
            <span><?php echo app('translator')->get("Blocked Mercants"); ?></span>
            <small class="d-none"><?php echo app('translator')->get("Merchant Management > Blocked Mercants"); ?></small>
            <?php if($merchantCounts->blocked > 0): ?>
                <span class="badge bg-primary rounded-pill "><?php echo e($merchantCounts->blocked); ?></span>
            <?php endif; ?>
        </a>
        <a href="<?php echo e(route('admin.merchants','email-unverified')); ?>" class="nav-link d-flex justify-content-between
                               <?php echo e(request()->is('admin/merchants/email-unverified') ? 'active' : ''); ?>">
            <span><?php echo app('translator')->get("Email Unverified"); ?></span>
            <small class="d-none"><?php echo app('translator')->get("Merchant Management > Email Unverified"); ?></small>
            <?php if($merchantCounts->email_unverified > 0): ?>
                <span class="badge bg-primary rounded-pill "><?php echo e($merchantCounts->email_unverified); ?></span>
            <?php endif; ?>
        </a>
        <a href="<?php echo e(route('admin.merchants','sms-unverified')); ?>" class="nav-link d-flex justify-content-between
                               <?php echo e(request()->is('admin/merchants/sms-unverified') ? 'active' : ''); ?>">
            <span><?php echo app('translator')->get("Sms Unverified"); ?></span>
            <small class="d-none"><?php echo app('translator')->get("Merchant Management > Sms Unverified"); ?></small>
            <?php if($merchantCounts->sms_unverified > 0): ?>
                <span class="badge bg-primary rounded-pill "><?php echo e($merchantCounts->sms_unverified); ?></span>
            <?php endif; ?>
        </a>

    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\currency\Modules/Merchant\resources/views/partials/admin_extra_menu_sidebar.blade.php ENDPATH**/ ?>