<?php

/**
 * API Key Authentication Example
 * 
 * This example demonstrates how to use the new /api/authenticate endpoint
 * with public<PERSON><PERSON> and secret<PERSON><PERSON> instead of username and password.
 * 
 * Prerequisites:
 * 1. Have a user account with generated API keys
 * 2. Replace the placeholder keys below with your actual keys
 * 3. Update the base URL to match your application URL
 */

// Configuration
$baseUrl = 'http://localhost'; // Change this to your application URL
$publicKey = 'your_public_key_here'; // Replace with your actual public key
$secretKey = 'your_secret_key_here'; // Replace with your actual secret key

/**
 * Step 1: Authenticate using API keys
 */
function authenticateWithApiKeys($baseUrl, $publicKey, $secretKey)
{
    $url = $baseUrl . '/api/authenticate';
    
    $data = [
        'publicKey' => $publicKey,
        'secretKey' => $secretKey
    ];
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "Authentication Request:\n";
    echo "URL: $url\n";
    echo "HTTP Status: $httpCode\n";
    echo "Response: $response\n\n";
    
    $result = json_decode($response, true);
    
    if ($result && $result['status'] === 'success') {
        echo "✅ Authentication successful!\n";
        echo "Token: " . substr($result['token'], 0, 20) . "...\n";
        echo "User: " . $result['user']['username'] . " (" . $result['user']['type'] . ")\n\n";
        return $result['token'];
    } else {
        echo "❌ Authentication failed!\n";
        echo "Error: " . ($result['message'] ?? 'Unknown error') . "\n\n";
        return null;
    }
}

/**
 * Step 2: Use the token to make an authenticated API call
 */
function makeAuthenticatedApiCall($baseUrl, $token)
{
    $url = $baseUrl . '/api/validate-account';
    
    $data = [
        'accountNumber' => '**********',
        'bankCode' => '123'
    ];
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "API Call Request:\n";
    echo "URL: $url\n";
    echo "HTTP Status: $httpCode\n";
    echo "Response: $response\n\n";
    
    $result = json_decode($response, true);
    
    if ($result && $result['status'] === 'success') {
        echo "✅ API call successful!\n";
        return true;
    } else {
        echo "❌ API call failed!\n";
        echo "Error: " . ($result['message'] ?? 'Unknown error') . "\n\n";
        return false;
    }
}

/**
 * Step 3: Example of getting user dashboard data
 */
function getDashboardData($baseUrl, $token)
{
    $url = $baseUrl . '/api/dashboard';
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    echo "Dashboard Request:\n";
    echo "URL: $url\n";
    echo "HTTP Status: $httpCode\n";
    echo "Response: " . substr($response, 0, 200) . "...\n\n";
    
    return json_decode($response, true);
}

// Main execution
echo "=== API Key Authentication Example ===\n\n";

// Check if keys are provided
if ($publicKey === 'your_public_key_here' || $secretKey === 'your_secret_key_here') {
    echo "❌ Please update the \$publicKey and \$secretKey variables with your actual API keys.\n";
    echo "\nTo get your API keys:\n";
    echo "1. Login to your account\n";
    echo "2. Go to Settings → API Keys\n";
    echo "3. Generate new API keys if you don't have them\n";
    echo "4. Copy the keys and update this script\n\n";
    exit;
}

// Step 1: Authenticate
$token = authenticateWithApiKeys($baseUrl, $publicKey, $secretKey);

if ($token) {
    // Step 2: Make an authenticated API call
    echo "Making authenticated API call...\n";
    makeAuthenticatedApiCall($baseUrl, $token);
    
    // Step 3: Get dashboard data
    echo "Getting dashboard data...\n";
    getDashboardData($baseUrl, $token);
} else {
    echo "Cannot proceed without valid authentication token.\n";
}

echo "=== Example completed ===\n";

/**
 * Alternative: Class-based approach for better organization
 */
class ApiKeyAuthClient
{
    private $baseUrl;
    private $publicKey;
    private $secretKey;
    private $token;
    
    public function __construct($baseUrl, $publicKey, $secretKey)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->publicKey = $publicKey;
        $this->secretKey = $secretKey;
    }
    
    public function authenticate()
    {
        $response = $this->makeRequest('POST', '/api/authenticate', [
            'publicKey' => $this->publicKey,
            'secretKey' => $this->secretKey
        ]);
        
        if ($response && $response['status'] === 'success') {
            $this->token = $response['token'];
            return true;
        }
        
        return false;
    }
    
    public function validateAccount($accountNumber, $bankCode)
    {
        if (!$this->token) {
            throw new Exception('Not authenticated. Call authenticate() first.');
        }
        
        return $this->makeRequest('POST', '/api/validate-account', [
            'accountNumber' => $accountNumber,
            'bankCode' => $bankCode
        ], ['Authorization: Bearer ' . $this->token]);
    }
    
    private function makeRequest($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => array_merge($defaultHeaders, $headers)
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        curl_close($curl);
        
        return json_decode($response, true);
    }
}

// Example usage of the class:
/*
$client = new ApiKeyAuthClient('http://localhost', 'your_public_key', 'your_secret_key');

if ($client->authenticate()) {
    $result = $client->validateAccount('**********', '123');
    print_r($result);
}
*/
