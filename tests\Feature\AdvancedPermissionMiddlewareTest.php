<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Admin;
use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Models\AdvancedPermissionAudit;
use App\Http\Middleware\AdvancedPermissionMiddleware;
use App\Services\AdvancedPermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

/**
 * Advanced Permission Middleware Tests
 *
 * Tests the middleware functionality including permission checking,
 * constraint validation, and audit logging.
 */
class AdvancedPermissionMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected AdvancedPermissionMiddleware $middleware;
    protected AdvancedPermissionService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->artisan('migrate');
        $this->service = new AdvancedPermissionService();
        $this->middleware = new AdvancedPermissionMiddleware($this->service);
    }

    /** @test */
    public function it_allows_access_when_user_has_required_permission()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($permission);
        $user->assignAdvancedRole($role);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.read');

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    /** @test */
    public function it_denies_access_when_user_lacks_required_permission()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);

        $user->assignAdvancedRole($role);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.create');

        $this->assertEquals(302, $response->getStatusCode()); // Redirect back
    }

    /** @test */
    public function it_allows_access_for_super_admin()
    {
        $admin = Admin::factory()->create([
            'use_advanced_roles' => true,
            'is_super_admin' => true,
        ]);

        Auth::login($admin);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'any.permission');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_handles_or_logic_permissions()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($permission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.read|users.create'); // User has read but not create

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_handles_and_logic_permissions()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        $readPermission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($readPermission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.read&users.create'); // User has read but not create

        $this->assertEquals(302, $response->getStatusCode()); // Should fail
    }

    /** @test */
    public function it_validates_permission_constraints()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'finance.approve',
            'display_name' => 'Approve Finance',
            'resource' => 'finance',
            'action' => 'approve',
        ]);

        // Grant permission with constraints
        $role->grantPermission($permission, [
            'max_amount' => 10000,
            'ip_whitelist' => ['127.0.0.1'],
        ]);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        // Test with valid context
        $request = Request::create('/test', 'GET', ['amount' => 5000]);
        $request->server->set('REMOTE_ADDR', '127.0.0.1');

        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'finance.approve');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_logs_permission_checks()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($permission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.read');

        $this->assertDatabaseHas('advanced_permission_audit', [
            'event_type' => AdvancedPermissionAudit::EVENT_ACCESS_GRANTED,
            'action' => 'users.read',
            'user_id' => $user->id,
            'was_granted' => true,
        ]);
    }

    /** @test */
    public function it_logs_denied_access()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);

        $user->assignAdvancedRole($role);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.create');

        $this->assertDatabaseHas('advanced_permission_audit', [
            'event_type' => AdvancedPermissionAudit::EVENT_ACCESS_DENIED,
            'action' => 'users.create',
            'user_id' => $user->id,
            'was_granted' => false,
        ]);
    }

    /** @test */
    public function it_returns_json_response_for_api_requests()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        Auth::login($user);

        $request = Request::create('/api/test', 'GET');
        $request->headers->set('Accept', 'application/json');

        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.create');

        $this->assertEquals(403, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $data = json_decode($response->getContent(), true);
        $this->assertEquals('Forbidden', $data['error']);
    }

    /** @test */
    public function it_falls_back_to_basic_system_for_non_advanced_users()
    {
        $user = User::factory()->create(['use_advanced_roles' => false]);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'users.read');

        // Should pass through to next middleware/controller
        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_handles_temporal_role_assignments()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'temp_role',
            'display_name' => 'Temporary Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'temp.action',
            'display_name' => 'Temporary Action',
            'resource' => 'temp',
            'action' => 'action',
        ]);

        $role->grantPermission($permission);

        // Assign role that expires in the past
        $user->assignAdvancedRole($role, [
            'expires_at' => now()->subDay(),
        ]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'temp.action');

        $this->assertEquals(302, $response->getStatusCode()); // Should be denied
    }

    /** @test */
    public function it_handles_permission_inheritance()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);

        // Create parent role with permission
        $parentRole = AdvancedRole::create([
            'name' => 'parent_role',
            'display_name' => 'Parent Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'inherited.permission',
            'display_name' => 'Inherited Permission',
            'resource' => 'inherited',
            'action' => 'permission',
        ]);
        $parentRole->grantPermission($permission);

        // Create child role that inherits
        $childRole = AdvancedRole::create([
            'name' => 'child_role',
            'display_name' => 'Child Role',
            'parent_role_id' => $parentRole->id,
            'inherit_permissions' => true,
        ]);

        $user->assignAdvancedRole($childRole);
        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'inherited.permission');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_handles_multiple_roles_with_priority()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);

        // Create two roles with different priorities
        $highPriorityRole = AdvancedRole::create([
            'name' => 'high_priority',
            'display_name' => 'High Priority Role',
        ]);
        $lowPriorityRole = AdvancedRole::create([
            'name' => 'low_priority',
            'display_name' => 'Low Priority Role',
        ]);

        $permission = AdvancedPermission::create([
            'name' => 'priority.test',
            'display_name' => 'Priority Test',
            'resource' => 'priority',
            'action' => 'test',
        ]);

        $highPriorityRole->grantPermission($permission);

        // Assign both roles with different priorities
        $user->assignAdvancedRole($lowPriorityRole, ['priority' => 1]);
        $user->assignAdvancedRole($highPriorityRole, ['priority' => 10]);

        Auth::login($user);

        $request = Request::create('/test', 'GET');
        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'priority.test');

        $this->assertEquals(200, $response->getStatusCode());
    }

    /** @test */
    public function it_handles_constraint_validation_failure()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'constrained_role',
            'display_name' => 'Constrained Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'finance.approve',
            'display_name' => 'Approve Finance',
            'resource' => 'finance',
            'action' => 'approve',
        ]);

        // Grant permission with amount constraint
        $role->grantPermission($permission, ['max_amount' => 1000]);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        // Test with amount exceeding constraint
        $request = Request::create('/test', 'GET', ['amount' => 5000]);

        $response = $this->middleware->handle($request, function () {
            return new Response('Success');
        }, 'finance.approve');

        $this->assertEquals(302, $response->getStatusCode()); // Should be denied

        // Check audit log
        $this->assertDatabaseHas('advanced_permission_audit', [
            'event_type' => AdvancedPermissionAudit::EVENT_ACCESS_DENIED,
            'action' => 'finance.approve',
            'user_id' => $user->id,
            'was_granted' => false,
        ]);
    }
}
