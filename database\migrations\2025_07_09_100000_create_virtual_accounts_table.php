<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('virtual_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('provider')->default('numero')->comment('Virtual account provider (numero, etc.)');
            $table->string('currency', 3)->comment('Account currency code (NGN, USD, etc.)');
            $table->string('type')->default('individual')->comment('Account type (individual, business)');
            $table->string('account_number')->unique()->comment('Generated virtual account number');
            $table->string('account_name')->nullable()->comment('Account holder name');
            $table->string('bank_name')->nullable()->comment('Bank name for the virtual account');
            $table->string('bank_code')->nullable()->comment('Bank code');
            $table->json('provider_data')->nullable()->comment('Additional data from provider');
            $table->json('kyc_data_used')->nullable()->comment('KYC data used for account creation');
            $table->string('provider_reference')->nullable()->comment('Provider reference/ID');
            $table->boolean('is_active')->default(true)->comment('Account status');
            $table->timestamp('created_at_provider')->nullable()->comment('Creation timestamp from provider');
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'provider', 'currency', 'type']);
            $table->index(['provider', 'account_number']);
            $table->index(['user_id', 'is_active']);
            $table->index(['user_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('virtual_accounts');
    }
};
