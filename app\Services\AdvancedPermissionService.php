<?php

namespace App\Services;

use App\Models\AdvancedPermissionAudit;

/**
 * Advanced Permission Service
 * 
 * Core service for checking permissions with constraint validation.
 */
class AdvancedPermissionService
{
    /**
     * Check if user has permission with context validation
     */
    public function checkUserPermission($user, string $permissionName, array $context = []): array
    {
        // Super admin bypass
        if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
            return [
                'granted' => true,
                'reason' => 'Super admin access',
                'role' => null,
                'permission_model' => null,
            ];
        }

        // Get user's active roles
        $activeRoles = $this->getUserActiveRoles($user);
        
        if ($activeRoles->isEmpty()) {
            return [
                'granted' => false,
                'reason' => 'User has no active roles',
                'role' => null,
                'permission_model' => null,
            ];
        }

        // Check each role for the permission
        foreach ($activeRoles as $userRole) {
            $role = $userRole->role;
            if (!$role) continue;

            $result = $this->checkRolePermission($role, $permissionName, $context, $userRole);
            if ($result['granted']) {
                return $result;
            }
        }

        return [
            'granted' => false,
            'reason' => "Permission '{$permissionName}' not found in user's roles",
            'role' => $activeRoles->first()?->role,
            'permission_model' => null,
        ];
    }

    /**
     * Check if role has permission with constraints
     */
    protected function checkRolePermission($role, string $permissionName, array $context, $userRole): array
    {
        $rolePermissions = $role->rolePermissions()
            ->whereHas('permission', function ($query) use ($permissionName) {
                $query->where('name', $permissionName);
            })
            ->with('permission')
            ->get();

        foreach ($rolePermissions as $rolePermission) {
            // Check if permission is granted
            if (!$rolePermission->is_granted) {
                continue;
            }

            // Check temporal validity
            if (!$rolePermission->isCurrentlyValid()) {
                continue;
            }

            // Check constraints
            if (!$rolePermission->constraintsSatisfied($context)) {
                return [
                    'granted' => false,
                    'reason' => 'Permission constraints not satisfied: ' . $rolePermission->getConstraintDescription(),
                    'role' => $role,
                    'permission_model' => $rolePermission->permission,
                ];
            }

            // Check user role assignment validity
            if (!$userRole->isCurrentlyValid()) {
                continue;
            }

            return [
                'granted' => true,
                'reason' => 'Permission granted via role: ' . $role->display_name,
                'role' => $role,
                'permission_model' => $rolePermission->permission,
            ];
        }

        return [
            'granted' => false,
            'reason' => "Permission '{$permissionName}' not found or not valid in role: " . $role->display_name,
            'role' => $role,
            'permission_model' => null,
        ];
    }

    /**
     * Get user's active roles
     */
    protected function getUserActiveRoles($user)
    {
        if (!method_exists($user, 'advancedUserRoles')) {
            return collect();
        }

        return $user->advancedUserRoles()
            ->currentlyValid()
            ->with('role.rolePermissions.permission')
            ->byPriority()
            ->get();
    }

    /**
     * Check multiple permissions at once
     */
    public function checkMultiplePermissions($user, array $permissions, array $context = []): array
    {
        $results = [];
        
        foreach ($permissions as $permission) {
            $results[$permission] = $this->checkUserPermission($user, $permission, $context);
        }

        return $results;
    }

    /**
     * Get all permissions for user
     */
    public function getUserPermissions($user): array
    {
        if (method_exists($user, 'getAdvancedPermissions')) {
            return $user->getAdvancedPermissions()->pluck('name')->toArray();
        }

        return [];
    }

    /**
     * Check if user has permission (facade method)
     */
    public function check($user, string $permission, array $context = []): bool
    {
        if (!$user) {
            return false;
        }

        $result = $this->checkUserPermission($user, $permission, $context);
        return $result['granted'];
    }

    /**
     * Check detailed permission (facade method)
     */
    public function checkDetailed($user, string $permission, array $context = []): array
    {
        if (!$user) {
            return [
                'granted' => false,
                'reason' => 'User not provided',
                'role' => null,
                'permission_model' => null,
            ];
        }

        return $this->checkUserPermission($user, $permission, $context);
    }

    /**
     * Check multiple permissions with logic
     */
    public function checkMultiple($user, array $permissions, string $logic = 'and', array $context = []): bool
    {
        if (!$user) {
            return false;
        }

        if ($logic === 'or') {
            foreach ($permissions as $permission) {
                if ($this->check($user, $permission, $context)) {
                    return true;
                }
            }
            return false;
        } else {
            foreach ($permissions as $permission) {
                if (!$this->check($user, $permission, $context)) {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * Check if user has role
     */
    public function hasRole($user, string $roleName): bool
    {
        if (!$user || !method_exists($user, 'hasAdvancedRole')) {
            return false;
        }

        return $user->hasAdvancedRole($roleName);
    }

    /**
     * Check if user has any of the roles
     */
    public function hasAnyRole($user, array $roleNames): bool
    {
        foreach ($roleNames as $roleName) {
            if ($this->hasRole($user, $roleName)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get user roles
     */
    public function getUserRoles($user): array
    {
        if (!$user || !method_exists($user, 'advancedRoles')) {
            return [];
        }

        return $user->advancedRoles()->pluck('name')->toArray();
    }

    /**
     * Log access attempt
     */
    public function logAccess(string $action, bool $granted, $user = null, array $context = []): void
    {
        AdvancedPermissionAudit::logPermissionCheck(
            $action,
            $granted,
            $user,
            null,
            null,
            $granted ? 'Access granted' : 'Access denied',
            $context
        );
    }

    /**
     * Get CRUD permissions for resource
     */
    public function getResourcePermissions(string $resource): array
    {
        return [
            'create' => "{$resource}.create",
            'read' => "{$resource}.read",
            'update' => "{$resource}.update",
            'delete' => "{$resource}.delete",
        ];
    }

    /**
     * Get permission constraints for user
     */
    public function getPermissionConstraints($user, string $permission): array
    {
        if (!$user) {
            return [];
        }

        $constraints = [];
        $activeRoles = $this->getUserActiveRoles($user);

        foreach ($activeRoles as $userRole) {
            $rolePermissions = $userRole->role->rolePermissions()
                ->whereHas('permission', function ($query) use ($permission) {
                    $query->where('name', $permission);
                })
                ->get();

            foreach ($rolePermissions as $rolePermission) {
                if ($rolePermission->constraints) {
                    $constraints[] = [
                        'role' => $userRole->role->display_name,
                        'constraints' => $rolePermission->constraints,
                        'description' => $rolePermission->getConstraintDescription(),
                    ];
                }
            }
        }

        return $constraints;
    }

    /**
     * Validate constraints against context
     */
    public function validateConstraints(array $constraints, array $context): bool
    {
        foreach ($constraints as $constraint => $value) {
            switch ($constraint) {
                case 'ip_whitelist':
                    $userIp = $context['ip'] ?? request()->ip();
                    if (!$this->ipInWhitelist($userIp, $value)) {
                        return false;
                    }
                    break;

                case 'max_amount':
                    $amount = $context['amount'] ?? 0;
                    if ($amount > $value) {
                        return false;
                    }
                    break;

                case 'time_restriction':
                    if (!$this->isWithinTimeRestriction($value)) {
                        return false;
                    }
                    break;
            }
        }

        return true;
    }

    /**
     * Check if IP is in whitelist
     */
    protected function ipInWhitelist(string $ip, array $whitelist): bool
    {
        foreach ($whitelist as $allowedIp) {
            if ($this->ipInRange($ip, $allowedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is in range
     */
    protected function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $mask) = explode('/', $range);
        return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
    }

    /**
     * Check if current time is within restriction
     */
    protected function isWithinTimeRestriction(array $restriction): bool
    {
        $now = now();

        if (isset($restriction['days'])) {
            $currentDay = strtolower($now->format('l'));
            if (!in_array($currentDay, $restriction['days'])) {
                return false;
            }
        }

        if (isset($restriction['hours'])) {
            $currentTime = $now->format('H:i');
            $startTime = $restriction['hours']['start'] ?? '00:00';
            $endTime = $restriction['hours']['end'] ?? '23:59';

            if ($currentTime < $startTime || $currentTime > $endTime) {
                return false;
            }
        }

        return true;
    }
}
