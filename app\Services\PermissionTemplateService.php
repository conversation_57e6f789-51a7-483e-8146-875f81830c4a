<?php

namespace App\Services;

use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use Illuminate\Support\Collection;

/**
 * Permission Template Service
 * 
 * Provides pre-defined permission templates for common roles and bulk operations.
 */
class PermissionTemplateService
{
    /**
     * Get all available permission templates
     */
    public function getAvailableTemplates(): array
    {
        return [
            'super_admin' => $this->getSuperAdminTemplate(),
            'admin' => $this->getAdminTemplate(),
            'finance_manager' => $this->getFinanceManagerTemplate(),
            'finance_clerk' => $this->getFinanceClerkTemplate(),
            'user_manager' => $this->getUserManagerTemplate(),
            'support_agent' => $this->getSupportAgentTemplate(),
            'compliance_officer' => $this->getComplianceOfficerTemplate(),
            'merchant_manager' => $this->getMerchantManagerTemplate(),
            'readonly_user' => $this->getReadOnlyTemplate(),
            'basic_user' => $this->getBasicUserTemplate(),
        ];
    }

    /**
     * Super Admin Template - All permissions
     */
    protected function getSuperAdminTemplate(): array
    {
        return [
            'name' => 'super_admin',
            'display_name' => 'Super Administrator',
            'description' => 'Full system access with all permissions',
            'category' => 'admin',
            'permissions' => ['*'], // Special case for all permissions
            'is_system' => true,
        ];
    }

    /**
     * Admin Template - Most administrative permissions
     */
    protected function getAdminTemplate(): array
    {
        return [
            'name' => 'admin',
            'display_name' => 'Administrator',
            'description' => 'Administrative access to most system functions',
            'category' => 'admin',
            'permissions' => [
                'users.*',
                'admins.read',
                'admins.update',
                'roles.*',
                'permissions.read',
                'transactions.read',
                'payouts.*',
                'deposits.*',
                'kyc.*',
                'disputes.*',
                'tickets.*',
                'notifications.*',
                'templates.*',
                'reports.*',
                'settings.read',
                'settings.update',
            ],
        ];
    }

    /**
     * Finance Manager Template
     */
    protected function getFinanceManagerTemplate(): array
    {
        return [
            'name' => 'finance_manager',
            'display_name' => 'Finance Manager',
            'description' => 'Full access to financial operations and reporting',
            'category' => 'finance',
            'permissions' => [
                'transactions.*',
                'payouts.*',
                'deposits.*',
                'forex.*',
                'currencies.*',
                'wallets.*',
                'invoices.*',
                'vouchers.*',
                'escrows.*',
                'transfers.*',
                'exchanges.*',
                'reports.read',
                'reports.export',
                'users.read',
                'merchants.read',
            ],
        ];
    }

    /**
     * Finance Clerk Template
     */
    protected function getFinanceClerkTemplate(): array
    {
        return [
            'name' => 'finance_clerk',
            'display_name' => 'Finance Clerk',
            'description' => 'Limited financial operations access',
            'category' => 'finance',
            'permissions' => [
                'transactions.read',
                'payouts.read',
                'payouts.process',
                'deposits.read',
                'forex.read',
                'currencies.read',
                'wallets.read',
                'invoices.read',
                'invoices.create',
                'vouchers.read',
                'transfers.read',
                'reports.read',
            ],
        ];
    }

    /**
     * User Manager Template
     */
    protected function getUserManagerTemplate(): array
    {
        return [
            'name' => 'user_manager',
            'display_name' => 'User Manager',
            'description' => 'User and merchant management capabilities',
            'category' => 'user_management',
            'permissions' => [
                'users.*',
                'merchants.*',
                'kyc.*',
                'notifications.read',
                'notifications.create',
                'templates.read',
                'reports.read',
                'transactions.read',
            ],
        ];
    }

    /**
     * Support Agent Template
     */
    protected function getSupportAgentTemplate(): array
    {
        return [
            'name' => 'support_agent',
            'display_name' => 'Support Agent',
            'description' => 'Customer support and ticket management',
            'category' => 'support',
            'permissions' => [
                'tickets.*',
                'disputes.*',
                'users.read',
                'users.update',
                'merchants.read',
                'merchants.update',
                'transactions.read',
                'notifications.create',
                'templates.read',
            ],
        ];
    }

    /**
     * Compliance Officer Template
     */
    protected function getComplianceOfficerTemplate(): array
    {
        return [
            'name' => 'compliance_officer',
            'display_name' => 'Compliance Officer',
            'description' => 'KYC, compliance, and audit access',
            'category' => 'compliance',
            'permissions' => [
                'kyc.*',
                'users.read',
                'merchants.read',
                'transactions.read',
                'reports.*',
                'logs.read',
                'disputes.read',
                'disputes.update',
            ],
        ];
    }

    /**
     * Merchant Manager Template
     */
    protected function getMerchantManagerTemplate(): array
    {
        return [
            'name' => 'merchant_manager',
            'display_name' => 'Merchant Manager',
            'description' => 'Merchant account and service management',
            'category' => 'merchant_management',
            'permissions' => [
                'merchants.*',
                'transactions.read',
                'payouts.read',
                'deposits.read',
                'notifications.create',
                'templates.read',
                'reports.read',
            ],
        ];
    }

    /**
     * Read-Only Template
     */
    protected function getReadOnlyTemplate(): array
    {
        return [
            'name' => 'readonly_user',
            'display_name' => 'Read-Only User',
            'description' => 'Read-only access to most system data',
            'category' => 'general',
            'permissions' => [
                '*.read',
                'reports.read',
            ],
        ];
    }

    /**
     * Basic User Template
     */
    protected function getBasicUserTemplate(): array
    {
        return [
            'name' => 'basic_user',
            'display_name' => 'Basic User',
            'description' => 'Basic user permissions for standard operations',
            'category' => 'user',
            'permissions' => [
                'profile.read',
                'profile.update',
                'transactions.read',
                'wallets.read',
                'notifications.read',
            ],
        ];
    }

    /**
     * Create role from template
     */
    public function createRoleFromTemplate(string $templateName, array $overrides = []): AdvancedRole
    {
        $templates = $this->getAvailableTemplates();
        
        if (!isset($templates[$templateName])) {
            throw new \InvalidArgumentException("Template '{$templateName}' not found");
        }

        $template = array_merge($templates[$templateName], $overrides);
        
        // Create the role
        $role = AdvancedRole::create([
            'name' => $template['name'],
            'display_name' => $template['display_name'],
            'description' => $template['description'],
            'category' => $template['category'],
            'is_system' => $template['is_system'] ?? false,
        ]);

        // Assign permissions
        $this->assignPermissionsFromTemplate($role, $template['permissions']);

        return $role;
    }

    /**
     * Assign permissions from template patterns
     */
    protected function assignPermissionsFromTemplate(AdvancedRole $role, array $permissionPatterns): void
    {
        foreach ($permissionPatterns as $pattern) {
            if ($pattern === '*') {
                // Assign all permissions
                $permissions = AdvancedPermission::active()->get();
                foreach ($permissions as $permission) {
                    $role->grantPermission($permission);
                }
            } elseif (str_ends_with($pattern, '.*')) {
                // Assign all permissions for a resource
                $resource = str_replace('.*', '', $pattern);
                $permissions = AdvancedPermission::forResource($resource);
                foreach ($permissions as $permission) {
                    $role->grantPermission($permission);
                }
            } elseif (str_ends_with($pattern, '.read')) {
                // Assign read permissions for all resources
                $permissions = AdvancedPermission::where('action', 'read')->active()->get();
                foreach ($permissions as $permission) {
                    $role->grantPermission($permission);
                }
            } else {
                // Assign specific permission
                $permission = AdvancedPermission::findByName($pattern);
                if ($permission) {
                    $role->grantPermission($permission);
                }
            }
        }
    }

    /**
     * Get template by name
     */
    public function getTemplate(string $templateName): ?array
    {
        $templates = $this->getAvailableTemplates();
        return $templates[$templateName] ?? null;
    }

    /**
     * Bulk create roles from all templates
     */
    public function createAllTemplateRoles(): array
    {
        $created = [];
        $errors = [];

        foreach ($this->getAvailableTemplates() as $templateName => $template) {
            try {
                // Skip if role already exists
                if (AdvancedRole::findByName($template['name'])) {
                    continue;
                }

                $role = $this->createRoleFromTemplate($templateName);
                $created[] = $role;
            } catch (\Exception $e) {
                $errors[] = "Failed to create role from template '{$templateName}': " . $e->getMessage();
            }
        }

        return [
            'created' => $created,
            'errors' => $errors,
        ];
    }

    /**
     * Get permission suggestions for a role category
     */
    public function getPermissionSuggestions(string $category): Collection
    {
        $categoryPermissions = AdvancedPermission::inCategory($category)->active()->get();
        
        // Add related permissions
        $relatedCategories = $this->getRelatedCategories($category);
        foreach ($relatedCategories as $relatedCategory) {
            $related = AdvancedPermission::inCategory($relatedCategory)
                ->whereIn('action', ['read']) // Only suggest read permissions for related categories
                ->active()
                ->get();
            $categoryPermissions = $categoryPermissions->merge($related);
        }

        return $categoryPermissions->unique('id')->sortBy('name');
    }

    /**
     * Get related categories for permission suggestions
     */
    protected function getRelatedCategories(string $category): array
    {
        return match ($category) {
            'finance' => ['user_management', 'reporting'],
            'user_management' => ['finance', 'compliance'],
            'compliance' => ['user_management', 'finance'],
            'support' => ['user_management', 'finance'],
            'admin' => ['user_management', 'finance', 'compliance', 'support'],
            default => [],
        };
    }

    /**
     * Validate template structure
     */
    public function validateTemplate(array $template): array
    {
        $errors = [];

        $required = ['name', 'display_name', 'description', 'category', 'permissions'];
        foreach ($required as $field) {
            if (!isset($template[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }

        if (isset($template['permissions']) && !is_array($template['permissions'])) {
            $errors[] = "Permissions must be an array";
        }

        if (isset($template['name']) && !preg_match('/^[a-z_]+$/', $template['name'])) {
            $errors[] = "Role name must contain only lowercase letters and underscores";
        }

        return $errors;
    }

    /**
     * Export role as template
     */
    public function exportRoleAsTemplate(AdvancedRole $role): array
    {
        $permissions = $role->getAllPermissions();
        $permissionNames = $permissions->pluck('name')->toArray();

        return [
            'name' => $role->name,
            'display_name' => $role->display_name,
            'description' => $role->description,
            'category' => $role->category,
            'permissions' => $permissionNames,
            'is_system' => $role->is_system,
            'exported_at' => now()->toISOString(),
            'exported_by' => auth()->user()?->email ?? 'system',
        ];
    }
}
