<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ForexAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $accounts = [
            [
                'account_name' => 'USD Main Account',
                'account_type' => 'USD',
                'currency_code' => 'USD',
                'balance' => 0.********,
                'pending_balance' => 0.********,
                'description' => 'Main USD account for foreign exchange transactions',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'account_name' => 'CBN Naira Account',
                'account_type' => 'CBN',
                'currency_code' => 'NGN',
                'balance' => 0.********,
                'pending_balance' => 0.********,
                'description' => 'Naira account for CBN rate transactions',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'account_name' => 'Difference Account',
                'account_type' => 'Difference',
                'currency_code' => 'NGN',
                'balance' => 0.********,
                'pending_balance' => 0.********,
                'description' => 'Account for rate differentials and premiums',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'account_name' => 'Investment Account',
                'account_type' => 'Investment',
                'currency_code' => 'NGN',
                'balance' => 0.********,
                'pending_balance' => 0.********,
                'description' => 'Account for internal transfers and investments',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('forex_accounts')->insert($accounts);
    }
}
