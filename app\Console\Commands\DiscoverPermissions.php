<?php

namespace App\Console\Commands;

use App\Services\PermissionDiscoveryService;
use App\Models\AdvancedPermission;
use Illuminate\Console\Command;

/**
 * Discover Permissions Command
 * 
 * Artisan command to discover and sync permissions from controllers and routes.
 */
class DiscoverPermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permissions:discover 
                            {--sync : Sync discovered permissions to database}
                            {--stats : Show permission statistics only}
                            {--category= : Filter by category}
                            {--resource= : Filter by resource}
                            {--dry-run : Show what would be created without actually creating}';

    /**
     * The console command description.
     */
    protected $description = 'Discover permissions from controllers and routes';

    /**
     * Permission discovery service
     */
    protected PermissionDiscoveryService $discoveryService;

    /**
     * Create a new command instance.
     */
    public function __construct(PermissionDiscoveryService $discoveryService)
    {
        parent::__construct();
        $this->discoveryService = $discoveryService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Advanced Permission Discovery System');
        $this->newLine();

        // Show statistics only
        if ($this->option('stats')) {
            return $this->showStatistics();
        }

        // Discover permissions
        $this->info('Discovering permissions from controllers and routes...');
        $permissions = $this->discoveryService->discoverAllPermissions();

        // Apply filters
        $permissions = $this->applyFilters($permissions);

        $this->info("Found {count($permissions)} permissions");
        $this->newLine();

        // Show discovered permissions
        $this->displayPermissions($permissions);

        // Sync to database if requested
        if ($this->option('sync') && !$this->option('dry-run')) {
            return $this->syncPermissions($permissions);
        }

        if ($this->option('dry-run')) {
            $this->warn('🔥 DRY RUN MODE - No changes were made to the database');
            $this->showSyncPreview($permissions);
        }

        return Command::SUCCESS;
    }

    /**
     * Show permission statistics
     */
    protected function showStatistics(): int
    {
        $stats = $this->discoveryService->getPermissionStatistics();

        $this->info('📊 Permission Statistics');
        $this->newLine();

        $this->table(['Metric', 'Count'], [
            ['Total Discovered', $stats['total_discovered']],
            ['Total in Database', $stats['total_existing']],
            ['New Permissions', $stats['total_discovered'] - $stats['total_existing']],
        ]);

        $this->newLine();
        $this->info('📂 By Category:');
        foreach ($stats['by_category'] as $category => $count) {
            $this->line("  • {$category}: {$count}");
        }

        $this->newLine();
        $this->info('🎯 By Resource (Top 10):');
        arsort($stats['by_resource']);
        $topResources = array_slice($stats['by_resource'], 0, 10, true);
        foreach ($topResources as $resource => $count) {
            $this->line("  • {$resource}: {$count}");
        }

        $this->newLine();
        $this->info('⚡ By Action:');
        arsort($stats['by_action']);
        foreach ($stats['by_action'] as $action => $count) {
            $this->line("  • {$action}: {$count}");
        }

        return Command::SUCCESS;
    }

    /**
     * Apply command line filters
     */
    protected function applyFilters(array $permissions): array
    {
        if ($category = $this->option('category')) {
            $permissions = array_filter($permissions, fn($p) => $p['category'] === $category);
        }

        if ($resource = $this->option('resource')) {
            $permissions = array_filter($permissions, fn($p) => $p['resource'] === $resource);
        }

        return array_values($permissions);
    }

    /**
     * Display discovered permissions in a table
     */
    protected function displayPermissions(array $permissions): void
    {
        if (empty($permissions)) {
            $this->warn('No permissions found matching the criteria.');
            return;
        }

        // Group by category for better display
        $grouped = [];
        foreach ($permissions as $permission) {
            $grouped[$permission['category']][] = $permission;
        }

        foreach ($grouped as $category => $categoryPermissions) {
            $this->info("📁 Category: " . ucwords(str_replace('_', ' ', $category)));
            
            $tableData = [];
            foreach ($categoryPermissions as $permission) {
                $tableData[] = [
                    $permission['name'],
                    $permission['display_name'],
                    $permission['resource'],
                    $permission['action'],
                ];
            }

            $this->table(
                ['Permission Name', 'Display Name', 'Resource', 'Action'],
                $tableData
            );
            $this->newLine();
        }
    }

    /**
     * Sync permissions to database
     */
    protected function syncPermissions(array $permissions): int
    {
        $this->info('💾 Syncing permissions to database...');
        
        $progressBar = $this->output->createProgressBar(count($permissions));
        $progressBar->start();

        $result = $this->discoveryService->syncPermissionsToDatabase($permissions);

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        $this->info('✅ Sync completed!');
        $this->newLine();

        $this->table(['Metric', 'Count'], [
            ['Total Processed', $result['total_discovered']],
            ['Created', $result['created']],
            ['Updated', $result['updated']],
            ['Errors', count($result['errors'])],
        ]);

        // Show errors if any
        if (!empty($result['errors'])) {
            $this->newLine();
            $this->error('❌ Errors encountered:');
            foreach ($result['errors'] as $error) {
                $this->line("  • {$error}");
            }
        }

        // Show success summary
        if ($result['created'] > 0) {
            $this->info("🎉 Created {$result['created']} new permissions");
        }
        if ($result['updated'] > 0) {
            $this->info("🔄 Updated {$result['updated']} existing permissions");
        }

        return Command::SUCCESS;
    }

    /**
     * Show what would be synced in dry-run mode
     */
    protected function showSyncPreview(array $permissions): void
    {
        $this->newLine();
        $this->info('📋 Sync Preview (what would be created/updated):');
        $this->newLine();

        $existing = AdvancedPermission::pluck('name')->toArray();
        $toCreate = [];
        $toUpdate = [];

        foreach ($permissions as $permission) {
            if (in_array($permission['name'], $existing)) {
                $toUpdate[] = $permission['name'];
            } else {
                $toCreate[] = $permission['name'];
            }
        }

        if (!empty($toCreate)) {
            $this->info("🆕 Would CREATE {count($toCreate)} permissions:");
            foreach (array_slice($toCreate, 0, 10) as $name) {
                $this->line("  • {$name}");
            }
            if (count($toCreate) > 10) {
                $this->line("  ... and " . (count($toCreate) - 10) . " more");
            }
            $this->newLine();
        }

        if (!empty($toUpdate)) {
            $this->info("🔄 Would UPDATE {count($toUpdate)} permissions:");
            foreach (array_slice($toUpdate, 0, 10) as $name) {
                $this->line("  • {$name}");
            }
            if (count($toUpdate) > 10) {
                $this->line("  ... and " . (count($toUpdate) - 10) . " more");
            }
            $this->newLine();
        }

        $this->info('💡 Run with --sync to actually perform the sync');
    }
}
