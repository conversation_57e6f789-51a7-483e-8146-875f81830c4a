<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Models\ForexRate;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class ForexWalletPaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $usdCurrency;
    protected $ngnCurrency;
    protected $walletService;
    protected $bookingService;
    protected $forexAccount;
    protected $activeRate;

    protected function setUp(): void
    {
        parent::setUp();

        // Create currencies table if it doesn't exist
        if (!Schema::hasTable('currencies')) {
            Schema::create('currencies', function ($table) {
                $table->id();
                $table->string('name');
                $table->string('code', 10)->unique();
                $table->string('symbol', 10);
                $table->boolean('is_active')->default(1);
                $table->integer('currency_type')->default(0);
                $table->string('driver')->default('local');
                $table->string('logo')->nullable();
                $table->timestamps();
            });
        }

        // Create wallets table if it doesn't exist
        if (!Schema::hasTable('wallets')) {
            Schema::create('wallets', function ($table) {
                $table->id();
                $table->unsignedBigInteger('user_id');
                $table->unsignedBigInteger('currency_id');
                $table->decimal('balance', 20, 8)->default(0);
                $table->timestamps();
                $table->unique(['user_id', 'currency_id']);
            });
        }

        // Create test currencies
        $this->usdCurrency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
            'is_active' => 1,
            'currency_type' => 0,
            'driver' => 'local',
            'logo' => 'usd.png'
        ]);

        $this->ngnCurrency = Currency::create([
            'name' => 'Nigerian Naira',
            'code' => 'NGN',
            'symbol' => '₦',
            'is_active' => 1,
            'currency_type' => 1,
            'driver' => 'local',
            'logo' => 'ngn.png'
        ]);

        // Create test user
        $this->user = User::create([
            'firstname' => 'Test',
            'lastname' => 'User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'email_verified_at' => now(),
        ]);

        // Create forex account
        $this->forexAccount = ForexAccount::create([
            'account_name' => 'Test USD Account',
            'account_type' => 'USD',
            'currency_code' => 'USD',
            'balance' => 10000.00,
            'pending_balance' => 0.00,
            'is_active' => true,
            'description' => 'Test account for USD transactions'
        ]);

        // Create active forex rate
        $this->activeRate = ForexRate::create([
            'cbn_rate' => 800.00,
            'parallel_rate' => 1200.00,
            'markup_percentage' => 5.00,
            'cbn_sell_rate' => 790.00,
            'parallel_sell_rate' => 1190.00,
            'sell_markup_percentage' => 3.00,
            'is_active' => true,
            'created_by' => 1,
        ]);

        $this->walletService = app(ForexWalletService::class);
        $this->bookingService = app(ForexBookingService::class);
    }

    /** @test */
    public function it_can_check_if_user_has_wallet()
    {
        // User doesn't have USD wallet initially
        $this->assertFalse($this->walletService->userHasWallet($this->user->id, 'USD'));

        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        // Now user has USD wallet
        $this->assertTrue($this->walletService->userHasWallet($this->user->id, 'USD'));
        $this->assertFalse($this->walletService->userHasWallet($this->user->id, 'NGN'));
    }

    /** @test */
    public function it_can_get_wallet_availability()
    {
        // Create USD wallet only
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        $availability = $this->walletService->getWalletAvailability($this->user->id);

        $this->assertTrue($availability['usd_wallet']['exists']);
        $this->assertFalse($availability['ngn_wallet']['exists']);
        $this->assertNotNull($availability['usd_wallet']['wallet']);
        $this->assertNull($availability['ngn_wallet']['wallet']);
    }

    /** @test */
    public function it_can_determine_target_wallet_currency()
    {
        // For buying (NGN to USD), user receives USD
        $this->assertEquals('USD', $this->walletService->getTargetWalletCurrency('buying'));

        // For selling (USD to NGN), user receives NGN
        $this->assertEquals('NGN', $this->walletService->getTargetWalletCurrency('selling'));
    }

    /** @test */
    public function it_can_get_payment_method_options_with_wallet()
    {
        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        $options = $this->walletService->getPaymentMethodOptions($this->user->id, 'buying');

        $this->assertTrue($options['has_wallet']);
        $this->assertEquals('USD', $options['target_currency']);
        $this->assertArrayHasKey('account_details', $options['payment_methods']);
        $this->assertArrayHasKey('wallet', $options['payment_methods']);
        $this->assertEquals('Pay to USD Wallet', $options['payment_methods']['wallet']['label']);
    }

    /** @test */
    public function it_can_get_payment_method_options_without_wallet()
    {
        $options = $this->walletService->getPaymentMethodOptions($this->user->id, 'buying');

        $this->assertFalse($options['has_wallet']);
        $this->assertEquals('USD', $options['target_currency']);
        $this->assertArrayHasKey('account_details', $options['payment_methods']);
        $this->assertArrayNotHasKey('wallet', $options['payment_methods']);
    }

    /** @test */
    public function it_can_validate_wallet_payment()
    {
        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        // Valid wallet payment
        $this->assertTrue($this->walletService->validateWalletPayment(
            $this->user->id,
            'wallet',
            $this->usdCurrency->id
        ));

        // Invalid - user doesn't have NGN wallet
        $this->assertFalse($this->walletService->validateWalletPayment(
            $this->user->id,
            'wallet',
            $this->ngnCurrency->id
        ));

        // Valid - account details payment (no validation needed)
        $this->assertTrue($this->walletService->validateWalletPayment(
            $this->user->id,
            'account_details',
            null
        ));
    }

    /** @test */
    public function it_can_fund_wallet_from_booking_completion()
    {
        // Create USD wallet for user
        $wallet = Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        // Create a booking with wallet payment
        $booking = ForexBooking::create([
            'booking_reference' => 'FXB20240706B1234',
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_type' => 'user',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'cbn_rate' => 800.00,
            'parallel_rate' => 1200.00,
            'markup_percentage' => 5.00,
            'customer_rate' => 1260.00,
            'customer_total' => 126000.00,
            'target_account_id' => $this->forexAccount->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id,
            'status' => 'completed',
            'initiated_by' => 1,
        ]);

        $initialBalance = $wallet->balance;

        // Fund wallet from booking
        $transaction = $this->walletService->fundWalletFromBooking($booking, 1);

        // Check transaction was created
        $this->assertNotNull($transaction);
        $this->assertEquals($this->user->id, $transaction->user_id);
        $this->assertEquals($this->usdCurrency->id, $transaction->currency_id);
        $this->assertEquals(100.00, $transaction->amount);
        $this->assertEquals('+', $transaction->trx_type);

        // Check wallet balance was updated
        $wallet->refresh();
        $this->assertEquals($initialBalance + 100.00, $wallet->balance);
    }

    /** @test */
    public function it_does_not_fund_wallet_for_non_wallet_bookings()
    {
        $booking = ForexBooking::create([
            'booking_reference' => 'FXB20240706B1234',
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_type' => 'user',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->forexAccount->id,
            'payment_method' => 'account_details', // Not wallet payment
            'status' => 'completed',
            'initiated_by' => 1,
        ]);

        $transaction = $this->walletService->fundWalletFromBooking($booking, 1);

        // Should return null for non-wallet bookings
        $this->assertNull($transaction);
    }

    /** @test */
    public function it_calculates_correct_fund_amount_for_buying()
    {
        $booking = new ForexBooking([
            'transaction_type' => 'buying',
            'amount' => 100.00,
            'customer_total' => 126000.00,
        ]);

        $reflection = new \ReflectionClass($this->walletService);
        $method = $reflection->getMethod('calculateWalletFundAmount');
        $method->setAccessible(true);

        $fundAmount = $method->invoke($this->walletService, $booking);

        // For buying, user receives USD amount
        $this->assertEquals(100.00, $fundAmount);
    }

    /** @test */
    public function it_calculates_correct_fund_amount_for_selling()
    {
        $booking = new ForexBooking([
            'transaction_type' => 'selling',
            'amount' => 100.00,
            'customer_total' => 119000.00,
        ]);

        $reflection = new \ReflectionClass($this->walletService);
        $method = $reflection->getMethod('calculateWalletFundAmount');
        $method->setAccessible(true);

        $fundAmount = $method->invoke($this->walletService, $booking);

        // For selling, user receives NGN amount (customer_total)
        $this->assertEquals(119000.00, $fundAmount);
    }
}
