# Webhook Security Implementation Guide

This document provides a comprehensive guide for implementing HMAC-SHA256 webhook security in your application.

## Overview

All webhooks sent by the system are signed using HMAC-SHA256 with the recipient's API secret key. Recipients must verify these signatures to ensure webhook authenticity and prevent spoofing attacks.

## Security Features

- **HMAC-SHA256 Signature**: All webhook payloads are signed using HMAC-SHA256
- **API Key Based**: Uses the recipient's API secret key for signing
- **Standard Header Format**: Signature included in `X-Webhook-Signature` header
- **Timing-Safe Comparison**: Prevents timing attacks during verification
- **Comprehensive Logging**: Security events are logged for monitoring

## Implementation

### 1. Signature Generation (Sending Webhooks)

The system automatically generates signatures when sending webhooks:

```php
use App\Services\WebhookSecurityService;

$payload = ['event' => 'TRANSFER_NOTIFICATION', 'data' => [...] ];
$secretKey = $user->secret_key;

$signature = WebhookSecurityService::generateSignature($payload, $secretKey);
// Returns: 'sha256=<hex_digest>'
```

### 2. Signature Verification (Receiving Webhooks)

When receiving webhooks, verify the signature:

```php
use App\Services\WebhookSecurityService;

$request = request();
$secretKey = 'your_api_secret_key';

$isValid = WebhookSecurityService::validateIncomingWebhook($request, $secretKey);

if (!$isValid) {
    return response()->json(['error' => 'Invalid signature'], 401);
}

// Process webhook safely
$payload = json_decode($request->getContent(), true);
```

### 3. Manual Verification

For custom implementations:

```php
use App\Services\WebhookSecurityService;

$payload = $request->getContent(); // Raw request body
$signature = $request->header('X-Webhook-Signature');
$secretKey = 'your_api_secret_key';

$isValid = WebhookSecurityService::verifySignature($payload, $signature, $secretKey);
```

## Middleware Usage

Use the provided middleware for automatic verification:

```php
// In routes/api.php
Route::middleware('verifyWebhookSignature:your_secret_key')->group(function () {
    Route::post('/webhook/endpoint', 'WebhookController@handle');
});
```

## Configuration

### Environment Variables

Add to your `.env` file:

```env
WEBHOOK_SECRET=your_default_webhook_secret_key
SKIP_WEBHOOK_VERIFICATION=false  # Set to true for local development only
```

### Config File

Add to `config/app.php`:

```php
'webhook_secret' => env('WEBHOOK_SECRET'),
'skip_webhook_verification' => env('SKIP_WEBHOOK_VERIFICATION', false),
```

## Security Best Practices

### 1. Always Verify Signatures
Never process webhook data without verifying the signature first:

```php
// ❌ WRONG - Processing without verification
$data = json_decode($request->getContent(), true);
processWebhook($data);

// ✅ CORRECT - Verify first, then process
if (WebhookSecurityService::validateIncomingWebhook($request, $secretKey)) {
    $data = json_decode($request->getContent(), true);
    processWebhook($data);
}
```

### 2. Use Timing-Safe Comparison
The service automatically uses timing-safe comparison methods to prevent timing attacks.

### 3. Secure Secret Key Storage
- Store API secret keys securely
- Never expose them in client-side code
- Rotate keys regularly
- Use environment variables for configuration

### 4. HTTPS Only
Always use HTTPS for webhook URLs to prevent man-in-the-middle attacks.

### 5. Implement Idempotency
Handle duplicate webhook deliveries gracefully:

```php
$transactionId = $data['transaction_id'];

// Check if already processed
if (Transaction::where('webhook_transaction_id', $transactionId)->exists()) {
    return response()->json(['status' => 'already_processed']);
}

// Process webhook
processWebhook($data);
```

### 6. Log Security Events
Log failed signature verifications for security monitoring:

```php
if (!$isValid) {
    Log::warning('Invalid webhook signature', [
        'ip' => $request->ip(),
        'url' => $request->url(),
        'signature' => $request->header('X-Webhook-Signature')
    ]);
}
```

## Testing

### Unit Tests

Run the webhook security tests:

```bash
php artisan test tests/Unit/WebhookSecurityServiceTest.php
```

### Manual Testing

Use the test webhook endpoint:

```bash
curl -X POST https://your-domain.com/api/webhook/test \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"webhook_url": "https://your-webhook-endpoint.com/webhook"}'
```

### Example Test Endpoint

Create a test endpoint to verify webhook reception:

```php
Route::post('/test-webhook', function (Request $request) {
    $signature = $request->header('X-Webhook-Signature');
    $payload = $request->getContent();
    $secretKey = 'your_secret_key';
    
    $isValid = WebhookSecurityService::verifySignature($payload, $signature, $secretKey);
    
    return response()->json([
        'signature_valid' => $isValid,
        'signature_received' => $signature,
        'payload_received' => json_decode($payload, true)
    ]);
});
```

## Troubleshooting

### Common Issues

1. **Signature Mismatch**: Ensure you're using the raw request body for verification
2. **Wrong Secret Key**: Verify you're using the correct API secret key
3. **Header Not Found**: Check that the `X-Webhook-Signature` header is present
4. **JSON Encoding**: Ensure consistent JSON encoding between generation and verification

### Debug Mode

For debugging, you can temporarily log signature details:

```php
Log::debug('Webhook signature debug', [
    'payload' => $payload,
    'signature_received' => $signature,
    'signature_expected' => WebhookSecurityService::generateSignature($payload, $secretKey),
    'secret_key_length' => strlen($secretKey)
]);
```

## Support

For additional support or questions about webhook security implementation, please refer to the API documentation or contact the development team.
