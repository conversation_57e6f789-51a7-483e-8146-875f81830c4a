<?php

namespace App\Services;

use App\Models\ForexBooking;
use App\Models\ForexBookingReservation;
use App\Models\ForexAccount;
use App\Models\ForexRate;
use App\Models\ForexTransaction;

use App\Models\User;
use App\Services\ForexWalletService;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ForexBookingService
{
    protected $walletService;

    public function __construct(ForexWalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    /**
     * Create a new forex booking with rate calculations
     */
    public function createBooking(array $data, int $adminId): ForexBooking
    {
        return DB::transaction(function () use ($data, $adminId) {
            $activeRate = ForexRate::getActiveRate();
            if (!$activeRate) {
                throw new \Exception('No active forex rate found');
            }

            // Create the booking
            $booking = new ForexBooking();
            $booking->fill($data);
            $booking->calculateTotals(
                $activeRate->cbn_rate,
                $activeRate->parallel_rate,
                $activeRate->markup_percentage,
                $activeRate->cbn_sell_rate,
                $activeRate->parallel_sell_rate,
                $activeRate->sell_markup_percentage
            );
            $booking->initiated_by = $adminId;
            $booking->status = 'pending';
            $booking->save();

            // Create booking transactions (deduct from target account)
            $this->createBookingTransactions($booking, $adminId);

            // Send booking confirmation email
            $this->sendForexBookingConfirmationEmail($booking);

            // Log the booking creation
            Log::info('Forex booking created', [
                'booking_id' => $booking->id,
                'reference' => $booking->booking_reference,
                'admin_id' => $adminId,
                'transaction_type' => $booking->transaction_type,
                'amount' => $booking->amount,
                'currency' => $booking->currency
            ]);

            return $booking;
        });
    }

    /**
     * Complete a forex booking and process all transactions
     */
    public function completeBooking(ForexBooking $booking, int $adminId, ?string $notes = null): void
    {
        if ($booking->status !== 'pending') {
            throw new \Exception('Booking is not in pending status');
        }

        DB::transaction(function () use ($booking, $adminId, $notes) {
            // Update booking status
            $booking->update([
                'status' => 'completed',
                'completed_by' => $adminId,
                'completed_at' => now(),
                'status_notes' => $notes,
            ]);

            // Process the actual exchange transactions (credit NGN accounts, etc.)
            $this->processBookingTransactions($booking, $adminId);

            // Handle wallet funding if payment method is wallet
            if ($booking->isWalletPayment()) {
                $this->walletService->fundWalletFromBooking($booking, $adminId);
            }

            // Send completion email
            $this->sendForexBookingCompletionEmail($booking, $notes);

            // Log the completion
            Log::info('Forex booking completed', [
                'booking_id' => $booking->id,
                'reference' => $booking->booking_reference,
                'admin_id' => $adminId
            ]);
        });
    }

    /**
     * Cancel a forex booking
     */
    public function cancelBooking(ForexBooking $booking, int $adminId, string $reason): void
    {
        if ($booking->status !== 'pending') {
            throw new \Exception('Booking is not in pending status');
        }

        DB::transaction(function () use ($booking, $adminId, $reason) {
            // Update booking status
            $booking->update([
                'status' => 'cancelled',
                'completed_by' => $adminId,
                'completed_at' => now(),
                'status_notes' => $reason,
            ]);

            // Create credit transactions for cancellation (refund)
            $this->createCancellationTransactions($booking, $adminId);

            // Log the cancellation
            Log::info('Forex booking cancelled', [
                'booking_id' => $booking->id,
                'reference' => $booking->booking_reference,
                'reason' => $reason,
                'admin_id' => $adminId
            ]);
        });
    }

    /**
     * Process all transactions for a completed booking
     */
    private function processBookingTransactions(ForexBooking $booking, int $adminId): void
    {
        if ($booking->transaction_type === 'buying') {
            $this->processBuyingTransaction($booking, $adminId);
        } else {
            $this->processSellingTransaction($booking, $adminId);
        }
    }

    /**
     * Process buying transaction (NGN to USD)
     * Business Logic: Keep USD debited (already done), credit NGN to CBN and Difference accounts
     */
    private function processBuyingTransaction(ForexBooking $booking, int $adminId): void
    {
        $accounts = $this->getRequiredAccounts();

        // Credit CBN account with cbn_total + markup_amount
        $cbnAmount = $booking->cbn_total + $booking->markup_amount;
        $accounts['cbn']->credit(
            $cbnAmount,
            "NGN payment received - {$booking->booking_reference} (CBN portion)",
            $adminId,
            $booking->id,
            [
                'transaction_subtype' => 'payment_received',
                'booking_type' => 'buying_usd',
                'client_name' => $booking->client_name,
                'client_email' => $booking->client_email,
                'payment_component' => 'cbn_plus_markup'
            ]
        );

        // Credit Difference account with difference_amount
        if ($booking->difference_amount > 0) {
            $accounts['difference']->credit(
                $booking->difference_amount,
                "NGN payment received - {$booking->booking_reference} (Difference portion)",
                $adminId,
                $booking->id,
                [
                    'transaction_subtype' => 'payment_received',
                    'booking_type' => 'buying_usd',
                    'client_name' => $booking->client_name,
                    'client_email' => $booking->client_email,
                    'payment_component' => 'difference'
                ]
            );
        }

        // Mark all reservations as completed and their corresponding transactions
        $reservations = $booking->reservations()->where('status', 'reserved')->get();
        foreach ($reservations as $reservation) {
            // Mark reservation as completed
            $reservation->update(['status' => 'completed']);

            // Mark the corresponding booked transaction as completed
            if ($reservation->transaction) {
                $reservation->transaction->update(['is_completed' => true]);
            }
        }
    }

    /**
     * Process selling transaction (USD to NGN)
     * Business Logic: Keep NGN debited (already done), credit USD to USD account
     */
    private function processSellingTransaction(ForexBooking $booking, int $adminId): void
    {
        $accounts = $this->getRequiredAccounts();

        // Credit USD to USD account (customer's USD payment)
        $accounts['usd']->credit(
            $booking->amount,
            "USD payment received - {$booking->booking_reference}",
            $adminId,
            $booking->id,
            [
                'transaction_subtype' => 'payment_received',
                'booking_type' => 'selling_usd',
                'client_name' => $booking->client_name,
                'client_email' => $booking->client_email,
                'payment_component' => 'usd_payment'
            ]
        );

        // Mark all reservations as completed and their corresponding transactions
        $reservations = $booking->reservations()->where('status', 'reserved')->get();
        foreach ($reservations as $reservation) {
            // Mark reservation as completed
            $reservation->update(['status' => 'completed']);

            // Mark the corresponding booked transaction as completed
            if ($reservation->transaction) {
                $reservation->transaction->update(['is_completed' => true]);
            }
        }
    }





    /**
     * Get required forex accounts
     */
    private function getRequiredAccounts(): array
    {
        $accounts = [
            'usd' => ForexAccount::byType('USD')->first(),
            'cbn' => ForexAccount::byType('CBN')->first(),
            'difference' => ForexAccount::byType('Difference')->first(),
            'investment' => ForexAccount::byType('Investment')->first(),
        ];

        foreach ($accounts as $type => $account) {
            if (!$account) {
                throw new \Exception("Required {$type} account not found");
            }
        }

        return $accounts;
    }

    /**
     * Get NGN accounts in priority order for USD to NGN transactions
     */
    private function getNgnAccountsInPriorityOrder(): array
    {
        $accounts = $this->getRequiredAccounts();
        return [
            $accounts['cbn'],
            $accounts['difference'],
            $accounts['investment']
        ];
    }

    /**
     * Distribute NGN reservation across multiple accounts in priority order
     */
    private function distributeNgnReservation(float $totalAmount): array
    {
        $accounts = $this->getNgnAccountsInPriorityOrder();
        $distribution = [];
        $remainingAmount = $totalAmount;

        foreach ($accounts as $account) {
            if ($remainingAmount <= 0) {
                break;
            }

            $availableBalance = $account->available_balance;
            $toReserve = min($remainingAmount, $availableBalance);

            if ($toReserve > 0) {
                $distribution[] = [
                    'account' => $account,
                    'amount' => $toReserve,
                    'priority' => count($distribution) + 1
                ];
                $remainingAmount -= $toReserve;
            }
        }

        if ($remainingAmount > 0) {
            throw new \Exception(
                'Insufficient NGN balance across all accounts. Required: ' .
                number_format($totalAmount, 2) . ' NGN, Available: ' .
                number_format($totalAmount - $remainingAmount, 2) . ' NGN'
            );
        }

        return $distribution;
    }

    /**
     * Calculate pending amount for a booking
     */
    private function calculatePendingAmount(ForexBooking $booking): float
    {
        if ($booking->transaction_type === 'buying') {
            // NGN to USD: We're selling USD, so reserve USD amount
            return $booking->amount;
        } else {
            // USD to NGN: We're selling NGN, so reserve NGN amount (customer total)
            return $booking->customer_total;
        }
    }

    /**
     * Validate booking data before creation
     */
    public function validateBookingData(array $data): array
    {
        $errors = [];

        // Check if target account exists and is active (still needed for form validation)
        if (isset($data['target_account_id'])) {
            $targetAccount = ForexAccount::find($data['target_account_id']);
            if (!$targetAccount || !$targetAccount->is_active) {
                $errors[] = 'Selected target account is not available.';
            }
        }

        // Validate sufficient balances based on transaction type
        $activeRate = ForexRate::getActiveRate();
        if ($activeRate && isset($data['amount']) && isset($data['transaction_type'])) {
            // Create a temporary booking to calculate required amounts
            $tempBooking = new ForexBooking();
            $tempBooking->fill($data);
            $tempBooking->calculateTotals(
                $activeRate->cbn_rate,
                $activeRate->parallel_rate,
                $activeRate->markup_percentage,
                $activeRate->cbn_sell_rate,
                $activeRate->parallel_sell_rate,
                $activeRate->sell_markup_percentage
            );

            // Validate account balances based on transaction type
            if ($data['transaction_type'] === 'buying') {
                // NGN to USD: Check USD account balance
                $usdAccount = ForexAccount::byType('USD')->first();
                if (!$usdAccount || $usdAccount->available_balance < $tempBooking->amount) {
                    $errors[] = 'Booking cannot be completed at the moment. Please contact us.';
                    Log::info('USD account validation failed', [
                        'available_balance' => $usdAccount->available_balance ?? 0,
                        'required_amount' => $tempBooking->amount,
                        'currency' => 'USD'
                    ]);
                }
            } else {
                // USD to NGN: Check aggregated NGN balance across accounts
                try {
                    $this->distributeNgnReservation($tempBooking->customer_total);
                } catch (\Exception $e) {
                    $errors[] = 'Booking cannot be completed at the moment. Please contact us.';
                    Log::info('NGN accounts validation failed', [
                        'required_amount' => $tempBooking->customer_total,
                        'currency' => 'NGN',
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        // Check if user exists for user/merchant bookings
        if (in_array($data['client_type'] ?? '', ['user', 'merchant'])) {
            $user = User::find($data['user_id'] ?? null);
            if (!$user || $user->status != 1) {
                $errors[] = 'Invalid or inactive user';
            }
        }

        // Check if active rate exists
        if (!ForexRate::getActiveRate()) {
            $errors[] = 'No active forex rate found';
        }

        return $errors;
    }

    /**
     * Create booking transactions and reservations
     */
    private function createBookingTransactions(ForexBooking $booking, int $adminId): void
    {
        if ($booking->transaction_type === 'buying') {
            // NGN to USD: Reserve USD from USD account
            $this->createSingleAccountReservation($booking, $adminId);
        } else {
            // USD to NGN: Reserve NGN across multiple accounts
            $this->createMultiAccountReservation($booking, $adminId);
        }
    }

    /**
     * Create single account reservation (for NGN to USD)
     */
    private function createSingleAccountReservation(ForexBooking $booking, int $adminId): void
    {
        $targetAccount = $booking->targetAccount;
        $amountToDeduct = $booking->amount;

        if ($targetAccount->available_balance < $amountToDeduct) {
            throw new \Exception('Insufficient balance in target account. Available: ' .
                number_format($targetAccount->available_balance, 2) . ' ' . $targetAccount->currency_code .
                ', Required: ' . number_format($amountToDeduct, 2) . ' ' . $targetAccount->currency_code);
        }

        // Create booked transaction (deduct from account)
        $bookedTransaction = $targetAccount->debit(
            $amountToDeduct,
            "Booking created - {$booking->booking_reference}",
            $adminId,
            $booking->id,
            [
                'transaction_subtype' => 'booked',
                'booking_type' => 'buying_usd',
                'client_name' => $booking->client_name
            ]
        );

        // Update transaction subtype and completion status
        $bookedTransaction->update([
            'transaction_subtype' => 'booked',
            'is_completed' => false
        ]);

        // Create reservation record
        ForexBookingReservation::create([
            'forex_booking_id' => $booking->id,
            'forex_account_id' => $targetAccount->id,
            'reserved_amount' => $amountToDeduct,
            'status' => 'reserved',
            'forex_transaction_id' => $bookedTransaction->id,
            'metadata' => [
                'transaction_type' => 'buying',
                'currency' => $booking->currency,
                'priority' => 1
            ]
        ]);
    }

    /**
     * Create multi-account reservation (for USD to NGN)
     */
    private function createMultiAccountReservation(ForexBooking $booking, int $adminId): void
    {
        $totalAmount = $booking->customer_total;
        $distribution = $this->distributeNgnReservation($totalAmount);

        foreach ($distribution as $item) {
            $account = $item['account'];
            $amount = $item['amount'];
            $priority = $item['priority'];

            // Create booked transaction (debit from account)
            $bookedTransaction = $account->debit(
                $amount,
                "Booking created - {$booking->booking_reference} (Part {$priority})",
                $adminId,
                $booking->id,
                [
                    'transaction_subtype' => 'booked',
                    'booking_type' => 'selling_usd',
                    'client_name' => $booking->client_name,
                    'account_priority' => $priority,
                    'total_accounts_used' => count($distribution)
                ]
            );

            // Update transaction subtype and completion status
            $bookedTransaction->update([
                'transaction_subtype' => 'booked',
                'is_completed' => false
            ]);

            // Create reservation record
            ForexBookingReservation::create([
                'forex_booking_id' => $booking->id,
                'forex_account_id' => $account->id,
                'reserved_amount' => $amount,
                'status' => 'reserved',
                'forex_transaction_id' => $bookedTransaction->id,
                'metadata' => [
                    'transaction_type' => 'selling',
                    'currency' => $booking->currency,
                    'priority' => $priority,
                    'account_type' => $account->account_type,
                    'total_accounts_used' => count($distribution)
                ]
            ]);
        }

        Log::info('Multi-account booking reservation created', [
            'booking_id' => $booking->id,
            'booking_reference' => $booking->booking_reference,
            'total_amount' => $totalAmount,
            'accounts_used' => count($distribution),
            'distribution' => array_map(function($item) {
                return [
                    'account_type' => $item['account']->account_type,
                    'amount' => $item['amount'],
                    'priority' => $item['priority']
                ];
            }, $distribution)
        ]);
    }



    /**
     * Create cancellation transactions for multi-account reservations
     */
    private function createCancellationTransactions(ForexBooking $booking, int $adminId): void
    {
        // Get all reservations for this booking
        $reservations = $booking->reservations()->where('status', 'reserved')->get();

        if ($reservations->isEmpty()) {
            throw new \Exception("No active reservations found for booking {$booking->booking_reference}");
        }

        $totalRefunded = 0;
        $cancellationTransactions = [];

        foreach ($reservations as $reservation) {
            $account = $reservation->account;
            $refundAmount = $reservation->reserved_amount;

            // Create cancellation transaction (credit - refund)
            $cancellationTransaction = $account->credit(
                $refundAmount,
                "Booking cancelled - refund - {$booking->booking_reference}",
                $adminId,
                $booking->id,
                [
                    'transaction_subtype' => 'cancelled_refund',
                    'booking_type' => $booking->transaction_type === 'buying' ? 'buying_usd' : 'selling_usd',
                    'client_name' => $booking->client_name,
                    'client_email' => $booking->client_email,
                    'cancellation_reason' => $booking->status_notes,
                    'account_type' => $account->account_type,
                    'reservation_id' => $reservation->id
                ]
            );

            // Update cancellation transaction details
            $cancellationTransaction->update([
                'transaction_subtype' => 'cancelled_refund',
                'related_transaction_id' => $reservation->forex_transaction_id
            ]);

            // Mark the original booked transaction as completed (cancelled)
            if ($reservation->transaction) {
                $reservation->transaction->update([
                    'is_completed' => true,
                    'related_transaction_id' => $cancellationTransaction->id
                ]);
            }

            // Mark reservation as cancelled
            $reservation->update(['status' => 'cancelled']);

            $totalRefunded += $refundAmount;
            $cancellationTransactions[] = $cancellationTransaction->id;
        }

        Log::info('Multi-account booking cancellation processed', [
            'booking_id' => $booking->id,
            'booking_reference' => $booking->booking_reference,
            'reservations_cancelled' => $reservations->count(),
            'total_refunded' => $totalRefunded,
            'cancellation_transaction_ids' => $cancellationTransactions,
            'accounts_affected' => $reservations->pluck('account.account_type')->toArray()
        ]);
    }

    /**
     * Send forex booking confirmation email using direct SendMail approach
     */
    private function sendForexBookingConfirmationEmail(ForexBooking $booking): void
    {
        try {
            $basic = basicControl();
            $email_from = $basic->sender_email;

            // Build email message using template
            $templateObj = \App\Models\NotificationTemplate::where('template_key', 'FOREX_BOOKING_CONFIRMATION')
                ->where('notify_for', 0)
                ->first();

            if (!$templateObj || !$templateObj->status['mail']) {
                Log::warning('Forex booking confirmation template not found or disabled', [
                    'booking_id' => $booking->id,
                    'template_found' => !!$templateObj,
                    'mail_enabled' => $templateObj ? $templateObj->status['mail'] : false
                ]);
                return;
            }

            // Replace template placeholders
            $message = $templateObj->email;
            $totalToPayIntoCBNAccount = (float) $booking->customer_total - (float) $booking->difference_amount;
            $replacements = [
                '[[client_name]]' => $booking->client_name,
                '[[booking_reference]]' => $booking->booking_reference,
                '[[transaction_type]]' => ucfirst($booking->transaction_type),
                '[[amount]]' => number_format($booking->amount, 2),
                '[[currency]]' => $booking->currency,
                '[[customer_rate]]' => number_format($booking->customer_rate, 2),
                '[[customer_total]]' => number_format($booking->customer_total, 2),
                '[[cbn_total]]' => number_format($booking->cbn_total, 2),
                '[[parallel_total]]' => number_format($booking->parallel_total, 2),
                '[[difference_total]]' => number_format($booking->difference_amount, 2),
                '[[total_to_pay_into_cbn_account]]' => number_format($totalToPayIntoCBNAccount, 2),
                '[[status]]' => ucfirst($booking->status),
                '[[payment_instructions]]' => $booking->payment_instructions ?? 'Please contact support for payment instructions.',
            ];

            foreach ($replacements as $placeholder => $value) {
                $message = str_replace($placeholder, $value, $message);
            }

            // Wrap in email template
            $email_body = $basic->email_description;
            $finalMessage = str_replace("[[name]]", $booking->client_name, $email_body);
            $finalMessage = str_replace("[[message]]", $message, $finalMessage);

            $subject = str_replace('[[booking_reference]]', $booking->booking_reference, $templateObj->subject);

            // Create email log entry
            $emailLog = \App\Models\ForexEmailLog::createLog(
                $booking->id,
                $booking->client_email,
                $subject,
                $finalMessage,
                'booking_confirmation'
            );

            // Send email directly using SendMail (same as password reset)
            \Illuminate\Support\Facades\Mail::to($booking->client_email)->queue(new \App\Mail\SendMail($email_from, $subject, $finalMessage));

            // Mark email log as sent and update booking
            $emailLog->markAsSent();
            $booking->update(['email_sent' => true]);

            Log::info('Forex booking confirmation email sent', [
                'booking_id' => $booking->id,
                'recipient' => $booking->client_email,
                'email_log_id' => $emailLog->id
            ]);

        } catch (\Exception $e) {
            // Mark email log as failed if it was created
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('Failed to send forex booking confirmation email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send forex booking completion email using direct SendMail approach
     */
    private function sendForexBookingCompletionEmail(ForexBooking $booking, string $notes = null): void
    {
        try {
            $basic = basicControl();
            $email_from = $basic->sender_email;

            // Build email message using template
            $templateObj = \App\Models\NotificationTemplate::where('template_key', 'FOREX_BOOKING_COMPLETION')
                ->where('notify_for', 0)
                ->first();

            if (!$templateObj || !$templateObj->status['mail']) {
                Log::warning('Forex booking completion template not found or disabled', [
                    'booking_id' => $booking->id
                ]);
                return;
            }

            // Replace template placeholders
            $message = $templateObj->email;
            $replacements = [
                '[[client_name]]' => $booking->client_name,
                '[[booking_reference]]' => $booking->booking_reference,
                '[[transaction_type]]' => ucfirst($booking->transaction_type),
                '[[amount]]' => number_format($booking->amount, 2),
                '[[currency]]' => $booking->currency,
                '[[customer_rate]]' => number_format($booking->customer_rate, 2),
                '[[customer_total]]' => number_format($booking->customer_total, 2),
                '[[status]]' => ucfirst($booking->status),
                '[[completion_date]]' => $booking->updated_at->format('M d, Y H:i'),
                '[[completion_message]]' => $notes ?? 'Your forex booking has been successfully completed.',
            ];

            foreach ($replacements as $placeholder => $value) {
                $message = str_replace($placeholder, $value, $message);
            }

            // Wrap in email template
            $email_body = $basic->email_description;
            $finalMessage = str_replace("[[name]]", $booking->client_name, $email_body);
            $finalMessage = str_replace("[[message]]", $message, $finalMessage);

            $subject = str_replace('[[booking_reference]]', $booking->booking_reference, $templateObj->subject);

            // Create email log entry
            $emailLog = \App\Models\ForexEmailLog::createLog(
                $booking->id,
                $booking->client_email,
                $subject,
                $finalMessage,
                'completion_notification'
            );

            // Send email directly using SendMail
            \Illuminate\Support\Facades\Mail::to($booking->client_email)->queue(new \App\Mail\SendMail($email_from, $subject, $finalMessage));

            // Mark email log as sent
            $emailLog->markAsSent();

            Log::info('Forex booking completion email sent', [
                'booking_id' => $booking->id,
                'recipient' => $booking->client_email,
                'email_log_id' => $emailLog->id
            ]);

        } catch (\Exception $e) {
            // Mark email log as failed if it was created
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('Failed to send forex booking completion email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send forex payment reminder email using direct SendMail approach
     */
    public function sendForexPaymentReminderEmail(ForexBooking $booking, string $reminderMessage = null): void
    {
        try {
            if ($booking->status !== 'pending') {
                throw new \InvalidArgumentException('Can only send payment reminders for pending bookings');
            }

            $basic = basicControl();
            $email_from = $basic->sender_email;

            // Build email message using template
            $templateObj = \App\Models\NotificationTemplate::where('template_key', 'FOREX_PAYMENT_REMINDER')
                ->where('notify_for', 0)
                ->first();

            if (!$templateObj || !$templateObj->status['mail']) {
                Log::warning('Forex payment reminder template not found or disabled', [
                    'booking_id' => $booking->id
                ]);
                return;
            }

            // Replace template placeholders
            $message = $templateObj->email;
            $replacements = [
                '[[client_name]]' => $booking->client_name,
                '[[booking_reference]]' => $booking->booking_reference,
                '[[transaction_type]]' => ucfirst($booking->transaction_type),
                '[[amount]]' => number_format($booking->amount, 2),
                '[[currency]]' => $booking->currency,
                '[[customer_rate]]' => number_format($booking->customer_rate, 2),
                '[[customer_total]]' => number_format($booking->customer_total, 2),
                '[[status]]' => ucfirst($booking->status),
                '[[created_date]]' => $booking->created_at->format('M d, Y H:i'),
                '[[reminder_message]]' => $reminderMessage ?? 'This is a friendly reminder about your pending forex booking.',
            ];

            foreach ($replacements as $placeholder => $value) {
                $message = str_replace($placeholder, $value, $message);
            }

            // Wrap in email template
            $email_body = $basic->email_description;
            $finalMessage = str_replace("[[name]]", $booking->client_name, $email_body);
            $finalMessage = str_replace("[[message]]", $message, $finalMessage);

            $subject = str_replace('[[booking_reference]]', $booking->booking_reference, $templateObj->subject);

            // Create email log entry
            $emailLog = \App\Models\ForexEmailLog::createLog(
                $booking->id,
                $booking->client_email,
                $subject,
                $finalMessage,
                'payment_reminder'
            );

            // Send email directly using SendMail
            \Illuminate\Support\Facades\Mail::to($booking->client_email)->queue(new \App\Mail\SendMail($email_from, $subject, $finalMessage));

            // Mark email log as sent
            $emailLog->markAsSent();

            Log::info('Forex payment reminder email sent', [
                'booking_id' => $booking->id,
                'recipient' => $booking->client_email,
                'email_log_id' => $emailLog->id
            ]);

        } catch (\Exception $e) {
            // Mark email log as failed if it was created
            if (isset($emailLog)) {
                $emailLog->markAsFailed($e->getMessage());
            }

            Log::error('Failed to send forex payment reminder email', [
                'booking_id' => $booking->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send bulk payment reminders for pending bookings
     */
    public function sendBulkPaymentReminders(array $bookingIds, string $reminderMessage = null): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $bookings = ForexBooking::whereIn('id', $bookingIds)
            ->where('status', 'pending')
            ->get();

        foreach ($bookings as $booking) {
            try {
                $this->sendForexPaymentReminderEmail($booking, $reminderMessage);
                $results['success']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = "Booking {$booking->booking_reference}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Schedule automatic payment reminders for overdue bookings
     */
    public function scheduleAutomaticReminders(): array
    {
        $results = [
            'reminders_sent' => 0,
            'errors' => []
        ];

        // Get bookings that are pending for more than 24 hours and haven't received a reminder in the last 24 hours
        $overdueBookings = ForexBooking::pending()
            ->where('created_at', '<', now()->subDay())
            ->whereDoesntHave('emailLogs', function ($query) {
                $query->where('email_type', 'payment_reminder')
                    ->where('created_at', '>', now()->subDay());
            })
            ->get();

        foreach ($overdueBookings as $booking) {
            try {
                $daysPending = $booking->created_at->diffInDays(now());
                $reminderMessage = "Your forex booking has been pending for {$daysPending} " .
                    ($daysPending === 1 ? 'day' : 'days') . ". Please complete your payment to avoid cancellation.";

                $this->sendForexPaymentReminderEmail($booking, $reminderMessage);
                $results['reminders_sent']++;
            } catch (\Exception $e) {
                $results['errors'][] = "Booking {$booking->booking_reference}: " . $e->getMessage();
            }
        }

        return $results;
    }


}
