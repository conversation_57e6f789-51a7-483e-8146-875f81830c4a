<?php

/**
 * JSON Parsing Fix Verification Script
 * 
 * This script specifically tests the fix for the JSON parsing issue
 * where $request->all() was returning empty arrays for JSON requests.
 */

class JsonFixVerifier
{
    private $baseUrl;

    public function __construct($baseUrl = 'http://currency.test')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Test the debug endpoint to verify JSON parsing
     */
    public function testDebugEndpoint()
    {
        echo "=== Testing Debug Endpoint ===\n";
        
        $url = $this->baseUrl . '/api/debug-request';
        $testData = [
            'publicKey' => 'test_public_key_12345',
            'secretKey' => 'test_secret_key_67890',
            'testField' => 'test_value',
            'number' => 123,
            'boolean' => true
        ];

        echo "Sending JSON data to debug endpoint...\n";
        echo "URL: $url\n";
        echo "Data: " . json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

        $response = $this->makeJsonRequest('POST', $url, $testData);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if (isset($result['status']) && $result['status'] === 'success') {
                echo "✅ Debug endpoint responded successfully!\n\n";
                
                // Check if request_all contains our data
                if (!empty($result['request_all']) && 
                    isset($result['request_all']['publicKey']) && 
                    $result['request_all']['publicKey'] === 'test_public_key_12345') {
                    
                    echo "✅ JSON parsing is working! request->all() contains expected data:\n";
                    echo json_encode($result['request_all'], JSON_PRETTY_PRINT) . "\n\n";
                    
                    echo "Additional debug info:\n";
                    echo "- Content-Type: " . ($result['content_type'] ?? 'not set') . "\n";
                    echo "- isJson(): " . ($result['is_json'] ? 'true' : 'false') . "\n";
                    echo "- wantsJson(): " . ($result['wants_json'] ? 'true' : 'false') . "\n";
                    echo "- Method: " . ($result['method'] ?? 'unknown') . "\n\n";
                    
                    return true;
                } else {
                    echo "❌ JSON parsing still not working. request->all() is:\n";
                    echo json_encode($result['request_all'], JSON_PRETTY_PRINT) . "\n\n";
                    
                    echo "Raw response:\n";
                    echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
                    return false;
                }
            } else {
                echo "❌ Debug endpoint failed. Response:\n";
                echo $response . "\n\n";
                return false;
            }
        } else {
            echo "❌ No response from debug endpoint\n\n";
            return false;
        }
    }

    /**
     * Test the actual authenticate endpoint
     */
    public function testAuthenticateEndpoint()
    {
        echo "=== Testing Authenticate Endpoint ===\n";
        
        $url = $this->baseUrl . '/api/authenticate';
        $testData = [
            'publicKey' => 'test_public_key_12345',
            'secretKey' => 'test_secret_key_67890'
        ];

        echo "Testing authenticate endpoint with JSON data...\n";
        echo "URL: $url\n";
        echo "Data: " . json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

        $response = $this->makeJsonRequest('POST', $url, $testData);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if (isset($result['status'])) {
                if ($result['status'] === 'failed') {
                    // Check the error message to determine if JSON was parsed
                    if (strpos($result['message'], 'required') !== false) {
                        echo "❌ JSON parsing still not working. Got validation error about required fields:\n";
                        echo "Message: " . $result['message'] . "\n\n";
                        return false;
                    } elseif (strpos($result['message'], 'Invalid API credentials') !== false || 
                              strpos($result['message'], 'do not match') !== false) {
                        echo "✅ JSON parsing is working! Got expected authentication error:\n";
                        echo "Message: " . $result['message'] . "\n\n";
                        return true;
                    } else {
                        echo "⚠️  Got unexpected error message:\n";
                        echo "Message: " . $result['message'] . "\n\n";
                        return false;
                    }
                } elseif ($result['status'] === 'success') {
                    echo "✅ JSON parsing is working! Authentication succeeded:\n";
                    echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
                    return true;
                }
            }
            
            echo "❌ Unexpected response format:\n";
            echo $response . "\n\n";
            return false;
        } else {
            echo "❌ No response from authenticate endpoint\n\n";
            return false;
        }
    }

    /**
     * Test with the exact cURL command from the user
     */
    public function testExactUserCommand()
    {
        echo "=== Testing Exact User cURL Command ===\n";
        
        $url = $this->baseUrl . '/api/authenticate';
        $testData = [
            'publicKey' => '12345',
            'secretKey' => '2d05d381bb3cf03fb6c496d5f0ba05a369c956b5'
        ];

        echo "Testing with exact data from user's cURL command...\n";
        echo "URL: $url\n";
        echo "Data: " . json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

        $response = $this->makeJsonRequest('POST', $url, $testData);
        
        if ($response) {
            $result = json_decode($response, true);
            
            echo "Response received:\n";
            echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
            
            if (isset($result['status']) && $result['status'] === 'failed') {
                if (strpos($result['message'], 'required') !== false) {
                    echo "❌ Still getting validation errors - JSON parsing not fixed\n\n";
                    return false;
                } else {
                    echo "✅ JSON parsing is working - got authentication error as expected\n\n";
                    return true;
                }
            } elseif (isset($result['status']) && $result['status'] === 'success') {
                echo "✅ JSON parsing is working - authentication succeeded\n\n";
                return true;
            }
            
            return false;
        } else {
            echo "❌ No response received\n\n";
            return false;
        }
    }

    /**
     * Make a JSON request
     */
    private function makeJsonRequest($method, $url, $data = null)
    {
        $curl = curl_init();
        
        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_VERBOSE => false
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        if (curl_errno($curl)) {
            echo "cURL Error: " . curl_error($curl) . "\n";
            curl_close($curl);
            return false;
        }
        
        curl_close($curl);
        
        echo "HTTP Status: $httpCode\n";
        
        return $response;
    }
}

// Run the verification
echo "JSON Parsing Fix Verification\n";
echo "============================\n\n";

$verifier = new JsonFixVerifier('http://currency.test'); // Adjust URL as needed

echo "This script will test if the JSON parsing issue has been resolved.\n";
echo "The issue was: \$request->all() returning empty array for JSON requests.\n\n";

$allPassed = true;

// Test 1: Debug endpoint
echo "Test 1: Debug endpoint to verify JSON parsing\n";
if (!$verifier->testDebugEndpoint()) {
    $allPassed = false;
}

// Test 2: Authenticate endpoint
echo "Test 2: Authenticate endpoint with test data\n";
if (!$verifier->testAuthenticateEndpoint()) {
    $allPassed = false;
}

// Test 3: Exact user command
echo "Test 3: Exact user cURL command data\n";
if (!$verifier->testExactUserCommand()) {
    $allPassed = false;
}

echo "============================\n";
if ($allPassed) {
    echo "✅ ALL TESTS PASSED! JSON parsing issue has been resolved.\n";
    echo "\nYou can now use the authenticate endpoint with JSON data:\n";
    echo "curl --location 'http://currency.test/api/authenticate' \\\n";
    echo "--header 'Content-Type: application/json' \\\n";
    echo "--data '{\n";
    echo "    \"publicKey\": \"your_public_key\",\n";
    echo "    \"secretKey\": \"your_secret_key\"\n";
    echo "}'\n";
} else {
    echo "❌ Some tests failed. JSON parsing issue may not be fully resolved.\n";
    echo "\nCheck the middleware configuration and ensure:\n";
    echo "1. ValidateRequestData middleware skips JSON requests\n";
    echo "2. API routes have proper middleware stack\n";
    echo "3. Laravel is properly parsing JSON request bodies\n";
}

echo "\nNote: Remember to remove the debug endpoint (/api/debug-request) in production!\n";
