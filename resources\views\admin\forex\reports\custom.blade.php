@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.index') }}">
                                    @lang('Forex Reports')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Custom Report Builder')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Custom Report Builder')</h1>
                    <p class="page-header-text">@lang('Build custom forex reports with advanced filtering and analytics options')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.reports.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Reports')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Report Builder -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Report Configuration')</h4>
                        <span class="badge bg-soft-primary text-primary">
                            <i class="bi-sliders"></i> @lang('Custom Builder')
                        </span>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.forex.reports.generate') }}" method="POST" id="customReportForm">
                            @csrf

                            <!-- Report Type Selection -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Report Type')</h6>
                                    <div class="row">
                                        <div class="col-sm-6 col-lg-3 mb-3">
                                            <div class="form-check form-check-card">
                                                <input class="form-check-input" type="radio" name="report_type" id="cbnFocusedReport" value="cbn_focused" checked>
                                                <label class="form-check-label" for="cbnFocusedReport">
                                                    <span class="form-check-card-body">
                                                        <i class="bi-bank2 form-check-card-icon"></i>
                                                        <span class="form-check-card-title">@lang('CBN Focused Report')</span>
                                                        <span class="form-check-card-text">@lang('CBN rates and markup only')</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-lg-3 mb-3">
                                            <div class="form-check form-check-card">
                                                <input class="form-check-input" type="radio" name="report_type" id="bookingsReport" value="bookings">
                                                <label class="form-check-label" for="bookingsReport">
                                                    <span class="form-check-card-body">
                                                        <i class="bi-journal-bookmark form-check-card-icon"></i>
                                                        <span class="form-check-card-title">@lang('Bookings Report')</span>
                                                        <span class="form-check-card-text">@lang('Detailed booking analysis')</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-lg-3 mb-3">
                                            <div class="form-check form-check-card">
                                                <input class="form-check-input" type="radio" name="report_type" id="transactionVolumeReport" value="volume">
                                                <label class="form-check-label" for="transactionVolumeReport">
                                                    <span class="form-check-card-body">
                                                        <i class="bi-bar-chart form-check-card-icon"></i>
                                                        <span class="form-check-card-title">@lang('Transaction Volume')</span>
                                                        <span class="form-check-card-text">@lang('Volume in NGN and USD')</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-lg-3 mb-3">
                                            <div class="form-check form-check-card">
                                                <input class="form-check-input" type="radio" name="report_type" id="performanceReport" value="performance">
                                                <label class="form-check-label" for="performanceReport">
                                                    <span class="form-check-card-body">
                                                        <i class="bi-graph-up form-check-card-icon"></i>
                                                        <span class="form-check-card-title">@lang('Monthly Performance')</span>
                                                        <span class="form-check-card-text">@lang('Performance analytics')</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-lg-3 mb-3">
                                            <div class="form-check form-check-card">
                                                <input class="form-check-input" type="radio" name="report_type" id="buyTransactionsReport" value="buy_transactions">
                                                <label class="form-check-label" for="buyTransactionsReport">
                                                    <span class="form-check-card-body">
                                                        <i class="bi-arrow-up-circle form-check-card-icon"></i>
                                                        <span class="form-check-card-title">@lang('Buy Transactions')</span>
                                                        <span class="form-check-card-text">@lang('NGN to USD transactions')</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-lg-3 mb-3">
                                            <div class="form-check form-check-card">
                                                <input class="form-check-input" type="radio" name="report_type" id="sellTransactionsReport" value="sell_transactions">
                                                <label class="form-check-label" for="sellTransactionsReport">
                                                    <span class="form-check-card-body">
                                                        <i class="bi-arrow-down-circle form-check-card-icon"></i>
                                                        <span class="form-check-card-title">@lang('Sell Transactions')</span>
                                                        <span class="form-check-card-text">@lang('USD to NGN transactions')</span>
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date Range and Frequency -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Date Range & Frequency')</h6>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="dateRangeType" class="form-label">@lang('Report Frequency')</label>
                                    <select class="form-select" name="date_range_type" id="dateRangeType" required>
                                        <option value="daily">@lang('Daily Report')</option>
                                        <option value="weekly">@lang('Weekly Report')</option>
                                        <option value="monthly" selected>@lang('Monthly Report')</option>
                                        <option value="custom">@lang('Custom Date Range')</option>
                                    </select>
                                </div>

                                <!-- Daily Report Fields -->
                                <div class="col-sm-6 col-lg-3 mb-3 date-field daily-field">
                                    <label for="specificDate" class="form-label">@lang('Report Date')</label>
                                    <input type="date" class="form-control" name="specific_date" id="specificDate"
                                           value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}">
                                </div>

                                <!-- Weekly Report Fields -->
                                <div class="col-sm-6 col-lg-3 mb-3 date-field weekly-field" style="display: none;">
                                    <label for="weekStartDate" class="form-label">@lang('Week Start Date')</label>
                                    <input type="date" class="form-control" name="start_date" id="weekStartDate"
                                           value="{{ date('Y-m-d', strtotime('monday this week')) }}">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3 date-field weekly-field" style="display: none;">
                                    <label for="weekEndDate" class="form-label">@lang('Week End Date')</label>
                                    <input type="date" class="form-control" name="end_date" id="weekEndDate"
                                           value="{{ date('Y-m-d', strtotime('sunday this week')) }}">
                                </div>

                                <!-- Monthly Report Fields -->
                                <div class="col-sm-6 col-lg-3 mb-3 date-field monthly-field">
                                    <label for="reportMonth" class="form-label">@lang('Month')</label>
                                    <select class="form-select" name="month" id="reportMonth">
                                        @for($i = 1; $i <= 12; $i++)
                                            <option value="{{ $i }}" {{ $i == date('n') ? 'selected' : '' }}>
                                                {{ date('F', mktime(0, 0, 0, $i, 1)) }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3 date-field monthly-field">
                                    <label for="reportYear" class="form-label">@lang('Year')</label>
                                    <select class="form-select" name="year" id="reportYear">
                                        @for($year = 2020; $year <= date('Y') + 1; $year++)
                                            <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>
                                                {{ $year }}
                                            </option>
                                        @endfor
                                    </select>
                                </div>

                                <!-- Custom Range Fields -->
                                <div class="col-sm-6 col-lg-3 mb-3 date-field custom-field" style="display: none;">
                                    <label for="customStartDate" class="form-label">@lang('Start Date')</label>
                                    <input type="date" class="form-control" name="start_date" id="customStartDate"
                                           value="{{ date('Y-m-01') }}">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3 date-field custom-field" style="display: none;">
                                    <label for="customEndDate" class="form-label">@lang('End Date')</label>
                                    <input type="date" class="form-control" name="end_date" id="customEndDate"
                                           value="{{ date('Y-m-d') }}">
                                </div>
                            </div>

                            <!-- Filters -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Filters & Options')</h6>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="transactionType" class="form-label">@lang('Transaction Type')</label>
                                    <select class="form-select" name="transaction_type" id="transactionType">
                                        <option value="both">@lang('Both Buy & Sell')</option>
                                        <option value="buying">@lang('Buying (NGN to USD)')</option>
                                        <option value="selling">@lang('Selling (USD to NGN)')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="currency" class="form-label">@lang('Currency Focus')</label>
                                    <select class="form-select" name="currency" id="currency">
                                        <option value="both">@lang('Both USD & NGN')</option>
                                        <option value="USD">@lang('USD Only')</option>
                                        <option value="NGN">@lang('NGN Only')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="clientType" class="form-label">@lang('Client Type')</label>
                                    <select class="form-select" name="client_type" id="clientType">
                                        <option value="">@lang('All Client Types')</option>
                                        <option value="user">@lang('Registered Users')</option>
                                        <option value="merchant">@lang('Merchants')</option>
                                        <option value="external">@lang('External Clients')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="status" class="form-label">@lang('Status')</label>
                                    <select class="form-select" name="status" id="status">
                                        <option value="">@lang('All Statuses')</option>
                                        <option value="completed">@lang('Completed')</option>
                                        <option value="pending">@lang('Pending')</option>
                                        <option value="cancelled">@lang('Cancelled')</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Field Selection for Custom Reports -->
                            <div class="row mb-4" id="fieldSelectionSection">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Field Selection & Customization')</h6>
                                    <p class="text-muted small mb-3">@lang('Select which fields to include in your report and customize their display names')</p>
                                </div>

                                <!-- Field Selection Controls -->
                                <div class="col-12 mb-3">
                                    <div class="d-flex gap-2 mb-3">
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllFields">
                                            <i class="bi-check-all me-1"></i> @lang('Select All')
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearAllFields">
                                            <i class="bi-x-square me-1"></i> @lang('Clear All')
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" id="loadFieldPreset">
                                            <i class="bi-bookmark me-1"></i> @lang('Load Preset')
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" id="loadCbnPreset">
                                            <i class="bi-shield-check me-1"></i> @lang('CBN Compliance')
                                        </button>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="row">
                                        <!-- Core Booking Fields -->
                                        <div class="col-md-4">
                                            <h6 class="mb-3 text-primary">@lang('Core Booking Fields')</h6>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="booking_reference" id="field_booking_reference" checked>
                                                <label class="form-check-label" for="field_booking_reference">
                                                    @lang('Booking Reference')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[booking_reference]" placeholder="@lang('Custom label')" value="Booking Reference">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="client_name" id="field_client_name" checked>
                                                <label class="form-check-label" for="field_client_name">
                                                    @lang('Client Name')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[client_name]" placeholder="@lang('Custom label')" value="Client Name">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="client_email" id="field_client_email">
                                                <label class="form-check-label" for="field_client_email">
                                                    @lang('Client Email')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[client_email]" placeholder="@lang('Custom label')" value="Email">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="client_phone" id="field_client_phone">
                                                <label class="form-check-label" for="field_client_phone">
                                                    @lang('Client Phone')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[client_phone]" placeholder="@lang('Custom label')" value="Phone">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="client_type" id="field_client_type">
                                                <label class="form-check-label" for="field_client_type">
                                                    @lang('Client Type')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[client_type]" placeholder="@lang('Custom label')" value="Client Type">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="transaction_type" id="field_transaction_type">
                                                <label class="form-check-label" for="field_transaction_type">
                                                    @lang('Transaction Type')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[transaction_type]" placeholder="@lang('Custom label')" value="Transaction Type">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="currency" id="field_currency">
                                                <label class="form-check-label" for="field_currency">
                                                    @lang('Currency')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[currency]" placeholder="@lang('Custom label')" value="Currency">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="amount" id="field_amount" checked>
                                                <label class="form-check-label" for="field_amount">
                                                    @lang('Amount')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[amount]" placeholder="@lang('Custom label')" value="Amount">
                                            </div>
                                        </div>

                                        <!-- Rate & Pricing Fields -->
                                        <div class="col-md-4">
                                            <h6 class="mb-3 text-success">@lang('Rate & Pricing Fields')</h6>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="cbn_rate" id="field_cbn_rate" checked>
                                                <label class="form-check-label" for="field_cbn_rate">
                                                    @lang('CBN Rate')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[cbn_rate]" placeholder="@lang('Custom label')" value="CBN Rate">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="parallel_rate" id="field_parallel_rate">
                                                <label class="form-check-label" for="field_parallel_rate">
                                                    @lang('Parallel Rate')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[parallel_rate]" placeholder="@lang('Custom label')" value="Parallel Rate">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="markup_percentage" id="field_markup_percentage" checked>
                                                <label class="form-check-label" for="field_markup_percentage">
                                                    @lang('Markup Percentage')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[markup_percentage]" placeholder="@lang('Custom label')" value="Markup %">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="customer_rate" id="field_customer_rate" checked>
                                                <label class="form-check-label" for="field_customer_rate">
                                                    @lang('Customer Rate')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[customer_rate]" placeholder="@lang('Custom label')" value="Final Rate">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="cbn_total" id="field_cbn_total">
                                                <label class="form-check-label" for="field_cbn_total">
                                                    @lang('CBN Total')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[cbn_total]" placeholder="@lang('Custom label')" value="CBN Total">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="parallel_total" id="field_parallel_total">
                                                <label class="form-check-label" for="field_parallel_total">
                                                    @lang('Parallel Total')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[parallel_total]" placeholder="@lang('Custom label')" value="Parallel Total">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="customer_total" id="field_customer_total" checked>
                                                <label class="form-check-label" for="field_customer_total">
                                                    @lang('Customer Total')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[customer_total]" placeholder="@lang('Custom label')" value="Total Amount">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="customer_payment_amount" id="field_customer_payment_amount">
                                                <label class="form-check-label" for="field_customer_payment_amount">
                                                    @lang('Customer Payment Amount')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[customer_payment_amount]" placeholder="@lang('Custom label')" value="Payment Amount">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="markup_amount" id="field_markup_amount">
                                                <label class="form-check-label" for="field_markup_amount">
                                                    @lang('Markup Amount')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[markup_amount]" placeholder="@lang('Custom label')" value="Markup Amount">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="difference_amount" id="field_difference_amount">
                                                <label class="form-check-label" for="field_difference_amount">
                                                    @lang('Difference Amount')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[difference_amount]" placeholder="@lang('Custom label')" value="Difference Amount">
                                            </div>
                                        </div>

                                        <!-- Status & Tracking Fields -->
                                        <div class="col-md-4">
                                            <h6 class="mb-3 text-warning">@lang('Status & Tracking Fields')</h6>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="status" id="field_status" checked>
                                                <label class="form-check-label" for="field_status">
                                                    @lang('Status')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[status]" placeholder="@lang('Custom label')" value="Status">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="status_notes" id="field_status_notes">
                                                <label class="form-check-label" for="field_status_notes">
                                                    @lang('Status Notes')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[status_notes]" placeholder="@lang('Custom label')" value="Status Notes">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="created_at" id="field_created_at" checked>
                                                <label class="form-check-label" for="field_created_at">
                                                    @lang('Date Created')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[created_at]" placeholder="@lang('Custom label')" value="Date Created">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="completed_at" id="field_completed_at">
                                                <label class="form-check-label" for="field_completed_at">
                                                    @lang('Date Completed')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[completed_at]" placeholder="@lang('Custom label')" value="Date Completed">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="initiated_by_name" id="field_initiated_by_name">
                                                <label class="form-check-label" for="field_initiated_by_name">
                                                    @lang('Initiated By')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[initiated_by_name]" placeholder="@lang('Custom label')" value="Initiated By">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="completed_by_name" id="field_completed_by_name">
                                                <label class="form-check-label" for="field_completed_by_name">
                                                    @lang('Completed By')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[completed_by_name]" placeholder="@lang('Custom label')" value="Completed By">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="target_account_name" id="field_target_account_name">
                                                <label class="form-check-label" for="field_target_account_name">
                                                    @lang('Target Account')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[target_account_name]" placeholder="@lang('Custom label')" value="Target Account">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="account_details" id="field_account_details">
                                                <label class="form-check-label" for="field_account_details">
                                                    @lang('Account Details')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[account_details]" placeholder="@lang('Custom label')" value="Account Details">
                                            </div>

                                            <div class="form-check mb-2">
                                                <input class="form-check-input field-checkbox" type="checkbox" name="selected_fields[]" value="payment_instructions" id="field_payment_instructions">
                                                <label class="form-check-label" for="field_payment_instructions">
                                                    @lang('Payment Instructions')
                                                </label>
                                                <input type="text" class="form-control form-control-sm mt-1 field-label" name="field_labels[payment_instructions]" placeholder="@lang('Custom label')" value="Payment Instructions">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Amount Range -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Amount Range & Advanced Filters')</h6>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="minAmount" class="form-label">@lang('Minimum Amount')</label>
                                    <input type="number" class="form-control" name="min_amount" id="minAmount"
                                           step="0.01" min="0" placeholder="@lang('0.00')">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="maxAmount" class="form-label">@lang('Maximum Amount')</label>
                                    <input type="number" class="form-control" name="max_amount" id="maxAmount"
                                           step="0.01" min="0" placeholder="@lang('No limit')">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="amountPreset" class="form-label">@lang('Amount Presets')</label>
                                    <select class="form-select" id="amountPreset">
                                        <option value="">@lang('Custom Range')</option>
                                        <option value="0-100">$0 - $100</option>
                                        <option value="100-500">$100 - $500</option>
                                        <option value="500-1000">$500 - $1,000</option>
                                        <option value="1000-5000">$1,000 - $5,000</option>
                                        <option value="5000-10000">$5,000 - $10,000</option>
                                        <option value="10000+">$10,000+</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="markupRange" class="form-label">@lang('Markup % Range')</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="min_markup" placeholder="Min %" step="0.1" min="0">
                                        <span class="input-group-text">-</span>
                                        <input type="number" class="form-control" name="max_markup" placeholder="Max %" step="0.1" min="0">
                                    </div>
                                </div>
                            </div>

                            <!-- Sorting & Grouping Configuration -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Sorting & Grouping Configuration')</h6>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="sortBy" class="form-label">@lang('Sort By')</label>
                                    <select class="form-select" name="sort_by" id="sortBy">
                                        <option value="created_at">@lang('Date Created')</option>
                                        <option value="amount">@lang('Amount')</option>
                                        <option value="customer_total">@lang('Customer Total')</option>
                                        <option value="markup_amount">@lang('Markup Amount')</option>
                                        <option value="markup_percentage">@lang('Markup Percentage')</option>
                                        <option value="cbn_rate">@lang('CBN Rate')</option>
                                        <option value="client_name">@lang('Client Name')</option>
                                        <option value="status">@lang('Status')</option>
                                        <option value="booking_reference">@lang('Booking Reference')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="sortDirection" class="form-label">@lang('Sort Direction')</label>
                                    <select class="form-select" name="sort_direction" id="sortDirection">
                                        <option value="desc">@lang('Descending (Newest/Highest First)')</option>
                                        <option value="asc">@lang('Ascending (Oldest/Lowest First)')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="groupBy" class="form-label">@lang('Group By')</label>
                                    <select class="form-select" name="group_by" id="groupBy">
                                        <option value="">@lang('No Grouping')</option>
                                        <option value="status">@lang('Status')</option>
                                        <option value="transaction_type">@lang('Transaction Type')</option>
                                        <option value="client_type">@lang('Client Type')</option>
                                        <option value="currency">@lang('Currency')</option>
                                        <option value="date">@lang('Date (Daily)')</option>
                                        <option value="week">@lang('Week')</option>
                                        <option value="month">@lang('Month')</option>
                                        <option value="target_account">@lang('Target Account')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="limitResults" class="form-label">@lang('Limit Results')</label>
                                    <select class="form-select" name="limit_results" id="limitResults">
                                        <option value="">@lang('All Results')</option>
                                        <option value="10">@lang('Top 10')</option>
                                        <option value="25">@lang('Top 25')</option>
                                        <option value="50">@lang('Top 50')</option>
                                        <option value="100">@lang('Top 100')</option>
                                        <option value="500">@lang('Top 500')</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Aggregation & Summary Options -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Aggregation & Summary Options')</h6>
                                </div>
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="mb-2">@lang('Include Calculations')</h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_totals" id="includeTotals" value="1" checked>
                                                <label class="form-check-label" for="includeTotals">
                                                    @lang('Include column totals')
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_averages" id="includeAverages" value="1">
                                                <label class="form-check-label" for="includeAverages">
                                                    @lang('Include column averages')
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_counts" id="includeCounts" value="1" checked>
                                                <label class="form-check-label" for="includeCounts">
                                                    @lang('Include record counts')
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_percentages" id="includePercentages" value="1">
                                                <label class="form-check-label" for="includePercentages">
                                                    @lang('Include percentage breakdowns')
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="mb-2">@lang('Summary Statistics')</h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_min_max" id="includeMinMax" value="1">
                                                <label class="form-check-label" for="includeMinMax">
                                                    @lang('Include min/max values')
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_median" id="includeMedian" value="1">
                                                <label class="form-check-label" for="includeMedian">
                                                    @lang('Include median values')
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_growth_rates" id="includeGrowthRates" value="1">
                                                <label class="form-check-label" for="includeGrowthRates">
                                                    @lang('Include growth rates (period comparison)')
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" name="include_conversion_rates" id="includeConversionRates" value="1">
                                                <label class="form-check-label" for="includeConversionRates">
                                                    @lang('Include conversion rates (pending to completed)')
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Report Options -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Report Options')</h6>
                                </div>
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_charts" id="includeCharts" value="1" checked>
                                        <label class="form-check-label" for="includeCharts">
                                            @lang('Include charts and graphs')
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_summary" id="includeSummary" value="1" checked>
                                        <label class="form-check-label" for="includeSummary">
                                            @lang('Include executive summary')
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_details" id="includeDetails" value="1" checked>
                                        <label class="form-check-label" for="includeDetails">
                                            @lang('Include detailed transactions')
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_trends" id="includeTrends" value="1">
                                        <label class="form-check-label" for="includeTrends">
                                            @lang('Include trend analysis')
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_comparisons" id="includeComparisons" value="1">
                                        <label class="form-check-label" for="includeComparisons">
                                            @lang('Include period comparisons')
                                        </label>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_forecasts" id="includeForecasts" value="1">
                                        <label class="form-check-label" for="includeForecasts">
                                            @lang('Include forecasts and projections')
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Export Options -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="mb-3">@lang('Export Options')</h6>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="exportFormat" class="form-label">@lang('Export Format')</label>
                                    <select class="form-select" name="format" id="exportFormat">
                                        <option value="html">@lang('View Online')</option>
                                        <option value="excel">@lang('Excel Download')</option>
                                        <option value="pdf">@lang('PDF Download')</option>
                                        <option value="csv">@lang('CSV Download')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="reportTitle" class="form-label">@lang('Report Title')</label>
                                    <input type="text" class="form-control" name="report_title" id="reportTitle"
                                           placeholder="@lang('Custom Forex Report')" value="Custom Forex Report">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="reportDescription" class="form-label">@lang('Description')</label>
                                    <input type="text" class="form-control" name="report_description" id="reportDescription"
                                           placeholder="@lang('Report description')">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="emailReport" class="form-label">@lang('Email Report')</label>
                                    <input type="email" class="form-control" name="email_to" id="emailReport"
                                           placeholder="@lang('Optional: Email address')">
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="button" class="btn btn-outline-secondary" id="previewReport">
                                        <i class="bi-eye me-1"></i> @lang('Preview Report')
                                    </button>
                                    <button type="button" class="btn btn-outline-info" id="saveTemplate">
                                        <i class="bi-bookmark me-1"></i> @lang('Save as Template')
                                    </button>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi-file-earmark-bar-graph me-1"></i> @lang('Generate Report')
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Saved Templates -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Saved Report Templates')</h4>
                        <div class="card-header-content">
                            <button type="button" class="btn btn-sm btn-primary" id="refreshTemplates">
                                <i class="bi-arrow-clockwise me-1"></i> @lang('Refresh')
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row" id="templatesContainer">
                            @if($templates->count() > 0)
                                @foreach($templates as $template)
                                    <div class="col-sm-6 col-lg-4 mb-3" data-template-id="{{ $template->id }}">
                                        <div class="card card-sm">
                                            <div class="card-body">
                                                <h6 class="card-title">{{ $template->name }}</h6>
                                                <p class="card-text small text-muted">{{ $template->description ?: 'No description available' }}</p>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="badge bg-soft-info text-info">{{ ucfirst($template->report_type) }}</span>
                                                    <small class="text-muted">Used {{ $template->usage_count }} times</small>
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <button class="btn btn-sm btn-outline-primary load-template" data-template-id="{{ $template->id }}">
                                                        <i class="bi-upload me-1"></i> @lang('Load')
                                                    </button>
                                                    @if($template->created_by === auth('admin')->id() || $template->is_public)
                                                        <button class="btn btn-sm btn-outline-danger delete-template" data-template-id="{{ $template->id }}">
                                                            <i class="bi-trash me-1"></i> @lang('Delete')
                                                        </button>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="col-12">
                                    <div class="text-center py-4">
                                        <div class="mb-3">
                                            <i class="bi-bookmark display-4 text-muted"></i>
                                        </div>
                                        <h5 class="text-muted">@lang('No Templates Found')</h5>
                                        <p class="text-muted">@lang('Create your first template by configuring a report and saving it.')</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Template Modal -->
        <div class="modal fade" id="saveTemplateModal" tabindex="-1" aria-labelledby="saveTemplateModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="saveTemplateModalLabel">@lang('Save Report Template')</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="saveTemplateForm">
                            <div class="mb-3">
                                <label for="templateName" class="form-label">@lang('Template Name') <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="templateName" name="template_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="templateDescription" class="form-label">@lang('Description')</label>
                                <textarea class="form-control" id="templateDescription" name="template_description" rows="3" placeholder="@lang('Optional description for this template')"></textarea>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isPublicTemplate" name="is_public" value="1">
                                <label class="form-check-label" for="isPublicTemplate">
                                    @lang('Make this template available to all users')
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="button" class="btn btn-primary" id="saveTemplateBtn">@lang('Save Template')</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Date range type handling
            $('#dateRangeType').on('change', function() {
                const type = $(this).val();
                $('.date-field').hide();

                switch(type) {
                    case 'daily':
                        $('.daily-field').show();
                        break;
                    case 'weekly':
                        $('.weekly-field').show();
                        break;
                    case 'monthly':
                        $('.monthly-field').show();
                        break;
                    case 'custom':
                        $('.custom-field').show();
                        break;
                }
            });

            // Initialize date fields based on current selection
            $('#dateRangeType').trigger('change');

            // Report type handling for field selection
            $('input[name="report_type"]').on('change', function() {
                const reportType = $(this).val();

                // Reset all checkboxes
                $('.field-checkbox').prop('checked', false);

                // Set default fields based on report type
                switch(reportType) {
                    case 'cbn_focused':
                        $('#field_booking_reference, #field_client_name, #field_amount, #field_cbn_rate, #field_markup_percentage, #field_customer_rate, #field_status, #field_created_at').prop('checked', true);
                        // Hide parallel rate for CBN focused reports
                        $('#field_parallel_rate').closest('.form-check').hide();
                        break;
                    case 'volume':
                        $('#field_booking_reference, #field_amount, #field_customer_total, #field_created_at, #field_status').prop('checked', true);
                        $('#field_parallel_rate').closest('.form-check').show();
                        break;
                    case 'performance':
                        $('#field_booking_reference, #field_amount, #field_cbn_rate, #field_markup_percentage, #field_customer_total, #field_created_at').prop('checked', true);
                        $('#field_parallel_rate').closest('.form-check').show();
                        break;
                    default:
                        $('#field_booking_reference, #field_client_name, #field_amount, #field_customer_rate, #field_customer_total, #field_status, #field_created_at').prop('checked', true);
                        $('#field_parallel_rate').closest('.form-check').show();
                        break;
                }
            });

            // Initialize report type
            $('input[name="report_type"]:checked').trigger('change');

            // Field checkbox handling
            $('.field-checkbox').on('change', function() {
                const labelInput = $(this).closest('.form-check').find('.field-label');
                if ($(this).is(':checked')) {
                    labelInput.show();
                } else {
                    labelInput.hide();
                }
            });

            // Initialize field labels
            $('.field-checkbox').trigger('change');

            // Select all fields
            $('#selectAllFields').on('click', function() {
                $('.field-checkbox').prop('checked', true).trigger('change');
            });

            // Clear all fields
            $('#clearAllFields').on('click', function() {
                $('.field-checkbox').prop('checked', false).trigger('change');
            });

            // Load field preset
            $('#loadFieldPreset').on('click', function() {
                const presets = {
                    'cbn_compliance': {
                        name: 'CBN Compliance Report',
                        fields: ['booking_reference', 'client_name', 'amount', 'cbn_rate', 'markup_percentage', 'customer_rate', 'status', 'created_at'],
                        labels: {
                            'booking_reference': 'Reference',
                            'client_name': 'Client',
                            'amount': 'Amount',
                            'cbn_rate': 'CBN Rate',
                            'markup_percentage': 'Markup %',
                            'customer_rate': 'Final Rate',
                            'status': 'Status',
                            'created_at': 'Date'
                        }
                    },
                    'volume_analysis': {
                        name: 'Volume Analysis Report',
                        fields: ['booking_reference', 'transaction_type', 'currency', 'amount', 'customer_total', 'status', 'created_at'],
                        labels: {
                            'booking_reference': 'Reference',
                            'transaction_type': 'Type',
                            'currency': 'Currency',
                            'amount': 'Amount',
                            'customer_total': 'Total',
                            'status': 'Status',
                            'created_at': 'Date'
                        }
                    },
                    'revenue_tracking': {
                        name: 'Revenue Tracking Report',
                        fields: ['booking_reference', 'client_name', 'amount', 'markup_percentage', 'markup_amount', 'customer_payment_amount', 'status', 'created_at'],
                        labels: {
                            'booking_reference': 'Reference',
                            'client_name': 'Client',
                            'amount': 'Amount',
                            'markup_percentage': 'Markup %',
                            'markup_amount': 'Revenue',
                            'customer_payment_amount': 'Payment',
                            'status': 'Status',
                            'created_at': 'Date'
                        }
                    },
                    'client_activity': {
                        name: 'Client Activity Report',
                        fields: ['client_name', 'client_email', 'client_type', 'transaction_type', 'amount', 'status', 'created_at'],
                        labels: {
                            'client_name': 'Client Name',
                            'client_email': 'Email',
                            'client_type': 'Type',
                            'transaction_type': 'Transaction',
                            'amount': 'Amount',
                            'status': 'Status',
                            'created_at': 'Date'
                        }
                    }
                };

                const presetNames = Object.keys(presets).map(key => presets[key].name);
                const selectedPreset = prompt('Select a preset:\n' + presetNames.map((name, index) => `${index + 1}. ${name}`).join('\n') + '\n\nEnter the number:');

                if (selectedPreset && selectedPreset >= 1 && selectedPreset <= presetNames.length) {
                    const presetKey = Object.keys(presets)[selectedPreset - 1];
                    const preset = presets[presetKey];

                    // Clear all fields first
                    $('.field-checkbox').prop('checked', false);

                    // Select preset fields
                    preset.fields.forEach(field => {
                        $(`#field_${field}`).prop('checked', true);
                        if (preset.labels[field]) {
                            $(`input[name="field_labels[${field}]"]`).val(preset.labels[field]);
                        }
                    });

                    $('.field-checkbox').trigger('change');
                    alert(`Loaded preset: ${preset.name}`);
                }
            });

            // CBN Compliance Preset
            $('#loadCbnPreset').on('click', function() {
                // Clear all fields first
                $('.field-checkbox').prop('checked', false);

                // Set CBN-focused report type
                $('#cbnFocusedReport').prop('checked', true);

                // Select CBN compliance fields (excluding parallel rate)
                const cbnFields = [
                    'booking_reference', 'client_name', 'transaction_type', 'currency',
                    'amount', 'cbn_rate', 'markup_percentage', 'customer_rate',
                    'cbn_total', 'markup_amount', 'status', 'created_at'
                ];

                const cbnLabels = {
                    'booking_reference': 'Reference',
                    'client_name': 'Client',
                    'transaction_type': 'Transaction Type',
                    'currency': 'Currency',
                    'amount': 'Amount',
                    'cbn_rate': 'CBN Official Rate',
                    'markup_percentage': 'Approved Markup %',
                    'customer_rate': 'Final Customer Rate',
                    'cbn_total': 'CBN Total Value',
                    'markup_amount': 'Markup Revenue',
                    'status': 'Status',
                    'created_at': 'Date'
                };

                cbnFields.forEach(field => {
                    $(`#field_${field}`).prop('checked', true);
                    if (cbnLabels[field]) {
                        $(`input[name="field_labels[${field}]"]`).val(cbnLabels[field]);
                    }
                });

                // Hide parallel rate field for CBN compliance
                $('#field_parallel_rate').closest('.form-check').hide();

                // Set other CBN-specific options
                $('#transactionType').val('both');
                $('#currency').val('both');
                $('#sortBy').val('created_at');
                $('#sortDirection').val('desc');
                $('#includeTotals').prop('checked', true);
                $('#includeCounts').prop('checked', true);
                $('#includeAverages').prop('checked', true);

                $('.field-checkbox').trigger('change');
                $('input[name="report_type"]:checked').trigger('change');

                // Show success message
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="bi-shield-check"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <strong>CBN Compliance Preset Loaded!</strong><br>
                                This configuration excludes parallel market data and focuses on CBN official rates only.
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;

                $('#fieldSelectionSection').prepend(alertHtml);

                // Auto-scroll to field selection
                $('html, body').animate({
                    scrollTop: $('#fieldSelectionSection').offset().top - 100
                }, 500);
            });

            // Markup range validation
            $('input[name="min_markup"], input[name="max_markup"]').on('change', function() {
                const minMarkup = parseFloat($('input[name="min_markup"]').val()) || 0;
                const maxMarkup = parseFloat($('input[name="max_markup"]').val()) || 0;

                if (maxMarkup > 0 && minMarkup > maxMarkup) {
                    $('input[name="max_markup"]').val(minMarkup);
                }
            });

            // Group by change handling
            $('#groupBy').on('change', function() {
                const groupBy = $(this).val();
                if (groupBy) {
                    $('#includeTotals, #includeCounts').prop('checked', true);
                    if (groupBy === 'date' || groupBy === 'week' || groupBy === 'month') {
                        $('#includeGrowthRates').prop('checked', true);
                    }
                }
            });

            // Amount preset handling
            $('#amountPreset').on('change', function() {
                const preset = $(this).val();
                let minAmount = '', maxAmount = '';

                switch(preset) {
                    case '0-100':
                        minAmount = '0'; maxAmount = '100';
                        break;
                    case '100-500':
                        minAmount = '100'; maxAmount = '500';
                        break;
                    case '500-1000':
                        minAmount = '500'; maxAmount = '1000';
                        break;
                    case '1000-5000':
                        minAmount = '1000'; maxAmount = '5000';
                        break;
                    case '5000-10000':
                        minAmount = '5000'; maxAmount = '10000';
                        break;
                    case '10000+':
                        minAmount = '10000'; maxAmount = '';
                        break;
                }

                $('#minAmount').val(minAmount);
                $('#maxAmount').val(maxAmount);
            });

            // Date validation
            $('#startDate').on('change', function() {
                const startDate = new Date($(this).val());
                const endDate = new Date($('#endDate').val());

                if (startDate > endDate) {
                    $('#endDate').val($(this).val());
                }
            });

            // Amount validation
            $('#minAmount').on('change', function() {
                const minAmount = parseFloat($(this).val()) || 0;
                const maxAmount = parseFloat($('#maxAmount').val()) || 0;

                if (maxAmount > 0 && minAmount > maxAmount) {
                    $('#maxAmount').val($(this).val());
                }
            });

            // Preview report
            $('#previewReport').on('click', function() {
                const formData = new FormData($('#customReportForm')[0]);
                formData.set('format', 'html');
                formData.set('preview', '1');

                // Open preview in new window
                const form = $('<form>', {
                    'method': 'POST',
                    'action': $('#customReportForm').attr('action'),
                    'target': '_blank'
                });

                for (let [key, value] of formData.entries()) {
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': key,
                        'value': value
                    }));
                }

                $('body').append(form);
                form.submit();
                form.remove();
            });

            // Save template
            $('#saveTemplate').on('click', function() {
                $('#saveTemplateModal').modal('show');
            });

            // Save template form submission
            $('#saveTemplateBtn').on('click', function() {
                const formData = new FormData($('#customReportForm')[0]);
                const configuration = {};

                // Collect all form data into configuration object
                for (let [key, value] of formData.entries()) {
                    if (key.includes('[') && key.includes(']')) {
                        // Handle array fields like selected_fields[] and field_labels[field_name]
                        const match = key.match(/^([^[]+)\[([^\]]*)\]$/);
                        if (match) {
                            const baseKey = match[1];
                            const subKey = match[2];
                            if (!configuration[baseKey]) {
                                configuration[baseKey] = subKey ? {} : [];
                            }
                            if (subKey) {
                                configuration[baseKey][subKey] = value;
                            } else {
                                configuration[baseKey].push(value);
                            }
                        }
                    } else {
                        configuration[key] = value;
                    }
                }

                const templateData = {
                    template_name: $('#templateName').val(),
                    template_description: $('#templateDescription').val(),
                    is_public: $('#isPublicTemplate').is(':checked'),
                    configuration: configuration,
                    _token: '{{ csrf_token() }}'
                };

                $.ajax({
                    url: '{{ route("admin.forex.reports.templates.save") }}',
                    method: 'POST',
                    data: templateData,
                    success: function(response) {
                        if (response.success) {
                            $('#saveTemplateModal').modal('hide');
                            $('#saveTemplateForm')[0].reset();
                            alert('@lang("Template saved successfully!")');
                            refreshTemplates();
                        }
                    },
                    error: function(xhr) {
                        const errors = xhr.responseJSON?.errors;
                        if (errors) {
                            let errorMessage = '';
                            Object.values(errors).forEach(errorArray => {
                                errorMessage += errorArray.join('\n') + '\n';
                            });
                            alert('Error: ' + errorMessage);
                        } else {
                            alert('@lang("Failed to save template. Please try again.")');
                        }
                    }
                });
            });

            // Load template
            $(document).on('click', '.load-template', function() {
                const templateId = $(this).data('template-id');

                $.ajax({
                    url: '{{ route("admin.forex.reports.templates.load", ":id") }}'.replace(':id', templateId),
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            loadConfigurationIntoForm(response.configuration);
                            alert('@lang("Template loaded successfully!")');
                        }
                    },
                    error: function() {
                        alert('@lang("Failed to load template. Please try again.")');
                    }
                });
            });

            // Delete template
            $(document).on('click', '.delete-template', function() {
                if (confirm('@lang("Are you sure you want to delete this template?")')) {
                    const templateId = $(this).data('template-id');

                    $.ajax({
                        url: '{{ route("admin.forex.reports.templates.delete", ":id") }}'.replace(':id', templateId),
                        method: 'DELETE',
                        data: { _token: '{{ csrf_token() }}' },
                        success: function(response) {
                            if (response.success) {
                                $(`[data-template-id="${templateId}"]`).remove();
                                alert('@lang("Template deleted successfully!")');
                            }
                        },
                        error: function() {
                            alert('@lang("Failed to delete template. Please try again.")');
                        }
                    });
                }
            });

            // Refresh templates
            $('#refreshTemplates').on('click', function() {
                refreshTemplates();
            });

            function refreshTemplates() {
                $.ajax({
                    url: '{{ route("admin.forex.reports.templates.index") }}',
                    method: 'GET',
                    data: { public_only: true },
                    success: function(response) {
                        if (response.success) {
                            updateTemplatesContainer(response.templates);
                        }
                    },
                    error: function() {
                        alert('@lang("Failed to refresh templates.")');
                    }
                });
            }

            function updateTemplatesContainer(templates) {
                const container = $('#templatesContainer');
                container.empty();

                if (templates.length === 0) {
                    container.html(`
                        <div class="col-12">
                            <div class="text-center py-4">
                                <div class="mb-3">
                                    <i class="bi-bookmark display-4 text-muted"></i>
                                </div>
                                <h5 class="text-muted">@lang('No Templates Found')</h5>
                                <p class="text-muted">@lang('Create your first template by configuring a report and saving it.')</p>
                            </div>
                        </div>
                    `);
                    return;
                }

                templates.forEach(template => {
                    const templateHtml = `
                        <div class="col-sm-6 col-lg-4 mb-3" data-template-id="${template.id}">
                            <div class="card card-sm">
                                <div class="card-body">
                                    <h6 class="card-title">${template.name}</h6>
                                    <p class="card-text small text-muted">${template.description || 'No description available'}</p>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="badge bg-soft-info text-info">${template.report_type.charAt(0).toUpperCase() + template.report_type.slice(1)}</span>
                                        <small class="text-muted">Used ${template.usage_count} times</small>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-sm btn-outline-primary load-template" data-template-id="${template.id}">
                                            <i class="bi-upload me-1"></i> @lang('Load')
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger delete-template" data-template-id="${template.id}">
                                            <i class="bi-trash me-1"></i> @lang('Delete')
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    container.append(templateHtml);
                });
            }

            function loadConfigurationIntoForm(config) {
                // Reset form first
                $('#customReportForm')[0].reset();
                $('.field-checkbox').prop('checked', false);

                // Load basic configuration
                Object.keys(config).forEach(key => {
                    const element = $(`[name="${key}"]`);
                    if (element.length) {
                        if (element.is(':checkbox') || element.is(':radio')) {
                            element.filter(`[value="${config[key]}"]`).prop('checked', true);
                        } else {
                            element.val(config[key]);
                        }
                    }
                });

                // Load selected fields
                if (config.selected_fields) {
                    config.selected_fields.forEach(field => {
                        $(`#field_${field}`).prop('checked', true);
                    });
                }

                // Load field labels
                if (config.field_labels) {
                    Object.keys(config.field_labels).forEach(field => {
                        $(`input[name="field_labels[${field}]"]`).val(config.field_labels[field]);
                    });
                }

                // Trigger change events to update UI
                $('input[name="report_type"]:checked').trigger('change');
                $('#dateRangeType').trigger('change');
                $('.field-checkbox').trigger('change');
            }
        });
    </script>
@endpush
