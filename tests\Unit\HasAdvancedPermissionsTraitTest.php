<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

/**
 * Test controller using the HasAdvancedPermissions trait
 */
class TestController
{
    use HasAdvancedPermissions;

    public function testHasPermission(string $permission, array $context = []): bool
    {
        return $this->hasPermission($permission, $context);
    }

    public function testHasAllPermissions(array $permissions, array $context = []): bool
    {
        return $this->hasAllPermissions($permissions, $context);
    }

    public function testHasAnyPermission(array $permissions, array $context = []): bool
    {
        return $this->hasAnyPermission($permissions, $context);
    }

    public function testGetUserPermissions(): array
    {
        return $this->getUserPermissions();
    }

    public function testHasRole(string $roleName): bool
    {
        return $this->hasRole($roleName);
    }

    public function testGetResourcePermissions(string $resource): array
    {
        return $this->getResourcePermissions($resource);
    }

    public function testGetResourcePermissionStatus(string $resource): array
    {
        return $this->getResourcePermissionStatus($resource);
    }

    public function testGetAvailableActions(string $resource): array
    {
        return $this->getAvailableActions($resource);
    }

    public function testFilterByPermissions(array $data, array $permissionMap): array
    {
        return $this->filterByPermissions($data, $permissionMap);
    }
}

/**
 * Has Advanced Permissions Trait Tests
 * 
 * Tests the trait functionality for permission checking in controllers.
 */
class HasAdvancedPermissionsTraitTest extends TestCase
{
    use RefreshDatabase;

    protected TestController $controller;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->artisan('migrate');
        $this->controller = new TestController();
    }

    /** @test */
    public function it_can_check_single_permission()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($permission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $this->assertTrue($this->controller->testHasPermission('users.read'));
        $this->assertFalse($this->controller->testHasPermission('users.create'));
    }

    /** @test */
    public function it_can_check_multiple_permissions_with_and_logic()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $readPermission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);
        
        $createPermission = AdvancedPermission::create([
            'name' => 'users.create',
            'display_name' => 'Create Users',
            'resource' => 'users',
            'action' => 'create',
        ]);

        $role->grantPermission($readPermission);
        $role->grantPermission($createPermission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $this->assertTrue($this->controller->testHasAllPermissions(['users.read', 'users.create']));
        $this->assertFalse($this->controller->testHasAllPermissions(['users.read', 'users.delete']));
    }

    /** @test */
    public function it_can_check_multiple_permissions_with_or_logic()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $readPermission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($readPermission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $this->assertTrue($this->controller->testHasAnyPermission(['users.read', 'users.create']));
        $this->assertFalse($this->controller->testHasAnyPermission(['users.create', 'users.delete']));
    }

    /** @test */
    public function it_can_get_user_permissions()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $permission1 = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);
        
        $permission2 = AdvancedPermission::create([
            'name' => 'users.create',
            'display_name' => 'Create Users',
            'resource' => 'users',
            'action' => 'create',
        ]);

        $role->grantPermission($permission1);
        $role->grantPermission($permission2);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $permissions = $this->controller->testGetUserPermissions();
        
        $this->assertContains('users.read', $permissions);
        $this->assertContains('users.create', $permissions);
    }

    /** @test */
    public function it_can_check_user_roles()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'admin',
            'display_name' => 'Administrator',
        ]);

        $user->assignAdvancedRole($role);
        Auth::login($user);

        $this->assertTrue($this->controller->testHasRole('admin'));
        $this->assertFalse($this->controller->testHasRole('user'));
    }

    /** @test */
    public function it_can_get_resource_permissions()
    {
        $permissions = $this->controller->testGetResourcePermissions('users');
        
        $expected = [
            'create' => 'users.create',
            'read' => 'users.read',
            'update' => 'users.update',
            'delete' => 'users.delete',
        ];

        $this->assertEquals($expected, $permissions);
    }

    /** @test */
    public function it_can_get_resource_permission_status()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $readPermission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);
        
        $createPermission = AdvancedPermission::create([
            'name' => 'users.create',
            'display_name' => 'Create Users',
            'resource' => 'users',
            'action' => 'create',
        ]);

        $role->grantPermission($readPermission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $status = $this->controller->testGetResourcePermissionStatus('users');
        
        $this->assertTrue($status['read']);
        $this->assertFalse($status['create']);
        $this->assertFalse($status['update']);
        $this->assertFalse($status['delete']);
    }

    /** @test */
    public function it_can_get_available_actions_for_resource()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $readPermission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);
        
        $updatePermission = AdvancedPermission::create([
            'name' => 'users.update',
            'display_name' => 'Update Users',
            'resource' => 'users',
            'action' => 'update',
        ]);

        $role->grantPermission($readPermission);
        $role->grantPermission($updatePermission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $actions = $this->controller->testGetAvailableActions('users');
        
        $this->assertContains('read', $actions);
        $this->assertContains('update', $actions);
        $this->assertNotContains('create', $actions);
        $this->assertNotContains('delete', $actions);
    }

    /** @test */
    public function it_can_filter_data_by_permissions()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $readPermission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($readPermission);
        $user->assignAdvancedRole($role);
        Auth::login($user);

        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'sensitive_data' => 'secret',
        ];

        $permissionMap = [
            'name' => 'users.read',
            'email' => 'users.read',
            'sensitive_data' => 'users.admin',
        ];

        $filtered = $this->controller->testFilterByPermissions($data, $permissionMap);
        
        $this->assertArrayHasKey('name', $filtered);
        $this->assertArrayHasKey('email', $filtered);
        $this->assertArrayNotHasKey('sensitive_data', $filtered);
    }

    /** @test */
    public function it_returns_false_for_unauthenticated_users()
    {
        Auth::logout();

        $this->assertFalse($this->controller->testHasPermission('users.read'));
        $this->assertFalse($this->controller->testHasRole('admin'));
        $this->assertEmpty($this->controller->testGetUserPermissions());
    }

    /** @test */
    public function it_handles_users_without_advanced_roles()
    {
        $user = User::factory()->create(['use_advanced_roles' => false]);
        Auth::login($user);

        $this->assertFalse($this->controller->testHasPermission('users.read'));
        $this->assertEmpty($this->controller->testGetUserPermissions());
    }
}
