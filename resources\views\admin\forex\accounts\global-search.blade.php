@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Global Search')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Global Transaction Search')</h1>
                    <p class="page-header-text">@lang('Search transactions across all forex accounts')</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Accounts')
                        </a>
                        <a class="btn btn-outline-info" href="{{ route('admin.forex.accounts.transfer.history') }}">
                            <i class="bi-clock-history me-1"></i> @lang('Transfer History')
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Summary Stats -->
        <div class="row mb-3 mb-lg-5">
            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Transactions')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-dark">
                                    {{ number_format($summary['total_transactions']) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Active Accounts')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-success">
                                    {{ number_format($summary['total_accounts']) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Credits')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-info">
                                    {{ number_format($summary['total_credits'], 2) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Debits')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-warning">
                                    {{ number_format($summary['total_debits'], 2) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Summary Stats -->

        <!-- Advanced Filters -->
        <div class="card mb-3 mb-lg-5">
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <h4 class="card-header-title">@lang('Advanced Search & Filters')</h4>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" id="saveSearch">
                        <i class="bi-bookmark me-1"></i> @lang('Save Search')
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="loadSearch">
                        <i class="bi-folder-open me-1"></i> @lang('Load Search')
                    </button>
                </div>
            </div>
            <div class="card-body">
                <form id="advancedFilterForm">
                    <div class="row">
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="account" class="form-label">@lang('Account')</label>
                            <select class="form-select" id="account" name="account_id">
                                <option value="">@lang('All Accounts')</option>
                                @foreach($accounts as $account)
                                    <option value="{{ $account->id }}">{{ $account->account_name }} ({{ $account->currency_code }})</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="transactionType" class="form-label">@lang('Transaction Type')</label>
                            <select class="form-select" id="transactionType" name="transaction_type">
                                <option value="">@lang('All Types')</option>
                                @foreach($transactionTypes as $key => $value)
                                    <option value="{{ $key }}">{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="currency" class="form-label">@lang('Currency')</label>
                            <select class="form-select" id="currency" name="currency">
                                <option value="">@lang('All Currencies')</option>
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="GBP">GBP</option>
                                <option value="NGN">NGN</option>
                            </select>
                        </div>
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="searchTerm" class="form-label">@lang('Search Term')</label>
                            <input type="text" class="form-control" id="searchTerm" name="search"
                                   placeholder="@lang('Reference, description, booking, account')">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="dateFrom" class="form-label">@lang('Date From')</label>
                            <input type="date" class="form-control" id="dateFrom" name="date_from">
                        </div>
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="dateTo" class="form-label">@lang('Date To')</label>
                            <input type="date" class="form-control" id="dateTo" name="date_to">
                        </div>
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="amountMin" class="form-label">@lang('Amount Min')</label>
                            <input type="number" class="form-control" id="amountMin" name="amount_min"
                                   step="0.01" placeholder="0.00">
                        </div>
                        <div class="col-sm-6 col-lg-3 mb-3">
                            <label for="amountMax" class="form-label">@lang('Amount Max')</label>
                            <input type="number" class="form-control" id="amountMax" name="amount_max"
                                   step="0.01" placeholder="0.00">
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi-search me-1"></i> @lang('Search Transactions')
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clearFilters">
                            <i class="bi-x-circle me-1"></i> @lang('Clear All')
                        </button>
                        <button type="button" class="btn btn-outline-success" id="exportBtn">
                            <i class="bi-download me-1"></i> @lang('Export Results')
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- End Advanced Filters -->

        <!-- Search Results -->
        <div class="card">
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <h4 class="card-header-title">@lang('Search Results')</h4>
                    <span id="searchResultsCount" class="text-muted"></span>
                </div>
                <div class="d-grid d-sm-flex gap-2">
                    <div class="input-group input-group-merge navbar-input-group">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input type="search" id="datatableSearch" class="search form-control"
                               placeholder="@lang('Quick search in results')" aria-label="@lang('Quick search in results')">
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table id="globalSearchTable" class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                         "columnDefs": [{
                            "targets": [0],
                            "orderable": false
                          }],
                         "order": [],
                         "info": {
                           "totalQty": "#datatableWithPaginationInfoTotalQty"
                         },
                         "search": "#datatableSearch",
                         "entries": "#datatableEntries",
                         "pageLength": 25,
                         "isResponsive": false,
                         "isShowPaging": true,
                         "pagination": "datatablePagination"
                       }'>
                    <thead class="thead-light">
                    <tr>
                        <th>@lang('Date')</th>
                        <th>@lang('Account')</th>
                        <th>@lang('Reference')</th>
                        <th>@lang('Type')</th>
                        <th>@lang('Amount')</th>
                        <th>@lang('Balance After')</th>
                        <th>@lang('Description')</th>
                        <th>@lang('Booking')</th>
                        <th>@lang('Created By')</th>
                        <th>@lang('Actions')</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing:')</span>
                            <!-- Select -->
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto"
                                        autocomplete="off"
                                        data-hs-tom-select-options='{
                                            "searchInDropdown": false,
                                            "hideSearch": true
                                          }'>
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <!-- End Select -->
                            <span class="text-secondary me-2">@lang('of')</span>
                            <!-- Dynamic Data -->
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <!-- End Col -->

                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <!-- Pagination -->
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Search Results -->
    </div>
@endsection

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
@endpush

@push('js-lib')
    <script src="{{ asset('assets/admin/js/tom-select.complete.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/hs.datatables.js') }}"></script>
@endpush

@push('script')
    <script>
        'use strict';

        $(document).on('ready', function () {

            // Initialize DataTable
            const table = $('#globalSearchTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{{ route("admin.forex.accounts.search.global") }}',
                    data: function (d) {
                        d.account_id = $('#account').val();
                        d.transaction_type = $('#transactionType').val();
                        d.currency = $('#currency').val();
                        d.date_from = $('#dateFrom').val();
                        d.date_to = $('#dateTo').val();
                        d.amount_min = $('#amountMin').val();
                        d.amount_max = $('#amountMax').val();
                        d.search = $('#searchTerm').val();
                    }
                },
                columns: [
                    {data: 'date', name: 'created_at'},
                    {data: 'account', name: 'account'},
                    {data: 'reference', name: 'transaction_reference'},
                    {data: 'type', name: 'transaction_type'},
                    {data: 'amount', name: 'amount'},
                    {data: 'balance_after', name: 'balance_after'},
                    {data: 'description', name: 'description'},
                    {data: 'booking', name: 'booking'},
                    {data: 'created_by', name: 'created_by'},
                    {data: 'actions', name: 'actions', orderable: false, searchable: false}
                ],
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true,
                drawCallback: function(settings) {
                    $('#searchResultsCount').text('(' + settings.json.recordsTotal + ' total records)');
                }
            });

            // Advanced filter form submission
            $('#advancedFilterForm').on('submit', function(e) {
                e.preventDefault();
                table.ajax.reload();
            });

            // Clear filters
            $('#clearFilters').on('click', function() {
                $('#advancedFilterForm')[0].reset();
                table.ajax.reload();
            });

            // Export functionality
            $('#exportBtn').on('click', function() {
                const params = new URLSearchParams({
                    account_id: $('#account').val(),
                    transaction_type: $('#transactionType').val(),
                    currency: $('#currency').val(),
                    date_from: $('#dateFrom').val(),
                    date_to: $('#dateTo').val(),
                    amount_min: $('#amountMin').val(),
                    amount_max: $('#amountMax').val(),
                    search: $('#searchTerm').val(),
                    export: 'csv'
                });

                window.open('{{ route("admin.forex.accounts.search.global") }}?' + params.toString());
            });

            // Save search functionality
            $('#saveSearch').on('click', function() {
                const searchData = {
                    account_id: $('#account').val(),
                    transaction_type: $('#transactionType').val(),
                    currency: $('#currency').val(),
                    date_from: $('#dateFrom').val(),
                    date_to: $('#dateTo').val(),
                    amount_min: $('#amountMin').val(),
                    amount_max: $('#amountMax').val(),
                    search: $('#searchTerm').val()
                };

                const searchName = prompt('@lang("Enter a name for this search:")');
                if (searchName) {
                    localStorage.setItem('forex_search_' + searchName, JSON.stringify(searchData));
                    alert('@lang("Search saved successfully!")');
                }
            });

            // Load search functionality
            $('#loadSearch').on('click', function() {
                const savedSearches = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.startsWith('forex_search_')) {
                        savedSearches.push(key.replace('forex_search_', ''));
                    }
                }

                if (savedSearches.length === 0) {
                    alert('@lang("No saved searches found.")');
                    return;
                }

                const searchName = prompt('@lang("Available searches:") ' + savedSearches.join(', ') + '\n@lang("Enter search name to load:")');
                if (searchName) {
                    const searchData = localStorage.getItem('forex_search_' + searchName);
                    if (searchData) {
                        const data = JSON.parse(searchData);
                        $('#account').val(data.account_id);
                        $('#transactionType').val(data.transaction_type);
                        $('#currency').val(data.currency);
                        $('#dateFrom').val(data.date_from);
                        $('#dateTo').val(data.date_to);
                        $('#amountMin').val(data.amount_min);
                        $('#amountMax').val(data.amount_max);
                        $('#searchTerm').val(data.search);
                        table.ajax.reload();
                    } else {
                        alert('@lang("Search not found.")');
                    }
                }
            });

            // Initialize counters
            HSCore.components.HSCounter.init('.js-counter');
        });
    </script>
@endpush
