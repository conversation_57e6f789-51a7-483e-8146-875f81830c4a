@if($virtualAccounts->isNotEmpty())
    <div class="col-lg-6 mb-3 mb-lg-5 order-2 order-lg-1">
        <div class="row g-1">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="card-header-title">@lang('My Virtual Accounts')</h4>
                <div class="">
                    <button class="btn btn-sm btn-white btn-icon owl-prev-va"><i class="bi-chevron-left"></i></button>
                    <button class="btn btn-sm btn-white btn-icon owl-next-va"><i class="bi-chevron-right"></i></button>
                </div>
            </div>
            <div class="owl-carousel owl-theme card-carousel-va">
                @foreach($virtualAccounts as $account)
                    <div class="card virtual-account-card bg-gradient-secondary text-white rounded-4 shadow-sm" style="max-width: 25rem;">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-5">
                                <div>
                                    <h6 class="text-white mb-1">{{ $account->currency }} {{ ucfirst($account->type) }}</h6>
                                    <small class="text-white-50">{{ ucfirst($account->bank_name) }} </small>
                                </div>

                                <div class="col-auto">
                                    <div class="dropdown">
                                        <a href="#" class="text-white" id="virtualAccountDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi-three-dots-vertical"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-end" aria-labelledby="virtualAccountDropdown">
                                            {{-- <a class="dropdown-item" href="javascript:void(0)" onclick="copyAccountDetails({{ $account->id }})">
                                                <i class="bi-clipboard me-2"></i>@lang('Copy Details')
                                            </a> --}}
                                            <a class="dropdown-item" href="javascript:void(0)" onclick="showAccountDetails({{ $account->id }})">
                                                <i class="bi-eye me-2"></i>@lang('View Details')
                                            </a>
                                            {{-- <div class="dropdown-divider"></div>
                                            <a class="dropdown-item virtual-account-btn"
                                               href="javascript:void(0)">
                                                <i class="bi-bank me-2"></i>@lang('Create Another Account')
                                            </a> --}}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex align-items-center justify-content-between">
                                <div class="virtual-account-details mt-4">
                                    <h2 class="fw-bold mb-0">{{ $account->account_number }}</h2>
                                    <span class="small text-white">{{ $account->account_name }}</span>
                                    <div class="mt-2">
                                        {{-- <small class="text-white-50">{{ $account->bank_name }}</small> --}}
                                    </div>
                                </div>
                                <div class="virtual-account-icon mt-4">
                                    <div class="">
                                        <i class="bi-{{ $account->type === 'business' ? 'building' : 'person' }} text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif

<!-- Virtual Account Details Modal -->
<div class="modal fade" id="virtualAccountDetailsModal" tabindex="-1" aria-labelledby="virtualAccountDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="virtualAccountDetailsModalLabel">@lang('Virtual Account Details')</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="virtualAccountDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Close')</button>
                {{-- <button type="button" class="btn btn-primary" onclick="copyAllDetails()">@lang('Copy All Details')</button> --}}
            </div>
        </div>
    </div>
</div>

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/global/css/owl.carousel.min.css') }}">
@endpush

@push('style')
    <style>
        .virtual-account-card {
            background: linear-gradient(90deg, rgba(52, 58, 64, 1) 0%, rgba(73, 80, 87, 1) 100%);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            height: 200px !important;
        }

        .virtual-account-details h2 {
            font-size: 1.5rem;
            line-height: 1.2;
            color: #fff;
        }

        .virtual-account-card small,
        .virtual-account-card span {
            opacity: 0.9;
        }

        .virtual-account-card::before {
            content: "";
            position: absolute;
            top: -40px;
            right: -40px;
            width: 100px;
            height: 100px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            overflow: hidden;
        }

        .card-carousel-va {
            height: 100%;
        }
        .card-carousel-va .virtual-account-card {
            margin-right: 52px;
        }
        .card-carousel-va .owl-stage-outer {
            height: 100%;
            padding: 10px 0;
        }
        .card-carousel-va .owl-stage {
            height: 100%;
            display: flex;
            align-items: center;
        }
        .card-carousel-va .owl-item {
            height: 100%;
            display: flex;
            align-items: center;
        }
    </style>
@endpush

@push('script')
    <script>

        function showAccountDetails(accountId) {
            // Get account details from the data
            const accounts = @json($virtualAccounts);
            const account = accounts.find(acc => acc.id === accountId);

            if (account) {
                const content = `
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <strong>@lang('Account Number'):</strong><br>
                                            <span class="text-primary">${account.account_number}</span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>@lang('Account Name'):</strong><br>
                                            <span>${account.account_name}</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <strong>@lang('Bank Name'):</strong><br>
                                            <span>${account.bank_name}</span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>@lang('Currency'):</strong><br>
                                            <span>${account.currency}</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <strong>@lang('Account Type'):</strong><br>
                                            <span class="badge bg-primary">${account.type.charAt(0).toUpperCase() + account.type.slice(1)}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.getElementById('virtualAccountDetailsContent').innerHTML = content;
                new bootstrap.Modal(document.getElementById('virtualAccountDetailsModal')).show();
            }
        }

        function copyAccountDetails(accountId) {
            const accounts = @json($virtualAccounts);
            const account = accounts.find(acc => acc.id === accountId);

            if (account) {
                const details = `Account Number: ${account.account_number}\nAccount Name: ${account.account_name}\nBank Name: ${account.bank_name}\nCurrency: ${account.currency}`;

               if (navigator.clipboard) {
                    navigator.clipboard.writeText(details)
                    .then(() => Notiflix.Notify.success('@lang("Account details copied to clipboard!")'))
                    .catch(err => {
                        console.error('Could not copy text:', err);
                        Notiflix.Notify.failure('@lang("Failed to copy account details")');
                    });
                } else {
                    console.warn('Clipboard API not supported or not available');
                    Notiflix.Notify.failure('Clipboard API not supported in this browser');
                }
            }
        }

        function copyAllDetails() {
            const accounts = @json($virtualAccounts);
            let allDetails = '@lang("My Virtual Accounts"):\n\n';

            accounts.forEach((account, index) => {
                allDetails += `${index + 1}. ${account.currency} ${account.type.charAt(0).toUpperCase() + account.type.slice(1)} Account\n`;
                allDetails += `   Account Number: ${account.account_number}\n`;
                allDetails += `   Account Name: ${account.account_name}\n`;
                allDetails += `   Bank Name: ${account.bank_name}\n`;
                //allDetails += `   Provider: ${account.provider.charAt(0).toUpperCase() + account.provider.slice(1)}\n\n`;
            });

            navigator.clipboard.writeText(allDetails).then(function() {
                Notiflix.Notify.success('@lang("All account details copied to clipboard!")');
                bootstrap.Modal.getInstance(document.getElementById('virtualAccountDetailsModal')).hide();
            }, function(err) {
                console.error('Could not copy text: ', err);
                Notiflix.Notify.failure('@lang("Failed to copy account details")');
            });
        }

        // Handle virtual account creation button
        $('.virtual-account-btn').on('click', function() {
            checkVirtualAccountEligibility();
        });

        function checkVirtualAccountEligibility() {
            $.ajax({
                url: '{{ route("user.virtual.accounts.check.eligibility") }}',
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        if (response.data.can_create) {
                            showCreateVirtualAccountModal();
                        } else {
                            showVirtualAccountExistsMessage();
                        }
                    } else {
                        showErrorMessage(response.message || 'Failed to check eligibility');
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Failed to check eligibility';
                    showErrorMessage(errorMsg);
                }
            });
        }

        function showCreateVirtualAccountModal() {
            Notiflix.Confirm.show(
                '@lang("Create Virtual Account")',
                '@lang("Would you like to create a new NGN customer virtual account?")',
                '@lang("Yes, Create")',
                '@lang("Cancel")',
                function() {
                    createVirtualAccount();
                }
            );
        }

        function showVirtualAccountExistsMessage() {
            Notiflix.Notify.info('@lang("You already have a customer virtual account")');
        }

        function createVirtualAccount() {
            $.ajax({
                url: '{{ route("user.virtual.accounts.create") }}',
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        Notiflix.Notify.success(response.message);
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        showErrorMessage(response.message || 'Failed to create virtual account');
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Failed to create virtual account';
                    showErrorMessage(errorMsg);
                }
            });
        }

        function showErrorMessage(message) {
            Notiflix.Notify.failure(message);
        }
    </script>
@endpush
