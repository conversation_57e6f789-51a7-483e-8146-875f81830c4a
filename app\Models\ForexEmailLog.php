<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForexEmailLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'forex_booking_id',
        'email_to',
        'email_subject',
        'email_body',
        'email_type',
        'status',
        'error_message',
        'sent_at',
        'retry_count'
    ];

    protected $casts = [
        'sent_at' => 'datetime',
        'retry_count' => 'integer',
    ];

    // Relationships
    public function forexBooking()
    {
        return $this->belongsTo(ForexBooking::class, 'forex_booking_id');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('email_type', $type);
    }

    public function scopeRetryable($query)
    {
        return $query->where('status', 'failed')
                    ->where('retry_count', '<', 3);
    }

    // Accessors
    public function getStatusClassAttribute()
    {
        return [
            'pending' => 'warning',
            'sent' => 'success',
            'failed' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    public function getTypeClassAttribute()
    {
        return [
            'booking_confirmation' => 'primary',
            'payment_reminder' => 'warning',
            'completion_notification' => 'success',
        ][$this->email_type] ?? 'secondary';
    }

    // Business Logic Methods
    public function markAsSent()
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
        ]);
    }

    public function canRetry()
    {
        return $this->status === 'failed' && $this->retry_count < 3;
    }

    public static function createLog($bookingId, $emailTo, $subject, $body, $type)
    {
        return static::create([
            'forex_booking_id' => $bookingId,
            'email_to' => $emailTo,
            'email_subject' => $subject,
            'email_body' => $body,
            'email_type' => $type,
            'status' => 'pending',
            'retry_count' => 0,
        ]);
    }
}
