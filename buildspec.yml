version: 0.2
run-as: root

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - REPOSITORY_URL=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$REPOSITORY_NAME
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REPOSITORY_URL

  build:
    commands:
      - echo Build started on `date`
      - echo Building Docker image...
      - docker build -t $REPOSITORY_URL:$TAG-$CODEBUILD_BUILD_NUMBER .
      - docker tag $REPOSITORY_URL:$TAG-$CODEBUILD_BUILD_NUMBER $REPOSITORY_URL:latest

  post_build:
    commands:
        - echo Pushing Docker image....
        - docker push $REPOSITORY_URL:$TAG-$CODEBUILD_BUILD_NUMBER
        - docker push $REPOSITORY_URL:latest
        - echo Writing imagedefinitions.json....
        - printf '[{"name":"%s","imageUri":"%s"}]' $CONTAINER_NAME $REPOSITORY_URL:$TAG-$CODEBUILD_BUILD_NUMBER > imagedefinitions.json
        - cat imagedefinitions.json
artifacts:
  files:
    - imagedefinitions.json
