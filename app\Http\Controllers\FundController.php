<?php

namespace App\Http\Controllers;

use App\Models\ContentDetails;
use App\Models\Currency;
use App\Models\Deposit;
use App\Models\Gateway;
use App\Models\VirtualCardOrder;
use App\Traits\PaymentTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FundController extends Controller
{
    use PaymentTrait;

    public function index(Request $request)
    {
        $userId = Auth::id();
        $currencies = Currency::select('id', 'code', 'name')->orderBy('code', 'ASC')->get();

        $funds = Deposit::query()
            ->with(['sender', 'receiver', 'currency','gateway'])
            ->where('user_id', $userId)
            ->search($request->all())
            ->latest()
            ->paginate(20);
        return view('user.fund.index', compact('funds', 'currencies'));
    }

    public function initialize(Request $request, $from = null, $id = null)
    {
        $order = null;
        if ($from == 'card') {
            $order = VirtualCardOrder::with(['cardMethod'])->find($id);
        }
        if ($request->isMethod('get')) {
            $data['methods'] = Gateway::orderBy('sort_by', 'ASC')->where('status', 1)->get();
            $data['currencies'] = Currency::select('id', 'code', 'name', 'currency_type', 'is_active')->where('is_active', 1)->get();
            $data['template'] = ContentDetails::whereHas('content', function ($query) {
                $query->whereIn('name', ['add_fund']);
            })->first();
            return view('user.fund.create', $data, compact('order', 'from', 'id'));
        }

        elseif ($request->isMethod('post')) {

            $purifiedData = $request->all();
            $validationRules = [
                'amount' => 'required|numeric|min:1|not_in:0',
                'currency' => 'required|integer|min:1|not_in:0',
                'methodId' => 'required|integer|min:1|not_in:0',
            ];

            $validate = Validator::make($purifiedData, $validationRules);
            if ($validate->fails()) {
                return back()->withErrors($validate)->withInput();
            }
            $purifiedData = (object)$purifiedData;

            $amount = $purifiedData->amount;
            $currency_id = $purifiedData->currency;
            $methodId = $purifiedData->methodId;

            $cardOrderId = null;
            if ($request->has('orderCardId') && $request->orderCardId != null) {
                $cardOrder = $this->validateCardOrder($request->orderCardId, $purifiedData);
                if (!$cardOrder['status']) {
                    return back()->with('error', $cardOrder['message']);
                }
                $cardOrderId = $cardOrder['id'] ?? null;
            }

            $checkAmountValidate = $this->validatePayment($amount, $currency_id, config('transactionType.deposit'), $methodId);//7 = deposit
            if (!$checkAmountValidate['status']) {
                return back()->withInput()->with('error', $checkAmountValidate['message']);
            }

            $deposit = $this->createDeposit($checkAmountValidate, null, null, $cardOrderId);

            return redirect(route('deposit.confirm', $deposit->trx_id));
        }
    }



    public function checkAmountValidate($amount, $currency_id, $transaction_type_id, $methodId){

        return $this->validatePayment($amount, $currency_id, $transaction_type_id, $methodId);
    }


}
