<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForexAccountTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'transfer_reference',
        'from_account_id',
        'to_account_id',
        'amount',
        'currency',
        'description',
        'notes',
        'created_by',
        'status'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function (ForexAccountTransfer $transfer) {
            if (empty($transfer->transfer_reference)) {
                $transfer->transfer_reference = $transfer->generateTransferReference();
            }
        });
    }

    // Relationships
    public function fromAccount()
    {
        return $this->belongsTo(ForexAccount::class, 'from_account_id');
    }

    public function toAccount()
    {
        return $this->belongsTo(ForexAccount::class, 'to_account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    // Accessors
    public function getStatusClassAttribute()
    {
        return [
            'pending' => 'warning',
            'completed' => 'success',
            'failed' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    // Business Logic Methods
    public static function createTransfer($fromAccountId, $toAccountId, $amount, $description, $adminId, $notes = null)
    {
        $fromAccount = ForexAccount::findOrFail($fromAccountId);
        $toAccount = ForexAccount::findOrFail($toAccountId);

        // Validate same currency
        if ($fromAccount->currency_code !== $toAccount->currency_code) {
            throw new \Exception('Cannot transfer between different currencies');
        }

        // Validate sufficient balance
        if ($fromAccount->available_balance < $amount) {
            throw new \Exception('Insufficient balance in source account');
        }

        $transfer = static::create([
            'from_account_id' => $fromAccountId,
            'to_account_id' => $toAccountId,
            'amount' => $amount,
            'currency' => $fromAccount->currency_code,
            'description' => $description,
            'notes' => $notes,
            'created_by' => $adminId,
            'status' => 'completed',
        ]);

        // Process the transfer
        $transfer->processTransfer($adminId);

        return $transfer;
    }

    private function processTransfer($adminId)
    {
        try {
            // Debit from source account
            $debitTransaction = $this->fromAccount->debit(
                $this->amount,
                "Transfer to {$this->toAccount->account_name} - {$this->transfer_reference}",
                $adminId,
                null,
                [
                    'transfer_id' => $this->id,
                    'transfer_reference' => $this->transfer_reference,
                    'transfer_type' => 'outgoing',
                    'counterpart_account' => $this->toAccount->account_name,
                    'counterpart_account_id' => $this->toAccount->id
                ]
            );

            // Credit to destination account
            $creditTransaction = $this->toAccount->credit(
                $this->amount,
                "Transfer from {$this->fromAccount->account_name} - {$this->transfer_reference}",
                $adminId,
                null,
                [
                    'transfer_id' => $this->id,
                    'transfer_reference' => $this->transfer_reference,
                    'transfer_type' => 'incoming',
                    'counterpart_account' => $this->fromAccount->account_name,
                    'counterpart_account_id' => $this->fromAccount->id,
                    'linked_transaction_id' => $debitTransaction->id
                ]
            );

            // Update the debit transaction with the linked credit transaction ID
            $debitTransaction->update([
                'metadata' => array_merge($debitTransaction->metadata ?? [], ['linked_transaction_id' => $creditTransaction->id])
            ]);

            $this->update(['status' => 'completed']);
        } catch (\Exception $e) {
            $this->update(['status' => 'failed']);
            throw $e;
        }
    }

    private function generateTransferReference()
    {
        return 'FXR' . date('YmdHis') . rand(100, 999);
    }
}
