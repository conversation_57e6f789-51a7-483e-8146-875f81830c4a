@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">@lang('Forex Trading Accounts')</h1>
                    <p class="page-header-text">@lang('Manage forex trading accounts, balances, and fund transfers')</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-info" href="{{ route('admin.forex.accounts.search.global') }}">
                            <i class="bi-search me-1"></i> @lang('Global Search')
                        </a>
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.transfer.history') }}">
                            <i class="bi-clock-history me-1"></i> @lang('Transfer History')
                        </a>
                        <a class="btn btn-outline-warning" href="{{ route('admin.forex.accounts.balance.summary') }}">
                            <i class="bi-bar-chart me-1"></i> @lang('Balance Reports')
                        </a>
                        <a class="btn btn-primary" href="{{ route('admin.forex.accounts.transfer') }}">
                            <i class="bi-arrow-left-right me-1"></i> @lang('Transfer Funds')
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        {{-- <!-- Stats Cards -->
        <div class="row">
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('USD Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($totalBalances['USD']['available'] ?? 0, 2) }}
                            </span>
                        </div>
                        @if(($totalBalances['USD']['pending'] ?? 0) > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($totalBalances['USD']['pending'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($totalBalances['USD']['total'] ?? 0, 2) }} USD
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('EUR Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($totalBalances['EUR']['available'] ?? 0, 2) }}
                            </span>
                        </div>
                        @if(($totalBalances['EUR']['pending'] ?? 0) > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($totalBalances['EUR']['pending'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($totalBalances['EUR']['total'] ?? 0, 2) }} EUR
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('GBP Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($totalBalances['GBP']['available'] ?? 0, 2) }}
                            </span>
                        </div>
                        @if(($totalBalances['GBP']['pending'] ?? 0) > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($totalBalances['GBP']['pending'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($totalBalances['GBP']['total'] ?? 0, 2) }} GBP
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('NGN Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($totalBalances['NGN']['available'] ?? 0, 2) }}
                            </span>
                        </div>
                        @if(($totalBalances['NGN']['pending'] ?? 0) > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($totalBalances['NGN']['pending'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($totalBalances['NGN']['total'] ?? 0, 2) }} NGN
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}
        <!-- End Currency Balance Cards -->

        <!-- Additional Stats -->
        <div class="row">
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Active Accounts')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $accounts->count() }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Accounts')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-info text-info">
                                    <i class="bi-bank"></i> @lang('Active')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Quick Actions')</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.forex.accounts.transfer') }}" class="btn btn-sm btn-primary">
                                <i class="bi-arrow-left-right me-1"></i> @lang('Transfer')
                            </a>
                            <a href="{{ route('admin.forex.dashboard') }}" class="btn btn-sm btn-outline-secondary">
                                <i class="bi-speedometer2 me-1"></i> @lang('Dashboard')
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Stats Cards -->

        <!-- Accounts List -->
        <div class="row">
            @foreach($accounts as $account)
                <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                    <div class="card h-100">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-header-title">{{ $account->account_name }}</h5>
                                <span class="badge bg-soft-{{ $account->account_type === 'USD' ? 'success' : 'warning' }} text-{{ $account->account_type === 'USD' ? 'success' : 'warning' }}">
                                    {{ $account->account_type }}
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Available Balance -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Available Balance')</span>
                                    <span class="fw-semibold text-success">{{ $account->formatted_balance }}</span>
                                </div>
                            </div>

                            @if($account->pending_balance > 0)
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-muted">@lang('Pending Balance')</span>
                                        <span class="text-warning fw-semibold">{{ number_format($account->pending_balance, 2) }} {{ $account->currency_code }}</span>
                                    </div>
                                </div>
                            @endif

                            <!-- Total Balance -->
                            <div class="mb-3 pt-2 border-top">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="fw-semibold">@lang('Total Balance')</span>
                                    <span class="fw-bold text-primary">{{ $account->formatted_total_balance }}</span>
                                </div>
                            </div>

                            <!-- Description -->
                            @if($account->description)
                                <p class="text-muted small mb-3">{{ $account->description }}</p>
                            @endif

                            <!-- Action Buttons -->
                            <div class="d-grid gap-2">
                                <a href="{{ route('admin.forex.accounts.show', $account->id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi-eye me-1"></i> @lang('View Details')
                                </a>
                                <a href="{{ route('admin.forex.accounts.fund', $account->id) }}" class="btn btn-success btn-sm">
                                    <i class="bi-plus-circle me-1"></i> @lang('Fund Account')
                                </a>
                            </div>
                        </div>
                        <div class="card-footer">
                            <small class="text-muted">
                                <i class="bi-clock me-1"></i>
                                @lang('Last updated'): {{ $account->updated_at->diffForHumans() }}
                            </small>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <!-- End Accounts List -->

        @if($accounts->isEmpty())
            <div class="card">
                <div class="card-body">
                    <div class="text-center p-4">
                        <img class="dataTables-image mb-3" src="{{ asset('assets/admin/img/oc-error.svg') }}"
                             alt="Image Description" data-hs-theme-appearance="default">
                        <img class="dataTables-image mb-3" src="{{ asset('assets/admin/img/oc-error-light.svg') }}"
                             alt="Image Description" data-hs-theme-appearance="dark">
                        <p class="mb-0">@lang('No forex accounts found')</p>
                        <p class="text-muted">@lang('Please run the forex account seeder to create default accounts')</p>
                        {{-- <div class="mt-3">
                            <code>php artisan db:seed --class=ForexAccountSeeder</code>
                        </div> --}}
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Initialize counter animations
            HSCore.components.HSCounter.init('.js-counter');
        });
    </script>
@endpush
