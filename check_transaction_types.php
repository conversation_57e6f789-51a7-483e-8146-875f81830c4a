<?php

require 'vendor/autoload.php';

use App\Models\ForexTransaction;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== CHECKING TRANSACTION TYPES ===\n\n";

$lastTransactions = ForexTransaction::latest()->take(5)->get();

foreach ($lastTransactions as $transaction) {
    echo "Transaction ID: {$transaction->id}\n";
    echo "Type: [{$transaction->transaction_type}]\n";
    echo "Subtype: [{$transaction->transaction_subtype}]\n";
    echo "Amount: {$transaction->amount}\n";
    echo "Description: {$transaction->description}\n";
    echo "Balance Before: {$transaction->balance_before}\n";
    echo "Balance After: {$transaction->balance_after}\n";
    echo "---\n";
}
?>
