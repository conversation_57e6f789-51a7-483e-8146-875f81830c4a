<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class VirtualAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'provider',
        'currency',
        'type',
        'account_number',
        'account_name',
        'bank_name',
        'bank_code',
        'provider_data',
        'kyc_data_used',
        'provider_reference',
        'is_active',
        'created_at_provider'
    ];

    /**
     * Valid account types
     */
    public const VALID_TYPES = ['customer', 'individual', 'business'];

    protected $casts = [
        'provider_data' => 'array',
        'kyc_data_used' => 'array',
        'is_active' => 'boolean',
        'created_at_provider' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();
        static::saved(function () {
            Cache::forget('virtualAccountRecord');
        });
    }

    /**
     * Get the user that owns the virtual account
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the currency for this virtual account
     */
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency', 'code');
    }

    /**
     * Scope to get active virtual accounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by provider
     */
    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Scope to filter by currency
     */
    public function scopeByCurrency($query, $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Scope to filter by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Check if user already has a virtual account for this provider and currency
     */
    public static function userHasAccount($userId, $provider, $currency, $type = null)
    {
        if ($type) {
            return self::where([
                'user_id' => $userId,
                'provider' => $provider,
                'currency' => $currency,
                'is_active' => true,
                'type' => $type
            ])->exists();
        }
        return self::where([
            'user_id' => $userId,
            'provider' => $provider,
            'currency' => $currency,
            'is_active' => true
        ])->exists();
    }

    /**
     * Find virtual account by account number
     */
    public static function findByAccountNumber($accountNumber, $provider = null)
    {
        $query = self::where('account_number', $accountNumber)->where('is_active', true);

        if ($provider) {
            $query->where('provider', $provider);
        }

        return $query->first();
    }

    /**
     * Get formatted account details for display
     */
    public function getFormattedDetailsAttribute()
    {
        return [
            'account_number' => $this->account_number,
            'account_name' => $this->account_name,
            'bank_name' => $this->bank_name,
            'bank_code' => $this->bank_code,
            'currency' => $this->currency,
            'type' => ucfirst($this->type),
            'provider' => ucfirst($this->provider)
        ];
    }
}
