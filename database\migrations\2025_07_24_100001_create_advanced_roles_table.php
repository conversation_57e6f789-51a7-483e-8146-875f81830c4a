<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the advanced_roles table for storing dynamic role definitions.
     * Roles can inherit from other roles and have hierarchical relationships.
     */
    public function up(): void
    {
        Schema::create('advanced_roles', function (Blueprint $table) {
            $table->id();
            
            // Role identification
            $table->string('name', 100)->unique()->comment('Unique role name (e.g., finance_manager)');
            $table->string('display_name', 150)->comment('Human-readable role name');
            $table->text('description')->nullable()->comment('Detailed description of role responsibilities');
            
            // Role hierarchy and inheritance
            $table->unsignedBigInteger('parent_role_id')->nullable()->comment('Parent role for inheritance');
            $table->integer('level')->default(0)->comment('Hierarchy level (0 = top level)');
            $table->string('hierarchy_path', 500)->nullable()->comment('Full hierarchy path for efficient queries');
            
            // Role categorization
            $table->string('category', 50)->nullable()->index()->comment('Role category (admin, user, finance, etc.)');
            $table->string('scope', 30)->default('global')->comment('Role scope: global, department, project, etc.');
            
            // Role metadata
            $table->json('metadata')->nullable()->comment('Additional role metadata (constraints, settings, etc.)');
            $table->integer('sort_order')->default(0)->comment('Display order for UI');
            $table->string('color', 7)->nullable()->comment('UI color code for role display');
            
            // Status and control
            $table->boolean('is_system')->default(false)->comment('System role that cannot be deleted');
            $table->boolean('is_active')->default(true)->comment('Whether role is active and can be assigned');
            $table->boolean('is_default')->default(false)->comment('Default role for new users');
            $table->boolean('inherit_permissions')->default(true)->comment('Whether to inherit parent role permissions');
            
            // Limits and constraints
            $table->integer('max_users')->nullable()->comment('Maximum users that can have this role');
            $table->timestamp('expires_at')->nullable()->comment('Role expiration date');
            
            // Audit fields
            $table->unsignedBigInteger('created_by')->nullable()->comment('Admin who created this role');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('Admin who last updated this role');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['parent_role_id', 'is_active']);
            $table->index(['category', 'is_active']);
            $table->index(['scope', 'is_active']);
            $table->index(['is_system', 'is_active']);
            $table->index(['level', 'sort_order']);
            
            // Foreign key constraints
            $table->foreign('parent_role_id')->references('id')->on('advanced_roles')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('admins')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('admins')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advanced_roles');
    }
};
