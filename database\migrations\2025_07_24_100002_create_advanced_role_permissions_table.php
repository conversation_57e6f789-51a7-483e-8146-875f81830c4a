<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the advanced_role_permissions pivot table for role-permission relationships.
     * Supports permission constraints and conditional access.
     */
    public function up(): void
    {
        Schema::create('advanced_role_permissions', function (Blueprint $table) {
            $table->id();
            
            // Primary relationships
            $table->unsignedBigInteger('role_id')->comment('Reference to advanced_roles table');
            $table->unsignedBigInteger('permission_id')->comment('Reference to advanced_permissions table');
            
            // Permission constraints and conditions
            $table->json('constraints')->nullable()->comment('Additional constraints for this permission (time, IP, conditions)');
            $table->boolean('is_granted')->default(true)->comment('Whether permission is granted (true) or explicitly denied (false)');
            $table->integer('priority')->default(0)->comment('Priority for conflict resolution (higher wins)');
            
            // Temporal access control
            $table->timestamp('valid_from')->nullable()->comment('Permission valid from this date');
            $table->timestamp('valid_until')->nullable()->comment('Permission valid until this date');
            $table->json('schedule')->nullable()->comment('Time-based schedule for permission (days, hours)');
            
            // Audit fields
            $table->unsignedBigInteger('granted_by')->nullable()->comment('Admin who granted this permission');
            $table->timestamp('granted_at')->nullable()->comment('When permission was granted');
            $table->text('grant_reason')->nullable()->comment('Reason for granting/denying permission');
            $table->timestamps();
            
            // Unique constraint to prevent duplicate role-permission pairs
            $table->unique(['role_id', 'permission_id'], 'unique_role_permission');
            
            // Indexes for performance
            $table->index(['role_id', 'is_granted']);
            $table->index(['permission_id', 'is_granted']);
            $table->index(['valid_from', 'valid_until']);
            $table->index(['granted_by', 'granted_at']);
            
            // Foreign key constraints
            $table->foreign('role_id')->references('id')->on('advanced_roles')->onDelete('cascade');
            $table->foreign('permission_id')->references('id')->on('advanced_permissions')->onDelete('cascade');
            $table->foreign('granted_by')->references('id')->on('admins')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advanced_role_permissions');
    }
};
