<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Admin;
use App\Models\Payout;
use App\Models\Currency;
use App\Models\PayoutMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PayoutApprovalTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $user;
    protected $currency;
    protected $payoutMethod;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->admin = Admin::factory()->create();
        $this->user = User::factory()->create();
        $this->currency = Currency::factory()->create(['code' => 'NGN']);
        
        $this->payoutMethod = PayoutMethod::factory()->create([
            'code' => 'numero',
            'is_automatic' => 1,
            'is_active' => 1,
            'parameters' => (object) [
                'api_key' => 'test_api_key',
                'public_key' => 'test_public_key',
                'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
            ]
        ]);
    }

    /**
     * Test that approval with existing response_id doesn't make new API call
     */
    public function test_approval_with_existing_response_id_prevents_double_api_call()
    {
        // Create a payout with existing response_id (API already called)
        $payout = Payout::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'status' => 1, // Pending
            'response_id' => 'existing-response-123',
            'amount' => 1000,
            'charge' => 50,
            'net_amount' => 950
        ]);

        // Act as admin and approve the payout
        $response = $this->actingAs($this->admin, 'admin')
            ->put(route('admin.payout.action', $payout->id), [
                'id' => $payout->id,
                'status' => '2', // Approve
                'feedback' => 'Approved'
            ]);

        // Assert the payout was approved without making new API call
        $payout->refresh();
        $this->assertEquals(2, $payout->status); // Should be approved
        $this->assertEquals('existing-response-123', $payout->response_id); // Should keep existing response_id
        
        // Should redirect back with success message
        $response->assertRedirect();
        $response->assertSessionHas('success', 'Payout Confirmed - Transaction already processed');
    }

    /**
     * Test that approval with last_error prevents new API call
     */
    public function test_approval_with_last_error_prevents_new_api_call()
    {
        // Create a payout with last_error (previous API call failed)
        $payout = Payout::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'status' => 1, // Pending
            'last_error' => 'Previous API call failed',
            'amount' => 1000,
            'charge' => 50,
            'net_amount' => 950
        ]);

        // Act as admin and try to approve the payout
        $response = $this->actingAs($this->admin, 'admin')
            ->put(route('admin.payout.action', $payout->id), [
                'id' => $payout->id,
                'status' => '2', // Approve
                'feedback' => 'Approved'
            ]);

        // Assert the payout was not approved and error message shown
        $payout->refresh();
        $this->assertEquals(1, $payout->status); // Should still be pending
        
        // Should redirect back with error message
        $response->assertRedirect();
        $response->assertSessionHas('error');
    }

    /**
     * Test status check functionality
     */
    public function test_status_check_functionality()
    {
        // Create a payout with response_id for status checking
        $payout = Payout::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'status' => 1, // Pending
            'response_id' => 'test-reference-123',
            'amount' => 1000,
            'charge' => 50,
            'net_amount' => 950
        ]);

        // Act as admin and check status
        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.payout.checkStatus', $payout->id), [
                'id' => $payout->id
            ]);

        // Should redirect back (status check completed)
        $response->assertRedirect();
    }

    /**
     * Test status check with no response_id fails
     */
    public function test_status_check_without_response_id_fails()
    {
        // Create a payout without response_id
        $payout = Payout::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'status' => 1, // Pending
            'response_id' => null,
            'amount' => 1000,
            'charge' => 50,
            'net_amount' => 950
        ]);

        // Act as admin and try to check status
        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.payout.checkStatus', $payout->id), [
                'id' => $payout->id
            ]);

        // Should redirect back with error
        $response->assertRedirect();
        $response->assertSessionHas('error', 'No transaction reference found. Cannot check status.');
    }

    /**
     * Test status check with manual payout method fails
     */
    public function test_status_check_with_manual_method_fails()
    {
        // Create manual payout method
        $manualMethod = PayoutMethod::factory()->create([
            'code' => 'manual',
            'is_automatic' => 0, // Manual
            'is_active' => 1
        ]);

        // Create a payout with manual method
        $payout = Payout::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $manualMethod->id,
            'status' => 1, // Pending
            'response_id' => 'test-reference-123',
            'amount' => 1000,
            'charge' => 50,
            'net_amount' => 950
        ]);

        // Act as admin and try to check status
        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.payout.checkStatus', $payout->id), [
                'id' => $payout->id
            ]);

        // Should redirect back with error
        $response->assertRedirect();
        $response->assertSessionHas('error', 'Status check not available for manual payout methods.');
    }
}
