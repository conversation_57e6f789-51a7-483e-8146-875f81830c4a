<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== DEBUG EMAIL TEST ===\n\n";

// Get services
$walletService = new ForexWalletService();
$bookingService = new ForexBookingService($walletService);

// Get USD account
$usdAccount = ForexAccount::byType('USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

// Create a test booking
$bookingData = [
    'client_type' => 'external',
    'client_name' => 'Debug Test Client',
    'client_email' => '<EMAIL>',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 10.00,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Debug test account',
];

try {
    echo "Creating booking...\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    
    echo "Booking created: {$booking->booking_reference}\n";
    echo "Email sent flag: " . ($booking->email_sent ? 'Yes' : 'No') . "\n";
    
    echo "\nCheck the logs for debug information:\n";
    echo "tail storage/logs/laravel.log\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
