[program:currency-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php artisan queue:work database --sleep=3 --tries=3 --max-time=3600 --timeout=60
directory=c:\Users\<USER>\Herd\currency
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=c:\Users\<USER>\Herd\currency\storage\logs\queue-worker.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=3600
