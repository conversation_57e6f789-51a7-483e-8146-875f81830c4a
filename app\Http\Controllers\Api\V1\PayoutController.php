<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\MerchantPayoutConfiguration;
use App\Models\Payout;
use App\Models\PayoutMethod;
use App\Models\TwoFactorSetting;
use App\Models\Wallet;
use App\Traits\PayoutTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use <PERSON>bauman\Purify\Facades\Purify;

class PayoutController extends Controller
{
    use PayoutTrait ;

    public function payoutRequest(Request $request)
    {
        try {
            $amount = $request->amount;
            $supportedCurrency = $request->supported_currency;
            $payoutMethodId = $request->payout_method_id;

            $validator = Validator::make($request->all(), [
                'amount' => 'required',
                'supported_currency' => 'required',
                'payout_method_id' => 'required',
            ]);
            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $validateData = $this->validationCheck($amount, $supportedCurrency, $payoutMethodId);
            if (!$validateData['status']) {
                return response()->json($this->withErrors($validateData['message']));
            }

            $payout = $this->createPayout($validateData);

            $data['trx_id'] = $payout->trx_id;
            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payoutConfirmPreview($utr)
    {
        try {
            $user = Auth::user();
            $payout = Payout::where('trx_id', $utr)->where('user_id',$user->id)->first();
            if (!$payout) {
                return response()->json($this->withErrors('Record not found'));
            }
            $payoutMethod = PayoutMethod::query()->find($payout->payout_method_id);

            $data['dynamicForm'] = $payoutMethod->inputForm;
            $data['bankName'] = $payoutMethod->banks ?? [];
            $data['supportedCurrency'] = (!empty($payoutMethod->supported_currency)) ? $payoutMethod->supported_currency : null;
            $data['isAutomatic'] = $payoutMethod->is_automatic;
            $data['wallet'] = Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $payout->currency_id]);
            $data['payout'] = $payout;
            $data['payout']['method'] = $payout->method?->name ?? null;
            $data['payout']['currencyCode'] = $payout->payout_currency_code ?? null;
            $twoFactorSetting = TwoFactorSetting::firstOrCreate(['user_id' => $user->id]);
            $data['enable_for'] = in_array('payout', is_null($twoFactorSetting->enable_for)
                ? [] : json_decode($twoFactorSetting->enable_for, true));

            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payoutConfirmSubmit(Request $request)
    {
        DB::beginTransaction();
        try {
            $trx_id = request('trx_id');
            $user = Auth::user();
            $payout = Payout::with('method')->where('trx_id', $trx_id)->where('user_id',$user->id)->first();
            if (!$payout) {
                return response()->json($this->withErrors('Record not found'));
            }
            $payoutMethod = PayoutMethod::find($payout->payout_method_id);
            if (!$payoutMethod) {
                return response()->json($this->withErrors('Method not found'));
            }

            $validator = Validator::make($request->all(), $this->getValidationRules($payoutMethod, $request));
            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $securityPin = $request->security_pin;
            $pinValidation = $this->validateSecurityPin($user, $securityPin);
            if (isset($pinValidation['error'])) {
                return response()->json($this->withErrors($pinValidation['error']));
            }

            $wallet = Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $payout->currency_id]);
            $validateData = $this->validationCheck($payout->amount, $payout->payout_currency_code, $payout->payout_method_id);
            if (!$validateData['status']) {
                return response()->json($this->withErrors($validateData['message']));
            }

            $reqField = $this->getRequestFields($payoutMethod, $request);
            $this->addRequestField($reqField, 'amount', 'Amount', getAmount($payout->amount));
            $this->addRequestField($reqField, 'currency', 'Currency', $payout->payout_currency_code);
            if ($payoutMethod->code == "paypal") {
                $this->addRequestField($reqField, 'recipient_type', 'Recipient Type', $request->recipient_type);
            }

            $payout->information = $reqField;
            $payout->save();
            updateWallet($payout->user_id, $payout->currency_id, $payout->net_amount, 0);

            // Create transaction record for payout debit
            $this->createPayoutTransaction($payout, '-', 'Payout request - amount debited');

            $result = $this->processPayout($payout);
            $message = 'Payout generated successfully';
            if ($result['success']) {
                $message = $result['message'];
            }
            if ($payout->status == 1){
                $this->userNotify($user, $payout);
            }

            DB::commit();
            return response()->json($this->withSuccess($message));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payoutConfirmSubmitFlutter(Request $request)
    {
        DB::beginTransaction();
        try {
            $trx_id = request('trx_id');
            $user = Auth::user();
            $payout = Payout::with('method')->where('trx_id', $trx_id)->where('user_id',$user->id)->first();
            if (!$payout) {
                return response()->json($this->withErrors('Record not found'));
            }
            $payoutMethod = PayoutMethod::find($payout->payout_method_id);
            if ($payoutMethod->code != 'flutterwave') {
                return response()->json($this->withErrors('Method not allowed'));
            }

            $purifiedData = Purify::clean($request->all());
            if (empty($purifiedData['transfer_name'])) {
                return response()->json($this->withErrors('Transfer field is required'));
            }
            $validation = config('banks.' . $purifiedData['transfer_name'] . '.validation');

            $rules = [];
            if ($validation != null) {
                foreach ($validation as $key => $cus) {
                    $rules[$key] = 'required';
                }
            }
            if (in_array($request->transfer_name, ['NGN BANK', 'NGN DOM', 'GHS BANK', 'KES BANK', 'ZAR BANK'])) {
                $rules['bank'] = 'required';
            }
            $validate = Validator::make($request->all(), $rules);
            if ($validate->fails()) {
                return response()->json($this->withErrors(collect($validate->errors())->collapse()));
            }

            $securityPin = $request->security_pin;
            $pinValidation = $this->validateSecurityPin($user, $securityPin);
            if (isset($pinValidation['error'])) {
                return response()->json($this->withErrors($pinValidation['error']));
            }

            $wallet = Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $payout->currency_id]);
            $validateData = $this->validationCheck($payout->amount, $payout->payout_currency_code, $payout->payout_method_id);
            if (!$validateData['status']) {
                return response()->json($this->withErrors($validateData['message']));
            }

            $collection = collect($purifiedData);
            $reqField = [];
            $metaField = [];
            $inputForm = config('banks.' . $purifiedData['transfer_name'] . '.input_form');

            if ($inputForm != null) {
                foreach ($collection as $k => $v) {
                    foreach ($inputForm as $inKey => $inVal) {
                        if ($k != $inKey) {
                            continue;
                        } else {
                            if ($inVal == 'meta') {
                                $metaField[$inKey] = [
                                    'field_name' => $k,
                                    'field_value' => $v,
                                    'type' => 'text',
                                ];
                            } else {
                                $reqField[$inKey] = [
                                    'field_name' => $k,
                                    'field_value' => $v,
                                    'type' => 'text',
                                ];
                            }
                        }
                    }
                }
                $transferName = $request->transfer_name;

                if (in_array($transferName, ['NGN BANK', 'NGN DOM', 'GHS BANK', 'KES BANK', 'ZAR BANK'])) {
                    $accountBank = 'Account Bank';
                } elseif ($transferName == 'XAF/XOF MOMO') {
                    $accountBank = 'MTN';
                } elseif (in_array($transferName, ['FRANCOPGONE', 'mPesa', 'Rwanda Momo', 'Uganda Momo', 'Zambia Momo'])) {
                    $accountBank = 'MPS';
                } elseif (in_array($transferName, ['Barter', 'flutterwave'])) {
                    $accountBank = 'barter';
                } elseif ($transferName == 'NUMERO') {
                    $accountBank = 'Numero Bank';
                }

                $reqField['account_bank'] = [
                    'field_name' => $accountBank ?? '',
                    'field_value' => $transferName ?? '',
                    'type' => 'text',
                ];
                $reqField['amount'] = [
                    'field_name' => 'Amount',
                    'field_value' => getAmount($payout->amount),
                    'type' => 'text',
                ];
                $reqField['currency_code'] = [
                    'field_name' => 'Currency',
                    'field_value' => $payout->payout_currency_code,
                    'type' => 'text',
                ];
                $payout->information = $reqField;
                $payout->meta_field = $metaField;
            } else {
                $payout->information = null;
                $payout->meta_field = null;
            }
            $payout->save();
            updateWallet($payout->user_id, $payout->currency_id, $payout->net_amount, 0);

            // Create transaction record for payout debit
            $this->createPayoutTransaction($payout, '-', 'Payout request - amount debited');

            $result = $this->processPayout($payout);
            $message = 'Payout generated successfully';
            if ($result['success']) {
                $message = $result['message'];
            }
            if ($payout->status == 1){
                $this->userNotify($user, $payout);
            }

            DB::commit();
            return response()->json($this->withSuccess($message));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json($this->withErrors($e->getMessage()));
        }

    }

    public function payoutConfirmSubmitPaystack(Request $request)
    {
        DB::beginTransaction();
        try {
            $trx_id = $request->trx_id;
            $user = auth()->user();
            $basicControl = basicControl();
            $payout = Payout::where('trx_id', $trx_id)->where('user_id',$user->id)->first();
            if (!$payout) {
                return response()->json($this->withErrors('Record not found'));
            }
            $payoutMethod = PayoutMethod::find($payout->payout_method_id);
            if ($payoutMethod->code != 'paystack') {
                return response()->json($this->withErrors('Method not allowed'));
            }

            $validate = Validator::make($request->all(), $this->getValidationRules($payoutMethod, $request));
            if ($validate->fails()) {
                return response()->json($this->withErrors(collect($validate->errors())->collapse()[0]));
            }
            if (empty($request->bank)) {
                return response()->json($this->withErrors('Bank field is required'));
            }

            $securityPin = $request->security_pin;
            $pinValidation = $this->validateSecurityPin($user, $securityPin);
            if (isset($pinValidation['error'])) {
                return response()->json($this->withErrors($pinValidation['error']));
            }

            $wallet = Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $payout->currency_id]);
            $validateData = $this->validationCheck($payout->amount, $payout->payout_currency_code, $payout->payout_method_id);
            if (!$validateData['status']) {
                return response()->json($this->withErrors($validateData['message']));
            }

            $reqField = $this->getRequestFields($payoutMethod, $request);
            $this->addRequestField($reqField, 'bank_code', 'Bank Code', $request->bank);
            $this->addRequestField($reqField, 'amount', 'Amount', getAmount($payout->amount));
            $this->addRequestField($reqField, 'currency', 'Currency', $payout->payout_currency_code);
            if (!empty($request->type)) {
                $this->addRequestField($reqField, 'type', 'Type', $request->type);
            }

            $payout->information = $reqField;
            $payout->save();
            updateWallet($payout->user_id, $payout->currency_id, $payout->net_amount, 0);

            // Create transaction record for payout debit
            $this->createPayoutTransaction($payout, '-', 'Payout request - amount debited');

            $result = $this->processPayout($payout);
            $message = 'Payout generated successfully';
            if ($result['success']) {
                $message = $result['message'];
            }
            if ($payout->status == 1){
                $this->userNotify($user, $payout);
            }

            DB::commit();
            return response()->json($this->withSuccess($message));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payoutConfirmSubmitNumero(Request $request)
    {
        DB::beginTransaction();
        try {
            $trx_id = request('trx_id');
            $user = Auth::user();
            $payout = Payout::with('method')->where('trx_id', $trx_id)->where('user_id',$user->id)->first();
            if (!$payout) {
                return response()->json($this->withErrors('Record not found'));
            }
            $payoutMethod = PayoutMethod::find($payout->payout_method_id);
            if ($payoutMethod->code != 'numero') {
                return response()->json($this->withErrors('Method not allowed'));
            }

            $purifiedData = Purify::clean($request->all());
            if (empty($purifiedData['bank_code'])) {
                return response()->json($this->withErrors('Bank code field is required'));
            }

            $validator = Validator::make($request->all(), $this->getValidationRules($payoutMethod, $request));
            if ($validator->fails()) {
                return response()->json($this->withErrors($validator->errors()->all()));
            }

            $securityPin = $request->security_pin;
            $pinValidation = $this->validateSecurityPin($user, $securityPin);
            if (isset($pinValidation['error'])) {
                return response()->json($this->withErrors($pinValidation['error']));
            }

            $reqField = $this->getRequestFields($payoutMethod, $request);
            $this->addRequestField($reqField, 'bank_code', 'Bank Code', $request->bank_code);
            $this->addRequestField($reqField, 'amount', 'Amount', getAmount($payout->amount));
            $this->addRequestField($reqField, 'currency', 'Currency', $payout->payout_currency_code);

            $payout->information = $reqField;
            $payout->save();
            updateWallet($payout->user_id, $payout->currency_id, $payout->net_amount, 0);

            // Create transaction record for payout debit
            $this->createPayoutTransaction($payout, '-', 'Payout request - amount debited');

            $result = $this->processPayout($payout);
            $message = 'Payout generated successfully';
            if ($result['success']) {
                $message = $result['message'];
            }
            if ($payout->status == 1){
                $this->userNotify($user, $payout);
            }

            DB::commit();
            return response()->json($this->withSuccess($message));
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json($this->withErrors($e->getMessage()));
        }
    }


    public function payoutGetBankList(Request $request)
    {
        try {
            $currencyCode = $request->currencyCode;
            $method = $request->method ?? 'paystack'; // Default to paystack for backward compatibility

            // Support different payout providers
            switch ($method) {
                case 'numero':
                    $methodObj = 'App\\Services\\Payout\\numero\\Card';
                    break;
                case 'flutterwave':
                    $methodObj = 'App\\Services\\Payout\\flutterwave\\Card';
                    break;
                default:
                    $methodObj = 'App\\Services\\Payout\\paystack\\Card';
                    break;
            }

            $data = $methodObj::getBank($currencyCode);
            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payoutGetBankFrom(Request $request)
    {
        try {
            $bankName = $request->bankName;
            $bankArr = config('banks.' . $bankName);
            $value['bank'] = null;
            if ($bankArr['api'] != null) {
                // Use appropriate provider based on bank name
                if ($bankName == 'NUMERO') {
                    $methodObj = 'App\\Services\\Payout\\numero\\Card';
                } else {
                    $methodObj = 'App\\Services\\Payout\\flutterwave\\Card';
                }
                $data = $methodObj::getBank($bankArr['api']);
                $value['bank'] = $data;
            }
            $value['input_form'] = $bankArr['input_form'];
            return response()->json($this->withSuccess($value));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payout()
    {
        try {
            $userId = Auth::id();

            $data['gateways'] = PayoutMethod::whereIs_active(1)->get()->map(function ($query) use ($userId) {
                $method['id'] = $query->id;
                $method['name'] = $query->name;
                $method['image'] = getFile($query->driver, $query->logo);
                $method['description'] = $query->description;

                $method['supported_currency'] = $query->supported_currency;
                $method['currency_type'] = $query->currency_type;
                $method['isAutomatic'] = $query->is_automatic;
                $method['inputForm'] = $query->inputForm;

                // Get effective payout currencies (with merchant-specific overrides if any)
                $effectivePayoutCurrencies = [];
                foreach ($query->payout_currencies as $currency) {
                    try {
                        $currencyName = $currency['name'] ?? $currency['currency_symbol'] ?? 'NGN';
                        $effectiveConfig = MerchantPayoutConfiguration::getEffectiveConfigFor($userId, $query->id, $currencyName);
                        $effectivePayoutCurrencies[] = $effectiveConfig;
                    } catch (\Exception $e) {
                        // If error getting effective config, use original currency config
                        $effectivePayoutCurrencies[] = array_merge($currency, [
                            'has_custom_config' => false,
                            'custom_fields' => []
                        ]);
                    }
                }

                $method['payout_currencies'] = $effectivePayoutCurrencies;
                $method['has_custom_config'] = collect($effectivePayoutCurrencies)->contains('has_custom_config', true);

                return $method;
            });
            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    public function payoutList(Request $request)
    {
        try {
            $userId = Auth::id();
            $filters = $request->all();

            $data['payouts'] = Payout::query()
                ->where('user_id', $userId)
                ->search($filters, $userId)
                ->latest()->paginate(15)
                ->through(fn($payout) => $this->transformPayout($payout));

            $data['currencies'] = Currency::select('id', 'code', 'name')->orderBy('code', 'ASC')->get();

            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    private function transformPayout($payout): array
    {
        return [
            'requested_amount' => getAmount($payout->amount),
            'processing_fee' => getAmount($payout->charge),
            'total_debited' => getAmount($payout->net_amount),
            'currency' => $payout->payout_currency_code,
            'transactionId' => $payout->trx_id,
            'gateway' => $payout->method?->name,
            'status' => match ($payout->status) {
                0 => 'Pending',
                1 => 'Generated',
                2 => 'Payout Done',
                3 => 'Canceled',
                6 => 'Failed',
                default => 'Unknown'
            },
            'createdTime' => $payout->created_at,
        ];
    }

    /**
     * Check transaction status by reference
     */
    public function checkTransactionStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'reference' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $reference = $request->reference;

            // First, check if this is our internal transaction reference
            $payout = Payout::where('trx_id', $reference)
                ->orWhere('response_id', $reference)
                ->first();

            if ($payout) {
                // This is our internal transaction, return our status
                return response()->json($this->withSuccess([
                    'reference' => $reference,
                    'status' => match ($payout->status) {
                        0 => 'pending',
                        1 => 'processing',
                        2 => 'success',
                        3 => 'cancelled',
                        6 => 'failed',
                        default => 'unknown'
                    },
                    'amount' => getAmount($payout->amount),
                    'currency' => $payout->payout_currency_code,
                    //'method' => $payout->method?->name,
                    'created_at' => $payout->created_at,
                    'updated_at' => $payout->updated_at,
                    'provider_reference' => $payout->response_id,
                ]));
            }

            // If not found in our records, assume it's a Numero reference and check with Numero
            $numeroMethod = PayoutMethod::where('code', 'numero')->first();
            if (!$numeroMethod) {
                return response()->json($this->withErrors('payout method not configured'));
            }

            // Use Numero's checkTransferStatus method
            $methodClass = 'App\\Services\\Payout\\numero\\Card';
            if (!method_exists($methodClass, 'checkTransferStatus')) {
                return response()->json($this->withErrors('Status check not supported for this provider'));
            }

            $statusResponse = $methodClass::checkTransferStatus($reference, $numeroMethod);

            if ($statusResponse['status'] === 'success') {
                return response()->json($this->withSuccess([
                    'reference' => $reference,
                    'provider' => 'numero',
                    'data' => $statusResponse['data']
                ]));
            } else {
                return response()->json($this->withErrors($statusResponse['data'] ?? 'Status check failed'));
            }

        } catch (\Exception $e) {
            return response()->json($this->withErrors('Status check failed: ' . $e->getMessage()));
        }
    }

}
