<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MerchantPayoutConfiguration;
use App\Models\PayoutMethod;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class MerchantPayoutConfigurationController extends Controller
{
    /**
     * Display merchant payout configuration page
     */
    public function index(Request $request, $merchantId)
    {
        $merchant = User::findOrFail($merchantId);
        
        // Get all active payout methods
        $payoutMethods = PayoutMethod::where('is_active', 1)->get();
        
        // Get existing configurations for this merchant
        $existingConfigs = MerchantPayoutConfiguration::where('merchant_id', $merchantId)
            ->where('is_active', 1)
            ->get()
            ->groupBy('payout_method_id')
            ->map(function ($configs) {
                return $configs->keyBy('currency');
            });
        
        $data = [
            'merchant' => $merchant,
            'payoutMethods' => $payoutMethods,
            'existingConfigs' => $existingConfigs,
            'page_title' => 'Merchant Payout Configuration'
        ];
        
        return view('merchant::admin.payout-configuration.index', $data);
    }

    /**
     * Update merchant payout configuration
     */
    public function update(Request $request, $merchantId)
    {
        $merchant = User::findOrFail($merchantId);
        
        $validator = Validator::make($request->all(), [
            'configurations' => 'required|array',
            'configurations.*.payout_method_id' => 'required|exists:payout_methods,id',
            'configurations.*.currency' => 'required|string|max:10',
            'configurations.*.min_limit' => 'nullable|numeric|min:0',
            'configurations.*.max_limit' => 'nullable|numeric|min:0',
            'configurations.*.percentage_charge' => 'nullable|numeric|min:0|max:100',
            'configurations.*.fixed_charge' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();
        try {
            foreach ($request->configurations as $config) {
                $payoutMethodId = $config['payout_method_id'];
                $currency = $config['currency'];

                // Check if any custom values are provided
                $hasCustomValues = !empty($config['min_limit'])
                    || !empty($config['max_limit'])
                    || !empty($config['percentage_charge'])
                    || !empty($config['fixed_charge']);

                if ($hasCustomValues) {
                    // Validate max_limit > min_limit if both are provided
                    if (!empty($config['min_limit']) && !empty($config['max_limit'])) {
                        if ($config['max_limit'] <= $config['min_limit']) {
                            return back()->with('error', "Maximum limit must be greater than minimum limit for {$currency} in payout method ID: {$payoutMethodId}");
                        }
                    }
                    // Create or update configuration
                    MerchantPayoutConfiguration::updateOrCreate(
                        [
                            'merchant_id' => $merchantId,
                            'payout_method_id' => $payoutMethodId,
                            'currency' => $currency
                        ],
                        [
                            'min_limit' => !empty($config['min_limit']) || $config['min_limit'] == 0 ? $config['min_limit'] : null,
                            'max_limit' => !empty($config['max_limit'])  || $config['max_limit'] == 0 ? $config['max_limit'] : null,
                            'percentage_charge' => !empty($config['percentage_charge']) || $config['percentage_charge'] == 0 ? $config['percentage_charge'] : null,
                            'fixed_charge' => !empty($config['fixed_charge']) || $config['fixed_charge'] == 0 ? $config['fixed_charge'] : null,
                            'is_active' => 1
                        ]
                    );
                } else {
                    // Remove configuration if no custom values
                    MerchantPayoutConfiguration::where([
                        'merchant_id' => $merchantId,
                        'payout_method_id' => $payoutMethodId,
                        'currency' => $currency
                    ])->delete();
                }
            }

            DB::commit();
            return back()->with('success', 'Merchant payout configuration updated successfully');
            
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to update configuration: ' . $e->getMessage());
        }
    }

    /**
     * Get effective configuration for a merchant and payout method (AJAX)
     */
    public function getEffectiveConfig(Request $request, $merchantId, $payoutMethodId)
    {
        try {
            $currency = $request->get('currency', 'NGN');
            $effectiveConfig = MerchantPayoutConfiguration::getEffectiveConfigFor($merchantId, $payoutMethodId, $currency);
            
            return response()->json([
                'status' => 'success',
                'data' => $effectiveConfig
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Delete specific merchant payout configuration
     */
    public function destroy(Request $request, $merchantId, $payoutMethodId)
    {
        try {
            $currency = $request->input('currency', 'NGN');

            $deleted = MerchantPayoutConfiguration::where([
                'merchant_id' => $merchantId,
                'payout_method_id' => $payoutMethodId,
                'currency' => $currency
            ])->delete();

            if ($deleted) {
                return back()->with('success', "Custom configuration for {$currency} removed successfully");
            } else {
                return back()->with('error', 'Configuration not found');
            }
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to remove configuration: ' . $e->getMessage());
        }
    }

    /**
     * Get default configuration for a payout method (AJAX)
     */
    public function getDefaultConfig($payoutMethodId)
    {
        try {
            $payoutMethod = PayoutMethod::findOrFail($payoutMethodId);
            $defaultConfig = collect($payoutMethod->payout_currencies)->first();
            
            return response()->json([
                'status' => 'success',
                'data' => $defaultConfig
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Bulk update configurations for multiple merchants
     */
    public function bulkUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'merchant_ids' => 'required|array',
            'merchant_ids.*' => 'exists:users,id',
            'payout_method_id' => 'required|exists:payout_methods,id',
            'min_limit' => 'nullable|numeric|min:0',
            'max_limit' => 'nullable|numeric|min:0',
            'percentage_charge' => 'nullable|numeric|min:0|max:100',
            'fixed_charge' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Validate max_limit > min_limit if both are provided
        if (!empty($request->min_limit) && !empty($request->max_limit)) {
            if ($request->max_limit <= $request->min_limit) {
                return back()->with('error', 'Maximum limit must be greater than minimum limit');
            }
        }

        DB::beginTransaction();
        try {
            foreach ($request->merchant_ids as $merchantId) {
                MerchantPayoutConfiguration::updateOrCreate(
                    [
                        'merchant_id' => $merchantId,
                        'payout_method_id' => $request->payout_method_id
                    ],
                    [
                        'min_limit' => $request->min_limit,
                        'max_limit' => $request->max_limit,
                        'percentage_charge' => $request->percentage_charge,
                        'fixed_charge' => $request->fixed_charge,
                        'is_active' => 1
                    ]
                );
            }

            DB::commit();
            $count = count($request->merchant_ids);
            return back()->with('success', "Bulk configuration applied to {$count} merchants successfully");
            
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to apply bulk configuration: ' . $e->getMessage());
        }
    }
}
