<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the basic roles table if it doesn't exist.
     * This ensures compatibility with the existing role system.
     */
    public function up(): void
    {
        // Only create if the table doesn't exist
        if (!Schema::hasTable('roles')) {
            Schema::create('roles', function (Blueprint $table) {
                $table->id();
                $table->string('name', 100)->comment('Role name');
                $table->json('permission')->nullable()->comment('Array of permission IDs');
                $table->boolean('status')->default(1)->comment('Role status: 0=inactive, 1=active');
                $table->unsignedBigInteger('user_id')->nullable()->comment('Admin who created this role');
                $table->timestamps();
                
                // Indexes
                $table->index(['status']);
                
                // Foreign key
                $table->foreign('user_id')->references('id')->on('admins')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only drop if we created it
        if (Schema::hasTable('roles')) {
            Schema::dropIfExists('roles');
        }
    }
};
