<?php

require 'vendor/autoload.php';

use App\Mail\SendMail;
use App\Models\NotificationTemplate;
use Illuminate\Support\Facades\DB;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING EMAIL FORMATTING FIX ===\n\n";

// Check initial queue count
$initialCount = DB::table('jobs')->count();
echo "Initial queue jobs: {$initialCount}\n\n";

// Test the SendMail formatting with sample content that has line breaks
$testMessage = "Dear Test User,\r\n\r\nThis is a test message with line breaks.\r\n\r\nDetails:\r\n- Item 1\r\n- Item 2\r\n- Item 3\r\n\r\nThank you!";

echo "Original message with \\r\\n:\n";
echo json_encode($testMessage) . "\n\n";

// Create SendMail instance to test formatting
$basic = basicControl();
$sendMail = new SendMail($basic->sender_email, 'Test Email Formatting', $testMessage);

echo "Formatted message (should have <br> tags):\n";
echo $sendMail->message . "\n\n";

// Test with a real forex template
$forexTemplate = NotificationTemplate::where('template_key', 'FOREX_BOOKING_CONFIRMATION')->first();
if ($forexTemplate) {
    echo "Testing with real forex template:\n";
    echo "Original template (first 200 chars):\n";
    echo substr($forexTemplate->email, 0, 200) . "\n\n";
    
    // Create SendMail with the template content
    $testForexMail = new SendMail($basic->sender_email, 'Test Forex Email', $forexTemplate->email);
    
    echo "Formatted forex template (first 300 chars):\n";
    echo substr($testForexMail->message, 0, 300) . "\n\n";
    
    echo "Line break conversion check:\n";
    echo "Contains \\r\\n: " . (strpos($testForexMail->message, "\r\n") !== false ? 'Yes' : 'No') . "\n";
    echo "Contains <br>: " . (strpos($testForexMail->message, '<br>') !== false ? 'Yes' : 'No') . "\n";
}

// Test sending an actual email to see if it queues properly
echo "\nTesting actual email sending...\n";
try {
    \Illuminate\Support\Facades\Mail::to('<EMAIL>')->queue($sendMail);
    
    $finalCount = DB::table('jobs')->count();
    echo "Final queue jobs: {$finalCount}\n";
    echo "Jobs added: " . ($finalCount - $initialCount) . "\n";
    
    if ($finalCount > $initialCount) {
        echo "✅ Email successfully queued with proper formatting!\n";
    } else {
        echo "❌ Email was not queued\n";
    }
} catch (\Exception $e) {
    echo "❌ Error sending email: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "This fix applies to ALL emails in the system!\n";
