<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * Advanced User Role Assignment Model
 * 
 * Represents the assignment of roles to users/admins with temporal and contextual control.
 * Supports multiple roles per user, priority-based resolution, and assignment tracking.
 * 
 * @property int $id
 * @property int $user_id User/Admin ID
 * @property string $user_type User model type
 * @property int $role_id Reference to advanced_roles
 * @property bool $is_active Whether assignment is active
 * @property int $priority Role priority for conflict resolution
 * @property Carbon|null $assigned_at When role was assigned
 * @property Carbon|null $expires_at When assignment expires
 * @property array|null $schedule Time-based schedule
 * @property string|null $context Assignment context
 * @property array|null $context_data Additional context data
 * @property int|null $assigned_by <PERSON><PERSON> who assigned role
 * @property string|null $assignment_reason Reason for assignment
 * @property int|null $revoked_by <PERSON><PERSON> who revoked role
 * @property Carbon|null $revoked_at When role was revoked
 * @property string|null $revocation_reason Reason for revocation
 */
class AdvancedUserRole extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_type',
        'role_id',
        'is_active',
        'priority',
        'assigned_at',
        'expires_at',
        'schedule',
        'context',
        'context_data',
        'assigned_by',
        'assignment_reason',
        'revoked_by',
        'revoked_at',
        'revocation_reason',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'priority' => 'integer',
        'assigned_at' => 'datetime',
        'expires_at' => 'datetime',
        'revoked_at' => 'datetime',
        'schedule' => 'array',
        'context_data' => 'array',
    ];

    protected $attributes = [
        'is_active' => true,
        'priority' => 0,
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (auth()->check() && auth()->user() instanceof Admin) {
                $model->assigned_by = auth()->id();
                $model->assigned_at = now();
            }
        });
    }

    /**
     * Get the user (polymorphic)
     */
    public function user(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the role
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(AdvancedRole::class, 'role_id');
    }

    /**
     * Get the admin who assigned this role
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'assigned_by');
    }

    /**
     * Get the admin who revoked this role
     */
    public function revokedBy(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'revoked_by');
    }

    /**
     * Scope: Only active assignments
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Currently valid assignments
     */
    public function scopeCurrentlyValid(Builder $query): Builder
    {
        $now = now();
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>=', $now);
            });
    }

    /**
     * Scope: For specific user
     */
    public function scopeForUser(Builder $query, $user): Builder
    {
        return $query->where('user_id', $user->id)
            ->where('user_type', get_class($user));
    }

    /**
     * Scope: In specific context
     */
    public function scopeInContext(Builder $query, string $context): Builder
    {
        return $query->where('context', $context);
    }

    /**
     * Scope: Ordered by priority (highest first)
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderByDesc('priority');
    }

    /**
     * Check if assignment is currently valid
     */
    public function isCurrentlyValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        
        $now = now();
        
        // Check expiration
        if ($this->expires_at && $now->isAfter($this->expires_at)) {
            return false;
        }
        
        // Check schedule if present
        if ($this->schedule) {
            return $this->isValidBySchedule($now);
        }
        
        return true;
    }

    /**
     * Check if assignment is valid according to schedule
     */
    protected function isValidBySchedule(Carbon $dateTime): bool
    {
        if (!$this->schedule) {
            return true;
        }
        
        $schedule = $this->schedule;
        
        // Check day of week
        if (isset($schedule['days'])) {
            $currentDay = strtolower($dateTime->format('l'));
            if (!in_array($currentDay, $schedule['days'])) {
                return false;
            }
        }
        
        // Check time range
        if (isset($schedule['hours'])) {
            $currentTime = $dateTime->format('H:i');
            $startTime = $schedule['hours']['start'] ?? '00:00';
            $endTime = $schedule['hours']['end'] ?? '23:59';
            
            if ($currentTime < $startTime || $currentTime > $endTime) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Revoke this role assignment
     */
    public function revoke(string $reason = null): void
    {
        $this->update([
            'is_active' => false,
            'revoked_by' => auth()->id(),
            'revoked_at' => now(),
            'revocation_reason' => $reason,
        ]);
    }

    /**
     * Reactivate this role assignment
     */
    public function reactivate(): void
    {
        $this->update([
            'is_active' => true,
            'revoked_by' => null,
            'revoked_at' => null,
            'revocation_reason' => null,
        ]);
    }

    /**
     * Extend assignment expiration
     */
    public function extend(Carbon $newExpirationDate): void
    {
        $this->update(['expires_at' => $newExpirationDate]);
    }

    /**
     * Get assignment status
     */
    public function getStatus(): string
    {
        if (!$this->is_active) {
            return 'revoked';
        }
        
        if ($this->expires_at && now()->isAfter($this->expires_at)) {
            return 'expired';
        }
        
        if (!$this->isCurrentlyValid()) {
            return 'inactive';
        }
        
        return 'active';
    }

    /**
     * Get status badge HTML
     */
    public function getStatusBadge(): string
    {
        $status = $this->getStatus();
        
        return match ($status) {
            'active' => '<span class="badge bg-success">Active</span>',
            'inactive' => '<span class="badge bg-warning">Inactive</span>',
            'expired' => '<span class="badge bg-danger">Expired</span>',
            'revoked' => '<span class="badge bg-secondary">Revoked</span>',
            default => '<span class="badge bg-light">Unknown</span>',
        };
    }

    /**
     * Get assignment duration description
     */
    public function getDurationDescription(): string
    {
        if (!$this->assigned_at) {
            return 'Unknown duration';
        }
        
        $start = $this->assigned_at->format('Y-m-d H:i');
        
        if (!$this->expires_at) {
            return "From {$start} (permanent)";
        }
        
        $end = $this->expires_at->format('Y-m-d H:i');
        return "From {$start} to {$end}";
    }

    /**
     * Get schedule description
     */
    public function getScheduleDescription(): string
    {
        if (!$this->schedule) {
            return 'Always available';
        }
        
        $parts = [];
        
        if (isset($this->schedule['days'])) {
            $parts[] = 'Days: ' . implode(', ', $this->schedule['days']);
        }
        
        if (isset($this->schedule['hours'])) {
            $start = $this->schedule['hours']['start'] ?? '00:00';
            $end = $this->schedule['hours']['end'] ?? '23:59';
            $parts[] = "Hours: {$start} - {$end}";
        }
        
        return implode('; ', $parts);
    }

    /**
     * Assign role to user
     */
    public static function assignRole(
        $user,
        AdvancedRole $role,
        array $options = []
    ): self {
        return static::create(array_merge([
            'user_id' => $user->id,
            'user_type' => get_class($user),
            'role_id' => $role->id,
        ], $options));
    }

    /**
     * Get all active roles for a user
     */
    public static function getActiveRolesForUser($user): \Illuminate\Support\Collection
    {
        return static::forUser($user)
            ->currentlyValid()
            ->with('role')
            ->byPriority()
            ->get();
    }

    /**
     * Check if user has specific role
     */
    public static function userHasRole($user, string $roleName): bool
    {
        return static::forUser($user)
            ->currentlyValid()
            ->whereHas('role', function ($query) use ($roleName) {
                $query->where('name', $roleName);
            })
            ->exists();
    }
}
