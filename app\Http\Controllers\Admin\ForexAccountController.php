<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ForexAccount;
use App\Models\ForexAccountTransfer;
use App\Models\ForexTransaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;

class ForexAccountController extends Controller
{
    public function index()
    {
        $data['pageTitle'] = 'Forex Accounts';
        $data['accounts'] = ForexAccount::active()->latest()->get();
        $data['totalBalances'] = [
            'USD' => [
                'available' => ForexAccount::byCurrency('USD')->sum('balance'),
                'pending' => ForexAccount::byCurrency('USD')->sum('pending_balance'),
                'total' => ForexAccount::byCurrency('USD')->get()->sum('total_balance'),
            ],
            'EUR' => [
                'available' => ForexAccount::byCurrency('EUR')->sum('balance'),
                'pending' => ForexAccount::byCurrency('EUR')->sum('pending_balance'),
                'total' => ForexAccount::byCurrency('EUR')->get()->sum('total_balance'),
            ],
            'GBP' => [
                'available' => ForexAccount::byCurrency('GBP')->sum('balance'),
                'pending' => ForexAccount::byCurrency('GBP')->sum('pending_balance'),
                'total' => ForexAccount::byCurrency('GBP')->get()->sum('total_balance'),
            ],
            'NGN' => [
                'available' => ForexAccount::byCurrency('NGN')->sum('balance'),
                'pending' => ForexAccount::byCurrency('NGN')->sum('pending_balance'),
                'total' => ForexAccount::byCurrency('NGN')->get()->sum('total_balance'),
            ],
        ];
        return view('admin.forex.accounts.index', $data);
    }

    public function show($id)
    {
        $data['pageTitle'] = 'Account Details';
        $data['account'] = ForexAccount::with(['transactions' => function($query) {
            $query->latest()->limit(50);
        }])->findOrFail($id);
        return view('admin.forex.accounts.show', $data);
    }

    public function transactions($id, Request $request)
    {
        $account = ForexAccount::findOrFail($id);

        // If this is an export request, return CSV
        if ($request->has('export') && $request->export === 'csv') {
            return $this->exportTransactions($account, $request);
        }

        // If this is an AJAX request, return DataTables data
        if ($request->ajax()) {
            return $this->getTransactionsDataTable($account, $request);
        }

        // Otherwise, return the view
        $data['pageTitle'] = 'Transaction History';
        $data['account'] = $account;
        $data['transactionTypes'] = [
            'credit' => 'Credit',
            'debit' => 'Debit',
            'pending' => 'Pending (Reserved)',
            'pending_release' => 'Pending Released',
            'pending_cancelled' => 'Pending Cancelled',
            'pending_cleared' => 'Pending Cleared (Paid)',
            'transfer' => 'Transfer'
        ];

        // Get summary statistics
        $data['summary'] = [
            'total_transactions' => $account->transactions()->count(),
            'total_credits' => $account->transactions()->credits()->sum('amount'),
            'total_debits' => $account->transactions()->debits()->sum('amount'),
            'last_transaction' => $account->transactions()->latest()->first(),
        ];

        return view('admin.forex.accounts.transactions', $data);
    }

    public function transactionSearch($id, Request $request)
    {
        $account = ForexAccount::findOrFail($id);
        $search = $request->search['value'] ?? null;
        $filterTransactionType = $request->filterTransactionType;
        $filterDate = $request->filterDate;

        $transactions = $account->transactions()
            ->with(['forexBooking', 'createdBy'])
            ->when(!empty($search), function ($query) use ($search) {
                return $query->where('transaction_reference', 'LIKE', "%{$search}%")
                    ->orWhere('description', 'LIKE', "%{$search}%");
            })
            ->when($filterTransactionType, function ($query) use ($filterTransactionType) {
                return $query->where('transaction_type', $filterTransactionType);
            })
            ->when($filterDate, function ($query) use ($filterDate) {
                $dates = explode(' - ', $filterDate);
                if (count($dates) == 2) {
                    $query->whereDate('created_at', '>=', $dates[0])
                          ->whereDate('created_at', '<=', $dates[1]);
                }
            })
            ->latest();

        return DataTables::of($transactions)
            ->addColumn('date', function ($item) {
                return $item->created_at->format('M d, Y H:i');
            })
            ->addColumn('reference', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transaction.show', [$item->forex_account_id, $item->id]) . '" class="text-primary">' .
                       $item->transaction_reference . '</a>';
            })
            ->addColumn('type', function ($item) {
                $class = $item->type_class;
                return '<span class="badge bg-' . $class . '">' . ucfirst(str_replace('_', ' ', $item->transaction_type)) . '</span>';
            })
            ->addColumn('amount', function ($item) {
                return $item->formatted_amount;
            })
            ->addColumn('balance_after', function ($item) {
                return $item->formatted_balance_after;
            })
            ->addColumn('description', function ($item) {
                return $item->description;
            })
            ->addColumn('booking', function ($item) {
                return $item->forexBooking ?
                    '<a href="' . route('admin.forex.bookings.show', $item->forexBooking->id) . '">' . $item->forexBooking->booking_reference . '</a>' :
                    '-';
            })
            ->addColumn('created_by', function ($item) {
                return $item->createdBy->name ?? 'System';
            })
            ->addColumn('action', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transaction.show', [$item->forex_account_id, $item->id]) . '" class="btn btn-sm btn-outline-primary">' .
                       '<i class="bi-eye"></i> View</a>';
            })
            ->rawColumns(['reference', 'type', 'booking', 'action'])
            ->make(true);
    }

    private function getTransactionsDataTable($account, $request)
    {
        $transactions = $account->transactions()
            ->with(['forexBooking', 'createdBy'])
            ->when($request->transaction_type, function ($query) use ($request) {
                return $query->where('transaction_type', $request->transaction_type);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->search, function ($query) use ($request) {
                return $query->where(function ($q) use ($request) {
                    $q->where('transaction_reference', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            })
            ->latest();

        return DataTables::of($transactions)
            ->addColumn('reference', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transaction.show', [$item->forex_account_id, $item->id]) . '" class="text-primary">' .
                       $item->transaction_reference . '</a>';
            })
            ->addColumn('type', function ($item) {
                $class = $item->type_class;
                return '<span class="badge bg-' . $class . '">' . ucfirst(str_replace('_', ' ', $item->transaction_type)) . '</span>';
            })
            ->addColumn('amount', function ($item) {
                return $item->formatted_amount;
            })
            ->addColumn('balance_after', function ($item) {
                return $item->formatted_balance_after;
            })
            ->addColumn('description', function ($item) {
                return '<span title="' . $item->description . '">' .
                       Str::limit($item->description, 50) . '</span>';
            })
            ->addColumn('booking', function ($item) {
                return $item->forexBooking ?
                    '<a href="' . route('admin.forex.bookings.show', $item->forexBooking->id) . '">' .
                    $item->forexBooking->booking_reference . '</a>' : 'N/A';
            })
            ->addColumn('created_by', function ($item) {
                return $item->createdBy->name ?? 'System';
            })
            ->addColumn('date', function ($item) {
                return $item->created_at->format('M d, Y H:i');
            })
            ->addColumn('actions', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transaction.show', [$item->forex_account_id, $item->id]) . '" class="btn btn-sm btn-outline-primary">' .
                       '<i class="bi-eye"></i> View</a>';
            })
            ->rawColumns(['reference', 'type', 'description', 'booking', 'actions'])
            ->make(true);
    }

    public function transactionShow($accountId, $transactionId)
    {
        $account = ForexAccount::findOrFail($accountId);
        $transaction = ForexTransaction::with(['forexBooking', 'createdBy', 'forexAccount'])
            ->where('forex_account_id', $accountId)
            ->findOrFail($transactionId);

        $data['pageTitle'] = 'Transaction Details';
        $data['account'] = $account;
        $data['transaction'] = $transaction;

        // Get related transactions for transfers
        $data['relatedTransactions'] = collect([]);
        if ($transaction->metadata && isset($transaction->metadata['linked_transaction_id'])) {
            $data['relatedTransactions'] = ForexTransaction::with(['forexAccount'])
                ->where('id', $transaction->metadata['linked_transaction_id'])
                ->get();
        }

        return view('admin.forex.accounts.transaction-detail', $data);
    }

    private function exportTransactions($account, $request)
    {
        $transactions = $account->transactions()
            ->with(['forexBooking', 'createdBy'])
            ->when($request->transaction_type, function ($query) use ($request) {
                return $query->where('transaction_type', $request->transaction_type);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->search, function ($query) use ($request) {
                return $query->where(function ($q) use ($request) {
                    $q->where('transaction_reference', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            })
            ->latest()
            ->get();

        $filename = 'transactions_' . $account->account_name . '_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Date',
                'Reference',
                'Type',
                'Amount',
                'Currency',
                'Balance Before',
                'Balance After',
                'Description',
                'Booking Reference',
                'Created By'
            ]);

            // CSV Data
            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->created_at->format('Y-m-d H:i:s'),
                    $transaction->transaction_reference,
                    ucfirst(str_replace('_', ' ', $transaction->transaction_type)),
                    $transaction->amount,
                    $transaction->currency,
                    $transaction->balance_before,
                    $transaction->balance_after,
                    $transaction->description,
                    $transaction->forexBooking ? $transaction->forexBooking->booking_reference : '',
                    $transaction->createdBy->name ?? 'System'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function fund($id)
    {
        $data['pageTitle'] = 'Fund Account';
        $data['account'] = ForexAccount::findOrFail($id);
        return view('admin.forex.accounts.fund', $data);
    }

    public function processFunding(Request $request, $id)
    {
        $account = ForexAccount::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $transaction = $account->credit(
                $request->amount,
                $request->description,
                Auth::id(),
                null,
                ['funding_notes' => $request->notes]
            );

            return redirect()->route('admin.forex.accounts.show', $account->id)
                ->with('success', 'Account funded successfully. Transaction: ' . $transaction->transaction_reference);
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to fund account: ' . $e->getMessage())->withInput();
        }
    }

    public function transfer()
    {
        $data['pageTitle'] = 'Account Transfer';
        $data['accounts'] = ForexAccount::active()->get();
        return view('admin.forex.accounts.transfer', $data);
    }

    public function processTransfer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_account_id' => 'required|exists:forex_accounts,id',
            'to_account_id' => 'required|exists:forex_accounts,id|different:from_account_id',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $transfer = ForexAccountTransfer::createTransfer(
                $request->from_account_id,
                $request->to_account_id,
                $request->amount,
                $request->description,
                Auth::id(),
                $request->notes
            );

            return redirect()->route('admin.forex.accounts.index')
                ->with('success', 'Transfer completed successfully. Reference: ' . $transfer->transfer_reference);
        } catch (\Exception $e) {
            return back()->with('error', 'Transfer failed: ' . $e->getMessage())->withInput();
        }
    }

    public function transferHistory(Request $request)
    {
        // If this is an export request, return CSV
        if ($request->has('export') && $request->export === 'csv') {
            return $this->exportTransfers($request);
        }

        // If this is an AJAX request, return DataTables data
        if ($request->ajax()) {
            return $this->getTransfersDataTable($request);
        }

        // Otherwise, return the view
        $data['pageTitle'] = 'Transfer History';
        $data['accounts'] = ForexAccount::active()->get();
        $data['statusOptions'] = [
            'pending' => 'Pending',
            'completed' => 'Completed',
            'failed' => 'Failed'
        ];

        // Get summary statistics
        $data['summary'] = [
            'total_transfers' => ForexAccountTransfer::count(),
            'completed_transfers' => ForexAccountTransfer::where('status', 'completed')->count(),
            'pending_transfers' => ForexAccountTransfer::where('status', 'pending')->count(),
            'failed_transfers' => ForexAccountTransfer::where('status', 'failed')->count(),
            'total_amount' => ForexAccountTransfer::where('status', 'completed')->sum('amount'),
        ];

        return view('admin.forex.accounts.transfer-history', $data);
    }

    public function transferSearch(Request $request)
    {
        $search = $request->search['value'] ?? null;
        $filterStatus = $request->filterStatus;
        $filterFromAccount = $request->filterFromAccount;
        $filterToAccount = $request->filterToAccount;
        $filterDate = $request->filterDate;

        $transfers = ForexAccountTransfer::query()
            ->with(['fromAccount', 'toAccount', 'createdBy'])
            ->when(!empty($search), function ($query) use ($search) {
                return $query->where('transfer_reference', 'LIKE', "%{$search}%")
                    ->orWhere('description', 'LIKE', "%{$search}%")
                    ->orWhereHas('fromAccount', function ($q) use ($search) {
                        $q->where('account_name', 'LIKE', "%{$search}%");
                    })
                    ->orWhereHas('toAccount', function ($q) use ($search) {
                        $q->where('account_name', 'LIKE', "%{$search}%");
                    });
            })
            ->when($filterStatus, function ($query) use ($filterStatus) {
                return $query->where('status', $filterStatus);
            })
            ->when($filterFromAccount, function ($query) use ($filterFromAccount) {
                return $query->where('from_account_id', $filterFromAccount);
            })
            ->when($filterToAccount, function ($query) use ($filterToAccount) {
                return $query->where('to_account_id', $filterToAccount);
            })
            ->when($filterDate, function ($query) use ($filterDate) {
                $dates = explode(' - ', $filterDate);
                if (count($dates) == 2) {
                    $query->whereDate('created_at', '>=', $dates[0])
                          ->whereDate('created_at', '<=', $dates[1]);
                }
            })
            ->latest();

        return DataTables::of($transfers)
            ->addColumn('date', function ($item) {
                return $item->created_at->format('M d, Y H:i');
            })
            ->addColumn('reference', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transfer.show', $item->id) . '" class="text-primary">' .
                       $item->transfer_reference . '</a>';
            })
            ->addColumn('from_account', function ($item) {
                return '<a href="' . route('admin.forex.accounts.show', $item->fromAccount->id) . '">' .
                       $item->fromAccount->account_name . '</a>';
            })
            ->addColumn('to_account', function ($item) {
                return '<a href="' . route('admin.forex.accounts.show', $item->toAccount->id) . '">' .
                       $item->toAccount->account_name . '</a>';
            })
            ->addColumn('amount', function ($item) {
                return $item->formatted_amount;
            })
            ->addColumn('status', function ($item) {
                $class = $item->status_class;
                return '<span class="badge bg-' . $class . '">' . ucfirst($item->status) . '</span>';
            })
            ->addColumn('created_by', function ($item) {
                return $item->createdBy->name ?? 'System';
            })
            ->addColumn('action', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transfer.show', $item->id) . '" class="btn btn-sm btn-outline-primary">' .
                       '<i class="bi-eye"></i> View</a>';
            })
            ->rawColumns(['reference', 'from_account', 'to_account', 'status', 'action'])
            ->make(true);
    }

    private function getTransfersDataTable($request)
    {
        $transfers = ForexAccountTransfer::query()
            ->with(['fromAccount', 'toAccount', 'createdBy'])
            ->when($request->status, function ($query) use ($request) {
                return $query->where('status', $request->status);
            })
            ->when($request->from_account_id, function ($query) use ($request) {
                return $query->where('from_account_id', $request->from_account_id);
            })
            ->when($request->to_account_id, function ($query) use ($request) {
                return $query->where('to_account_id', $request->to_account_id);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->search, function ($query) use ($request) {
                return $query->where(function ($q) use ($request) {
                    $q->where('transfer_reference', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            })
            ->latest();

        return DataTables::of($transfers)
            ->addColumn('reference', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transfer.show', $item->id) . '" class="text-primary">' .
                       $item->transfer_reference . '</a>';
            })
            ->addColumn('from_account', function ($item) {
                return '<a href="' . route('admin.forex.accounts.show', $item->fromAccount->id) . '">' .
                       $item->fromAccount->account_name . '</a>';
            })
            ->addColumn('to_account', function ($item) {
                return '<a href="' . route('admin.forex.accounts.show', $item->toAccount->id) . '">' .
                       $item->toAccount->account_name . '</a>';
            })
            ->addColumn('amount', function ($item) {
                return $item->formatted_amount;
            })
            ->addColumn('status', function ($item) {
                $class = $item->status_class;
                return '<span class="badge bg-' . $class . '">' . ucfirst($item->status) . '</span>';
            })
            ->addColumn('created_by', function ($item) {
                return $item->createdBy->name ?? 'System';
            })
            ->addColumn('date', function ($item) {
                return $item->created_at->format('M d, Y H:i');
            })
            ->addColumn('actions', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transfer.show', $item->id) . '" class="btn btn-sm btn-outline-primary">' .
                       '<i class="bi-eye"></i> View</a>';
            })
            ->rawColumns(['reference', 'from_account', 'to_account', 'status', 'actions'])
            ->make(true);
    }

    public function transferShow($id)
    {
        $transfer = ForexAccountTransfer::with(['fromAccount', 'toAccount', 'createdBy'])
            ->findOrFail($id);

        $data['pageTitle'] = 'Transfer Details';
        $data['transfer'] = $transfer;

        // Get related transactions
        $data['relatedTransactions'] = ForexTransaction::with(['forexAccount'])
            ->where('metadata->transfer_id', $id)
            ->get();

        return view('admin.forex.accounts.transfer-detail', $data);
    }

    private function exportTransfers($request)
    {
        $transfers = ForexAccountTransfer::query()
            ->with(['fromAccount', 'toAccount', 'createdBy'])
            ->when($request->status, function ($query) use ($request) {
                return $query->where('status', $request->status);
            })
            ->when($request->from_account_id, function ($query) use ($request) {
                return $query->where('from_account_id', $request->from_account_id);
            })
            ->when($request->to_account_id, function ($query) use ($request) {
                return $query->where('to_account_id', $request->to_account_id);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->search, function ($query) use ($request) {
                return $query->where(function ($q) use ($request) {
                    $q->where('transfer_reference', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            })
            ->latest()
            ->get();

        $filename = 'transfers_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($transfers) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Date',
                'Reference',
                'From Account',
                'To Account',
                'Amount',
                'Currency',
                'Status',
                'Description',
                'Created By'
            ]);

            // CSV Data
            foreach ($transfers as $transfer) {
                fputcsv($file, [
                    $transfer->created_at->format('Y-m-d H:i:s'),
                    $transfer->transfer_reference,
                    $transfer->fromAccount->account_name,
                    $transfer->toAccount->account_name,
                    $transfer->amount,
                    $transfer->currency,
                    ucfirst($transfer->status),
                    $transfer->description,
                    $transfer->createdBy->name ?? 'System'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function globalSearch(Request $request)
    {
        // If this is an export request, return CSV
        if ($request->has('export') && $request->export === 'csv') {
            return $this->exportGlobalSearch($request);
        }

        // If this is an AJAX request, return DataTables data
        if ($request->ajax()) {
            return $this->getGlobalSearchDataTable($request);
        }

        // Otherwise, return the view
        $data['pageTitle'] = 'Global Transaction Search';
        $data['accounts'] = ForexAccount::active()->get();
        $data['transactionTypes'] = [
            'credit' => 'Credit',
            'debit' => 'Debit',
            'pending' => 'Pending (Reserved)',
            'pending_release' => 'Pending Released',
            'pending_cancelled' => 'Pending Cancelled',
            'pending_cleared' => 'Pending Cleared (Paid)',
            'transfer' => 'Transfer'
        ];

        // Get summary statistics for all accounts
        $data['summary'] = [
            'total_transactions' => ForexTransaction::count(),
            'total_accounts' => ForexAccount::active()->count(),
            'total_credits' => ForexTransaction::credits()->sum('amount'),
            'total_debits' => ForexTransaction::debits()->sum('amount'),
            'recent_transactions' => ForexTransaction::latest()->limit(5)->get(),
        ];

        return view('admin.forex.accounts.global-search', $data);
    }

    private function getGlobalSearchDataTable($request)
    {
        $transactions = ForexTransaction::query()
            ->with(['forexAccount', 'forexBooking', 'createdBy'])
            ->when($request->account_id, function ($query) use ($request) {
                return $query->where('forex_account_id', $request->account_id);
            })
            ->when($request->transaction_type, function ($query) use ($request) {
                return $query->where('transaction_type', $request->transaction_type);
            })
            ->when($request->currency, function ($query) use ($request) {
                return $query->where('currency', $request->currency);
            })
            ->when($request->amount_min, function ($query) use ($request) {
                return $query->where('amount', '>=', $request->amount_min);
            })
            ->when($request->amount_max, function ($query) use ($request) {
                return $query->where('amount', '<=', $request->amount_max);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->search, function ($query) use ($request) {
                return $query->where(function ($q) use ($request) {
                    $q->where('transaction_reference', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%')
                      ->orWhereHas('forexBooking', function ($bookingQuery) use ($request) {
                          $bookingQuery->where('booking_reference', 'like', '%' . $request->search . '%');
                      })
                      ->orWhereHas('forexAccount', function ($accountQuery) use ($request) {
                          $accountQuery->where('account_name', 'like', '%' . $request->search . '%');
                      });
                });
            })
            ->latest();

        return DataTables::of($transactions)
            ->addColumn('account', function ($item) {
                return '<a href="' . route('admin.forex.accounts.show', $item->forexAccount->id) . '">' .
                       $item->forexAccount->account_name . '</a>';
            })
            ->addColumn('reference', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transaction.show', [$item->forex_account_id, $item->id]) . '" class="text-primary">' .
                       $item->transaction_reference . '</a>';
            })
            ->addColumn('type', function ($item) {
                $class = $item->type_class;
                return '<span class="badge bg-' . $class . '">' . ucfirst(str_replace('_', ' ', $item->transaction_type)) . '</span>';
            })
            ->addColumn('amount', function ($item) {
                return $item->formatted_amount;
            })
            ->addColumn('balance_after', function ($item) {
                return $item->formatted_balance_after;
            })
            ->addColumn('description', function ($item) {
                return '<span title="' . $item->description . '">' .
                       Str::limit($item->description, 50) . '</span>';
            })
            ->addColumn('booking', function ($item) {
                return $item->forexBooking ?
                    '<a href="' . route('admin.forex.bookings.show', $item->forexBooking->id) . '">' .
                    $item->forexBooking->booking_reference . '</a>' : 'N/A';
            })
            ->addColumn('created_by', function ($item) {
                return $item->createdBy->name ?? 'System';
            })
            ->addColumn('date', function ($item) {
                return $item->created_at->format('M d, Y H:i');
            })
            ->addColumn('actions', function ($item) {
                return '<a href="' . route('admin.forex.accounts.transaction.show', [$item->forex_account_id, $item->id]) . '" class="btn btn-sm btn-outline-primary">' .
                       '<i class="bi-eye"></i> View</a>';
            })
            ->rawColumns(['account', 'reference', 'type', 'description', 'booking', 'actions'])
            ->make(true);
    }

    private function exportGlobalSearch($request)
    {
        $transactions = ForexTransaction::query()
            ->with(['forexAccount', 'forexBooking', 'createdBy'])
            ->when($request->account_id, function ($query) use ($request) {
                return $query->where('forex_account_id', $request->account_id);
            })
            ->when($request->transaction_type, function ($query) use ($request) {
                return $query->where('transaction_type', $request->transaction_type);
            })
            ->when($request->currency, function ($query) use ($request) {
                return $query->where('currency', $request->currency);
            })
            ->when($request->amount_min, function ($query) use ($request) {
                return $query->where('amount', '>=', $request->amount_min);
            })
            ->when($request->amount_max, function ($query) use ($request) {
                return $query->where('amount', '<=', $request->amount_max);
            })
            ->when($request->date_from, function ($query) use ($request) {
                return $query->whereDate('created_at', '>=', $request->date_from);
            })
            ->when($request->date_to, function ($query) use ($request) {
                return $query->whereDate('created_at', '<=', $request->date_to);
            })
            ->when($request->search, function ($query) use ($request) {
                return $query->where(function ($q) use ($request) {
                    $q->where('transaction_reference', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%')
                      ->orWhereHas('forexBooking', function ($bookingQuery) use ($request) {
                          $bookingQuery->where('booking_reference', 'like', '%' . $request->search . '%');
                      })
                      ->orWhereHas('forexAccount', function ($accountQuery) use ($request) {
                          $accountQuery->where('account_name', 'like', '%' . $request->search . '%');
                      });
                });
            })
            ->latest()
            ->get();

        $filename = 'global_transactions_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Date',
                'Account',
                'Reference',
                'Type',
                'Amount',
                'Currency',
                'Balance Before',
                'Balance After',
                'Description',
                'Booking Reference',
                'Created By'
            ]);

            // CSV Data
            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->created_at->format('Y-m-d H:i:s'),
                    $transaction->forexAccount->account_name,
                    $transaction->transaction_reference,
                    ucfirst(str_replace('_', ' ', $transaction->transaction_type)),
                    $transaction->amount,
                    $transaction->currency,
                    $transaction->balance_before,
                    $transaction->balance_after,
                    $transaction->description,
                    $transaction->forexBooking ? $transaction->forexBooking->booking_reference : '',
                    $transaction->createdBy->name ?? 'System'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function balanceSummary(Request $request)
    {
        $data['pageTitle'] = 'Balance Summary & Reports';
        $data['accounts'] = ForexAccount::active()->get();

        // Get date range for historical data (default to last 30 days)
        $dateFrom = $request->date_from ? Carbon::parse($request->date_from) : Carbon::now()->subDays(30);
        $dateTo = $request->date_to ? Carbon::parse($request->date_to) : Carbon::now();

        // Current balance summary
        $data['currentSummary'] = [
            'available_usd' => ForexAccount::byCurrency('USD')->sum('balance'),
            'available_eur' => ForexAccount::byCurrency('EUR')->sum('balance'),
            'available_gbp' => ForexAccount::byCurrency('GBP')->sum('balance'),
            'available_ngn' => ForexAccount::byCurrency('NGN')->sum('balance'),
            'pending_usd' => ForexAccount::byCurrency('USD')->sum('pending_balance'),
            'pending_eur' => ForexAccount::byCurrency('EUR')->sum('pending_balance'),
            'pending_gbp' => ForexAccount::byCurrency('GBP')->sum('pending_balance'),
            'pending_ngn' => ForexAccount::byCurrency('NGN')->sum('pending_balance'),
            'total_usd' => ForexAccount::byCurrency('USD')->get()->sum('total_balance'),
            'total_eur' => ForexAccount::byCurrency('EUR')->get()->sum('total_balance'),
            'total_gbp' => ForexAccount::byCurrency('GBP')->get()->sum('total_balance'),
            'total_ngn' => ForexAccount::byCurrency('NGN')->get()->sum('total_balance'),
        ];

        // Account type breakdown
        $data['accountTypeBreakdown'] = ForexAccount::active()
            ->selectRaw('account_type, currency_code, COUNT(*) as count, SUM(balance) as total_balance, SUM(pending_balance) as total_pending')
            ->groupBy('account_type', 'currency_code')
            ->get();

        // Historical balance trends (daily snapshots)
        $data['historicalData'] = $this->getHistoricalBalanceData($dateFrom, $dateTo);

        // Transaction volume analysis
        $data['transactionAnalysis'] = [
            'daily_volume' => ForexTransaction::whereBetween('created_at', [$dateFrom, $dateTo])
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as volume')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
            'type_breakdown' => ForexTransaction::whereBetween('created_at', [$dateFrom, $dateTo])
                ->selectRaw('transaction_type, COUNT(*) as count, SUM(amount) as volume')
                ->groupBy('transaction_type')
                ->get(),
            'currency_breakdown' => ForexTransaction::whereBetween('created_at', [$dateFrom, $dateTo])
                ->selectRaw('currency, COUNT(*) as count, SUM(amount) as volume')
                ->groupBy('currency')
                ->get(),
        ];

        // Account utilization metrics
        $data['utilizationMetrics'] = $data['accounts']->map(function ($account) use ($dateFrom, $dateTo) {
            $transactionCount = $account->transactions()
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->count();

            $transactionVolume = $account->transactions()
                ->whereBetween('created_at', [$dateFrom, $dateTo])
                ->sum('amount');

            return [
                'account' => $account,
                'transaction_count' => $transactionCount,
                'transaction_volume' => $transactionVolume,
                'utilization_rate' => $account->balance > 0 ? ($transactionVolume / $account->balance) * 100 : 0,
                'last_activity' => $account->transactions()->latest()->first()?->created_at,
            ];
        });

        // Balance reconciliation data
        $data['reconciliation'] = [
            'accounts_with_discrepancies' => $this->findBalanceDiscrepancies(),
            'pending_balance_total' => ForexAccount::sum('pending_balance'),
            'accounts_requiring_attention' => ForexAccount::where('pending_balance', '>', 0)
                ->orWhere('balance', '<', 0)
                ->get(),
        ];

        $data['dateFrom'] = $dateFrom->format('Y-m-d');
        $data['dateTo'] = $dateTo->format('Y-m-d');

        return view('admin.forex.accounts.balance-summary', $data);
    }

    private function getHistoricalBalanceData($dateFrom, $dateTo)
    {
        // Get daily balance snapshots by analyzing transaction history
        $historicalData = [];
        $currentDate = $dateFrom->copy();

        while ($currentDate <= $dateTo) {
            $dayData = [
                'date' => $currentDate->format('Y-m-d'),
                'usd_balance' => 0,
                'eur_balance' => 0,
                'gbp_balance' => 0,
                'ngn_balance' => 0,
            ];

            // Calculate balance for each currency at end of day
            foreach (['USD', 'EUR', 'GBP', 'NGN'] as $currency) {
                $accounts = ForexAccount::byCurrency($currency)->get();
                $totalBalance = 0;

                foreach ($accounts as $account) {
                    // Get the last transaction before or on this date
                    $lastTransaction = $account->transactions()
                        ->whereDate('created_at', '<=', $currentDate)
                        ->latest()
                        ->first();

                    if ($lastTransaction) {
                        $totalBalance += $lastTransaction->balance_after;
                    }
                }

                $dayData[strtolower($currency) . '_balance'] = $totalBalance;
            }

            $historicalData[] = $dayData;
            $currentDate->addDay();
        }

        return $historicalData;
    }

    private function findBalanceDiscrepancies()
    {
        // Find accounts where calculated balance doesn't match recorded balance
        $discrepancies = [];

        foreach (ForexAccount::active()->get() as $account) {
            $lastTransaction = $account->transactions()->latest()->first();

            if ($lastTransaction && abs($lastTransaction->balance_after - $account->balance) > 0.01) {
                $discrepancies[] = [
                    'account' => $account,
                    'recorded_balance' => $account->balance,
                    'calculated_balance' => $lastTransaction->balance_after,
                    'discrepancy' => $account->balance - $lastTransaction->balance_after,
                ];
            }
        }

        return $discrepancies;
    }
}
