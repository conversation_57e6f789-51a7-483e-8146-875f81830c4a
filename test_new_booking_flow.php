<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Models\ForexTransaction;
use App\Services\ForexBookingService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== NEW BOOKING FLOW TEST ===\n\n";

// Get USD account
$usdAccount = ForexAccount::where('currency_code', 'USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

echo "USD Account before test:\n";
echo "- Balance: $" . number_format($usdAccount->balance, 2) . "\n\n";

// Test data
$bookingData = [
    'client_name' => 'Test Client',
    'client_email' => '<EMAIL>',
    'client_phone' => '**********',
    'client_type' => 'individual',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 50,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test account details'
];

$bookingService = app(ForexBookingService::class);

try {
    echo "=== STEP 1: CREATE BOOKING ===\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    echo "✅ Booking created: {$booking->booking_reference}\n";

    // Check account after booking creation
    $usdAccount->refresh();
    echo "USD Account after booking creation:\n";
    echo "- Balance: $" . number_format($usdAccount->balance, 2) . "\n";

    // Check transactions
    $bookingTransactions = ForexTransaction::where('forex_booking_id', $booking->id)->get();
    echo "Transactions created: " . $bookingTransactions->count() . "\n";
    foreach ($bookingTransactions as $transaction) {
        echo "- Type: {$transaction->transaction_type}, Amount: $" . number_format($transaction->amount, 2) .
             ", Description: {$transaction->description}\n";
    }

    echo "\n=== STEP 2: COMPLETE BOOKING ===\n";
    $bookingService->completeBooking($booking, 1, 'Test completion');
    echo "✅ Booking completed\n";

    // Check account after completion
    $usdAccount->refresh();
    echo "USD Account after completion:\n";
    echo "- Balance: $" . number_format($usdAccount->balance, 2) . "\n";

    // Check all transactions
    $allTransactions = ForexTransaction::where('forex_booking_id', $booking->id)->get();
    echo "Total transactions: " . $allTransactions->count() . "\n";
    foreach ($allTransactions as $transaction) {
        echo "- Type: {$transaction->transaction_type}, Amount: $" . number_format($transaction->amount, 2) .
             ", Description: {$transaction->description}\n";
    }

    echo "\n=== EXPECTED RESULT ===\n";
    echo "✅ Should have 2 transactions: 'booked' (debit) + 'completed' (debit)\n";
    echo "✅ Account balance should be reduced by $100 total ($50 + $50)\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST CANCELLATION FLOW ===\n";

try {
    // Create another booking to test cancellation
    $bookingData['amount'] = 25;
    $booking2 = $bookingService->createBooking($bookingData, 1);
    echo "✅ Second booking created: {$booking2->booking_reference}\n";

    $usdAccount->refresh();
    $balanceAfterSecondBooking = $usdAccount->balance;
    echo "USD Account after second booking: $" . number_format($balanceAfterSecondBooking, 2) . "\n";

    echo "\nCancelling second booking...\n";
    $bookingService->cancelBooking($booking2, 1, 'Test cancellation');
    echo "✅ Second booking cancelled\n";

    // Check account after cancellation
    $usdAccount->refresh();
    echo "USD Account after cancellation: $" . number_format($usdAccount->balance, 2) . "\n";

    // Check transactions for cancelled booking
    $cancelTransactions = ForexTransaction::where('forex_booking_id', $booking2->id)->get();
    echo "Cancellation transactions: " . $cancelTransactions->count() . "\n";
    foreach ($cancelTransactions as $transaction) {
        echo "- Type: {$transaction->transaction_type}, Amount: $" . number_format($transaction->amount, 2) .
             ", Description: {$transaction->description}\n";
    }

    echo "\n=== EXPECTED RESULT ===\n";
    echo "✅ Should have 2 transactions: 'booked' (debit) + 'cancelled_refund' (credit)\n";
    echo "✅ Account balance should be back to: $" . number_format($balanceAfterSecondBooking + 25, 2) . "\n";
    echo "✅ Actual balance: $" . number_format($usdAccount->balance, 2) . "\n";

} catch (\Exception $e) {
    echo "❌ Cancellation Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
