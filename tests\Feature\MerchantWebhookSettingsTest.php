<?php

namespace Tests\Feature;

use App\Models\User;
use Modules\Merchant\Models\MerchantSetting;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class MerchantWebhookSettingsTest extends TestCase
{
    use RefreshDatabase;

    protected $merchant;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->merchant = User::factory()->create([
            'type' => 'merchant',
            'status' => 1,
            'email_verification' => 1,
            'sms_verification' => 1,
            'two_fa_verify' => 1
        ]);
    }

    public function test_merchant_can_access_webhook_settings_page()
    {
        $response = $this->actingAs($this->merchant)
            ->get(route('merchant.webhook.settings'));

        $response->assertStatus(200);
        $response->assertViewIs('merchant::settings.webhook');
        $response->assertViewHas('setting');
    }

    public function test_merchant_can_update_webhook_url()
    {
        $webhookUrl = 'https://example.com/webhook';

        $response = $this->actingAs($this->merchant)
            ->post(route('merchant.webhook.settings.update'), [
                'webhook_url' => $webhookUrl
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Webhook settings updated successfully');

        $this->assertDatabaseHas('merchant_settings', [
            'merchant_id' => $this->merchant->id,
            'webhook_url' => $webhookUrl
        ]);
    }

    public function test_merchant_can_clear_webhook_url()
    {
        // First set a webhook URL
        MerchantSetting::create([
            'merchant_id' => $this->merchant->id,
            'webhook_url' => 'https://example.com/webhook'
        ]);

        // Then clear it
        $response = $this->actingAs($this->merchant)
            ->post(route('merchant.webhook.settings.update'), [
                'webhook_url' => ''
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Webhook settings updated successfully');

        $this->assertDatabaseHas('merchant_settings', [
            'merchant_id' => $this->merchant->id,
            'webhook_url' => null
        ]);
    }

    public function test_webhook_url_validation_requires_valid_url()
    {
        $response = $this->actingAs($this->merchant)
            ->post(route('merchant.webhook.settings.update'), [
                'webhook_url' => 'invalid-url'
            ]);

        $response->assertSessionHasErrors('webhook_url');
    }

    public function test_webhook_url_validation_accepts_https_urls()
    {
        $response = $this->actingAs($this->merchant)
            ->post(route('merchant.webhook.settings.update'), [
                'webhook_url' => 'https://secure.example.com/webhook'
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }

    public function test_webhook_url_validation_accepts_http_urls()
    {
        $response = $this->actingAs($this->merchant)
            ->post(route('merchant.webhook.settings.update'), [
                'webhook_url' => 'http://localhost:3000/webhook'
            ]);

        $response->assertRedirect();
        $response->assertSessionHasNoErrors();
    }

    public function test_api_webhook_settings_get()
    {
        $token = $this->merchant->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->get('/api/merchant/webhook-settings');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'data' => [
                'setting'
            ]
        ]);
    }

    public function test_api_webhook_settings_update()
    {
        $token = $this->merchant->createToken('test-token')->plainTextToken;
        $webhookUrl = 'https://api.example.com/webhook';

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json'
        ])->post('/api/merchant/webhook-settings-update', [
            'webhook_url' => $webhookUrl
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'success',
            'message' => 'Webhook settings updated successfully'
        ]);

        $this->assertDatabaseHas('merchant_settings', [
            'merchant_id' => $this->merchant->id,
            'webhook_url' => $webhookUrl
        ]);
    }

    public function test_non_merchant_cannot_access_webhook_settings()
    {
        $user = User::factory()->create([
            'type' => 'user',
            'status' => 1,
            'email_verification' => 1,
            'sms_verification' => 1,
            'two_fa_verify' => 1
        ]);

        $response = $this->actingAs($user)
            ->get(route('merchant.webhook.settings'));

        $response->assertStatus(403);
    }

    public function test_webhook_settings_creates_merchant_setting_if_not_exists()
    {
        // Ensure no merchant setting exists
        $this->assertDatabaseMissing('merchant_settings', [
            'merchant_id' => $this->merchant->id
        ]);

        $response = $this->actingAs($this->merchant)
            ->get(route('merchant.webhook.settings'));

        $response->assertStatus(200);

        // Should create a merchant setting record
        $this->assertDatabaseHas('merchant_settings', [
            'merchant_id' => $this->merchant->id
        ]);
    }

    public function test_webhook_settings_preserves_other_merchant_settings()
    {
        // Create existing merchant settings
        $existingSetting = MerchantSetting::create([
            'merchant_id' => $this->merchant->id,
            'charge_applied_to' => 'merchant',
            'auto_withdraw' => true,
            'withdraw_frequency' => 'weekly',
            'withdraw_amount' => 500,
            'withdraw_currency' => 'USD'
        ]);

        $webhookUrl = 'https://example.com/webhook';

        $response = $this->actingAs($this->merchant)
            ->post(route('merchant.webhook.settings.update'), [
                'webhook_url' => $webhookUrl
            ]);

        $response->assertRedirect();

        // Verify webhook URL was updated but other settings preserved
        $updatedSetting = MerchantSetting::where('merchant_id', $this->merchant->id)->first();
        
        $this->assertEquals($webhookUrl, $updatedSetting->webhook_url);
        $this->assertEquals('merchant', $updatedSetting->charge_applied_to);
        $this->assertEquals(true, $updatedSetting->auto_withdraw);
        $this->assertEquals('weekly', $updatedSetting->withdraw_frequency);
        $this->assertEquals(500, $updatedSetting->withdraw_amount);
        $this->assertEquals('USD', $updatedSetting->withdraw_currency);
    }
}
