<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PayoutMethod extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'code', 'description', 'bank_name', 'banks', 'parameters', 'extra_parameters', 'inputForm', 'currency_lists',
        'supported_currency', 'payout_currencies', 'is_active', 'is_automatic', 'is_sandbox', 'environment', 'confirm_payout', 'is_auto_update',
        'currency_type', 'logo', 'driver'];

    protected $casts = [
        'bank_name' => 'object',
        'banks' => 'array',
        'parameters' => 'object',
        'extra_parameters' => 'object',
        'convert_rate' => 'object',
        'currency_lists' => 'object',
        'supported_currency' => 'object',
        'automatic_input_form' => 'object',
        'payout_currencies' => 'object',
        'inputForm' => 'object'
    ];

    /**
     * Get the bank_name attribute with safe handling
     */
    public function getBankNameAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ? (object) $decoded : null;
        }
        return $value;
    }

    /**
     * Get the parameters attribute with safe handling
     */
    public function getParametersAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ? (object) $decoded : null;
        }
        return $value;
    }

    /**
     * Get the extra_parameters attribute with safe handling
     */
    public function getExtraParametersAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ? (object) $decoded : null;
        }
        return $value;
    }

    /**
     * Get the currency_lists attribute with safe handling
     */
    public function getCurrencyListsAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ? (object) $decoded : null;
        }
        return $value;
    }

    /**
     * Get the payout_currencies attribute with safe handling
     */
    public function getPayoutCurrenciesAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ? (object) $decoded : null;
        }
        return $value;
    }

    /**
     * Get the supported_currency attribute with safe handling
     */
    public function getSupportedCurrencyAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return $decoded ?: [];
        }
        if (is_object($value)) {
            return (array) $value;
        }
        return is_array($value) ? $value : [];
    }

    /**
     * Get the inputForm attribute with safe handling
     */
    public function getInputFormAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if ($decoded && is_array($decoded)) {
                // Convert array items to objects for consistent access
                $result = new \stdClass();
                foreach ($decoded as $key => $item) {
                    $result->$key = is_array($item) ? (object) $item : $item;
                }
                return $result;
            }
            return $decoded ? (object) $decoded : null;
        }
        return $value;
    }

    public function getImage(): string
    {
        return getFile($this->driver, $this->logo);
    }
}
