<?php

/**
 * Simple test script for the new API Key Authentication endpoint
 * 
 * This script tests the /api/authenticate endpoint that uses publicKey and secret<PERSON>ey
 * instead of username and password for authentication.
 * 
 * Usage: php tests/test-api-key-authentication.php
 */

class ApiKeyAuthTester
{
    private $baseUrl;
    private $token = null;

    public function __construct($baseUrl = 'http://localhost')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Test the new API key authentication endpoint
     */
    public function testApiKeyAuthentication($publicKey, $secretKey)
    {
        echo "Testing API Key Authentication...\n";
        echo "Public Key: " . substr($publicKey, 0, 10) . "...\n";
        echo "Secret Key: " . substr($secretKey, 0, 10) . "...\n\n";

        $url = $this->baseUrl . '/api/authenticate';
        $data = [
            'publicKey' => $publicKey,
            'secretKey' => $secretKey
        ];

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if (isset($result['status']) && $result['status'] === 'success') {
                echo "✅ Authentication successful!\n";
                echo "Token: " . substr($result['token'], 0, 20) . "...\n";
                echo "User: " . $result['user']['username'] . " (" . $result['user']['type'] . ")\n";
                echo "Mode: " . $result['user']['mode'] . "\n\n";
                
                $this->token = $result['token'];
                return true;
            } else {
                echo "❌ Authentication failed!\n";
                echo "Error: " . ($result['message'] ?? 'Unknown error') . "\n\n";
                return false;
            }
        } else {
            echo "❌ Request failed - no response received\n\n";
            return false;
        }
    }

    /**
     * Test using the token to access a protected endpoint
     */
    public function testProtectedEndpoint()
    {
        if (!$this->token) {
            echo "❌ No token available for testing protected endpoint\n\n";
            return false;
        }

        echo "Testing protected endpoint with token...\n";
        
        $url = $this->baseUrl . '/api/dashboard';
        $headers = [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $response = $this->makeRequest('GET', $url, null, $headers);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if (isset($result['status']) && $result['status'] === 'success') {
                echo "✅ Protected endpoint access successful!\n";
                echo "Dashboard data received\n\n";
                return true;
            } else {
                echo "❌ Protected endpoint access failed!\n";
                echo "Error: " . ($result['message'] ?? 'Unknown error') . "\n\n";
                return false;
            }
        } else {
            echo "❌ Protected endpoint request failed\n\n";
            return false;
        }
    }

    /**
     * Test with invalid credentials
     */
    public function testInvalidCredentials()
    {
        echo "Testing with invalid credentials...\n";
        
        $url = $this->baseUrl . '/api/authenticate';
        $data = [
            'publicKey' => 'invalid_public_key',
            'secretKey' => 'invalid_secret_key'
        ];

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response) {
            $result = json_decode($response, true);
            
            if (isset($result['status']) && $result['status'] === 'failed') {
                echo "✅ Invalid credentials correctly rejected!\n";
                echo "Error message: " . $result['message'] . "\n\n";
                return true;
            } else {
                echo "❌ Invalid credentials should have been rejected!\n\n";
                return false;
            }
        } else {
            echo "❌ Request failed\n\n";
            return false;
        }
    }

    /**
     * Make HTTP request
     */
    private function makeRequest($method, $url, $data = null, $headers = [])
    {
        $curl = curl_init();
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $allHeaders = array_merge($defaultHeaders, $headers);
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $allHeaders,
        ]);
        
        if ($data && ($method === 'POST' || $method === 'PUT')) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        if (curl_errno($curl)) {
            echo "cURL Error: " . curl_error($curl) . "\n";
            curl_close($curl);
            return false;
        }
        
        curl_close($curl);
        
        echo "HTTP Status: $httpCode\n";
        
        return $response;
    }
}

// Example usage
echo "=== API Key Authentication Testing ===\n\n";

$tester = new ApiKeyAuthTester('http://localhost'); // Adjust URL as needed

// Test with invalid credentials first
$tester->testInvalidCredentials();

// To test with real credentials, you would need to:
// 1. Have a user with generated API keys in the database
// 2. Replace the example keys below with real ones

echo "To test with real credentials:\n";
echo "1. Login to your application\n";
echo "2. Go to API settings and generate API keys\n";
echo "3. Replace the example keys below with your real keys\n";
echo "4. Uncomment the test lines\n\n";

// Example test with placeholder keys (uncomment and replace with real keys)
/*
$publicKey = 'your_real_public_key_here';
$secretKey = 'your_real_secret_key_here';

if ($tester->testApiKeyAuthentication($publicKey, $secretKey)) {
    $tester->testProtectedEndpoint();
}
*/

echo "=== Testing completed ===\n";
