<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Models\NotificationTemplate;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING FOREX EMAIL FUNCTIONALITY ===\n\n";

// Check email notification setting
$basic = basicControl();
echo "Email notification setting: " . ($basic->email_notification ? 'Enabled' : 'Disabled') . "\n";

if (!$basic->email_notification) {
    echo "❌ Email notifications are disabled. Please enable them first.\n";
    exit;
}

// Check if notification templates exist
echo "\n1. Checking Forex Notification Templates...\n";
$templates = NotificationTemplate::whereIn('template_key', [
    'FOREX_BOOKING_CONFIRMATION',
    'FOREX_BOOKING_COMPLETION',
    'FOREX_PAYMENT_REMINDER'
])->get();

foreach ($templates as $template) {
    echo "✓ Found template: {$template->name} (Key: {$template->template_key})\n";
    echo "  Status: " . json_encode($template->status) . "\n";
}

if ($templates->count() !== 3) {
    echo "❌ Missing forex notification templates. Expected 3, found {$templates->count()}\n";
    exit;
}

echo "\n2. Testing Forex Booking Creation with Email...\n";

// Get services
$walletService = new ForexWalletService();
$bookingService = new ForexBookingService($walletService);

// Get USD account for testing
$usdAccount = ForexAccount::byType('USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

// Test data for booking creation
$bookingData = [
    'client_type' => 'external',
    'client_name' => 'Test Client',
    'client_email' => '<EMAIL>',
    'client_phone' => '+**********',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 100.00,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test bank account details',
    'payment_instructions' => 'Please transfer to our account XYZ',
];

try {
    echo "Creating forex booking...\n";
    $booking = $bookingService->createBooking($bookingData, 1);

    echo "✓ Booking created successfully: {$booking->booking_reference}\n";
    echo "✓ Email sent flag: " . ($booking->email_sent ? 'Yes' : 'No') . "\n";

    // Test payment reminder
    echo "\n3. Testing Payment Reminder Email...\n";
    $bookingService->sendForexPaymentReminderEmail($booking, 'This is a test reminder message.');
    echo "✓ Payment reminder email sent\n";

    // Test booking completion
    echo "\n4. Testing Booking Completion...\n";
    $bookingService->completeBooking($booking, 1, 'Test completion notes');
    echo "✓ Booking completed and completion email sent\n";

    echo "\n✅ All forex email tests passed successfully!\n";
    echo "\nBooking Details:\n";
    echo "- Reference: {$booking->booking_reference}\n";
    echo "- Status: {$booking->status}\n";
    echo "- Client: {$booking->client_name} ({$booking->client_email})\n";
    echo "- Amount: {$booking->currency} " . number_format($booking->amount, 2) . "\n";

} catch (\Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
