<div class="card mb-3 mb-lg-5">
    <div class="card-header card-header-content-between">
        <h4 class="card-header-title"><?php echo app('translator')->get("Latest Users"); ?></h4>

        <a class="btn btn-white btn-sm" href="<?php echo e(route("admin.users")); ?>"><?php echo app('translator')->get("View All"); ?></a>
    </div>
    <div class="table-responsive">
        <table class="table table-borderless table-thead-bordered table-align-middle card-table">
            <thead class="thead-light">
            <tr>
                <th><?php echo app('translator')->get('Full Name'); ?></th>
                <th><?php echo app('translator')->get('Country'); ?></th>
                <th><?php echo app('translator')->get('Status'); ?></th>
                <th><?php echo app('translator')->get('Action'); ?></th>
            </tr>
            </thead>
            <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $latestUser; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td>
                        <a class="d-flex align-items-center me-2"
                           href="<?php echo e(route("admin.user.view.profile", $user->id)); ?>">
                            <div class="flex-shrink-0">
                                <?php echo $user->profilePicture(); ?>

                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="text-hover-primary mb-0"><?php echo e($user->firstname . ' ' . $user->lastname); ?></h5>
                                <span class="fs-6 text-body"><?php echo e('@'. $user->username); ?> <?php echo renderUserTypeBadge($user->type); ?></span>
                            </div>
                        </a>
                    </td>
                    <td>
                        <?php echo e($user->country ?? 'N/A'); ?>

                    </td>
                    <td>
                        <?php if($user->status == 1): ?>
                            <span class="badge bg-soft-success text-success">
                                <span class="legend-indicator bg-success"></span><?php echo app('translator')->get("Active"); ?>
                            </span>
                        <?php else: ?>
                            <span class="badge bg-soft-danger text-danger">
                                <span class="legend-indicator bg-danger"></span><?php echo app('translator')->get("Inactive"); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a class="btn btn-white btn-sm" href="<?php echo e(route('admin.user.edit', $user->id)); ?>">
                                <i class="bi-pencil-square me-1"></i> <?php echo app('translator')->get("Edit"); ?>
                            </a>
                            <div class="btn-group">
                                <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty" id="userEditDropdown" data-bs-toggle="dropdown" aria-expanded="false"></button>
                                <div class="dropdown-menu dropdown-menu-end mt-1" aria-labelledby="userEditDropdown" >
                                    <a class="dropdown-item" href="<?php echo e(route('admin.user.view.profile', $user->id)); ?>">
                                        <i class="bi-eye dropdown-item-icon"></i> <?php echo app('translator')->get("View Profile"); ?>
                                    </a>
                                    <a class="dropdown-item" href="<?php echo e(route('admin.send.email', $user->id)); ?>"> <i
                                            class="bi-envelope dropdown-item-icon"></i> <?php echo app('translator')->get("Send Mail"); ?> </a>
                                    <a class="dropdown-item loginAccount" href="javascript:void(0)"
                                       data-route="<?php echo e(route('admin.login.as.user', $user->id)); ?>"
                                       data-bs-toggle="modal" data-bs-target="#loginAsUserModal">
                                        <i class="bi bi-box-arrow-in-right dropdown-item-icon"></i>
                                        <?php echo app('translator')->get("Login As"); ?> <?php echo e(ucfirst($user->type)); ?>

                                    </a>
                                    <a class="dropdown-item addBalance" href="javascript:void(0)"
                                       data-route="<?php echo e(route('admin.user.update.balance', $user->id)); ?>"
                                       data-balance="<?php echo e(currencyPosition($user->balance)); ?>"
                                       data-bs-toggle="modal" data-bs-target="#addBalanceModal">
                                        <i class="bi bi-cash-coin dropdown-item-icon"></i>
                                        <?php echo app('translator')->get("Manage Balance"); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <div class="text-center p-4">
                        <img class="dataTables-image mb-3" src="<?php echo e(asset('assets/admin/img/oc-error.svg')); ?>" alt="Image Description" data-hs-theme-appearance="default">
                        <img class="dataTables-image mb-3" src="<?php echo e(asset('assets/admin/img/oc-error-light.svg')); ?>" alt="Image Description" data-hs-theme-appearance="dark">
                        <p class="mb-0"><?php echo app('translator')->get("No data to show"); ?></p>
                    </div>
                </tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/admin/partials/dashboard/latest_users.blade.php ENDPATH**/ ?>