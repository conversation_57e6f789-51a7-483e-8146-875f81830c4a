<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop the existing unique constraint
        Schema::table('merchant_payout_configurations', function (Blueprint $table) {
            $table->dropUnique('unique_merchant_payout_method');
        });

        // Add the currency column
        Schema::table('merchant_payout_configurations', function (Blueprint $table) {
            $table->string('currency', 10)->default('NGN')->after('payout_method_id')->comment('Currency code (NGN, USD, etc.)');
            $table->index('currency');
        });

        // Update existing records to have NGN as default currency
        DB::table('merchant_payout_configurations')->update(['currency' => 'NGN']);

        // Add new unique constraint including currency
        Schema::table('merchant_payout_configurations', function (Blueprint $table) {
            $table->unique(['merchant_id', 'payout_method_id', 'currency'], 'unique_merchant_payout_method_currency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('merchant_payout_configurations', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('unique_merchant_payout_method_currency');
            
            // Drop the currency column
            $table->dropColumn('currency');
            
            // Restore the old unique constraint
            $table->unique(['merchant_id', 'payout_method_id'], 'unique_merchant_payout_method');
        });
    }
};
