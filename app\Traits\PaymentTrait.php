<?php

namespace App\Traits;

use App\Models\ChargesLimit;
use App\Models\Currency;
use App\Models\Deposit;
use App\Models\Gateway;
use App\Models\VirtualCardOrder;
use App\Models\Wallet;
use Illuminate\Support\Facades\Auth;

trait PaymentTrait
{

    public function getCurrencyRate($supportedCurrency, $otherCurrency): float|int|null
    {
        try {
            $supportedRate = Currency::where('id', $supportedCurrency)->value('exchange_rate');
            $otherRate = Currency::where('id', $otherCurrency)->value('exchange_rate');

            if (!$supportedRate || !$otherRate) {
                throw new \Exception("Exchange rate not found for one or both currencies.");
            }
            if ($otherRate == 0) {
                throw new \Exception("Exchange rate for {$otherCurrency} is zero.");
            }

            return $otherRate / $supportedRate;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function validatePayment($amount, $currency_id, $transaction_type_id, $methodId, $extraCharge=0): array
    {
        if (!$currency_id){
            return ['status' => false, 'message' => "Please select a currency."];
        }

        $chargesLimit = ChargesLimit::query()
            ->with('currency')
            ->where('is_active',1)
            ->where([
                'currency_id' => $currency_id,
                'transaction_type_id' => $transaction_type_id,
                'payment_method_id' => $methodId
            ])->first();
        if (!$chargesLimit) {
            return ['status' => false, 'message' => "Payment method not available for this transaction"];
        }
        $currency_code = $chargesLimit->currency?->code;

        $wallet = Wallet::firstOrCreate(['user_id' => Auth::id(), 'currency_id' => $currency_id]);
        if ($wallet->is_active != 1) {
            return ['status' => false, 'message' => "Inactive wallet currency, not available for this transaction"];
        }

        $limit = optional($chargesLimit->currency)->currency_type == 0 ? 8 : 2;
        $amount = getAmount($amount, $limit);
        $balance = Auth::check() ? getAmount($wallet->balance, $limit) : 0;

        $percentage = getAmount($chargesLimit->percentage_charge, $limit);
        $percentage_charge = getAmount(($amount * $percentage) / 100, $limit);
        $fixed_charge = getAmount($chargesLimit->fixed_charge, $limit);
        $min_limit = getAmount($chargesLimit->min_limit, $limit);
        $max_limit = getAmount($chargesLimit->max_limit, $limit);
        $charge = getAmount($percentage_charge + $fixed_charge, $limit);
        $payable_amount = getAmount($amount + $charge + $extraCharge, $limit);
        $new_balance = getAmount($balance + $amount, $limit);

        $gateway = Gateway::where('id', $methodId)->where('status', 1)->first();
        if (!$gateway) {
            return ['status' => false, 'message' => "Payment method not available for this transaction"];
        }
        $receivableCurrencies = $gateway->receivable_currencies;
        if (!is_array($receivableCurrencies)) {
            return ['status' => false, 'message' => "Invalid currency configuration"];
        }
        $currencyInfo = collect($receivableCurrencies)->firstWhere('name', $currency_code)
            ?? collect($receivableCurrencies)->firstWhere('currency', $currency_code);
        if (!$currencyInfo) {
            return [
                'status' => false,
                'message' => "{$currency_code} currency is not supported by the selected method. Please choose a different currency or payment method."
            ];
        }

        if ($amount < $min_limit || $amount > $max_limit) {
            return ['status' => false, 'message' => "Minimum payment $min_limit and maximum payment limit $max_limit"];
        }

        $payable_amount_in_base_currency = getAmount($payable_amount / $currencyInfo->conversion_rate, $limit);
        $base_currency_charge = getAmount($charge / $currencyInfo->conversion_rate ?? 1, $limit);

        return [
            'status' => true,
            'message' => "Updated balance: $new_balance",
            'payment_method_id' => $gateway->id,
            'charges_limit_id' => $chargesLimit->id,
            'fixed_charge' => $fixed_charge,
            'percentage' => $percentage,
            'percentage_charge' => $percentage_charge,
            'min_limit' => $min_limit,
            'max_limit' => $max_limit,
            'balance' => $balance,
            'payable_amount' => $payable_amount,
            'new_balance' => $new_balance,
            'charge' => $charge,
            'amount' => $amount,
            'currency_id' => $currency_id,
            'currency_limit' => $limit,
            'base_currency_charge' => $base_currency_charge,
            'payable_amount_in_base_currency' => $payable_amount_in_base_currency,
            'payment_method_currency' => $currency_code,
            'base_currency' => basicControl()->base_currency
        ];
    }


    public function createDeposit($data, $morphType=null, $morphId=null, $cardOrderId=null)
    {
        $user = Auth::user();
        $depositData = [
            'user_id' => $user->id ?? null,
            'depositable_type' => $morphType,
            'depositable_id' => $morphId,
            'payment_method_id' => $data['payment_method_id'],
            'payment_method_currency' => $data['payment_method_currency'],
            'amount' => $data['amount'],
            'percentage' => $data['percentage'],
            'percentage_charge' => $data['percentage_charge'],
            'fixed_charge' => $data['fixed_charge'],
            'charge' => $data['charge'],
            'payable_amount' => $data['payable_amount'],
            'base_currency_charge' => $data['base_currency_charge'],
            'payable_amount_in_base_currency' => $data['payable_amount_in_base_currency'],
            'charges_limit_id' => $data['charges_limit_id'],
            'currency_id' => $data['currency_id'],
            'card_order_id' => $cardOrderId ?? null,
            'email' => $user->email ?? request('email') ?? null,
            'status' => 0,
        ];

        return Deposit::create($depositData);
    }


    public function validateCardOrder($orderCardId, $purifiedData): array
    {
        $amount = $purifiedData->amount;
        $currency_id = $purifiedData->currency;

        $cardOrder = VirtualCardOrder::select(['id', 'user_id'])->where('id', $orderCardId)->first();
        if (!$cardOrder) {
            return ['status' => false, 'message' => 'Invalid virtual card'];
        }
        if ($cardOrder->user_id != auth()->id()) {
            return ['status' => false, 'message' => 'You are not eligible for this transaction'];
        }

        $info = cardCurrencyCheck($cardOrder->id);
        if ($info['status'] !== 'success') {
            return ['status' => false, 'message' => 'Currency check failed'];
        }

        $currency = Currency::query()->select(['id','code'])->find($currency_id);
        if ($info['currencyCode'] != $currency->code) {
            return ['status' => false, 'message' => 'Please select your card currency for the transaction'];
        }

        if ($info['MinimumAmount'] >= $amount) {
            return ['status' => false, 'message' => 'Amount must be greater than ' . $info['MinimumAmount']];
        }

        if ($info['MaximumAmount'] <= $amount) {
            return ['status' => false, 'message' => 'Amount must be smaller than ' . $info['MaximumAmount']];
        }

        $percentValue = ($info['PercentCharge'] * $amount) / 100;
        $charge = $info['FixedCharge'] + $percentValue;
        if ($amount < $charge) {
            return ['status' => false, 'message' => 'Charge cannot be greater than input amount'];
        }

        return ['status' => true, 'id' => $cardOrder->id];
    }

}
