@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.bookings.index') }}">
                                    @lang('Forex Bookings')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $booking->booking_reference }}</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Booking Details')</h1>
                    <p class="page-header-text">{{ $booking->booking_reference }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.bookings.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Bookings')
                        </a>
                        @if($booking->status === 'pending')
                            <button class="btn btn-success complete-booking" data-id="{{ $booking->id }}">
                                <i class="bi-check-circle me-1"></i> @lang('Complete')
                            </button>
                            <button class="btn btn-danger cancel-booking" data-id="{{ $booking->id }}">
                                <i class="bi-x-circle me-1"></i> @lang('Cancel')
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Booking Status Alert -->
        <div class="alert alert-soft-{{ $booking->status === 'completed' ? 'success' : ($booking->status === 'cancelled' ? 'danger' : 'warning') }} mb-4" role="alert">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="bi-{{ $booking->status === 'completed' ? 'check-circle' : ($booking->status === 'cancelled' ? 'x-circle' : 'clock') }}"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h4 class="alert-heading">@lang('Booking Status'): {{ ucfirst($booking->status) }}</h4>
                    @if($booking->status_notes)
                        <p class="mb-0">{{ $booking->status_notes }}</p>
                    @endif
                    @if($booking->completed_at)
                        <small class="text-muted">@lang('Completed on'): {{ $booking->completed_at->format('M d, Y H:i') }}</small>
                    @endif
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Booking Information -->
            <div class="col-lg-8 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Booking Information')</h4>
                        <span class="badge bg-soft-{{ $booking->transaction_type === 'buying' ? 'success' : 'info' }} text-{{ $booking->transaction_type === 'buying' ? 'success' : 'info' }}">
                            {{ $booking->transaction_type === 'buying' ? __('Buy USD') : __('Sell USD') }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Reference'):</dt>
                                    <dd class="col-sm-7">
                                        <code>{{ $booking->booking_reference }}</code>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Client Type'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-soft-primary text-primary">{{ ucfirst($booking->client_type) }}</span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Client Name'):</dt>
                                    <dd class="col-sm-7">{{ $booking->client_name }}</dd>

                                    <dt class="col-sm-5">@lang('Client Email'):</dt>
                                    <dd class="col-sm-7">
                                        <a href="mailto:{{ $booking->client_email }}">{{ $booking->client_email }}</a>
                                    </dd>

                                    @if($booking->client_phone)
                                        <dt class="col-sm-5">@lang('Client Phone'):</dt>
                                        <dd class="col-sm-7">
                                            <a href="tel:{{ $booking->client_phone }}">{{ $booking->client_phone }}</a>
                                        </dd>
                                    @endif
                                </dl>
                            </div>
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Transaction'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-soft-{{ $booking->transaction_type === 'buying' ? 'success' : 'info' }} text-{{ $booking->transaction_type === 'buying' ? 'success' : 'info' }}">
                                            {{ $booking->transaction_type === 'buying' ? __('Buy USD') : __('Sell USD') }}
                                        </span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Amount'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="fw-semibold">
                                            {{ $booking->currency === 'USD' ? '$' : '₦' }}{{ number_format($booking->amount, 2) }} {{ $booking->currency }}
                                        </span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Customer Rate'):</dt>
                                    <dd class="col-sm-7">
                                        ₦{{ number_format($booking->customer_rate, 2) }} / $1
                                        @if($booking->markup_percentage > 0)
                                            <small class="text-muted">(+{{ number_format($booking->markup_percentage, 2) }}% markup)</small>
                                        @endif
                                    </dd>

                                    <dt class="col-sm-5">@lang('Customer Total'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="fw-semibold text-success">₦{{ number_format($booking->customer_total, 2) }}</span>
                                    </dd>

                                    @if($booking->customer_payment_amount)
                                    <dt class="col-sm-5">@lang('Payment Amount'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="fw-semibold text-primary">
                                            @if($booking->transaction_type === 'buying')
                                                ₦{{ number_format($booking->customer_payment_amount, 2) }}
                                            @else
                                                ${{ number_format($booking->customer_payment_amount, 2) }}
                                            @endif
                                        </span>
                                    </dd>
                                    @endif

                                    <dt class="col-sm-5">@lang('Target Account'):</dt>
                                    <dd class="col-sm-7">{{ $booking->targetAccount->account_name }}</dd>

                                    <dt class="col-sm-5">@lang('Created'):</dt>
                                    <dd class="col-sm-7">{{ $booking->created_at->format('M d, Y H:i') }}</dd>
                                </dl>
                            </div>
                        </div>

                        <!-- Account Reservations Section -->
                        @if($booking->reservations && $booking->reservations->count() > 0)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Account Reservations')</h6>
                                @if($booking->hasMultipleReservations())
                                    <p class="text-muted small mb-2">@lang('This booking uses multiple accounts for reservation')</p>
                                @endif

                                <div class="table-responsive">
                                    <table class="table table-sm table-borderless">
                                        <thead>
                                            <tr class="text-muted">
                                                <th>@lang('Account')</th>
                                                <th>@lang('Type')</th>
                                                <th class="text-end">@lang('Reserved Amount')</th>
                                                <th class="text-center">@lang('Status')</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($booking->reservations as $reservation)
                                                <tr>
                                                    <td>
                                                        <span class="fw-semibold">{{ $reservation->account->account_name }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-soft-secondary text-secondary">
                                                            {{ $reservation->account->account_type }}
                                                        </span>
                                                    </td>
                                                    <td class="text-end">
                                                        <span class="fw-semibold">
                                                            {{ $reservation->formatted_reserved_amount }}
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-soft-{{ $reservation->status_class }} text-{{ $reservation->status_class }}">
                                                            {{ ucfirst($reservation->status) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                        @if($booking->reservations->count() > 1)
                                            <tfoot>
                                                <tr class="border-top">
                                                    <td colspan="2" class="fw-semibold">@lang('Total Reserved')</td>
                                                    <td class="text-end fw-semibold">
                                                        {{ number_format($booking->getTotalReservedAmount(), 2) }}
                                                        {{ $booking->reservations->first()->account->currency_code }}
                                                    </td>
                                                    <td></td>
                                                </tr>
                                            </tfoot>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        @endif


                                </dl>
                            </div>
                        </div>

                        <!-- Payment Method Information -->
                        <div class="mt-3 pt-3 border-top">
                            <h6>@lang('Payment Method')</h6>
                            @if($booking->payment_method === 'wallet')
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-primary me-2">@lang('Wallet Payment')</span>
                                    @if($booking->walletCurrency)
                                        <span class="text-muted">{{ $booking->walletCurrency->code }} Wallet</span>
                                    @endif
                                </div>
                                @if($booking->isWalletPayment())
                                    <small class="text-success">
                                        <i class="bi-check-circle me-1"></i>
                                        @lang('Funds will be credited to user\'s :currency wallet upon completion', ['currency' => $booking->getWalletCurrencyCode()])
                                    </small>
                                @endif
                            @else
                                <span class="badge bg-secondary">@lang('Bank Account Details')</span>
                            @endif
                        </div>

                        @if($booking->account_details && $booking->payment_method !== 'wallet')
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Account Details')</h6>
                                <p class="text-muted">{{ $booking->account_details }}</p>
                            </div>
                        @endif

                        @if($booking->payment_instructions)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Payment Instructions')</h6>
                                <p class="text-muted">{{ $booking->payment_instructions }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Transaction Summary -->
            <div class="col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transaction Summary')</h4>
                    </div>
                    <div class="card-body">
                        @if($booking->transaction_type === 'buying')
                            <!-- Client buying USD -->
                            <div class="text-center mb-3">
                                <h6 class="text-muted">@lang('Client Pays')</h6>
                                <h4 class="text-danger">₦{{ number_format($booking->customer_total, 2) }}</h4>
                            </div>
                            <div class="text-center mb-3">
                                <i class="bi-arrow-down text-muted"></i>
                            </div>
                            <div class="text-center mb-3">
                                <h6 class="text-muted">@lang('Client Receives')</h6>
                                <h4 class="text-success">${{ number_format($booking->amount, 2) }}</h4>
                            </div>
                        @else
                            <!-- Client selling USD -->
                            <div class="text-center mb-3">
                                <h6 class="text-muted">@lang('Client Pays')</h6>
                                <h4 class="text-danger">${{ number_format($booking->customer_payment_amount, 2) }}</h4>
                            </div>
                            <div class="text-center mb-3">
                                <i class="bi-arrow-down text-muted"></i>
                            </div>
                            <div class="text-center mb-3">
                                <h6 class="text-muted">@lang('Client Receives')</h6>
                                <h4 class="text-success">₦{{ number_format($booking->customer_total, 2) }}</h4>
                            </div>
                        @endif

                        <div class="text-center pt-3 border-top">
                            <small class="text-muted">
                                @lang('Rate'): ₦{{ number_format($booking->customer_rate, 2) }} per $1
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions and Email Logs -->
        <div class="row">
            <!-- Related Transactions -->
            <div class="col-lg-6 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Related Transactions')</h4>
                    </div>
                    @if($booking->transactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                                <thead class="thead-light">
                                <tr>
                                    <th>@lang('Account')</th>
                                    <th>@lang('Type')</th>
                                    <th>@lang('Amount')</th>
                                    <th>@lang('Date')</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($booking->transactions as $transaction)
                                    <tr>
                                        <td>
                                            <span class="d-block h6 mb-0">{{ $transaction->forexAccount->account_name }}</span>
                                            <small class="text-muted">{{ $transaction->forexAccount->currency_code }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $transaction->type_class }}">
                                                {{ ucfirst(str_replace('_', ' ', $transaction->transaction_type)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-semibold {{ $transaction->transaction_type === 'credit' ? 'text-success' : 'text-danger' }}">
                                                {{ $transaction->transaction_type === 'credit' ? '+' : '-' }}{{ number_format($transaction->amount, 2) }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>{{ $transaction->created_at->format('M d, H:i') }}</small>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="card-body">
                            <div class="text-center p-3">
                                <i class="bi-journal-x text-muted" style="font-size: 2rem;"></i>
                                <p class="text-muted mb-0">@lang('No transactions yet')</p>
                                @if($booking->status === 'pending')
                                    <small class="text-muted">@lang('Transactions will be created when booking is completed')</small>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Email Logs -->
            <div class="col-lg-6 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Email Communications')</h4>
                    </div>
                    @if($booking->emailLogs->count() > 0)
                        <div class="card-body">
                            @foreach($booking->emailLogs as $emailLog)
                                <div class="d-flex mb-3 {{ !$loop->last ? 'pb-3 border-bottom' : '' }}">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-{{ $emailLog->status === 'sent' ? 'success' : ($emailLog->status === 'failed' ? 'danger' : 'warning') }} avatar-circle">
                                            <span class="avatar-initials">
                                                <i class="bi-envelope{{ $emailLog->status === 'sent' ? '-check' : ($emailLog->status === 'failed' ? '-x' : '') }}"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-1">{{ strtoupper($emailLog->email_type) }}</h6>
                                            <small class="text-muted">{{ $emailLog->created_at->diffForHumans() }}</small>
                                        </div>
                                        <p class="mb-1">@lang('To'): {{ $emailLog->email_to }}</p>
                                        <span class="badge bg-soft-{{ $emailLog->status === 'sent' ? 'success' : ($emailLog->status === 'failed' ? 'danger' : 'warning') }} text-{{ $emailLog->status === 'sent' ? 'success' : ($emailLog->status === 'failed' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($emailLog->status) }}
                                        </span>
                                        @if($emailLog->error_message)
                                            <p class="text-danger small mb-0 mt-1">{{ $emailLog->error_message }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="card-body">
                            <div class="text-center p-3">
                                <i class="bi-envelope text-muted" style="font-size: 2rem;"></i>
                                <p class="text-muted mb-0">@lang('No emails sent yet')</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Booking Timeline -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Booking Timeline')</h4>
                    </div>
                    <div class="card-body">
                        <div class="step step-icon-sm">
                            <!-- Created -->
                            <div class="step-item">
                                <div class="step-content-wrapper">
                                    <div class="step-avatar">
                                        <i class="bi-plus-circle"></i>
                                    </div>
                                    <div class="step-content">
                                        <h5 class="mb-1">@lang('Booking Created')</h5>
                                        <p class="fs-5 mb-1">@lang('Booking was created by') {{ $booking->initiatedBy->name ?? 'System' }}</p>
                                        <span class="text-muted small">{{ $booking->created_at->format('M d, Y H:i') }}</span>
                                    </div>
                                </div>
                            </div>

                            @if($booking->status === 'completed')
                                <!-- Completed -->
                                <div class="step-item">
                                    <div class="step-content-wrapper">
                                        <div class="step-avatar">
                                            <i class="bi-check-circle"></i>
                                        </div>
                                        <div class="step-content">
                                            <h5 class="mb-1">@lang('Booking Completed')</h5>
                                            <p class="fs-5 mb-1">@lang('Booking was completed by') {{ $booking->completedBy->name ?? 'System' }}</p>
                                            @if($booking->status_notes)
                                                <p class="text-muted small mb-1">{{ $booking->status_notes }}</p>
                                            @endif
                                            <span class="text-muted small">{{ $booking->completed_at->format('M d, Y H:i') }}</span>
                                        </div>
                                    </div>
                                </div>
                            @elseif($booking->status === 'cancelled')
                                <!-- Cancelled -->
                                <div class="step-item">
                                    <div class="step-content-wrapper">
                                        <div class="step-avatar">
                                            <i class="bi-x-circle"></i>
                                        </div>
                                        <div class="step-content">
                                            <h5 class="mb-1">@lang('Booking Cancelled')</h5>
                                            <p class="fs-5 mb-1">@lang('Booking was cancelled')</p>
                                            @if($booking->status_notes)
                                                <p class="text-muted small mb-1">{{ $booking->status_notes }}</p>
                                            @endif
                                            <span class="text-muted small">{{ $booking->updated_at->format('M d, Y H:i') }}</span>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Complete Booking Modal -->
    <div class="modal fade" id="completeModal" tabindex="-1" role="dialog" aria-labelledby="completeModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="completeModalLabel">@lang('Complete Booking')</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="completeForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="statusNotes" class="form-label">@lang('Completion Notes') (@lang('Optional'))</label>
                            <textarea class="form-control" name="status_notes" id="statusNotes" rows="3"
                                      placeholder="@lang('Add any notes about completing this booking...')"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn-success">@lang('Complete Booking')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="cancelModalLabel">@lang('Cancel Booking')</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="cancelForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="cancelNotes" class="form-label">@lang('Cancellation Reason') <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="status_notes" id="cancelNotes" rows="3"
                                      placeholder="@lang('Please provide a reason for cancelling this booking...')" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn-danger">@lang('Cancel Booking')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Complete booking functionality
            $('.complete-booking').on('click', function() {
                let bookingId = $(this).data('id');
                let completeUrl = "{{ route('admin.forex.bookings.complete', ':id') }}".replace(':id', bookingId);
                $('#completeForm').attr('action', completeUrl);
                $('#completeModal').modal('show');
            });

            // Cancel booking functionality
            $('.cancel-booking').on('click', function() {
                let bookingId = $(this).data('id');
                let cancelUrl = "{{ route('admin.forex.bookings.cancel', ':id') }}".replace(':id', bookingId);
                $('#cancelForm').attr('action', cancelUrl);
                $('#cancelModal').modal('show');
            });

            // Handle form submissions
            $('#completeForm, #cancelForm').on('submit', function(e) {
                e.preventDefault();

                let isCancel = $(this).attr('id') === 'cancelForm';

                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            if (isCancel) {
                                // Redirect to bookings list after cancelling
                                window.location.href = "{{ route('admin.forex.bookings.index') }}";
                            } else {
                                // Reload page after completing
                                location.reload();
                            }
                        }
                    },
                    error: function(xhr) {
                        let errors = xhr.responseJSON?.errors || {};
                        Object.keys(errors).forEach(function(key) {
                            alert(errors[key][0]);
                        });
                    }
                });
            });
        });
    </script>
@endpush
