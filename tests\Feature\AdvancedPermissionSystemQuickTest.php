<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\AdvancedPermission;
use App\Models\AdvancedRole;
use App\Models\User;
use App\Models\Admin;
use App\Services\AdvancedPermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

/**
 * Quick test to verify the Advanced Permission System is working
 */
class AdvancedPermissionSystemQuickTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_instantiate_core_services()
    {
        // Test that all core services can be instantiated
        $permissionService = app(\App\Services\AdvancedPermissionService::class);
        $this->assertInstanceOf(\App\Services\AdvancedPermissionService::class, $permissionService);

        $discoveryService = app(\App\Services\PermissionDiscoveryService::class);
        $this->assertInstanceOf(\App\Services\PermissionDiscoveryService::class, $discoveryService);

        $templateService = app(\App\Services\PermissionTemplateService::class);
        $this->assertInstanceOf(\App\Services\PermissionTemplateService::class, $templateService);
    }

    /** @test */
    public function it_can_create_basic_permissions_and_roles()
    {
        // Create a permission
        $permission = AdvancedPermission::create([
            'name' => 'test.read',
            'display_name' => 'Test Read',
            'resource' => 'test',
            'action' => 'read',
            'category' => 'testing',
        ]);

        $this->assertDatabaseHas('advanced_permissions', [
            'name' => 'test.read',
            'resource' => 'test',
            'action' => 'read',
        ]);

        // Create a role
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
            'category' => 'testing',
        ]);

        $this->assertDatabaseHas('advanced_roles', [
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);

        // Grant permission to role
        $role->grantPermission($permission);

        $this->assertDatabaseHas('advanced_role_permissions', [
            'role_id' => $role->id,
            'permission_id' => $permission->id,
        ]);
    }

    /** @test */
    public function it_can_assign_roles_to_users()
    {
        // Create user with advanced roles enabled
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $this->assertTrue($user->usesAdvancedRoles());

        // Create role
        $role = AdvancedRole::create([
            'name' => 'test_user',
            'display_name' => 'Test User',
        ]);

        // Assign role to user
        $user->assignAdvancedRole($role);

        $this->assertDatabaseHas('advanced_user_roles', [
            'user_id' => $user->id,
            'user_type' => User::class,
            'role_id' => $role->id,
            'is_active' => true,
        ]);

        // Check if user has role
        $this->assertTrue($user->hasAdvancedRole('test_user'));
    }

    /** @test */
    public function it_can_check_permissions()
    {
        // Create user, role, and permission
        $user = User::factory()->create(['use_advanced_roles' => true]);
        
        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role = AdvancedRole::create([
            'name' => 'user_reader',
            'display_name' => 'User Reader',
        ]);

        // Grant permission to role and assign role to user
        $role->grantPermission($permission);
        $user->assignAdvancedRole($role);

        // Test permission checking
        $this->assertTrue($user->hasAdvancedPermission('users.read'));
        $this->assertFalse($user->hasAdvancedPermission('users.create'));

        // Test service permission checking
        $permissionService = app(\App\Services\AdvancedPermissionService::class);
        $this->assertTrue($permissionService->check($user, 'users.read'));
        $this->assertFalse($permissionService->check($user, 'users.create'));
    }

    /** @test */
    public function it_can_handle_admin_users()
    {
        // Create admin with advanced roles enabled
        $admin = Admin::factory()->create(['use_advanced_roles' => true]);
        $this->assertTrue($admin->usesAdvancedRoles());

        // Create role
        $role = AdvancedRole::create([
            'name' => 'admin_role',
            'display_name' => 'Admin Role',
        ]);

        // Assign role to admin
        $admin->assignAdvancedRole($role);

        $this->assertTrue($admin->hasAdvancedRole('admin_role'));
    }

    /** @test */
    public function it_can_handle_super_admin()
    {
        // Create super admin
        $admin = Admin::factory()->create([
            'use_advanced_roles' => true,
            'is_super_admin' => true,
        ]);

        $this->assertTrue($admin->isSuperAdmin());

        // Super admin should have all permissions
        $permissionService = app(\App\Services\AdvancedPermissionService::class);
        $this->assertTrue($permissionService->check($admin, 'any.permission'));
        $this->assertTrue($permissionService->check($admin, 'nonexistent.permission'));
    }

    /** @test */
    public function it_can_run_basic_commands()
    {
        // Test that basic commands can run without errors
        $this->artisan('permissions:manage', ['action' => 'status'])
            ->assertExitCode(0);

        $this->artisan('permissions:health')
            ->assertExitCode(0);

        $this->artisan('permissions:templates', ['action' => 'list'])
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_discover_permissions()
    {
        // Test permission discovery
        $this->artisan('permissions:discover', ['--dry-run'])
            ->assertExitCode(0);

        // Test actual discovery
        $this->artisan('permissions:discover', ['--sync'])
            ->assertExitCode(0);

        // Should have discovered some permissions
        $this->assertGreaterThan(0, AdvancedPermission::count());
    }

    /** @test */
    public function it_can_seed_system()
    {
        // Test that seeders work
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder'])
            ->assertExitCode(0);

        $this->assertGreaterThan(0, AdvancedPermission::count());

        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder'])
            ->assertExitCode(0);

        $this->assertGreaterThan(0, AdvancedRole::count());
    }

    /** @test */
    public function it_has_proper_middleware_registration()
    {
        // Test that middleware is properly registered
        $middleware = app(\App\Http\Middleware\AdvancedPermissionMiddleware::class);
        $this->assertInstanceOf(\App\Http\Middleware\AdvancedPermissionMiddleware::class, $middleware);
    }

    /** @test */
    public function it_has_proper_blade_directives()
    {
        // Test that blade directives are registered
        $directives = app('blade.compiler')->getCustomDirectives();
        
        $this->assertArrayHasKey('canAdvanced', $directives);
        $this->assertArrayHasKey('hasRole', $directives);
        $this->assertArrayHasKey('isSuperAdmin', $directives);
        $this->assertArrayHasKey('canAny', $directives);
        $this->assertArrayHasKey('canAll', $directives);
    }

    /** @test */
    public function it_can_handle_permission_constraints()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        
        $permission = AdvancedPermission::create([
            'name' => 'test.constrained',
            'display_name' => 'Test Constrained',
            'resource' => 'test',
            'action' => 'constrained',
        ]);

        $role = AdvancedRole::create([
            'name' => 'constrained_role',
            'display_name' => 'Constrained Role',
        ]);

        // Grant permission with constraints
        $role->grantPermission($permission, [
            'constraints' => [
                'max_amount' => 1000,
            ]
        ]);

        $user->assignAdvancedRole($role);

        $permissionService = app(\App\Services\AdvancedPermissionService::class);

        // Should pass with valid context
        $this->assertTrue($permissionService->check($user, 'test.constrained', [
            'amount' => 500,
        ]));

        // Should fail with invalid context
        $this->assertFalse($permissionService->check($user, 'test.constrained', [
            'amount' => 2000,
        ]));
    }
}
