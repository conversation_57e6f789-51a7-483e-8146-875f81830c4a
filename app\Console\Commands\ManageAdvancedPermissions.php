<?php

namespace App\Console\Commands;

use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Models\AdvancedUserRole;
use App\Models\User;
use App\Models\Admin;
use App\Services\PermissionDiscoveryService;
use App\Services\PermissionTemplateService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Comprehensive Advanced Permission Management Command
 * 
 * Provides a unified interface for managing the advanced permission system.
 */
class ManageAdvancedPermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permissions:manage 
                            {action : Action to perform}
                            {--user= : User ID for user-specific actions}
                            {--role= : Role name for role-specific actions}
                            {--permission= : Permission name for permission-specific actions}
                            {--template= : Template name for template actions}
                            {--force : Force action without confirmation}
                            {--dry-run : Show what would be done without executing}';

    /**
     * The console command description.
     */
    protected $description = 'Comprehensive management of the advanced permission system';

    /**
     * Services
     */
    protected PermissionDiscoveryService $discoveryService;
    protected PermissionTemplateService $templateService;

    /**
     * Create a new command instance.
     */
    public function __construct(
        PermissionDiscoveryService $discoveryService,
        PermissionTemplateService $templateService
    ) {
        parent::__construct();
        $this->discoveryService = $discoveryService;
        $this->templateService = $templateService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        $this->info("🔧 Advanced Permission System Manager");
        $this->newLine();

        return match ($action) {
            'status' => $this->showSystemStatus(),
            'discover' => $this->discoverPermissions(),
            'seed' => $this->seedSystem(),
            'reset' => $this->resetSystem(),
            'user-info' => $this->showUserInfo(),
            'assign-role' => $this->assignRole(),
            'revoke-role' => $this->revokeRole(),
            'create-role' => $this->createRole(),
            'delete-role' => $this->deleteRole(),
            'list-roles' => $this->listRoles(),
            'list-permissions' => $this->listPermissions(),
            'cleanup' => $this->cleanupSystem(),
            'validate' => $this->validateSystem(),
            'export' => $this->exportData(),
            'import' => $this->importData(),
            default => $this->showHelp(),
        };
    }

    /**
     * Show system status
     */
    protected function showSystemStatus(): int
    {
        $this->info('📊 System Status');
        $this->newLine();

        // Basic counts
        $permissionCount = AdvancedPermission::count();
        $activePermissions = AdvancedPermission::where('is_active', true)->count();
        $systemPermissions = AdvancedPermission::where('is_system', true)->count();

        $roleCount = AdvancedRole::count();
        $activeRoles = AdvancedRole::where('is_active', true)->count();
        $systemRoles = AdvancedRole::where('is_system', true)->count();

        $assignmentCount = AdvancedUserRole::count();
        $activeAssignments = AdvancedUserRole::where('is_active', true)->count();

        $this->table(['Metric', 'Count'], [
            ['Total Permissions', $permissionCount],
            ['Active Permissions', $activePermissions],
            ['System Permissions', $systemPermissions],
            ['Total Roles', $roleCount],
            ['Active Roles', $activeRoles],
            ['System Roles', $systemRoles],
            ['Total Assignments', $assignmentCount],
            ['Active Assignments', $activeAssignments],
        ]);

        // Permission breakdown by category
        $this->newLine();
        $this->info('📂 Permissions by Category:');
        $categories = AdvancedPermission::select('category', DB::raw('count(*) as count'))
            ->groupBy('category')
            ->orderBy('count', 'desc')
            ->get();

        foreach ($categories as $category) {
            $categoryName = $category->category ?: 'Uncategorized';
            $this->line("  • {$categoryName}: {$category->count}");
        }

        // Role breakdown by category
        $this->newLine();
        $this->info('👥 Roles by Category:');
        $roleCategories = AdvancedRole::select('category', DB::raw('count(*) as count'))
            ->groupBy('category')
            ->orderBy('count', 'desc')
            ->get();

        foreach ($roleCategories as $category) {
            $categoryName = $category->category ?: 'Uncategorized';
            $this->line("  • {$categoryName}: {$category->count}");
        }

        // Users with advanced roles
        $this->newLine();
        $this->info('👤 Users with Advanced Roles:');
        $usersWithRoles = AdvancedUserRole::select('user_type', DB::raw('count(distinct user_id) as count'))
            ->where('is_active', true)
            ->groupBy('user_type')
            ->get();

        foreach ($usersWithRoles as $userType) {
            $typeName = class_basename($userType->user_type);
            $this->line("  • {$typeName}: {$userType->count}");
        }

        return Command::SUCCESS;
    }

    /**
     * Discover permissions
     */
    protected function discoverPermissions(): int
    {
        $this->info('🔍 Discovering Permissions...');

        if ($this->option('dry-run')) {
            $permissions = $this->discoveryService->discoverAllPermissions();
            $this->info("Would discover {count($permissions)} permissions");
            return Command::SUCCESS;
        }

        $result = $this->discoveryService->syncPermissionsToDatabase();

        $this->info("✅ Discovery completed:");
        $this->line("  • Total discovered: {$result['total_discovered']}");
        $this->line("  • Created: {$result['created']}");
        $this->line("  • Updated: {$result['updated']}");

        if (!empty($result['errors'])) {
            $this->warn("  • Errors: " . count($result['errors']));
            foreach ($result['errors'] as $error) {
                $this->line("    - {$error}");
            }
        }

        return Command::SUCCESS;
    }

    /**
     * Seed the system
     */
    protected function seedSystem(): int
    {
        if (!$this->option('force') && !$this->confirm('This will seed the permission system. Continue?')) {
            return Command::FAILURE;
        }

        $this->info('🌱 Seeding Advanced Permission System...');

        try {
            $this->call('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
            $this->call('db:seed', ['--class' => 'AdvancedRoleSeeder']);
            $this->info('✅ System seeded successfully');
        } catch (\Exception $e) {
            $this->error("❌ Seeding failed: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Reset the system
     */
    protected function resetSystem(): int
    {
        if (!$this->option('force') && !$this->confirm('This will RESET the entire permission system. Are you sure?')) {
            return Command::FAILURE;
        }

        $this->warn('🔄 Resetting Advanced Permission System...');

        DB::beginTransaction();
        try {
            AdvancedUserRole::truncate();
            DB::table('advanced_role_permissions')->truncate();
            AdvancedRole::truncate();
            AdvancedPermission::truncate();
            DB::table('advanced_permission_audit')->truncate();

            DB::commit();
            $this->info('✅ System reset successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("❌ Reset failed: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Show user information
     */
    protected function showUserInfo(): int
    {
        $userId = $this->option('user');
        if (!$userId) {
            $this->error('❌ User ID is required. Use --user=ID');
            return Command::FAILURE;
        }

        // Try to find user in both User and Admin models
        $user = User::find($userId) ?? Admin::find($userId);
        if (!$user) {
            $this->error("❌ User with ID {$userId} not found");
            return Command::FAILURE;
        }

        $this->info("👤 User Information: {$user->name} ({$user->email})");
        $this->newLine();

        // Check if user uses advanced roles
        if (!method_exists($user, 'usesAdvancedRoles') || !$user->usesAdvancedRoles()) {
            $this->warn('⚠️  User does not use advanced role system');
            return Command::SUCCESS;
        }

        // Show roles
        $assignments = AdvancedUserRole::forUser($user)->with('role')->get();
        if ($assignments->isEmpty()) {
            $this->info('No role assignments found');
            return Command::SUCCESS;
        }

        $this->info('🎭 Role Assignments:');
        foreach ($assignments as $assignment) {
            $status = $assignment->getStatus();
            $statusIcon = match ($status) {
                'active' => '✅',
                'inactive' => '⏸️',
                'expired' => '⏰',
                'revoked' => '❌',
                default => '❓',
            };

            $this->line("  {$statusIcon} {$assignment->role->display_name}");
            $this->line("     Status: {$status}");
            if ($assignment->context) {
                $this->line("     Context: {$assignment->context}");
            }
            if ($assignment->expires_at) {
                $this->line("     Expires: {$assignment->expires_at->format('Y-m-d H:i')}");
            }
        }

        // Show effective permissions
        $this->newLine();
        $this->info('🔑 Effective Permissions:');
        $permissions = $user->getAdvancedPermissions();
        $grouped = $permissions->groupBy('category');

        foreach ($grouped as $category => $categoryPermissions) {
            $categoryName = $category ?: 'Uncategorized';
            $this->line("  📂 {$categoryName} ({$categoryPermissions->count()})");
            foreach ($categoryPermissions->take(5) as $permission) {
                $this->line("     • {$permission->name}");
            }
            if ($categoryPermissions->count() > 5) {
                $remaining = $categoryPermissions->count() - 5;
                $this->line("     ... and {$remaining} more");
            }
        }

        return Command::SUCCESS;
    }

    /**
     * Assign role to user
     */
    protected function assignRole(): int
    {
        $userId = $this->option('user');
        $roleName = $this->option('role');

        if (!$userId || !$roleName) {
            $this->error('❌ Both --user and --role are required');
            return Command::FAILURE;
        }

        $user = User::find($userId) ?? Admin::find($userId);
        if (!$user) {
            $this->error("❌ User with ID {$userId} not found");
            return Command::FAILURE;
        }

        $role = AdvancedRole::findByName($roleName);
        if (!$role) {
            $this->error("❌ Role '{$roleName}' not found");
            return Command::FAILURE;
        }

        if ($this->option('dry-run')) {
            $this->info("Would assign role '{$role->display_name}' to user '{$user->email}'");
            return Command::SUCCESS;
        }

        try {
            $user->assignAdvancedRole($role);
            $this->info("✅ Assigned role '{$role->display_name}' to user '{$user->email}'");
        } catch (\Exception $e) {
            $this->error("❌ Assignment failed: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Revoke role from user
     */
    protected function revokeRole(): int
    {
        $userId = $this->option('user');
        $roleName = $this->option('role');

        if (!$userId || !$roleName) {
            $this->error('❌ Both --user and --role are required');
            return Command::FAILURE;
        }

        $user = User::find($userId) ?? Admin::find($userId);
        if (!$user) {
            $this->error("❌ User with ID {$userId} not found");
            return Command::FAILURE;
        }

        $role = AdvancedRole::findByName($roleName);
        if (!$role) {
            $this->error("❌ Role '{$roleName}' not found");
            return Command::FAILURE;
        }

        if ($this->option('dry-run')) {
            $this->info("Would revoke role '{$role->display_name}' from user '{$user->email}'");
            return Command::SUCCESS;
        }

        try {
            $user->removeAdvancedRole($role, 'Revoked via command');
            $this->info("✅ Revoked role '{$role->display_name}' from user '{$user->email}'");
        } catch (\Exception $e) {
            $this->error("❌ Revocation failed: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Show help
     */
    protected function showHelp(): int
    {
        $this->info('🔧 Advanced Permission System Manager');
        $this->newLine();
        $this->info('Available actions:');
        $this->line('  status          - Show system status and statistics');
        $this->line('  discover        - Discover permissions from controllers');
        $this->line('  seed            - Seed the permission system');
        $this->line('  reset           - Reset the entire system (destructive)');
        $this->line('  user-info       - Show user role and permission info');
        $this->line('  assign-role     - Assign role to user');
        $this->line('  revoke-role     - Revoke role from user');
        $this->line('  create-role     - Create role from template');
        $this->line('  delete-role     - Delete role');
        $this->line('  list-roles      - List all roles');
        $this->line('  list-permissions - List all permissions');
        $this->line('  cleanup         - Clean up unused permissions/roles');
        $this->line('  validate        - Validate system integrity');
        $this->line('  export          - Export system data');
        $this->line('  import          - Import system data');

        $this->newLine();
        $this->info('Options:');
        $this->line('  --user=ID       - User ID for user-specific actions');
        $this->line('  --role=NAME     - Role name for role-specific actions');
        $this->line('  --permission=NAME - Permission name for permission-specific actions');
        $this->line('  --template=NAME - Template name for template actions');
        $this->line('  --force         - Force action without confirmation');
        $this->line('  --dry-run       - Show what would be done without executing');

        return Command::SUCCESS;
    }

    /**
     * List roles
     */
    protected function listRoles(): int
    {
        $roles = AdvancedRole::with(['parentRole', 'permissions'])
            ->withCount(['userRoles', 'permissions'])
            ->orderBy('category')
            ->orderBy('level')
            ->orderBy('display_name')
            ->get();

        $this->info("🎭 All Roles ({$roles->count()})");
        $this->newLine();

        $tableData = [];
        foreach ($roles as $role) {
            $tableData[] = [
                $role->name,
                $role->display_name,
                $role->category ?: '-',
                $role->level,
                $role->permissions_count,
                $role->user_roles_count,
                $role->is_active ? '✅' : '❌',
            ];
        }

        $this->table(
            ['Name', 'Display Name', 'Category', 'Level', 'Permissions', 'Users', 'Active'],
            $tableData
        );

        return Command::SUCCESS;
    }

    /**
     * List permissions
     */
    protected function listPermissions(): int
    {
        $permissions = AdvancedPermission::withCount('roles')
            ->orderBy('category')
            ->orderBy('resource')
            ->orderBy('action')
            ->get();

        $this->info("🔑 All Permissions ({$permissions->count()})");
        $this->newLine();

        $grouped = $permissions->groupBy('category');
        foreach ($grouped as $category => $categoryPermissions) {
            $categoryName = $category ?: 'Uncategorized';
            $this->info("📂 {$categoryName} ({$categoryPermissions->count()})");

            foreach ($categoryPermissions as $permission) {
                $status = $permission->is_active ? '✅' : '❌';
                $system = $permission->is_system ? '🔒' : '';
                $usage = $permission->roles_count > 0 ? "({$permission->roles_count} roles)" : '';
                
                $this->line("  {$status}{$system} {$permission->name} {$usage}");
            }
            $this->newLine();
        }

        return Command::SUCCESS;
    }

    /**
     * Cleanup system
     */
    protected function cleanupSystem(): int
    {
        $this->info('🧹 Cleaning up system...');

        $cleaned = 0;

        // Remove unused permissions
        $unusedPermissions = AdvancedPermission::doesntHave('roles')
            ->where('is_system', false)
            ->get();

        if ($unusedPermissions->count() > 0) {
            if ($this->option('dry-run')) {
                $this->info("Would remove {$unusedPermissions->count()} unused permissions");
            } else {
                foreach ($unusedPermissions as $permission) {
                    $permission->delete();
                    $cleaned++;
                }
                $this->info("✅ Removed {$cleaned} unused permissions");
            }
        }

        // Remove expired role assignments
        $expiredAssignments = AdvancedUserRole::where('expires_at', '<', now())
            ->where('is_active', true)
            ->get();

        if ($expiredAssignments->count() > 0) {
            if ($this->option('dry-run')) {
                $this->info("Would deactivate {$expiredAssignments->count()} expired assignments");
            } else {
                foreach ($expiredAssignments as $assignment) {
                    $assignment->update(['is_active' => false]);
                    $cleaned++;
                }
                $this->info("✅ Deactivated {$expiredAssignments->count()} expired assignments");
            }
        }

        if ($cleaned === 0) {
            $this->info('✨ System is already clean');
        }

        return Command::SUCCESS;
    }

    /**
     * Validate system integrity
     */
    protected function validateSystem(): int
    {
        $this->info('🔍 Validating system integrity...');

        $issues = [];

        // Check for orphaned role permissions
        $orphanedRolePermissions = DB::table('advanced_role_permissions')
            ->leftJoin('advanced_roles', 'advanced_role_permissions.role_id', '=', 'advanced_roles.id')
            ->leftJoin('advanced_permissions', 'advanced_role_permissions.permission_id', '=', 'advanced_permissions.id')
            ->whereNull('advanced_roles.id')
            ->orWhereNull('advanced_permissions.id')
            ->count();

        if ($orphanedRolePermissions > 0) {
            $issues[] = "Found {$orphanedRolePermissions} orphaned role-permission relationships";
        }

        // Check for orphaned user roles
        $orphanedUserRoles = AdvancedUserRole::whereDoesntHave('role')->count();
        if ($orphanedUserRoles > 0) {
            $issues[] = "Found {$orphanedUserRoles} orphaned user role assignments";
        }

        // Check for circular role hierarchy
        $roles = AdvancedRole::whereNotNull('parent_role_id')->get();
        foreach ($roles as $role) {
            if ($this->hasCircularHierarchy($role)) {
                $issues[] = "Circular hierarchy detected for role: {$role->name}";
            }
        }

        if (empty($issues)) {
            $this->info('✅ System integrity validated - no issues found');
        } else {
            $this->warn('⚠️  System integrity issues found:');
            foreach ($issues as $issue) {
                $this->line("  • {$issue}");
            }
        }

        return empty($issues) ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Check for circular hierarchy
     */
    protected function hasCircularHierarchy(AdvancedRole $role): bool
    {
        $visited = [];
        $current = $role;

        while ($current && $current->parent_role_id) {
            if (in_array($current->id, $visited)) {
                return true;
            }
            $visited[] = $current->id;
            $current = $current->parentRole;
        }

        return false;
    }

    /**
     * Export system data
     */
    protected function exportData(): int
    {
        $this->info('📤 Exporting system data...');
        // Implementation would go here
        $this->info('✅ Export completed');
        return Command::SUCCESS;
    }

    /**
     * Import system data
     */
    protected function importData(): int
    {
        $this->info('📥 Importing system data...');
        // Implementation would go here
        $this->info('✅ Import completed');
        return Command::SUCCESS;
    }
}
