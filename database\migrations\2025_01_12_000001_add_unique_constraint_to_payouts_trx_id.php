<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's check for and fix any existing duplicate trx_ids
        $this->fixExistingDuplicates();
        
        // Add unique constraint to trx_id field
        Schema::table('payouts', function (Blueprint $table) {
            $table->unique('trx_id', 'unique_payout_trx_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
            $table->dropUnique('unique_payout_trx_id');
        });
    }

    /**
     * Fix existing duplicate trx_ids before adding unique constraint
     */
    private function fixExistingDuplicates(): void
    {
        // Find duplicate trx_ids
        $duplicates = DB::select("
            SELECT trx_id, COUNT(*) as count 
            FROM payouts 
            WHERE trx_id IS NOT NULL 
            GROUP BY trx_id 
            HAVING COUNT(*) > 1
        ");

        foreach ($duplicates as $duplicate) {
            echo "Fixing duplicate trx_id: {$duplicate->trx_id} (found {$duplicate->count} times)\n";
            
            // Get all records with this trx_id, ordered by id
            $records = DB::table('payouts')
                ->where('trx_id', $duplicate->trx_id)
                ->orderBy('id')
                ->get();

            // Keep the first record unchanged, update the others
            foreach ($records as $index => $record) {
                if ($index > 0) { // Skip the first record
                    $newTrxId = $this->generateUniqueTrxId();
                    DB::table('payouts')
                        ->where('id', $record->id)
                        ->update(['trx_id' => $newTrxId]);
                    
                    echo "Updated payout ID {$record->id} from {$duplicate->trx_id} to {$newTrxId}\n";
                }
            }
        }
    }

    /**
     * Generate a unique trx_id
     */
    private function generateUniqueTrxId(): string
    {
        do {
            // Get the last trx_id number
            $lastTrxId = DB::selectOne("
                SELECT trx_id FROM payouts 
                WHERE trx_id IS NOT NULL 
                ORDER BY id DESC 
                LIMIT 1
            ");
            
            $lastNumber = 1;
            if ($lastTrxId && $lastTrxId->trx_id) {
                $lastNumber = (int)substr($lastTrxId->trx_id, 1); // Remove 'P' prefix
                $lastNumber++;
            }
            
            $newTrxId = 'P' . str_pad($lastNumber, 10, '0', STR_PAD_LEFT);
            
            // Check if this trx_id already exists
            $exists = DB::table('payouts')->where('trx_id', $newTrxId)->exists();
            
        } while ($exists);
        
        return $newTrxId;
    }
};
