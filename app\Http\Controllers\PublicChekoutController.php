<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use App\Models\Gateway;
use App\Models\ProductOrder;
use App\Models\ProductOrderDetail;
use App\Models\Store;
use App\Models\StoreProductStock;
use App\Models\StoreShipping;
use App\Traits\PaymentTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>an\Purify\Facades\Purify;

class PublicChekoutController extends Controller
{
    use PaymentTrait;

	public function productCheckout($link)
	{
		$store = Store::with('user')->where('link', $link)->firstOrFail();
		$data['link'] = $link;
		$data['shippingAdds'] = StoreShipping::where('status', 1)->where('store_id', $store->id)->orderBy('address', 'asc')->get();
		$data['gateways'] = Gateway::getActiveMethods();
        $data['currencies'] = Currency::select('id', 'code', 'name', 'currency_type', 'is_active')->where('is_active', 1)->get();
        return view('user.store.shop.checkout', $data, compact('store'));
	}

	public function productCheckoutStore(Request $request)
	{
		$purifiedData = Purify::clean($request->all());
		$validator = Validator::make($purifiedData, [
			'name' => 'required',
			'email' => 'required',
			'phone' => 'required',
			'shippingId' => 'required',
			'detailAddress' => 'required',
			'methodId' => 'required',
		]);
		if ($validator->fails()) {
			return response()->json([
				'status' => 'error'
			]);
		}

		$cartItem = $request->cartItem;
		if (count($cartItem) < 1) {
			return response()->json([
				'status' => 'emptyCart'
			]);
		}

		foreach ($cartItem as $item) {
			$stock = StoreProductStock::with('product')->where('product_id', $item['id'])
                ->whereJsonContains('product_attr_lists_id', $item['attributes'])->first();
			if ($item['count'] > $stock->quantity) {
				$productName = optional($stock->product)->name ?? 'Unknown Item';
				return response()->json([
					'status' => 'stockOut',
					'product_name' => $productName
				]);
			}
		}

		$totalAmount = 0;
		$shipping = StoreShipping::with('store', 'user')->where('id', $request->shippingId)->first();

		foreach ($cartItem as $item) {
			$totalAmount += $item['price'] * $item['quantity'];
		}

		$methodId = $request->methodId;
		$currency_id = $request->currency_id;
        $store_currency_id = $shipping->user?->store_currency_id;

        $scRate = $this->getCurrencyRate($currency_id, $store_currency_id);
        $scAmount = $totalAmount / $scRate;

        $shippingCharge = $shipping->store?->shipping_charge == 1 ? $shipping->charge : 0;
        $scShippingCharge = $shippingCharge / $scRate;

		$checkAmountValidate = $this->validatePayment($scAmount, $currency_id,
            config('transactionType.deposit'), $methodId, $scShippingCharge);//7 = deposit
		if (!$checkAmountValidate['status']) {
			return response()->json([
				'status' => 'fail',
				'message' => $checkAmountValidate['message']
			]);
		}

		DB::beginTransaction();
		$order = new ProductOrder();
		$order->order_number = mt_rand(10000000, 99999999);
		$order->gateway_id = $request->methodId;
		$order->store_id = $shipping->store_id;
		$order->user_id = $shipping->user_id;
		$order->currency_id = optional($shipping->user)->store_currency_id;
		$order->fullname = $request->name;
		$order->email = $request->email;
		$order->phone = $request->phone;
		$order->alt_phone = $request->altPhone;
		$order->total_amount = $totalAmount;
		$order->shipping_id = $shipping->id;
		$order->shipping_charge = $shippingCharge;
		$order->detailed_address = $request->detailAddress;
		$order->order_note = $request->orderNote;
		$order->save();

		foreach ($cartItem as $item) {
			$orderDetails = new ProductOrderDetail();
			$orderDetails->order_id = $order->id;
			$orderDetails->product_id = $item['id'];
			$orderDetails->attributes_id = $item['attributes'];
			$orderDetails->quantity = $item['count'];
			$orderDetails->price = $item['price'];
			$orderDetails->total_price = $item['quantity'] * $item['price'];
			$orderDetails->save();
		}

        $deposit  = $this->createDeposit($checkAmountValidate, ProductOrder::class, $order->id);

		$order->utr = $deposit->trx_id;
		$order->save();

		DB::commit();
		return response()->json([
			'status' => 'success',
			'route' => route('payment.process', $deposit->trx_id)
		]);

	}


}
