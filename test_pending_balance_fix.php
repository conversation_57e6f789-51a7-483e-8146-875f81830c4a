<?php

require 'vendor/autoload.php';

use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Models\ForexBookingReservation;
use App\Models\ForexTransaction;
use App\Services\ForexBookingService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING PENDING BALANCE FIX ===\n\n";

// Get accounts
$usdAccount = ForexAccount::byType('USD')->first();
$cbnAccount = ForexAccount::byType('CBN')->first();
$diffAccount = ForexAccount::byType('Difference')->first();

if (!$usdAccount || !$cbnAccount || !$diffAccount) {
    echo "❌ Required accounts not found\n";
    exit;
}

// Add some balance to accounts for testing
$cbnAccount->update(['balance' => 2000000]); // 2M NGN
$diffAccount->update(['balance' => 500000]); // 500K NGN

echo "Initial Account States (after adding test balance):\n";
echo "USD Account: Balance=" . number_format((float)$usdAccount->balance, 2) . ", Pending=" . number_format((float)$usdAccount->pending_balance, 2) . "\n";
echo "CBN Account: Balance=" . number_format((float)$cbnAccount->balance, 2) . ", Pending=" . number_format((float)$cbnAccount->pending_balance, 2) . "\n";
echo "Diff Account: Balance=" . number_format((float)$diffAccount->balance, 2) . ", Pending=" . number_format((float)$diffAccount->pending_balance, 2) . "\n\n";

// Test USD to NGN booking (multi-account scenario) - smaller amounts
$bookingData = [
    'client_type' => 'external',
    'client_name' => 'Test Client',
    'client_email' => '<EMAIL>',
    'transaction_type' => 'selling',
    'currency' => 'USD',
    'amount' => 100, // $100 USD
    'target_account_id' => $cbnAccount->id,
    'cbn_rate' => 1600,
    'parallel_rate' => 1650,
    'markup_percentage' => 2,
    'customer_rate' => 1683,
    'cbn_total' => 160000, // 100 * 1600
    'parallel_total' => 165000, // 100 * 1650
    'markup_amount' => 3300, // 2% of 165000
    'difference_amount' => 5000, // parallel - cbn
    'customer_total' => 168300, // cbn + markup + difference
    'customer_payment_amount' => 100
];

$bookingService = app(ForexBookingService::class);

try {
    echo "=== STEP 1: CREATE BOOKING ===\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    echo "✅ Booking created: {$booking->booking_reference}\n";

    // Refresh accounts and check pending balances
    $usdAccount->refresh();
    $cbnAccount->refresh();
    $diffAccount->refresh();

    echo "\nAccount States After Booking:\n";
    echo "USD Account: Balance=" . number_format((float)$usdAccount->balance, 2) . ", Pending=" . number_format((float)$usdAccount->pending_balance, 2) . "\n";
    echo "CBN Account: Balance=" . number_format((float)$cbnAccount->balance, 2) . ", Pending=" . number_format((float)$cbnAccount->pending_balance, 2) . "\n";
    echo "Diff Account: Balance=" . number_format((float)$diffAccount->balance, 2) . ", Pending=" . number_format((float)$diffAccount->pending_balance, 2) . "\n";

    // Check reservations
    $reservations = $booking->reservations;
    echo "\nReservations Created: " . $reservations->count() . "\n";
    foreach ($reservations as $reservation) {
        echo "- {$reservation->account->account_name}: " . number_format((float)$reservation->reserved_amount, 2) . " (Status: {$reservation->status})\n";
    }

    // Check booked transactions
    $bookedTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_subtype', 'booked')
        ->get();
    echo "\nBooked Transactions: " . $bookedTransactions->count() . "\n";
    foreach ($bookedTransactions as $transaction) {
        echo "- {$transaction->forexAccount->account_name}: " . number_format((float)$transaction->amount, 2) . " (Completed: " . ($transaction->is_completed ? 'Yes' : 'No') . ")\n";
    }

    echo "\n=== STEP 2: COMPLETE BOOKING ===\n";
    $bookingService->completeBooking($booking, 1, 'Test completion');
    echo "✅ Booking completed\n";

    // Refresh accounts and check pending balances after completion
    $usdAccount->refresh();
    $cbnAccount->refresh();
    $diffAccount->refresh();

    echo "\nAccount States After Completion:\n";
    echo "USD Account: Balance=" . number_format((float)$usdAccount->balance, 2) . ", Pending=" . number_format((float)$usdAccount->pending_balance, 2) . "\n";
    echo "CBN Account: Balance=" . number_format((float)$cbnAccount->balance, 2) . ", Pending=" . number_format((float)$cbnAccount->pending_balance, 2) . "\n";
    echo "Diff Account: Balance=" . number_format((float)$diffAccount->balance, 2) . ", Pending=" . number_format((float)$diffAccount->pending_balance, 2) . "\n";

    // Check reservation statuses
    $booking->refresh();
    $reservations = $booking->reservations;
    echo "\nReservation Statuses After Completion:\n";
    foreach ($reservations as $reservation) {
        echo "- {$reservation->account->account_name}: " . number_format((float)$reservation->reserved_amount, 2) . " (Status: {$reservation->status})\n";
    }

    // Check booked transaction completion status
    $bookedTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_subtype', 'booked')
        ->get();
    echo "\nBooked Transaction Completion Status:\n";
    foreach ($bookedTransactions as $transaction) {
        $transaction->refresh();
        echo "- {$transaction->forexAccount->account_name}: " . number_format((float)$transaction->amount, 2) . " (Completed: " . ($transaction->is_completed ? 'Yes' : 'No') . ")\n";
    }

    // Verify pending balances are cleared
    $totalPendingAfter = $usdAccount->pending_balance + $cbnAccount->pending_balance + $diffAccount->pending_balance;
    if ($totalPendingAfter == 0) {
        echo "\n✅ SUCCESS: All pending balances cleared after completion!\n";
    } else {
        echo "\n❌ ISSUE: Some pending balances remain: " . number_format((float)$totalPendingAfter, 2) . "\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

?>
