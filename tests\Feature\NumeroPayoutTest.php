<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PayoutMethod;
use App\Services\Payout\numero\Card;
use Illuminate\Foundation\Testing\RefreshDatabase;

class NumeroPayoutTest extends TestCase
{
    /**
     * Test Numero bank list functionality
     */
    public function test_numero_bank_list()
    {
        $banks = Card::getBank('NGN');
        
        $this->assertIsArray($banks);
        $this->assertEquals('success', $banks['status']);
        $this->assertIsArray($banks['data']);
        $this->assertNotEmpty($banks['data']);
        
        // Check if banks have required fields
        $firstBank = $banks['data'][0];
        $this->assertArrayHasKey('code', $firstBank);
        $this->assertArrayHasKey('name', $firstBank);
    }

    /**
     * Test Numero signature generation
     */
    public function test_numero_signature_generation()
    {
        $requestBody = [
            'narration' => 'test',
            'amount' => 100,
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '000016',
            'destinationAccountName' => 'Test Account',
            'phoneNumber' => '***********'
        ];
        
        $publicKey = 'a73c40b5055844aebf834b3f665f4733';
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass(Card::class);
        $method = $reflection->getMethod('generateSignature');
        $method->setAccessible(true);
        
        $signature = $method->invoke(null, $requestBody, $publicKey);
        
        $this->assertNotNull($signature);
        $this->assertIsString($signature);
        $this->assertNotEmpty($signature);
    }

    /**
     * Test Numero webhook handling
     */
    public function test_numero_webhook_handling()
    {
        // Mock webhook payload
        $webhookPayload = (object) [
            'data' => (object) [
                'reference' => 'test-reference-123',
                'status' => 'successful',
                'requestState' => 'completed',
                'requestStateDetails' => 'Transfer completed successfully'
            ]
        ];

        // Test webhook method
        $result = Card::webhook($webhookPayload);
        
        // Since no payout exists with this reference, it should return false
        $this->assertFalse($result);
    }

    /**
     * Test Numero configuration validation
     */
    public function test_numero_configuration()
    {
        // Test that Numero is properly configured in banks config
        $numeroConfig = config('banks.NUMERO');
        
        $this->assertNotNull($numeroConfig);
        $this->assertEquals('NG', $numeroConfig['api']);
        $this->assertArrayHasKey('input_form', $numeroConfig);
        $this->assertArrayHasKey('validation', $numeroConfig);
        
        // Check required fields
        $inputForm = $numeroConfig['input_form'];
        $this->assertArrayHasKey('account_number', $inputForm);
        $this->assertArrayHasKey('account_name', $inputForm);
        $this->assertArrayHasKey('bank_code', $inputForm);
        $this->assertArrayHasKey('narration', $inputForm);
        $this->assertArrayHasKey('phone_number', $inputForm);
        
        // Check validation rules
        $validation = $numeroConfig['validation'];
        $this->assertEquals('required', $validation['account_number']);
        $this->assertEquals('required', $validation['account_name']);
        $this->assertEquals('required', $validation['bank_code']);
        $this->assertEquals('required', $validation['narration']);
        $this->assertEquals('required', $validation['phone_number']);
    }

    /**
     * Test Numero payout method seeder data
     */
    public function test_numero_seeder_data()
    {
        // This test would verify the seeder data structure
        // In a real test, you'd run the seeder and check the database

        $expectedParameters = [
            'api_key' => 'live_key_2wJVjV1WfDMbAtQzb0SsLAxPn',
            'public_key' => 'a73c40b5055844aebf834b3f665f4733',
            'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
        ];

        $this->assertIsArray($expectedParameters);
        $this->assertArrayHasKey('api_key', $expectedParameters);
        $this->assertArrayHasKey('public_key', $expectedParameters);
        $this->assertArrayHasKey('base_url', $expectedParameters);
    }

    /**
     * Test Numero checkTransferStatus method
     */
    public function test_numero_check_transfer_status()
    {
        // Create a mock payout method
        $method = new PayoutMethod();
        $method->parameters = (object) [
            'api_key' => 'test_api_key',
            'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
        ];

        // Test with a mock reference
        $reference = 'test-reference-123';

        // Note: This would normally make an actual API call
        // In a real test environment, you'd mock the HTTP client
        // For now, we just test that the method exists and can be called
        $this->assertTrue(method_exists(Card::class, 'checkTransferStatus'));

        // Test method signature
        $reflection = new \ReflectionClass(Card::class);
        $method = $reflection->getMethod('checkTransferStatus');
        $parameters = $method->getParameters();

        $this->assertCount(2, $parameters);
        $this->assertEquals('reference', $parameters[0]->getName());
        $this->assertEquals('method', $parameters[1]->getName());
    }

    /**
     * Test that checkTransferStatus returns proper structure
     */
    public function test_numero_check_transfer_status_structure()
    {
        // Mock method parameters
        $method = new PayoutMethod();
        $method->parameters = (object) [
            'api_key' => 'test_api_key',
            'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
        ];

        // Test with invalid reference to get error response
        $result = Card::checkTransferStatus('invalid-reference', $method);

        // Should return array with status key
        $this->assertIsArray($result);
        $this->assertArrayHasKey('status', $result);

        // For invalid reference, should return error
        if ($result['status'] === 'error') {
            $this->assertArrayHasKey('data', $result);
        }
    }
}
