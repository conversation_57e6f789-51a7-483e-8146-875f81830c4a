@extends('admin.layouts.app')
@section('page_title', __('Virtual Accounts'))

@section('content')
    <div class="content container-fluid">
        <x-page-header menu="Virtual Accounts" :statBtn="true">
            <x-slot name="push_header_button">
                <a class="btn btn-primary" href="{{ route('admin.virtual.accounts.create') }}">
                    <i class="bi-plus me-1"></i> @lang('Create Virtual Account')
                </a>
            </x-slot>
        </x-page-header>

        <!-- Stats -->
        <div class="row mb-3 d-none" id="statsSection">
            <x-stats-card
                title="Total Virtual Accounts"
                :value="$stats['total_accounts'] ?? 0"
                :total="$stats['total_accounts'] ?? 0"
                :percentage="100"
                icon="bi-bank"
                color="success"
            />
            <x-stats-card
                title="Active Accounts"
                :value="$stats['active_accounts'] ?? 0"
                :total="$stats['total_accounts'] ?? 0"
                :percentage="$stats['total_accounts'] > 0 ? round(($stats['active_accounts'] / $stats['total_accounts']) * 100, 2) : 0"
                icon="bi-check-circle"
                color="primary"
            />
            <x-stats-card
                title="NGN Accounts"
                :value="$stats['ngn_accounts'] ?? 0"
                :total="$stats['total_accounts'] ?? 0"
                :percentage="$stats['total_accounts'] > 0 ? round(($stats['ngn_accounts'] / $stats['total_accounts']) * 100, 2) : 0"
                icon="bi-currency-exchange"
                color="info"
            />
            <x-stats-card
                title="USD Accounts"
                :value="$stats['usd_accounts'] ?? 0"
                :total="$stats['total_accounts'] ?? 0"
                :percentage="$stats['total_accounts'] > 0 ? round(($stats['usd_accounts'] / $stats['total_accounts']) * 100, 2) : 0"
                icon="bi-currency-dollar"
                color="warning"
            />
        </div>
        <!-- End Stats -->

        <!-- Card -->
        <div class="card">
            <!-- Header -->
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <div class="input-group input-group-merge input-group-flush">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input id="datatableSearch" type="search" class="form-control" placeholder="@lang('Search virtual accounts')" aria-label="@lang('Search virtual accounts')">
                    </div>
                </div>

                <div class="d-grid d-sm-flex gap-2">
                    <div class="dropdown">
                        <button type="button" class="btn btn-white btn-sm dropdown-toggle w-100" id="usersFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi-filter me-1"></i> @lang('Filter')
                        </button>
                        <div class="dropdown-menu dropdown-menu-sm-end dropdown-card card-dropdown-filter-centered" aria-labelledby="usersFilterDropdown">
                            <div class="card">
                                <div class="card-header card-header-content-between">
                                    <h5 class="card-header-title">@lang('Filter virtual accounts')</h5>
                                    <button type="button" class="btn btn-ghost-secondary btn-icon btn-sm ms-2" id="filter_close_btn">
                                        <i class="bi-x-lg"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <form id="filter_form">
                                        <div class="mb-4">
                                            <span class="text-cap text-body">@lang('Currency')</span>
                                            <select class="form-select" name="currency" id="currency_filter">
                                                <option value="">@lang('All currencies')</option>
                                                @foreach($currencies as $currency)
                                                    <option value="{{ $currency->code }}">{{ $currency->code }} - {{ $currency->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="mb-4">
                                            <span class="text-cap text-body">@lang('Provider')</span>
                                            <select class="form-select" name="provider" id="provider_filter">
                                                <option value="">@lang('All providers')</option>
                                                <option value="numero">Numero</option>
                                            </select>
                                        </div>
                                        <div class="mb-4">
                                            <span class="text-cap text-body">@lang('Account Type')</span>
                                            <select class="form-select" name="type" id="type_filter">
                                                <option value="">@lang('All types')</option>
                                                <option value="customer">@lang('Customer')</option>
                                                <option value="individual">@lang('Individual')</option>
                                                <option value="business">@lang('Business')</option>
                                            </select>
                                        </div>
                                        <div class="mb-4">
                                            <span class="text-cap text-body">@lang('Status')</span>
                                            <select class="form-select" name="status" id="status_filter">
                                                <option value="">@lang('All statuses')</option>
                                                <option value="1">@lang('Active')</option>
                                                <option value="0">@lang('Inactive')</option>
                                            </select>
                                        </div>
                                        <div class="d-grid">
                                            <button type="button" class="btn btn-primary" id="filter_button">@lang('Apply')</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Header -->

            <!-- Table -->
            <div class="table-responsive datatable-custom">
                <table id="datatable" class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                       "columnDefs": [{
                          "targets": [0, 8],
                          "orderable": false
                        }],
                       "order": [],
                       "info": {
                         "totalQty": "#datatableWithPaginationInfoTotalQty"
                       },
                       "search": "#datatableSearch",
                       "entries": "#datatableEntries",
                       "pageLength": 15,
                       "isResponsive": false,
                       "isShowPaging": true,
                       "pagination": "datatablePagination"
                     }'>
                    <thead class="thead-light">
                    <tr>
                        <th scope="col" class="table-column-pe-0">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="datatableCheckAll">
                                <label class="form-check-label" for="datatableCheckAll"></label>
                            </div>
                        </th>
                        <th class="table-column-ps-0">@lang('User')</th>
                        <th>@lang('Account Details')</th>
                        <th>@lang('Provider')</th>
                        <th>@lang('Currency')</th>
                        <th>@lang('Type')</th>
                        <th>@lang('Status')</th>
                        <th>@lang('Created At')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <!-- End Table -->

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing:')</span>
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto" autocomplete="off">
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="20">20</option>
                                </select>
                            </div>
                            <span class="text-secondary me-2">@lang('of')</span>
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Card -->
    </div>
@endsection

<x-assets :datatable="true" :counter="true" :tomselect="true"/>

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
@endpush



@push('script')
    <script>
        'use strict';
        $(document).ready(function () {
            // Initialize DataTable using the same pattern as user management
            initDataTable("{{ route('admin.virtual.accounts.search') }}",
                {!! json_encode(['checkbox', 'user', 'account_details', 'provider', 'currency', 'type', 'status', 'created_at', 'action']) !!}
            );

            // Add filter functionality
            document.getElementById("filter_button").addEventListener("click", function () {
                applyFilter("{{ route('admin.virtual.accounts.search') }}", {
                    currency: $('#currency_filter').val(),
                    provider: $('#provider_filter').val(),
                    type: $('#type_filter').val(),
                    status: $('#status_filter').val(),
                });
            });
        });

        function toggleStatus(id, status) {
            $.ajax({
                url: "{{ route('admin.virtual.accounts.toggle.status', '') }}/" + id,
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: { status: status },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#datatable').DataTable().ajax.reload();
                        Notiflix.Notify.success(response.message);
                    } else {
                        Notiflix.Notify.failure(response.message);
                    }
                },
                error: function() {
                    Notiflix.Notify.failure('Failed to update status');
                }
            });
        }
    </script>
@endpush
