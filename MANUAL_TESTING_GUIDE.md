# Manual Testing Guide - Forex Wallet Payment Feature

## Prerequisites

1. **Database Setup**: Ensure all migrations have been run
2. **Test Data**: Create test users, currencies, and forex accounts
3. **Active Forex Rate**: Ensure there's an active forex rate in the system

## Test Data Setup

### 1. Create Test Currencies (if not exists)
```sql
INSERT INTO currencies (name, code, symbol, is_active, currency_type, driver, logo, created_at, updated_at) VALUES
('US Dollar', 'USD', '$', 1, 0, 'local', 'usd.png', NOW(), NOW()),
('Nigerian Naira', 'NGN', '₦', 1, 1, 'local', 'ngn.png', NOW(), NOW());
```

### 2. Create Test User
```sql
INSERT INTO users (firstname, lastname, username, email, password, status, email_verified_at, created_at, updated_at) VALUES
('Test', 'User', 'testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, NOW(), NOW(), NOW());
```

### 3. Create Test Wallets
```sql
-- Get currency and user IDs first
SET @usd_currency_id = (SELECT id FROM currencies WHERE code = 'USD' LIMIT 1);
SET @ngn_currency_id = (SELECT id FROM currencies WHERE code = 'NGN' LIMIT 1);
SET @test_user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- Create wallets
INSERT INTO wallets (user_id, currency_id, balance, created_at, updated_at) VALUES
(@test_user_id, @usd_currency_id, 100.00, NOW(), NOW()),
(@test_user_id, @ngn_currency_id, 50000.00, NOW(), NOW());
```

### 4. Create Forex Accounts
```sql
INSERT INTO forex_accounts (account_name, account_type, currency_code, balance, pending_balance, is_active, description, created_at, updated_at) VALUES
('USD Trading Account', 'USD', 'USD', 10000.00, 0.00, 1, 'Main USD account for forex trading', NOW(), NOW()),
('CBN Account', 'CBN', 'NGN', 5000000.00, 0.00, 1, 'Central Bank of Nigeria account', NOW(), NOW());
```

### 5. Create Active Forex Rate
```sql
INSERT INTO forex_rates (cbn_rate, parallel_rate, markup_percentage, cbn_sell_rate, parallel_sell_rate, sell_markup_percentage, is_active, created_by, created_at, updated_at) VALUES
(800.00, 1200.00, 5.00, 790.00, 1190.00, 3.00, 1, 1, NOW(), NOW());
```

## Manual Test Cases

### Test Case 1: Admin Creates Booking with Wallet Payment (NGN to USD)

**Steps:**
1. Login as admin
2. Navigate to `/admin/forex/bookings/create`
3. Select "Client Type" as "Registered User"
4. Select the test user from dropdown
5. Verify client name and email auto-populate
6. Select "Transaction Type" as "Buying USD (Client pays NGN, receives USD)"
7. Select "Currency" as "USD" and enter amount "100"
8. **Expected**: Payment method options should appear showing:
   - "Bank Account Details" option
   - "Pay to USD Wallet" option with current balance
9. Select "Pay to USD Wallet"
10. **Expected**: Account details field should be hidden
11. Fill in any payment instructions (optional)
12. Click "Create Booking"

**Expected Results:**
- Booking created successfully
- `payment_method` = 'wallet'
- `wallet_currency_id` = USD currency ID
- Status = 'pending'

### Test Case 2: Complete Wallet Payment Booking

**Steps:**
1. Go to the booking created in Test Case 1
2. Note the current USD wallet balance of the test user
3. Click "Complete Booking"
4. Add completion notes
5. Confirm completion

**Expected Results:**
- Booking status changes to 'completed'
- User's USD wallet balance increases by 100.00 USD
- New transaction record created with:
  - `user_id` = test user ID
  - `currency_id` = USD currency ID
  - `amount` = 100.00
  - `trx_type` = '+'
  - `transactional_type` = 'App\Models\ForexBooking'
  - `transactional_id` = booking ID

### Test Case 3: Admin Creates Booking with Account Details

**Steps:**
1. Navigate to `/admin/forex/bookings/create`
2. Select "Client Type" as "Registered User"
3. Select the test user
4. Configure for USD to NGN transaction (selling)
5. When payment options appear, select "Bank Account Details"
6. **Expected**: Account details field should be visible
7. Enter bank account details
8. Create booking

**Expected Results:**
- Booking created with `payment_method` = 'account_details'
- `wallet_currency_id` = NULL
- Account details stored properly

### Test Case 4: External Client Booking (No Wallet Option)

**Steps:**
1. Navigate to `/admin/forex/bookings/create`
2. Select "Client Type" as "External Client"
3. Fill in client details manually
4. Configure transaction
5. **Expected**: No payment method options should appear
6. Only account details field should be visible
7. Create booking

**Expected Results:**
- Booking created with `payment_method` = 'account_details'
- No wallet-related fields set

### Test Case 5: User Without Required Wallet

**Steps:**
1. Create a new user without USD wallet
2. Try to create a booking for NGN to USD with wallet payment
3. **Expected**: Validation should fail
4. Error message should indicate wallet not available

### Test Case 6: API Wallet Payment Options

**Steps:**
1. Get API token for test user
2. Make GET request to `/api/forex/wallet-payment-options?transaction_type=buying`
3. **Expected Response:**
```json
{
    "success": true,
    "data": {
        "has_wallet": true,
        "target_currency": "USD",
        "payment_methods": {
            "account_details": {
                "label": "Bank Account Details",
                "description": "Provide bank account details for payment"
            },
            "wallet": {
                "label": "Pay to USD Wallet",
                "description": "Fund your USD wallet (Balance: 100.00 USD)",
                "wallet_balance": 100.00
            }
        }
    }
}
```

### Test Case 7: API Booking Creation with Wallet

**Steps:**
1. Make POST request to `/api/forex/booking` with:
```json
{
    "transaction_type": "buying",
    "currency": "USD",
    "amount": 50.00,
    "client_name": "Test User",
    "client_email": "<EMAIL>",
    "payment_method": "wallet",
    "wallet_currency_id": 1
}
```

**Expected Results:**
- Booking created successfully via API
- Wallet payment method properly set

### Test Case 8: Booking List Display

**Steps:**
1. Navigate to `/admin/forex/bookings`
2. **Expected**: Booking list should show:
   - Payment method badges (Wallet/Bank)
   - Clear visual distinction between payment types

### Test Case 9: Booking Detail View

**Steps:**
1. Open a wallet payment booking
2. **Expected**: Booking details should show:
   - "Wallet Payment" badge
   - Target currency information
   - Clear indication of payment destination

### Test Case 10: USD to NGN Wallet Payment

**Steps:**
1. Create booking for USD to NGN (selling)
2. Select NGN wallet payment
3. Complete booking
4. **Expected**: 
   - NGN wallet balance increases by customer_total amount
   - Proper transaction record created

## Validation Checks

### Database Validation
```sql
-- Check booking was created correctly
SELECT payment_method, wallet_currency_id, status FROM forex_bookings WHERE id = [booking_id];

-- Check wallet balance after completion
SELECT balance FROM wallets WHERE user_id = [user_id] AND currency_id = [currency_id];

-- Check transaction record
SELECT * FROM transactions WHERE transactional_type = 'App\\Models\\ForexBooking' AND transactional_id = [booking_id];
```

### Frontend Validation
- Payment method options appear/disappear correctly
- Form validation works properly
- AJAX calls return expected data
- UI updates dynamically based on selections

### API Validation
- Endpoints return proper HTTP status codes
- Response format matches documentation
- Authentication/authorization works correctly
- Error handling is appropriate

## Edge Cases to Test

1. **User with only one currency wallet**: Should only show wallet option for appropriate transaction type
2. **User with no wallets**: Should only show account details option
3. **Invalid wallet currency ID**: Should fail validation
4. **Concurrent booking completion**: Should handle race conditions properly
5. **Large amounts**: Should handle decimal precision correctly
6. **Network failures**: Should handle AJAX failures gracefully

## Performance Testing

1. **Load wallet info for user with many wallets**: Should be fast
2. **Multiple concurrent bookings**: Should not cause deadlocks
3. **Large transaction amounts**: Should handle calculations correctly

## Security Testing

1. **Unauthorized wallet access**: Should prevent users from accessing other users' wallets
2. **Invalid currency IDs**: Should validate currency exists and user has access
3. **SQL injection**: Should be protected against malicious inputs
4. **CSRF protection**: Should validate CSRF tokens properly

## Rollback Testing

If issues are found, the feature can be safely disabled by:
1. Setting all new bookings to use 'account_details' payment method
2. The existing functionality remains completely intact
3. Database migration can be rolled back if needed

## Success Criteria

✅ All test cases pass
✅ No existing functionality is broken
✅ Performance is acceptable
✅ Security validations work
✅ User experience is intuitive
✅ Error handling is robust
✅ Documentation is complete
