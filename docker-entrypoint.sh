#!/bin/bash
set -e

echo "Starting Laravel application..."

# Wait for database to be ready (optional - add if using external DB)
# echo "Waiting for database..."
# until php artisan migrate:status > /dev/null 2>&1; do
#   echo "Database not ready, waiting..."
#   sleep 2
# done

# Run Laravel optimizations
echo "Optimizing Laravel application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run migrations
echo "Running database migrations..."
php artisan migrate --force

# Set proper permissions
echo "Setting permissions..."
chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache
chmod -R 775 /var/www/storage /var/www/bootstrap/cache

echo "Laravel application ready!"

# Test Laravel logging to CloudWatch
echo "Testing Laravel logging..."
php -r "require 'vendor/autoload.php'; \$app = require_once 'bootstrap/app.php'; \$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap(); \Illuminate\Support\Facades\Log::info('Container startup - Laravel logging test', ['timestamp' => date('Y-m-d H:i:s'), 'container' => 'startup']);"

# Execute the main command
exec "$@"
