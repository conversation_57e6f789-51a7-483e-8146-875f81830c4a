<div class="col-xl-7">
    <div class="card transaction-card mb-3 mb-lg-5" id="RecentTransactionGraph">
        <!-- Header -->
        <div class="card-header card-header-content-sm-between">
            <h4 class="card-header-title mb-2 mb-sm-0"><?php echo app('translator')->get("Recent Transaction"); ?></h4>

            <!-- Nav -->
            <ul class="nav nav-segment nav-fill" id="projectsTab" role="tablist">
                <li class="nav-item this-month" data-bs-toggle="chart" data-datasets="0"
                    data-trigger="click"
                    data-action="toggle">
                    <a class="nav-link active" href="javascript:void(0);"
                       data-bs-toggle="tab"><?php echo app('translator')->get("This Month"); ?></a>
                </li>
                <li class="nav-item" data-bs-toggle="chart" data-datasets="1" data-trigger="click"
                    data-action="toggle">
                    <a class="nav-link" href="javascript:void(0);"
                       data-bs-toggle="tab"><?php echo app('translator')->get("Last Month"); ?></a>
                </li>
            </ul>
            <!-- End Nav -->
        </div>
        <!-- End Header -->

        <!-- Body -->
        <div class="card-body">
            <div class="row align-items-sm-center mb-4">
                <div class="col-sm mb-3 mb-sm-0">
                    <div class="d-flex align-items-center">
                                    <span class="h5 mb-0"> <?php echo app('translator')->get("DEPOSIT:"); ?> <span
                                            class="deposit_amount"></span> </span>
                    </div>
                </div>
                <!-- End Col -->
                <div class="col-sm-auto">
                    <!-- Legend Indicators -->
                    <div class="row fs-6">
                        <div class="col-auto">
                            <span class="legend-indicator bg-primary"></span> <?php echo app('translator')->get("Deposit"); ?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Row -->

            <!-- Bar Chart -->
            <div class="chartjs-custom chartjs-height">
                <canvas style="height: 300px;" id="updatingLineChart" data-hs-chartjs-options='{
                      "type": "line",
                      "data": {
                         "labels": <?php echo json_encode($statistics['schedule']->keys(), 15, 512) ?>,
                         "datasets": [{
                          "backgroundColor": ["rgba(55,125,255, .5)", "rgba(255, 255, 255, .2)"],
                          "borderColor": "#377dff",
                          "borderWidth": 2,
                          "pointRadius": 0,
                          "hoverBorderColor": "#377dff",
                          "pointBackgroundColor": "#377dff",
                          "pointBorderColor": "#fff",
                          "pointHoverRadius": 0,
                          "tension": 0.4
                        },
                        {
                          "backgroundColor": ["rgba(0, 201, 219, .5)", "rgba(255, 255, 255, .2)"],
                          "borderColor": "#00c9db",
                          "borderWidth": 2,
                          "pointRadius": 0,
                          "hoverBorderColor": "#00c9db",
                          "pointBackgroundColor": "#00c9db",
                          "pointBorderColor": "#fff",
                          "pointHoverRadius": 0,
                          "tension": 0.4
                        }]
                      },
                      "options": {
                        "gradientPosition": {"y1": 200},
                         "scales": {
                            "y": {
                              "grid": {
                                "color": "#e7eaf3",
                                "drawBorder": false,
                                "zeroLineColor": "#e7eaf3"
                              },
                              "ticks": {
                                "min": 0,
                                "max": 100,
                                "stepSize": 100,
                                "fontColor": "#97a4af",
                                "fontFamily": "Open Sans, sans-serif",
                                "padding": 10,
                                "postfix": "k"
                              }
                            },
                            "x": {
                              "grid": {
                                "display": false,
                                "drawBorder": false
                              },
                              "ticks": {
                                "fontSize": 12,
                                "fontColor": "#97a4af",
                                "fontFamily": "Open Sans, sans-serif",
                                "padding": 5
                              }
                            }
                        },
                        "plugins": {
                          "tooltip": {
                            "prefix": "<?php echo e(basicControl()->currency_symbol); ?>",
                            "hasIndicator": true,
                            "mode": "index",
                            "intersect": false,
                            "lineMode": true,
                            "lineWithLineColor": "rgba(19, 33, 68, 0.075)"
                          }
                        },
                        "hover": {
                          "mode": "nearest",
                          "intersect": true
                        }
                      }
                    }'>
                </canvas>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('script'); ?>
    <script>
        var $monthlyDeposit = 0;
        var $totalDeposit = {};


        Notiflix.Block.standard('#RecentTransactionGraph');

        function monthlyDepositWithdraw(keyDataset = 0) {
            $.ajax({
                url: "<?php echo e(route('admin.monthly.deposit.withdraw')); ?>",
                data: {
                    keyDataset
                },
                dataType: 'json',
                type: "GET",
                success: function (data) {
                    $totalDeposit[keyDataset] = data.dailyDeposit;

                    $monthlyDeposit = (data.totalDeposit)

                    $(".deposit_amount").text($monthlyDeposit);

                    updateChart(keyDataset);


                    Notiflix.Block.remove('#RecentTransactionGraph');
                },
                async: false // Note that synchronous AJAX is deprecated and not recommended
            });
        }

        const updatingChartDatasets = [
            [$totalDeposit[0]],
            [$totalDeposit[1]]
        ];
        HSCore.components.HSChartJS.init(document.querySelector('#updatingLineChart'), {
            data: {
                datasets: [
                    {
                        data: updatingChartDatasets[0][0]
                    },
                    {
                        data: updatingChartDatasets[0][1]
                    }
                ]
            }
        });

        const updatingLineChart = HSCore.components.HSChartJS.getItem('updatingLineChart');



        // Function to update the chart
        function updateChart(keyDataset) {
            updatingChartDatasets[keyDataset] = [$totalDeposit[keyDataset]];

            updatingLineChart.data.datasets.forEach(function (dataset, key) {
                dataset.data = updatingChartDatasets[keyDataset][key];
            });
            updatingLineChart.update();
        }

        // Call when tab is clicked
        document.querySelectorAll('[data-bs-toggle="chart"]').forEach(item => {
            item.addEventListener('click', e => {
                let keyDataset = e.currentTarget.getAttribute('data-datasets');
                monthlyDepositWithdraw(keyDataset);
            });
        });

        // Initial rendering of the chart with the first dataset
        monthlyDepositWithdraw(0);


    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/admin/partials/dashboard/recentTran.blade.php ENDPATH**/ ?>