<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchant_payout_configurations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('merchant_id');
            $table->unsignedBigInteger('payout_method_id');
            $table->string('currency', 10)->comment('Currency code (NGN, USD, etc.)');
            $table->decimal('min_limit', 28, 8)->nullable()->comment('Custom minimum limit, NULL uses default');
            $table->decimal('max_limit', 28, 8)->nullable()->comment('Custom maximum limit, NULL uses default');
            $table->decimal('percentage_charge', 5, 2)->nullable()->comment('Custom percentage charge, NULL uses default');
            $table->decimal('fixed_charge', 28, 8)->nullable()->comment('Custom fixed charge, NULL uses default');
            $table->boolean('is_active')->default(1)->comment('1 = active, 0 = inactive');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('merchant_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('payout_method_id')->references('id')->on('payout_methods')->onDelete('cascade');

            // Unique constraint to prevent duplicate configurations per merchant/method/currency
            $table->unique(['merchant_id', 'payout_method_id', 'currency'], 'unique_merchant_payout_method_currency');

            // Indexes for performance
            $table->index('merchant_id');
            $table->index('payout_method_id');
            $table->index('currency');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchant_payout_configurations');
    }
};
