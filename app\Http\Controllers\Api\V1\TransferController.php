<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\PayoutMethod;
use App\Models\Payout;
use App\Models\Transaction;
use App\Models\Wallet;
use App\Models\MerchantPayoutConfiguration;
use App\Services\Payout\numero\Card as NumeroCard;
use App\Traits\ApiValidation;
use App\Traits\PayoutTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class TransferController extends Controller
{
    use ApiValidation, PayoutTrait;

    /**
     * Validate account number with bank using Numero provider
     */
    public function validateAccount(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'accountNumber' => 'required|string|min:10|max:10',
                'bankCode' => 'required|string|min:6|max:6'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            // Get Numero payout method
            $method = PayoutMethod::where('code', 'numero')->where('is_active', 1)->first();
            if (!isset($method)) {
                return response()->json($this->withErrors('Numero payout method not available'));
            }

            // Call Numero validate account
            $result = NumeroCard::validateAccount($request->accountNumber, $request->bankCode, $method);

            if ($result['status'] === 'success') {
                return response()->json([
                    'status' => 'success',
                    'message' => 'Account validation successful',
                    'data' => $result['data']
                ]);
            } else {
                return response()->json($this->withErrors($result['data']));
            }
        } catch (\Exception $e) {
            Log::error('Account validation failed: ' . $e->getMessage());
            return response()->json($this->withErrors('Account validation failed'));
        }
    }

    /**
     * Process single transfer
     */
    public function singleTransfer(Request $request)
    {
        DB::beginTransaction();
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'narration' => 'required|string|max:255',
                'destinationAccountNumber' => 'required|string|min:10|max:10',
                'destinationBankCode' => 'required|string|min:6|max:6',
                'destinationAccountName' => 'required|string|max:255',
                'currency' => 'optional|string|exists:currencies,code'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $cur = isset($request->currency) ? $request->currency : 'NGN';

            // Get currency
            $currency = Currency::where('code', $cur)->where('is_active', 1)->first();
            if (!isset($currency)) {
                return response()->json($this->withErrors('Invalid currency selected'));
            }

            // Get Numero payout method
            $method = PayoutMethod::where('code', 'numero')->where('is_active', 1)->first();
            if (!isset($method)) {
                return response()->json($this->withErrors('Numero payout method not available'));
            }

            // Get or create wallet
            $wallet = Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $currency->id]);

            // Validate amount and calculate charges
            $validateData = $this->validationCheck($request->amount, $cur, $method->id, $user->id);
            if (!isset($validateData['status'])) {
                return response()->json($this->withErrors($validateData['message']));
            }

            $amount = $validateData['amount'];
            $charge = $validateData['charge'];
            $netAmount = $validateData['net_payout_amount'];

            // Check wallet balance
            if ($wallet->balance < $netAmount) {
                return response()->json($this->withErrors('Insufficient wallet balance'));
            }

            // Create payout record
            $payout = new Payout();
            $payout->user_id = $user->id;
            $payout->currency_id = $currency->id;
            $payout->payout_method_id = $method->id;
            $payout->payout_currency_code = $cur;
            $payout->amount = $amount;
            $payout->charge = $charge;
            $payout->net_amount = $netAmount;
            $payout->amount_in_base_currency = $amount;
            $payout->charge_in_base_currency = $charge;
            $payout->net_amount_in_base_currency = $netAmount;
            $payout->trx_id = strRandom();
            $payout->information = [
                'destinationAccountNumber' => $request->destinationAccountNumber,
                'destinationBankCode' => $request->destinationBankCode,
                'destinationAccountName' => $request->destinationAccountName,
                'narration' => $request->narration
            ];
            $payout->status = 0; // Pending
            $payout->save();

            // Debit wallet
            updateWallet($user->id, $currency->id, $netAmount, 0);

            // Create transaction record
            $this->createPayoutTransaction($payout, '-', 'Transfer request - amount debited');

            // Process transfer through Numero
            $transferData = [
                'amount' => $amount,
                'narration' => $request->narration,
                'destinationAccountNumber' => $request->destinationAccountNumber,
                'destinationBankCode' => $request->destinationBankCode,
                'destinationAccountName' => $request->destinationAccountName,
                'reference' => $payout->trx_id
            ];

            $result = NumeroCard::payouts($payout);

            if ($result['status'] === 'success') {
                $payout->status = 1; // Success
                $payout->response_id = $result['response_id'] ?? null;
                $payout->save();

                DB::commit();
                return response()->json([
                    'status' => 'success',
                    'message' => 'Transfer processed successfully',
                    'data' => [
                        'transaction_id' => $payout->trx_id,
                        'amount' => $amount,
                        'charge' => $charge,
                        'net_amount' => $netAmount,
                        'reference' => $result['response_id'] ?? null
                    ]
                ]);
            } else {
                $payout->status = 6; // Failed
                $payout->last_error = $result['data'];
                $payout->save();

                // Refund wallet
                updateWallet($user->id, $currency->id, $netAmount, 1);

                // Create refund transaction
                $this->createPayoutTransaction($payout, '+', 'Transfer failed - amount refunded');

                DB::commit();
                return response()->json($this->withErrors('Transfer failed: ' . $result['data']));
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Single transfer failed: ' . $e->getMessage());
            return response()->json($this->withErrors('Transfer failed'));
        }
    }

    /**
     * Process bulk transfers
     */
    public function bulkTransfer(Request $request)
    {
        DB::beginTransaction();
        try {
            $user = Auth::user();

            $validator = Validator::make($request->all(), [
                'transfers' => 'required|array|min:1|max:100',
                'transfers.*.amount' => 'required|numeric|min:1',
                'transfers.*.narration' => 'required|string|max:255',
                'transfers.*.destinationAccountNumber' => 'required|string|min:10|max:10',
                'transfers.*.destinationBankCode' => 'required|string|min:6|max:6',
                'transfers.*.destinationAccountName' => 'required|string|max:255',
                'currency' => 'optional|string|exists:currencies,code'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $cur = isset($request->currency) ? $request->currency : 'NGN';

            // Get currency
            $currency = Currency::where('code', $cur)->where('is_active', 1)->first();
            if (!isset($currency)) {
                return response()->json($this->withErrors('Invalid currency selected'));
            }

            // Get Numero payout method
            $method = PayoutMethod::where('code', 'numero')->where('is_active', 1)->first();
            if (!isset($method)) {
                return response()->json($this->withErrors('Numero payout method not available'));
            }

            // Get or create wallet
            $wallet = Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $currency->id]);

            // Calculate total amount including charges for all transfers
            $totalAmount = 0;
            $totalCharge = 0;
            $totalNetAmount = 0;
            $transfersWithCharges = [];

            foreach ($request->transfers as $index => $transfer) {
                $validateData = $this->validationCheck($transfer['amount'], $cur, $method->id, $user->id);
                if (!isset($validateData['status'])) {
                    return response()->json($this->withErrors("Transfer #{$index}: " . $validateData['message']));
                }

                $transfersWithCharges[] = [
                    'original' => $transfer,
                    'amount' => $validateData['amount'],
                    'charge' => $validateData['charge'],
                    'net_amount' => $validateData['net_payout_amount']
                ];

                $totalAmount += $validateData['amount'];
                $totalCharge += $validateData['charge'];
                $totalNetAmount += $validateData['net_payout_amount'];
            }

            // Check wallet balance against total
            if ($wallet->balance < $totalNetAmount) {
                return response()->json($this->withErrors('Insufficient wallet balance for bulk transfer'));
            }

            // Generate unique batch identifier
            $batchId = 'BULK_' . strRandom(10);

            // Create payout records and process transfers
            $payouts = [];
            $numeroTransfers = [];
            $successfulTransfers = [];

            foreach ($transfersWithCharges as $index => $transferData) {
                // Create payout record
                $payout = new Payout();
                $payout->user_id = $user->id;
                $payout->currency_id = $currency->id;
                $payout->payout_method_id = $method->id;
                $payout->payout_currency_code = $cur;
                $payout->amount = $transferData['amount'];
                $payout->charge = $transferData['charge'];
                $payout->net_amount = $transferData['net_amount'];
                $payout->amount_in_base_currency = $transferData['amount'];
                $payout->charge_in_base_currency = $transferData['charge'];
                $payout->net_amount_in_base_currency = $transferData['net_amount'];
                $payout->trx_id = strRandom();
                $payout->information = [
                    'destinationAccountNumber' => $transferData['original']['destinationAccountNumber'],
                    'destinationBankCode' => $transferData['original']['destinationBankCode'],
                    'destinationAccountName' => $transferData['original']['destinationAccountName'],
                    'narration' => $transferData['original']['narration'],
                    'batch_id' => $batchId,
                    'is_bulk' => true
                ];
                $payout->status = 0; // Pending
                $payout->save();

                $payouts[] = $payout;

                // Prepare for Numero bulk transfer
                $numeroTransfers[] = [
                    'amount' => $transferData['amount'],
                    'narration' => $transferData['original']['narration'],
                    'destinationAccountNumber' => $transferData['original']['destinationAccountNumber'],
                    'destinationBankCode' => $transferData['original']['destinationBankCode'],
                    'destinationAccountName' => $transferData['original']['destinationAccountName'],
                    'reference' => $payout->trx_id
                ];
            }

            // Debit wallet for total amount
            updateWallet($user->id, $currency->id, $totalNetAmount, 0);

            // Create transaction records for each transfer
            foreach ($payouts as $payout) {
                $this->createPayoutTransaction($payout, '-', 'Bulk transfer request - amount debited (Batch: ' . $batchId . ')');
            }

            // Process bulk transfer through Numero
            $result = NumeroCard::bulkTransfer($numeroTransfers, $user->phone ?? '', $method);

            if ($result['status'] === 'success') {
                // Update all payouts as successful
                foreach ($payouts as $payout) {
                    $payout->status = 1; // Success
                    $payout->response_id = $result['data']['transferReference'] ?? null;
                    $payout->save();

                    $successfulTransfers[] = [
                        'transaction_id' => $payout->trx_id,
                        'amount' => $payout->amount,
                        'charge' => $payout->charge,
                        'net_amount' => $payout->net_amount,
                        'destination_account' => $payout->information->destinationAccountNumber
                    ];
                }

                DB::commit();
                return response()->json([
                    'status' => 'success',
                    'message' => 'Bulk transfer processed successfully',
                    'data' => [
                        'batch_id' => $batchId,
                        'total_transfers' => count($payouts),
                        'total_amount' => $totalAmount,
                        'total_charge' => $totalCharge,
                        'total_net_amount' => $totalNetAmount,
                        'batch_reference' => $result['data']['batchReference'] ?? null,
                        'transfers' => $successfulTransfers
                    ]
                ]);
            } else {
                // Update all payouts as failed and refund
                foreach ($payouts as $payout) {
                    $payout->status = 6; // Failed
                    $payout->last_error = $result['data'];
                    $payout->save();

                    // Create refund transaction
                    $this->createPayoutTransaction($payout, '+', 'Bulk transfer failed - amount refunded (Batch: ' . $batchId . ')');
                }

                // Refund wallet
                updateWallet($user->id, $currency->id, $totalNetAmount, 1);

                DB::commit();
                return response()->json($this->withErrors('Bulk transfer failed: ' . $result['data']));
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Bulk transfer failed: ' . $e->getMessage());
            return response()->json($this->withErrors('Bulk transfer failed'));
        }
    }

    public function checkTransactionStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'reference' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $reference = $request->reference;

            // First, check if this is our internal transaction reference
            $payout = Payout::where('trx_id', $reference)
                ->orWhere('response_id', $reference)
                ->first();

            if ($payout) {
                // This is our internal transaction, return our status
                return response()->json($this->withSuccess([
                    'reference' => $reference,
                    'status' => match ($payout->status) {
                        0 => 'pending',
                        1 => 'processing',
                        2 => 'success',
                        3 => 'cancelled',
                        6 => 'failed',
                        default => 'unknown'
                    },
                    'amount' => getAmount($payout->amount),
                    'currency' => $payout->payout_currency_code,
                    'created_at' => $payout->created_at,
                    'updated_at' => $payout->updated_at,
                ]));
            }

            // If not found in our records, assume it's a Numero reference and check with Numero
            $numeroMethod = PayoutMethod::where('code', 'numero')->first();
            if (!$numeroMethod) {
                return response()->json($this->withErrors('payout method not configured'));
            }

            // Use Numero's checkTransferStatus method
            $methodClass = 'App\\Services\\Payout\\numero\\Card';
            if (!method_exists($methodClass, 'checkTransferStatus')) {
                return response()->json($this->withErrors('Status check not supported for this provider'));
            }

            $statusResponse = $methodClass::checkTransferStatus($reference, $numeroMethod);

            if ($statusResponse['status'] === 'success') {
                return response()->json($this->withSuccess([
                    'reference' => $reference,
                    'status' => $statusResponse['data']['transactionRecord']['status'], // 'pending', 'processing', 'success', 'cancelled', 'failed'
                    'amount' => getAmount($statusResponse['data']['amount']),
                    'currency' => $statusResponse['data']['currency'],
                    'created_at' => $statusResponse['data']['createdAt'],
                    'updated_at' => $statusResponse['data']['updatedAt'],
                ]));
            } else {
                return response()->json($this->withErrors($statusResponse['data'] ?? 'Status check failed'));
            }
        } catch (\Exception $e) {
             Log::error('Status check failed: ' . $e->getMessage());
            return response()->json($this->withErrors('Status check failed'));
        }
    }
}
