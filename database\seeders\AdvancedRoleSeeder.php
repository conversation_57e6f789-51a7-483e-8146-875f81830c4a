<?php

namespace Database\Seeders;

use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Services\PermissionTemplateService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * Advanced Role System Seeder
 * 
 * Seeds the database with comprehensive roles and their permission assignments.
 */
class AdvancedRoleSeeder extends Seeder
{
    /**
     * Permission template service
     */
    protected PermissionTemplateService $templateService;

    public function __construct()
    {
        $this->templateService = new PermissionTemplateService();
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Advanced Role System Seeding...');

        DB::beginTransaction();
        try {
            // 1. Create template roles
            $this->createTemplateRoles();

            // 2. Create custom system roles
            $this->createSystemRoles();

            // 3. Create department-specific roles
            $this->createDepartmentRoles();

            // 4. Create project-specific roles
            $this->createProjectRoles();

            DB::commit();

            $totalRoles = AdvancedRole::count();
            $this->command->info("✅ Successfully seeded {$totalRoles} roles!");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error("❌ Seeding failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create roles from templates
     */
    protected function createTemplateRoles(): void
    {
        $this->command->info('📋 Creating roles from templates...');

        $templates = [
            'super_admin' => ['color' => '#dc3545'],
            'admin' => ['color' => '#fd7e14'],
            'finance_manager' => ['color' => '#198754'],
            'finance_clerk' => ['color' => '#20c997'],
            'user_manager' => ['color' => '#0d6efd'],
            'support_agent' => ['color' => '#6f42c1'],
            'compliance_officer' => ['color' => '#d63384'],
            'merchant_manager' => ['color' => '#ffc107'],
            'readonly_user' => ['color' => '#6c757d'],
            'basic_user' => ['color' => '#0dcaf0'],
        ];

        foreach ($templates as $templateName => $options) {
            try {
                $role = $this->templateService->createRoleFromTemplate($templateName, $options);
                $this->command->info("✓ Created role: {$role->display_name}");
            } catch (\Exception $e) {
                $this->command->warn("⚠ Failed to create template role {$templateName}: " . $e->getMessage());
            }
        }
    }

    /**
     * Create custom system roles
     */
    protected function createSystemRoles(): void
    {
        $this->command->info('🔧 Creating custom system roles...');

        $systemRoles = [
            [
                'name' => 'system_administrator',
                'display_name' => 'System Administrator',
                'description' => 'Full system administration with all technical permissions',
                'category' => 'system',
                'color' => '#e74c3c',
                'is_system' => true,
                'permissions' => [
                    'system.*',
                    'advanced_roles.*',
                    'advanced_permissions.*',
                    'user_roles.*',
                    'cache.*',
                    'maintenance.*',
                    'audit.*',
                ]
            ],
            [
                'name' => 'security_officer',
                'display_name' => 'Security Officer',
                'description' => 'Security monitoring and incident response',
                'category' => 'security',
                'color' => '#8e44ad',
                'permissions' => [
                    'audit.read',
                    'audit.export',
                    'users.read',
                    'users.suspend',
                    'merchants.read',
                    'merchants.deactivate',
                    'compliance.*',
                    'kyc.*',
                    'system.logs',
                ]
            ],
            [
                'name' => 'api_manager',
                'display_name' => 'API Manager',
                'description' => 'API and integration management',
                'category' => 'technical',
                'color' => '#34495e',
                'permissions' => [
                    'api.*',
                    'webhooks.*',
                    'integrations.*',
                    'merchants.read',
                    'transactions.read',
                    'system.logs',
                ]
            ],
            [
                'name' => 'data_analyst',
                'display_name' => 'Data Analyst',
                'description' => 'Data analysis and reporting specialist',
                'category' => 'analytics',
                'color' => '#3498db',
                'permissions' => [
                    'reports.*',
                    'analytics.*',
                    'dashboard.analytics',
                    'transactions.read',
                    'users.read',
                    'merchants.read',
                    'forex.rates.read',
                ]
            ],
        ];

        foreach ($systemRoles as $roleData) {
            $permissions = $roleData['permissions'];
            unset($roleData['permissions']);

            $role = AdvancedRole::create($roleData);
            $this->assignPermissionsToRole($role, $permissions);

            $this->command->info("✓ Created system role: {$role->display_name}");
        }
    }

    /**
     * Create department-specific roles
     */
    protected function createDepartmentRoles(): void
    {
        $this->command->info('🏢 Creating department-specific roles...');

        // Finance Department Hierarchy
        $financeManager = AdvancedRole::create([
            'name' => 'finance_department_head',
            'display_name' => 'Finance Department Head',
            'description' => 'Head of finance department with full financial authority',
            'category' => 'finance',
            'scope' => 'department',
            'color' => '#27ae60',
            'max_users' => 2,
        ]);

        $this->assignPermissionsToRole($financeManager, [
            'finance.*',
            'forex.*',
            'transactions.*',
            'payouts.*',
            'deposits.*',
            'virtual_accounts.*',
            'payout_methods.*',
            'reports.read',
            'reports.export',
            'users.read',
            'merchants.read',
        ]);

        $financeAnalyst = AdvancedRole::create([
            'name' => 'finance_analyst',
            'display_name' => 'Finance Analyst',
            'description' => 'Financial analysis and reporting specialist',
            'category' => 'finance',
            'scope' => 'department',
            'parent_role_id' => $financeManager->id,
            'color' => '#2ecc71',
        ]);

        $this->assignPermissionsToRole($financeAnalyst, [
            'transactions.read',
            'forex.rates.read',
            'forex.bookings.read',
            'reports.*',
            'analytics.*',
        ]);

        // Customer Support Hierarchy
        $supportManager = AdvancedRole::create([
            'name' => 'support_manager',
            'display_name' => 'Support Manager',
            'description' => 'Customer support team manager',
            'category' => 'support',
            'scope' => 'department',
            'color' => '#9b59b6',
            'max_users' => 3,
        ]);

        $this->assignPermissionsToRole($supportManager, [
            'tickets.*',
            'disputes.*',
            'users.*',
            'merchants.read',
            'merchants.update',
            'transactions.read',
            'notifications.*',
            'templates.*',
        ]);

        $supportAgent = AdvancedRole::create([
            'name' => 'support_specialist',
            'display_name' => 'Support Specialist',
            'description' => 'Specialized customer support agent',
            'category' => 'support',
            'scope' => 'department',
            'parent_role_id' => $supportManager->id,
            'color' => '#a569bd',
        ]);

        $this->assignPermissionsToRole($supportAgent, [
            'tickets.*',
            'disputes.read',
            'disputes.update',
            'users.read',
            'users.update',
            'merchants.read',
            'transactions.read',
            'notifications.create',
        ]);

        $this->command->info("✓ Created department roles with hierarchy");
    }

    /**
     * Create project-specific roles
     */
    protected function createProjectRoles(): void
    {
        $this->command->info('📊 Creating project-specific roles...');

        $projectRoles = [
            [
                'name' => 'forex_project_lead',
                'display_name' => 'Forex Project Lead',
                'description' => 'Lead for forex trading project implementation',
                'category' => 'project',
                'scope' => 'project',
                'color' => '#f39c12',
                'max_users' => 1,
                'expires_at' => now()->addMonths(6),
                'permissions' => [
                    'forex.*',
                    'currencies.*',
                    'exchange_rates.*',
                    'reports.read',
                    'users.read',
                    'merchants.read',
                ]
            ],
            [
                'name' => 'api_integration_specialist',
                'display_name' => 'API Integration Specialist',
                'description' => 'Specialist for API and webhook integrations',
                'category' => 'project',
                'scope' => 'project',
                'color' => '#16a085',
                'permissions' => [
                    'api.*',
                    'webhooks.*',
                    'integrations.*',
                    'merchants.read',
                    'transactions.read',
                    'system.logs',
                ]
            ],
            [
                'name' => 'compliance_auditor',
                'display_name' => 'Compliance Auditor',
                'description' => 'External compliance auditor with limited access',
                'category' => 'project',
                'scope' => 'project',
                'color' => '#e67e22',
                'expires_at' => now()->addMonths(3),
                'permissions' => [
                    'audit.read',
                    'compliance.reports',
                    'kyc.read',
                    'users.read',
                    'merchants.read',
                    'transactions.read',
                ]
            ],
        ];

        foreach ($projectRoles as $roleData) {
            $permissions = $roleData['permissions'];
            unset($roleData['permissions']);

            $role = AdvancedRole::create($roleData);
            $this->assignPermissionsToRole($role, $permissions);

            $this->command->info("✓ Created project role: {$role->display_name}");
        }
    }

    /**
     * Assign permissions to role using patterns
     */
    protected function assignPermissionsToRole(AdvancedRole $role, array $permissionPatterns): void
    {
        foreach ($permissionPatterns as $pattern) {
            if ($pattern === '*') {
                // Assign all permissions
                $permissions = AdvancedPermission::active()->get();
                foreach ($permissions as $permission) {
                    $role->grantPermission($permission);
                }
            } elseif (str_ends_with($pattern, '.*')) {
                // Assign all permissions for a resource
                $resource = str_replace('.*', '', $pattern);
                $permissions = AdvancedPermission::forResource($resource);
                foreach ($permissions as $permission) {
                    $role->grantPermission($permission);
                }
            } elseif (str_ends_with($pattern, '.read')) {
                // Assign read permissions for all resources
                $permissions = AdvancedPermission::where('action', 'read')->active()->get();
                foreach ($permissions as $permission) {
                    $role->grantPermission($permission);
                }
            } else {
                // Assign specific permission
                $permission = AdvancedPermission::findByName($pattern);
                if ($permission) {
                    $role->grantPermission($permission);
                }
            }
        }
    }
}
