<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class ForexAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_name',
        'account_type',
        'currency_code',
        'balance',
        'pending_balance',
        'description',
        'is_active'
    ];

    protected $casts = [
        'balance' => 'decimal:8',
        'pending_balance' => 'decimal:8',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('forex_account_balances');
        });
    }

    // Relationships
    public function transactions()
    {
        return $this->hasMany(ForexTransaction::class, 'forex_account_id');
    }

    public function bookings()
    {
        return $this->hasMany(ForexBooking::class, 'target_account_id');
    }

    public function transfersFrom()
    {
        return $this->hasMany(ForexAccountTransfer::class, 'from_account_id');
    }

    public function transfersTo()
    {
        return $this->hasMany(ForexAccountTransfer::class, 'to_account_id');
    }

    public function reservations()
    {
        return $this->hasMany(ForexBookingReservation::class, 'forex_account_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    public function scopeByCurrency($query, $currency)
    {
        return $query->where('currency_code', $currency);
    }

    // Relationships
    public function forexTransactions()
    {
        return $this->hasMany(ForexTransaction::class);
    }

    // Accessors
    public function getAvailableBalanceAttribute()
    {
        // Available balance is balance minus pending balance
        return $this->balance - $this->getPendingBalanceAttribute();
    }

    public function getPendingBalanceAttribute()
    {
        // Calculate pending balance from uncompleted booked transactions
        return $this->forexTransactions()
            ->where('transaction_subtype', 'booked')
            ->where('is_completed', false)
            ->where('transaction_type', 'debit')
            ->sum('amount');
    }

    public function getTotalBalanceAttribute()
    {
        // Total balance is balance + pending (what was originally available)
        return $this->balance + $this->pending_balance;
    }

    public function getFormattedBalanceAttribute()
    {
        return number_format($this->balance, 2) . ' ' . $this->currency_code;
    }

    public function getFormattedAvailableBalanceAttribute()
    {
        return number_format($this->available_balance, 2) . ' ' . $this->currency_code;
    }

    public function getFormattedTotalBalanceAttribute()
    {
        return number_format($this->total_balance, 2) . ' ' . $this->currency_code;
    }

    public function getTypeClassAttribute()
    {
        return [
            'USD' => 'primary',
            'CBN' => 'success',
            'Difference' => 'warning',
            'Investment' => 'info',
        ][$this->account_type] ?? 'secondary';
    }

    // Business Logic Methods
    public static function getAccountBalances()
    {
        return Cache::remember('forex_account_balances', 300, function () {
            return static::active()->get()->mapWithKeys(function ($account) {
                return [$account->account_type => $account];
            });
        });
    }

    public function credit($amount, $description, $adminId, $bookingId = null, $metadata = null)
    {
        $balanceBefore = $this->balance;
        $this->increment('balance', $amount);

        return $this->transactions()->create([
            'transaction_reference' => $this->generateTransactionReference(),
            'forex_booking_id' => $bookingId,
            'transaction_type' => 'credit',
            'currency' => $this->currency_code,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'created_by' => $adminId,
            'metadata' => $metadata,
        ]);
    }

    public function debit($amount, $description, $adminId, $bookingId = null, $metadata = null)
    {
        if ($this->available_balance < $amount) {
            throw new \Exception('Insufficient balance');
        }

        $balanceBefore = $this->balance;
        $this->decrement('balance', $amount);

        return $this->transactions()->create([
            'transaction_reference' => $this->generateTransactionReference(),
            'forex_booking_id' => $bookingId,
            'transaction_type' => 'debit',
            'currency' => $this->currency_code,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'created_by' => $adminId,
            'metadata' => $metadata,
        ]);
    }

    public function addPendingBalance($amount)
    {
        $this->increment('pending_balance', $amount);
    }

    public function removePendingBalance($amount)
    {
        $this->decrement('pending_balance', $amount);
    }

    public function releasePendingBalance($amount, $description, $adminId, $bookingId = null, $metadata = null)
    {
        if ($this->pending_balance < $amount) {
            throw new \Exception('Insufficient pending balance to release. Pending: ' . number_format($this->pending_balance, 2) . ', Requested: ' . number_format($amount, 2) . ' ' . $this->currency_code);
        }

        // Move amount from pending_balance back to balance
        $balanceBefore = $this->balance;
        $pendingBefore = $this->pending_balance;

        $this->increment('balance', $amount);
        $this->decrement('pending_balance', $amount);

        // Create a transaction record for the release
        return $this->transactions()->create([
            'transaction_reference' => $this->generateTransactionReference(),
            'forex_booking_id' => $bookingId,
            'transaction_type' => 'pending_cancelled',
            'currency' => $this->currency_code,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'created_by' => $adminId,
            'metadata' => array_merge($metadata ?? [], [
                'pending_balance_before' => $pendingBefore,
                'pending_balance_after' => $this->fresh()->pending_balance,
                'total_balance_before' => $balanceBefore + $pendingBefore,
                'total_balance_after' => $this->fresh()->balance + $this->fresh()->pending_balance,
                'action' => 'pending_balance_released'
            ]),
        ]);
    }

    public function clearPendingBalance($amount, $description, $adminId, $bookingId = null, $metadata = null)
    {
        if ($this->pending_balance < $amount) {
            throw new \Exception('Insufficient pending balance to clear. Pending: ' . number_format($this->pending_balance, 2) . ', Requested: ' . number_format($amount, 2) . ' ' . $this->currency_code);
        }

        // Simply remove from pending_balance (money is considered paid out)
        $balanceBefore = $this->balance;
        $pendingBefore = $this->pending_balance;

        $this->decrement('pending_balance', $amount);

        // Create a transaction record for the clearance
        return $this->transactions()->create([
            'transaction_reference' => $this->generateTransactionReference(),
            'forex_booking_id' => $bookingId,
            'transaction_type' => 'pending_cleared',
            'currency' => $this->currency_code,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->balance, // Balance stays the same
            'description' => $description,
            'created_by' => $adminId,
            'metadata' => array_merge($metadata ?? [], [
                'pending_balance_before' => $pendingBefore,
                'pending_balance_after' => $this->fresh()->pending_balance,
                'total_balance_before' => $balanceBefore + $pendingBefore,
                'total_balance_after' => $this->fresh()->balance + $this->fresh()->pending_balance,
                'action' => 'pending_balance_cleared_payment_made'
            ]),
        ]);
    }

    public function hasAvailableBalance($amount)
    {
        return $this->available_balance >= $amount;
    }

    public function reserveBalance($amount, $description, $adminId, $bookingId = null, $metadata = null)
    {
        if (!$this->hasAvailableBalance($amount)) {
            throw new \Exception('Insufficient available balance. Available: ' . $this->formatted_available_balance . ', Required: ' . number_format($amount, 2) . ' ' . $this->currency_code);
        }

        // Move amount from balance to pending_balance
        $balanceBefore = $this->balance;
        $pendingBefore = $this->pending_balance;

        $this->decrement('balance', $amount);
        $this->increment('pending_balance', $amount);

        // Create a transaction record for the reservation
        return $this->transactions()->create([
            'transaction_reference' => $this->generateTransactionReference(),
            'forex_booking_id' => $bookingId,
            'transaction_type' => 'pending',
            'currency' => $this->currency_code,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'description' => $description,
            'created_by' => $adminId,
            'metadata' => array_merge($metadata ?? [], [
                'pending_balance_before' => $pendingBefore,
                'pending_balance_after' => $this->fresh()->pending_balance,
                'total_balance_before' => $balanceBefore + $pendingBefore,
                'total_balance_after' => $this->fresh()->balance + $this->fresh()->pending_balance
            ]),
        ]);
    }

    public function generateTransactionReference()
    {
        return 'FXT' . strtoupper(substr($this->account_type, 0, 3)) . time() . rand(100, 999);
    }
}
