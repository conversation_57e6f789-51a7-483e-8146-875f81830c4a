# Advanced Permission System - Database Schema Documentation

## Overview

The Advanced Permission System introduces a sophisticated, granular permission management system that operates alongside the existing basic role system. This document details the database schema and relationships.

## Core Tables

### 1. advanced_permissions

Stores granular permissions with CRUD-level control.

**Purpose**: Define specific actions users can perform on resources.

**Key Fields**:
- `name`: Unique permission identifier (e.g., "users.create", "transactions.read")
- `display_name`: Human-readable name for UI
- `resource`: Resource/module name (e.g., "users", "transactions")
- `action`: Action type (create, read, update, delete, custom)
- `category`: Grouping category (admin, finance, user, etc.)
- `is_system`: System permissions that cannot be deleted
- `metadata`: JSON field for additional constraints

**Indexes**: Optimized for resource/action lookups and category filtering.

### 2. advanced_roles

Dynamic role definitions with hierarchy support.

**Purpose**: Define roles that can inherit from other roles and contain multiple permissions.

**Key Fields**:
- `name`: Unique role identifier
- `parent_role_id`: For role hierarchy/inheritance
- `level`: Hierarchy depth level
- `hierarchy_path`: Full path for efficient queries
- `scope`: Role scope (global, department, project)
- `inherit_permissions`: Whether to inherit parent permissions
- `max_users`: Maximum users allowed for this role

**Hierarchy Example**:
```
Admin (level 0)
├── Finance Manager (level 1, parent: Admin)
│   └── Finance Clerk (level 2, parent: Finance Manager)
└── User Manager (level 1, parent: Admin)
```

### 3. advanced_role_permissions

Pivot table linking roles to permissions with constraints.

**Purpose**: Define which permissions each role has, with optional constraints.

**Key Fields**:
- `role_id`: Reference to advanced_roles
- `permission_id`: Reference to advanced_permissions
- `is_granted`: Grant (true) or explicitly deny (false)
- `constraints`: JSON field for conditions (time, IP, etc.)
- `valid_from/valid_until`: Temporal access control
- `priority`: For conflict resolution

**Constraint Examples**:
```json
{
  "ip_whitelist": ["***********/24"],
  "time_restriction": {
    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
    "hours": {"start": "09:00", "end": "17:00"}
  },
  "resource_limit": {"max_amount": 10000}
}
```

### 4. advanced_user_roles

User-role assignments with polymorphic support.

**Purpose**: Assign roles to users/admins with context and temporal control.

**Key Fields**:
- `user_id/user_type`: Polymorphic relationship (User or Admin)
- `role_id`: Reference to advanced_roles
- `context`: Assignment context (project, department)
- `assigned_at/expires_at`: Temporal assignment
- `priority`: Role priority for multiple roles

**Polymorphic Support**: Works with both `App\Models\User` and `App\Models\Admin`.

### 5. advanced_permission_audit

Comprehensive audit trail for security and compliance.

**Purpose**: Track all permission checks, role assignments, and access attempts.

**Key Fields**:
- `event_type`: Type of event (access_granted, access_denied, role_assigned)
- `action`: Specific permission checked
- `user_id/user_type`: Who performed the action
- `was_granted`: Whether access was granted
- `ip_address`: Request IP for security tracking
- `check_duration_ms`: Performance monitoring

## Enhanced User/Admin Tables

### users Table Additions
- `use_advanced_roles`: Enable advanced system for this user
- `advanced_role_cache`: Cached permissions for performance
- `permission_overrides`: User-specific permission overrides

### admins Table Additions
- `role_id`: Link to basic roles (existing system)
- `use_advanced_roles`: Enable advanced system for this admin
- `is_super_admin`: Bypass all permission checks
- `permission_overrides`: Admin-specific overrides

## Relationships

```
advanced_roles
├── hasMany advanced_role_permissions
├── hasMany advanced_user_roles
├── belongsTo parent_role (self-referencing)
└── hasMany child_roles (self-referencing)

advanced_permissions
├── hasMany advanced_role_permissions
└── belongsToMany advanced_roles (through advanced_role_permissions)

advanced_user_roles
├── belongsTo advanced_roles
└── morphTo user (User or Admin)

advanced_permission_audit
├── belongsTo advanced_roles (optional)
└── belongsTo advanced_permissions (optional)
```

## Performance Considerations

### Indexes
- All foreign keys are indexed
- Composite indexes for common query patterns
- Time-based indexes for audit queries

### Caching Strategy
- Role permissions cached in user/admin records
- Cache invalidation on role/permission changes
- Redis caching for frequently accessed permissions

### Query Optimization
- Hierarchy path for efficient tree queries
- Polymorphic indexes for user lookups
- Audit table partitioning by date

## Migration Order

1. `create_advanced_permissions_table`
2. `create_advanced_roles_table`
3. `create_advanced_role_permissions_table`
4. `create_advanced_user_roles_table`
5. `create_advanced_permission_audit_table`
6. `add_advanced_role_fields_to_users_and_admins`
7. `create_roles_table_if_missing`

## Backward Compatibility

The system maintains full backward compatibility:
- Existing basic roles continue to work
- Users can opt-in to advanced system via `use_advanced_roles` flag
- Both systems can coexist during transition period
- No breaking changes to existing functionality
