<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class BasicControl extends Model
{
    use HasFactory;

    protected $guarded = ['id'];
    protected $casts = [
        'sandbox_gateways' => 'array'
    ];

    protected function metaKeywords(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => explode(", ", $value),
        );
    }

    protected static function boot()
    {
        parent::boot();
        static::saved(function () {
            \Cache::forget('ConfigureSetting');
        });
    }


    public function currency()
    {
        return $this->belongsTo(Currency::class, 'base_currency','code');
    }

    public function footerLogo()
    {
        return getFile($this->footer_logo_driver, $this->footer_logo);
    }

    public function getLogo()
    {
        return getFile($this->logo_driver, $this->logo);
    }

}
