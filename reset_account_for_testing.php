<?php

require 'vendor/autoload.php';

use App\Models\ForexAccount;
use App\Models\ForexTransaction;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== RESETTING ACCOUNT FOR TESTING ===\n\n";

$usdAccount = ForexAccount::where('currency_code', 'USD')->first();

echo "Current Account State:\n";
echo "- Total Balance: $" . number_format($usdAccount->balance, 2) . "\n";
echo "- Pending Balance: $" . number_format($usdAccount->pending_balance, 2) . "\n";
echo "- Available Balance: $" . number_format($usdAccount->available_balance, 2) . "\n\n";

// Find uncompleted booked transactions
$uncompletedBookedTransactions = ForexTransaction::where('forex_account_id', $usdAccount->id)
    ->where('transaction_subtype', 'booked')
    ->where('is_completed', false)
    ->get();

echo "Found " . $uncompletedBookedTransactions->count() . " uncompleted booked transactions:\n";

foreach ($uncompletedBookedTransactions as $transaction) {
    echo "- Transaction ID: {$transaction->id}, Amount: $" . number_format($transaction->amount, 2) . 
         ", Booking: {$transaction->forex_booking_id}\n";
    
    // Mark as completed to clear pending balance
    $transaction->update(['is_completed' => true]);
    echo "  ✅ Marked as completed\n";
}

// Set account balance to a reasonable amount for testing
$usdAccount->update(['balance' => 1000]);

echo "\nAccount reset complete!\n";

$usdAccount->refresh();
echo "\nNew Account State:\n";
echo "- Total Balance: $" . number_format($usdAccount->balance, 2) . "\n";
echo "- Pending Balance: $" . number_format($usdAccount->pending_balance, 2) . "\n";
echo "- Available Balance: $" . number_format($usdAccount->available_balance, 2) . "\n";

echo "\n=== RESET COMPLETE ===\n";
?>
