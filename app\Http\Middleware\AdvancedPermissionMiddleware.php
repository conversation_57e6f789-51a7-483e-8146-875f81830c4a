<?php

namespace App\Http\Middleware;

use App\Models\AdvancedPermissionAudit;
use App\Services\AdvancedPermissionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

/**
 * Advanced Permission Middleware
 * 
 * Provides granular permission checking with CRUD-level control,
 * constraint validation, and comprehensive audit logging.
 */
class AdvancedPermissionMiddleware
{
    /**
     * Advanced permission service
     */
    protected AdvancedPermissionService $permissionService;

    public function __construct(AdvancedPermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$permissions): Response
    {
        $startTime = microtime(true);
        
        // Get authenticated user
        $user = Auth::user();
        if (!$user) {
            return $this->handleUnauthorized($request, 'User not authenticated');
        }

        // Check if user uses advanced role system
        if (!$this->userUsesAdvancedRoles($user)) {
            // Fall back to basic role system or allow access
            return $next($request);
        }

        // Parse and validate permissions
        $permissionChecks = $this->parsePermissions($permissions);
        $context = $this->buildPermissionContext($request);

        // Check each permission
        foreach ($permissionChecks as $permissionCheck) {
            $result = $this->checkPermission($user, $permissionCheck, $context);
            
            if (!$result['granted']) {
                $duration = (microtime(true) - $startTime) * 1000;
                
                // Log access denied
                AdvancedPermissionAudit::logPermissionCheck(
                    $permissionCheck['permission'],
                    false,
                    $user,
                    $result['role'],
                    $result['permission_model'],
                    $result['reason'],
                    $context,
                    (int) $duration
                );

                return $this->handleForbidden($request, $result['reason']);
            }
        }

        // Log successful access for the primary permission
        if (!empty($permissionChecks)) {
            $primaryCheck = $permissionChecks[0];
            $result = $this->checkPermission($user, $primaryCheck, $context);
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            AdvancedPermissionAudit::logPermissionCheck(
                $primaryCheck['permission'],
                true,
                $user,
                $result['role'],
                $result['permission_model'],
                'Access granted',
                $context,
                (int) $duration
            );
        }

        return $next($request);
    }

    /**
     * Check if user uses advanced role system
     */
    protected function userUsesAdvancedRoles($user): bool
    {
        return method_exists($user, 'usesAdvancedRoles') && $user->usesAdvancedRoles();
    }

    /**
     * Parse permission strings into structured checks
     */
    protected function parsePermissions(array $permissions): array
    {
        $checks = [];

        foreach ($permissions as $permission) {
            // Handle different permission formats
            if (str_contains($permission, '|')) {
                // OR logic: user needs ANY of these permissions
                $orPermissions = explode('|', $permission);
                $checks[] = [
                    'type' => 'or',
                    'permissions' => array_map('trim', $orPermissions),
                ];
            } elseif (str_contains($permission, '&')) {
                // AND logic: user needs ALL of these permissions
                $andPermissions = explode('&', $permission);
                $checks[] = [
                    'type' => 'and',
                    'permissions' => array_map('trim', $andPermissions),
                ];
            } else {
                // Single permission
                $checks[] = [
                    'type' => 'single',
                    'permission' => trim($permission),
                ];
            }
        }

        return $checks;
    }

    /**
     * Build permission context from request
     */
    protected function buildPermissionContext(Request $request): array
    {
        return [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'route_name' => $request->route()?->getName(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'request_data' => $this->sanitizeRequestData($request),
            'timestamp' => now(),
        ];
    }

    /**
     * Sanitize request data for logging
     */
    protected function sanitizeRequestData(Request $request): array
    {
        $data = $request->except(['password', 'password_confirmation', 'token', '_token']);
        
        // Limit data size for logging
        $json = json_encode($data);
        if (strlen($json) > 1000) {
            return ['_truncated' => 'Request data too large for logging'];
        }

        return $data;
    }

    /**
     * Check a single permission requirement
     */
    protected function checkPermission($user, array $permissionCheck, array $context): array
    {
        switch ($permissionCheck['type']) {
            case 'or':
                return $this->checkOrPermissions($user, $permissionCheck['permissions'], $context);
            
            case 'and':
                return $this->checkAndPermissions($user, $permissionCheck['permissions'], $context);
            
            case 'single':
                return $this->checkSinglePermission($user, $permissionCheck['permission'], $context);
            
            default:
                return [
                    'granted' => false,
                    'reason' => 'Invalid permission check type',
                    'role' => null,
                    'permission_model' => null,
                ];
        }
    }

    /**
     * Check OR permissions (user needs ANY of these)
     */
    protected function checkOrPermissions($user, array $permissions, array $context): array
    {
        foreach ($permissions as $permission) {
            $result = $this->checkSinglePermission($user, $permission, $context);
            if ($result['granted']) {
                return $result;
            }
        }

        return [
            'granted' => false,
            'reason' => 'User does not have any of the required permissions: ' . implode(', ', $permissions),
            'role' => null,
            'permission_model' => null,
        ];
    }

    /**
     * Check AND permissions (user needs ALL of these)
     */
    protected function checkAndPermissions($user, array $permissions, array $context): array
    {
        $results = [];
        
        foreach ($permissions as $permission) {
            $result = $this->checkSinglePermission($user, $permission, $context);
            $results[] = $result;
            
            if (!$result['granted']) {
                return [
                    'granted' => false,
                    'reason' => "Missing required permission: {$permission}",
                    'role' => $result['role'],
                    'permission_model' => $result['permission_model'],
                ];
            }
        }

        // Return the first successful result
        return $results[0];
    }

    /**
     * Check a single permission
     */
    protected function checkSinglePermission($user, string $permissionName, array $context): array
    {
        return $this->permissionService->checkUserPermission($user, $permissionName, $context);
    }

    /**
     * Handle unauthorized access
     */
    protected function handleUnauthorized(Request $request, string $reason): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Unauthorized',
                'message' => $reason,
                'code' => 401,
            ], 401);
        }

        return redirect()->guest(route('login'))
            ->with('error', 'You must be logged in to access this page.');
    }

    /**
     * Handle forbidden access
     */
    protected function handleForbidden(Request $request, string $reason): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => $reason,
                'code' => 403,
            ], 403);
        }

        // For web requests, redirect with error message
        return back()->with('error', 'Access denied: ' . $reason);
    }
}

/**
 * Advanced Permission Service
 * 
 * Core service for checking permissions with constraint validation.
 */
class AdvancedPermissionService
{
    /**
     * Check if user has permission with context validation
     */
    public function checkUserPermission($user, string $permissionName, array $context = []): array
    {
        // Super admin bypass
        if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
            return [
                'granted' => true,
                'reason' => 'Super admin access',
                'role' => null,
                'permission_model' => null,
            ];
        }

        // Get user's active roles
        $activeRoles = $this->getUserActiveRoles($user);
        
        if ($activeRoles->isEmpty()) {
            return [
                'granted' => false,
                'reason' => 'User has no active roles',
                'role' => null,
                'permission_model' => null,
            ];
        }

        // Check each role for the permission
        foreach ($activeRoles as $userRole) {
            $role = $userRole->role;
            if (!$role) continue;

            $result = $this->checkRolePermission($role, $permissionName, $context, $userRole);
            if ($result['granted']) {
                return $result;
            }
        }

        return [
            'granted' => false,
            'reason' => "Permission '{$permissionName}' not found in user's roles",
            'role' => $activeRoles->first()?->role,
            'permission_model' => null,
        ];
    }

    /**
     * Check if role has permission with constraints
     */
    protected function checkRolePermission($role, string $permissionName, array $context, $userRole): array
    {
        $rolePermissions = $role->rolePermissions()
            ->whereHas('permission', function ($query) use ($permissionName) {
                $query->where('name', $permissionName);
            })
            ->with('permission')
            ->get();

        foreach ($rolePermissions as $rolePermission) {
            // Check if permission is granted
            if (!$rolePermission->is_granted) {
                continue;
            }

            // Check temporal validity
            if (!$rolePermission->isCurrentlyValid()) {
                continue;
            }

            // Check constraints
            if (!$rolePermission->constraintsSatisfied($context)) {
                return [
                    'granted' => false,
                    'reason' => 'Permission constraints not satisfied: ' . $rolePermission->getConstraintDescription(),
                    'role' => $role,
                    'permission_model' => $rolePermission->permission,
                ];
            }

            // Check user role assignment validity
            if (!$userRole->isCurrentlyValid()) {
                continue;
            }

            return [
                'granted' => true,
                'reason' => 'Permission granted via role: ' . $role->display_name,
                'role' => $role,
                'permission_model' => $rolePermission->permission,
            ];
        }

        return [
            'granted' => false,
            'reason' => "Permission '{$permissionName}' not found or not valid in role: " . $role->display_name,
            'role' => $role,
            'permission_model' => null,
        ];
    }

    /**
     * Get user's active roles
     */
    protected function getUserActiveRoles($user)
    {
        if (!method_exists($user, 'advancedUserRoles')) {
            return collect();
        }

        return $user->advancedUserRoles()
            ->currentlyValid()
            ->with('role.rolePermissions.permission')
            ->byPriority()
            ->get();
    }

    /**
     * Check multiple permissions at once
     */
    public function checkMultiplePermissions($user, array $permissions, array $context = []): array
    {
        $results = [];
        
        foreach ($permissions as $permission) {
            $results[$permission] = $this->checkUserPermission($user, $permission, $context);
        }

        return $results;
    }

    /**
     * Get all permissions for user
     */
    public function getUserPermissions($user): array
    {
        if (method_exists($user, 'getAdvancedPermissions')) {
            return $user->getAdvancedPermissions()->pluck('name')->toArray();
        }

        return [];
    }
}
