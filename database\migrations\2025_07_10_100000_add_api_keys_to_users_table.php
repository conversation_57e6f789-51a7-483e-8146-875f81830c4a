<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add API key columns if they don't exist
            if (!Schema::hasColumn('users', 'public_key')) {
                $table->string('public_key', 100)->nullable()->after('remember_token')->comment('Public API key for authentication');
            }
            if (!Schema::hasColumn('users', 'secret_key')) {
                $table->string('secret_key', 100)->nullable()->after('public_key')->comment('Secret API key for authentication');
            }
            if (!Schema::hasColumn('users', 'mode')) {
                $table->tinyInteger('mode')->default(0)->after('secret_key')->comment('0 = Test Mode, 1 = Live Mode');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove API key columns if they exist
            if (Schema::hasColumn('users', 'public_key')) {
                $table->dropColumn('public_key');
            }
            if (Schema::hasColumn('users', 'secret_key')) {
                $table->dropColumn('secret_key');
            }
            if (Schema::hasColumn('users', 'mode')) {
                $table->dropColumn('mode');
            }
        });
    }
};
