<?php

namespace App\Http\Middleware;

use App\Services\WebhookSecurityService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class VerifyWebhookSignature
{
    /**
     * Handle an incoming request to verify webhook signature
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $secretKey = null): Response
    {
        try {
            // Skip verification for non-webhook routes or if explicitly disabled
            if (!$this->shouldVerifySignature($request)) {
                return $next($request);
            }

            // Get secret key from parameter or environment
            $secret = $secretKey ?: config('app.webhook_secret');
            
            if (!$secret) {
                Log::error('VerifyWebhookSignature: No secret key provided for webhook verification', [
                    'url' => $request->url(),
                    'method' => $request->method()
                ]);
                
                return response()->json([
                    'error' => 'Webhook signature verification failed',
                    'message' => 'No secret key configured'
                ], 401);
            }

            // Validate the webhook signature
            $isValid = WebhookSecurityService::validateIncomingWebhook($request, $secret);
            
            if (!$isValid) {
                Log::warning('VerifyWebhookSignature: Invalid webhook signature', [
                    'url' => $request->url(),
                    'method' => $request->method(),
                    'headers' => $request->headers->all()
                ]);
                
                return response()->json([
                    'error' => 'Webhook signature verification failed',
                    'message' => 'Invalid signature'
                ], 401);
            }

            Log::info('VerifyWebhookSignature: Webhook signature verified successfully', [
                'url' => $request->url(),
                'method' => $request->method()
            ]);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('VerifyWebhookSignature: Exception during signature verification', [
                'error' => $e->getMessage(),
                'url' => $request->url(),
                'method' => $request->method()
            ]);
            
            return response()->json([
                'error' => 'Webhook signature verification failed',
                'message' => 'Internal error during verification'
            ], 500);
        }
    }

    /**
     * Determine if signature verification should be performed
     */
    private function shouldVerifySignature(Request $request): bool
    {
        // Skip verification for GET requests (typically not webhooks)
        if ($request->isMethod('GET')) {
            return false;
        }

        // Skip verification if explicitly disabled via header
        if ($request->header('X-Skip-Signature-Verification') === 'true') {
            return false;
        }

        // Skip verification for local development if configured
        if (app()->environment('local') && config('app.skip_webhook_verification', false)) {
            Log::info('VerifyWebhookSignature: Skipping verification in local environment');
            return false;
        }

        return true;
    }
}
