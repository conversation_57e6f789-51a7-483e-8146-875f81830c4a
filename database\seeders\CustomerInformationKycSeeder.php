<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CustomerInformationKycSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $inputForm = [
            'Email' => [
                'field_name' => 'Email',
                'field_label' => 'Email Address',
                'type' => 'email',
                'validation' => 'required'
            ],
            'FirstName' => [
                'field_name' => 'FirstName',
                'field_label' => 'First Name',
                'type' => 'text',
                'validation' => 'required'
            ],
            'LastName' => [
                'field_name' => 'LastName',
                'field_label' => 'Last Name',
                'type' => 'text',
                'validation' => 'required'
            ],
            'MobileNumber' => [
                'field_name' => 'MobileNumber',
                'field_label' => 'Mobile Number',
                'type' => 'text',
                'validation' => 'required'
            ],
            'BankVerificationNumber' => [
                'field_name' => 'BankVerificationNumber',
                'field_label' => 'Bank Verification Number (BVN)',
                'type' => 'text',
                'validation' => 'required'
            ]
        ];

        DB::table('kycs')->updateOrInsert(
            ['slug' => 'customer-information'],
            [
                'name' => 'Customer Information',
                'slug' => 'customer-information',
                'input_form' => json_encode($inputForm),
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        );
    }
}
