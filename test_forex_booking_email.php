<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== FOREX BOOKING EMAIL TEST ===\n\n";

// Get services
$walletService = new ForexWalletService();
$bookingService = new ForexBookingService($walletService);

// Get USD account for testing
$usdAccount = ForexAccount::byType('USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

echo "USD Account found: {$usdAccount->account_name}\n";
echo "Initial balance: $" . number_format($usdAccount->balance, 2) . "\n\n";

// Test data for booking creation
$bookingData = [
    'client_type' => 'external',
    'client_name' => 'Test Email Client',
    'client_email' => '<EMAIL>',
    'client_phone' => '+**********',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 50.00,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test bank account for email testing',
    'payment_instructions' => 'Please transfer funds to our test account for verification',
];

try {
    echo "Creating forex booking...\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    
    echo "✅ Booking created successfully!\n";
    echo "- Reference: {$booking->booking_reference}\n";
    echo "- Client: {$booking->client_name} ({$booking->client_email})\n";
    echo "- Amount: {$booking->currency} " . number_format($booking->amount, 2) . "\n";
    echo "- Customer Rate: ₦" . number_format($booking->customer_rate, 2) . "/$1\n";
    echo "- Total: ₦" . number_format($booking->customer_total, 2) . "\n";
    echo "- Status: {$booking->status}\n";
    echo "- Email sent flag: " . ($booking->email_sent ? 'Yes' : 'No') . "\n\n";
    
    // Test payment reminder
    echo "Testing payment reminder email...\n";
    $bookingService->sendForexPaymentReminderEmail($booking, 'This is a test payment reminder for your forex booking.');
    echo "✅ Payment reminder email sent\n\n";
    
    // Test completion
    echo "Testing booking completion...\n";
    $bookingService->completeBooking($booking, 1, 'Booking completed successfully for email testing');
    
    // Refresh booking to get updated status
    $booking->refresh();
    echo "✅ Booking completed!\n";
    echo "- Final Status: {$booking->status}\n";
    echo "- Completed at: {$booking->completed_at}\n";
    echo "- Notes: {$booking->status_notes}\n";
    
} catch (\Exception $e) {
    echo "❌ Error during booking process: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "Check your email queue or logs to verify emails were sent.\n";
