<?php

require 'vendor/autoload.php';

use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Services\ForexBookingService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING COMPLETION FIX ===\n\n";

// Find a pending booking
$pendingBooking = ForexBooking::where('status', 'pending')->first();

if (!$pendingBooking) {
    echo "❌ No pending booking found to test completion\n";
    exit;
}

echo "Found pending booking: {$pendingBooking->booking_reference}\n";
echo "Transaction type: {$pendingBooking->transaction_type}\n";
echo "Amount: {$pendingBooking->amount} {$pendingBooking->currency}\n";
echo "Status: {$pendingBooking->status}\n\n";

// Check reservations
$reservations = $pendingBooking->reservations;
echo "Reservations: " . $reservations->count() . "\n";
foreach ($reservations as $reservation) {
    echo "- {$reservation->account->account_name}: " . number_format((float)$reservation->reserved_amount, 2) . " (Status: {$reservation->status})\n";
}

$bookingService = app(ForexBookingService::class);

try {
    echo "\n=== ATTEMPTING COMPLETION ===\n";
    $bookingService->completeBooking($pendingBooking, 1, 'Test completion');
    echo "✅ Booking completed successfully!\n";
    
    // Check final status
    $pendingBooking->refresh();
    echo "Final booking status: {$pendingBooking->status}\n";
    
    // Check reservation statuses
    $reservations = $pendingBooking->reservations;
    echo "\nFinal reservation statuses:\n";
    foreach ($reservations as $reservation) {
        echo "- {$reservation->account->account_name}: " . number_format((float)$reservation->reserved_amount, 2) . " (Status: {$reservation->status})\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

?>
