# Use official PHP 8.3 image with FPM
FROM php:8.3-fpm

# Set working directory
WORKDIR /var/www

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpng-dev \
    libjpeg-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    curl \
    git \
    nginx \
    supervisor \
    libzip-dev \
    libpq-dev \
    libcurl4-openssl-dev \
    gnupg \
    ca-certificates \
    cron \
    nano

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd zip

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy Laravel files
COPY . /var/www

# Install Composer dependencies
RUN composer install --no-dev --optimize-autoloader

# Copy nginx config (optional - if you want nginx in the same container)
#COPY ./nginx/nginx.conf /etc/nginx/conf.d/default.conf

# Remove default Nginx site config
#RUN rm /etc/nginx/conf.d/default.conf


# Overwrite Nginx global config and site config
COPY ./nginx/nginx.conf /etc/nginx/nginx.conf
COPY ./nginx/laravel.conf /etc/nginx/conf.d/laravel.conf


# Set permissions
RUN chown -R www-data:www-data /var/www \
    && chmod -R 775 /var/www/storage /var/www/bootstrap/cache

# Expose ports
EXPOSE 80

# Copy Supervisor config
COPY ./supervisord.conf /etc/supervisord.conf

# Create startup script
COPY ./docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Start with entrypoint script
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]
