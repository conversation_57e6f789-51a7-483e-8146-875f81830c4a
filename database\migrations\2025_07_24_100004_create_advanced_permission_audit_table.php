<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the advanced_permission_audit table for tracking permission changes and access attempts.
     * Essential for security compliance and debugging permission issues.
     */
    public function up(): void
    {
        Schema::create('advanced_permission_audit', function (Blueprint $table) {
            $table->id();
            
            // Event identification
            $table->string('event_type', 50)->comment('Type of event: access_granted, access_denied, role_assigned, etc.');
            $table->string('action', 100)->comment('Specific action attempted (e.g., users.create, transactions.read)');
            
            // User information
            $table->unsignedBigInteger('user_id')->nullable()->comment('User/Admin ID who performed action');
            $table->string('user_type', 50)->nullable()->comment('User model type');
            $table->string('user_identifier', 100)->nullable()->comment('Username/email for reference');
            
            // Permission context
            $table->unsignedBigInteger('role_id')->nullable()->comment('Role used for permission check');
            $table->unsignedBigInteger('permission_id')->nullable()->comment('Permission that was checked');
            $table->string('resource', 100)->nullable()->comment('Resource being accessed');
            $table->string('resource_id', 100)->nullable()->comment('Specific resource ID if applicable');
            
            // Request context
            $table->string('ip_address', 45)->nullable()->comment('IP address of request');
            $table->string('user_agent', 500)->nullable()->comment('User agent string');
            $table->string('route_name', 200)->nullable()->comment('Laravel route name');
            $table->string('url', 500)->nullable()->comment('Full URL accessed');
            $table->string('method', 10)->nullable()->comment('HTTP method (GET, POST, etc.)');
            
            // Result and details
            $table->boolean('was_granted')->comment('Whether access was granted');
            $table->text('reason')->nullable()->comment('Reason for grant/denial');
            $table->json('context_data')->nullable()->comment('Additional context data');
            $table->json('request_data')->nullable()->comment('Relevant request data (sanitized)');
            
            // Performance tracking
            $table->integer('check_duration_ms')->nullable()->comment('Time taken for permission check in milliseconds');
            
            // Timestamps
            $table->timestamp('occurred_at')->comment('When the event occurred');
            $table->timestamps();
            
            // Indexes for performance and querying
            $table->index(['event_type', 'occurred_at']);
            $table->index(['user_id', 'user_type', 'occurred_at']);
            $table->index(['action', 'was_granted', 'occurred_at']);
            $table->index(['ip_address', 'occurred_at']);
            $table->index(['resource', 'occurred_at']);
            $table->index(['was_granted', 'occurred_at']);
            
            // Partitioning hint for large datasets
            $table->index(['occurred_at']); // For time-based partitioning
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advanced_permission_audit');
    }
};
