<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing data to new transaction types
        DB::table('forex_bookings')
            ->where('transaction_type', 'credit')
            ->update(['transaction_type' => 'buying']);
            
        DB::table('forex_bookings')
            ->where('transaction_type', 'debit')
            ->update(['transaction_type' => 'selling']);

        Schema::table('forex_bookings', function (Blueprint $table) {
            // Update transaction type enum
            $table->enum('transaction_type', ['buying', 'selling'])
                ->comment('buying = NGN to USD (client buying USD), selling = USD to NGN (client selling USD)')
                ->change();
            
            // Add customer payment amount field
            $table->decimal('customer_payment_amount', 18, 8)
                ->after('customer_total')
                ->comment('Final amount customer actually pays (in their payment currency)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forex_bookings', function (Blueprint $table) {
            // Remove customer payment amount field
            $table->dropColumn('customer_payment_amount');
            
            // Revert transaction type enum
            $table->enum('transaction_type', ['credit', 'debit'])
                ->comment('Credit = funding, Debit = exchange')
                ->change();
        });
        
        // Revert data back to old transaction types
        DB::table('forex_bookings')
            ->where('transaction_type', 'buying')
            ->update(['transaction_type' => 'credit']);
            
        DB::table('forex_bookings')
            ->where('transaction_type', 'selling')
            ->update(['transaction_type' => 'debit']);
    }
};
