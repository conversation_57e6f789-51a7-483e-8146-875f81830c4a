@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.index') }}">
                                    @lang('Forex Reports')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('CBN Compliance Report')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">{{ $reportData['title'] }}</h1>
                    <p class="page-header-text">{{ $reportData['subtitle'] ?? '' }}</p>
                    <p class="page-header-text">@lang('Report Period'): {{ $reportData['period'] }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="btn-group" role="group">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.reports.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Reports')
                        </a>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="bi-printer me-1"></i> @lang('Print')
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi-download me-1"></i> @lang('Export')
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                    <i class="bi-file-earmark-excel me-2"></i> @lang('Excel')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                    <i class="bi-file-earmark-pdf me-2"></i> @lang('PDF')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                    <i class="bi-file-earmark-text me-2"></i> @lang('CSV')
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Compliance Notice -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info" role="alert">
                    <div class="d-flex">
                        <div class="flex-shrink-0">
                            <i class="bi-info-circle-fill"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h4 class="alert-heading">@lang('CBN Compliance Report')</h4>
                            <p class="mb-2">@lang('This report is generated for Central Bank of Nigeria (CBN) compliance purposes and contains only official exchange rate data.')</p>
                            <hr>
                            <div class="mb-0">
                                @if(isset($reportData['compliance_notes']))
                                    <ul class="mb-0">
                                        @foreach($reportData['compliance_notes'] as $note)
                                            <li>{{ $note }}</li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Executive Summary')</h4>
                        <span class="badge bg-soft-success text-success">
                            <i class="bi-shield-check me-1"></i> @lang('CBN Compliant')
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Transaction Overview -->
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['total_bookings']) }}</span>
                                        <span class="d-block text-muted">@lang('Total Transactions')</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['completed_bookings']) }}</span>
                                        <span class="d-block text-muted">@lang('Completed Transactions')</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['compliance_metrics']['completion_rate'], 1) }}%</span>
                                        <span class="d-block text-muted">@lang('Completion Rate')</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['compliance_metrics']['average_processing_time'], 1) }}h</span>
                                        <span class="d-block text-muted">@lang('Avg Processing Time')</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CBN Rate Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('CBN Rate Analysis')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">₦{{ number_format($reportData['summary']['cbn_rate_range']['current'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Current CBN Rate')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">₦{{ number_format($reportData['summary']['cbn_rate_range']['avg'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Average CBN Rate')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">₦{{ number_format($reportData['summary']['cbn_rate_range']['min'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Minimum Rate')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">₦{{ number_format($reportData['summary']['cbn_rate_range']['max'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Maximum Rate')</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">@lang('Rate Stability')</span>
                                <span class="badge bg-soft-success text-success">{{ number_format($reportData['summary']['compliance_metrics']['cbn_rate_stability'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Markup Analysis')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['markup_analysis']['average_markup_percentage'], 2) }}%</span>
                                    <span class="d-block text-muted small">@lang('Average Markup')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['markup_analysis']['total_markup_amount'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Total Revenue')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['markup_analysis']['min_markup_percentage'], 2) }}%</span>
                                    <span class="d-block text-muted small">@lang('Min Markup')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['markup_analysis']['max_markup_percentage'], 2) }}%</span>
                                    <span class="d-block text-muted small">@lang('Max Markup')</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">@lang('Markup Consistency')</span>
                                <span class="badge bg-soft-info text-info">{{ number_format($reportData['summary']['compliance_metrics']['markup_consistency'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Breakdown -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transaction Volume')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['total_amount_usd'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Total USD Volume')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">₦{{ number_format($reportData['summary']['total_amount_ngn'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Total NGN Volume')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['transaction_breakdown']['buying_count']) }}</span>
                                    <span class="d-block text-muted small">@lang('Buying Transactions')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['transaction_breakdown']['selling_count']) }}</span>
                                    <span class="d-block text-muted small">@lang('Selling Transactions')</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('CBN Value Analysis')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">₦{{ number_format($reportData['summary']['cbn_totals']['total_cbn_value'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Total CBN Value')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">₦{{ number_format($reportData['summary']['cbn_totals']['buying_cbn_total'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Buying CBN Total')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">₦{{ number_format($reportData['summary']['cbn_totals']['selling_cbn_total'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Selling CBN Total')</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Transactions Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transaction Details')</h4>
                        <div class="card-header-content">
                            <span class="text-muted">{{ $reportData['bookings']->count() }} @lang('records')</span>
                        </div>
                    </div>
                    <div class="card-body">
                        @if($reportData['bookings']->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>@lang('Reference')</th>
                                            <th>@lang('Client')</th>
                                            <th>@lang('Type')</th>
                                            <th>@lang('Amount')</th>
                                            <th>@lang('CBN Rate')</th>
                                            <th>@lang('Markup %')</th>
                                            <th>@lang('Final Rate')</th>
                                            <th>@lang('CBN Total')</th>
                                            <th>@lang('Revenue')</th>
                                            <th>@lang('Status')</th>
                                            <th>@lang('Date')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reportData['bookings'] as $booking)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('admin.forex.bookings.show', $booking->id) }}" class="text-decoration-none">
                                                        {{ $booking->booking_reference }}
                                                    </a>
                                                </td>
                                                <td>{{ $booking->client_name }}</td>
                                                <td>
                                                    <span class="badge bg-soft-{{ $booking->transaction_type === 'buying' ? 'success' : 'primary' }} text-{{ $booking->transaction_type === 'buying' ? 'success' : 'primary' }}">
                                                        {{ ucfirst($booking->transaction_type) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if($booking->currency === 'USD')
                                                        ${{ number_format($booking->amount, 2) }}
                                                    @else
                                                        ₦{{ number_format($booking->amount, 2) }}
                                                    @endif
                                                </td>
                                                <td>₦{{ number_format($booking->cbn_rate, 2) }}</td>
                                                <td>{{ number_format($booking->markup_percentage, 2) }}%</td>
                                                <td>₦{{ number_format($booking->customer_rate, 2) }}</td>
                                                <td>₦{{ number_format($booking->cbn_total, 2) }}</td>
                                                <td>${{ number_format($booking->markup_amount, 2) }}</td>
                                                <td>
                                                    @if($booking->status === 'completed')
                                                        <span class="badge bg-soft-success text-success">@lang('Completed')</span>
                                                    @elseif($booking->status === 'pending')
                                                        <span class="badge bg-soft-warning text-warning">@lang('Pending')</span>
                                                    @elseif($booking->status === 'cancelled')
                                                        <span class="badge bg-soft-danger text-danger">@lang('Cancelled')</span>
                                                    @endif
                                                </td>
                                                <td>{{ $booking->created_at->format('M d, Y H:i') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <div class="mb-3">
                                    <i class="bi-inbox display-4 text-muted"></i>
                                </div>
                                <h5 class="text-muted">@lang('No transactions found')</h5>
                                <p class="text-muted">@lang('No transactions match the selected criteria for this period.')</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        
        function exportReport(format) {
            // Implementation for exporting CBN-focused reports
            alert('Export functionality will be implemented for ' + format + ' format');
        }
    </script>
@endpush
