<?php

namespace Database\Factories;

use App\Models\VirtualAccount;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class VirtualAccountFactory extends Factory
{
    protected $model = VirtualAccount::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'provider' => 'numero',
            'currency' => $this->faker->randomElement(['NGN', 'USD']),
            'type' => $this->faker->randomElement(['customer', 'individual', 'business']),
            'account_number' => $this->faker->unique()->numerify('##########'),
            'account_name' => $this->faker->name(),
            'bank_name' => $this->faker->randomElement(['Test Bank', 'Demo Bank', 'Virtual Bank']),
            'bank_code' => $this->faker->numerify('###'),
            'provider_data' => [
                'reference' => $this->faker->uuid(),
                'created_at_provider' => now()->toISOString(),
            ],
            'kyc_data_used' => [
                'email' => $this->faker->email(),
                'first_name' => $this->faker->firstName(),
                'last_name' => $this->faker->lastName(),
                'phone' => $this->faker->phoneNumber(),
            ],
            'provider_reference' => $this->faker->uuid(),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'created_at_provider' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the virtual account should be active.
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * Indicate that the virtual account should be inactive.
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    /**
     * Indicate that the virtual account should be NGN currency.
     */
    public function ngn()
    {
        return $this->state(function (array $attributes) {
            return [
                'currency' => 'NGN',
            ];
        });
    }

    /**
     * Indicate that the virtual account should be USD currency.
     */
    public function usd()
    {
        return $this->state(function (array $attributes) {
            return [
                'currency' => 'USD',
            ];
        });
    }

    /**
     * Indicate that the virtual account should be customer type.
     */
    public function customer()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'customer',
            ];
        });
    }

    /**
     * Indicate that the virtual account should be individual type.
     */
    public function individual()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'individual',
            ];
        });
    }

    /**
     * Indicate that the virtual account should be business type.
     */
    public function business()
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'business',
            ];
        });
    }
}
