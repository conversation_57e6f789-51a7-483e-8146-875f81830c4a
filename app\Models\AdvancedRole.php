<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Carbon\Carbon;

/**
 * Advanced Role Model
 *
 * Represents dynamic roles with hierarchy support and permission inheritance.
 * Roles can inherit from parent roles and have complex permission structures.
 *
 * @property int $id
 * @property string $name Unique role name
 * @property string $display_name Human-readable role name
 * @property string|null $description Role description
 * @property int|null $parent_role_id Parent role for inheritance
 * @property int $level Hierarchy level
 * @property string|null $hierarchy_path Full hierarchy path
 * @property string|null $category Role category
 * @property string $scope Role scope (global, department, etc.)
 * @property array|null $metadata Additional role metadata
 * @property int $sort_order Display order
 * @property string|null $color UI color code
 * @property bool $is_system System role that cannot be deleted
 * @property bool $is_active Whether role is active
 * @property bool $is_default Default role for new users
 * @property bool $inherit_permissions Whether to inherit parent permissions
 * @property int|null $max_users Maximum users for this role
 * @property Carbon|null $expires_at Role expiration date
 */
class AdvancedRole extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'parent_role_id',
        'level',
        'hierarchy_path',
        'category',
        'scope',
        'metadata',
        'sort_order',
        'color',
        'is_system',
        'is_active',
        'is_default',
        'inherit_permissions',
        'max_users',
        'expires_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_system' => 'boolean',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'inherit_permissions' => 'boolean',
        'level' => 'integer',
        'sort_order' => 'integer',
        'max_users' => 'integer',
        'expires_at' => 'datetime',
    ];

    protected $attributes = [
        'scope' => 'global',
        'level' => 0,
        'sort_order' => 0,
        'is_system' => false,
        'is_active' => true,
        'is_default' => false,
        'inherit_permissions' => true,
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically set hierarchy fields
        static::creating(function ($model) {
            if (auth()->check() && auth()->user() instanceof Admin) {
                $model->created_by = auth()->id();
            }
            $model->updateHierarchy();
        });

        static::updating(function ($model) {
            if (auth()->check() && auth()->user() instanceof Admin) {
                $model->updated_by = auth()->id();
            }
            if ($model->isDirty('parent_role_id')) {
                $model->updateHierarchy();
            }
        });

        // Prevent deletion of system roles
        static::deleting(function ($model) {
            if ($model->is_system) {
                throw new \Exception('System roles cannot be deleted.');
            }
            if ($model->userRoles()->count() > 0) {
                throw new \Exception('Cannot delete role that is assigned to users.');
            }
        });

        // Update child roles when parent changes
        static::updated(function ($model) {
            if ($model->wasChanged('hierarchy_path')) {
                $model->updateChildrenHierarchy();
            }
        });
    }

    /**
     * Get the parent role
     */
    public function parentRole(): BelongsTo
    {
        return $this->belongsTo(AdvancedRole::class, 'parent_role_id');
    }

    /**
     * Get child roles
     */
    public function childRoles(): HasMany
    {
        return $this->hasMany(AdvancedRole::class, 'parent_role_id');
    }

    /**
     * Get all permissions for this role
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(AdvancedPermission::class, 'advanced_role_permissions')
            ->withPivot([
                'constraints',
                'is_granted',
                'priority',
                'valid_from',
                'valid_until',
                'schedule',
                'granted_by',
                'granted_at',
                'grant_reason'
            ])
            ->withTimestamps();
    }

    /**
     * Get role permissions pivot records
     */
    public function rolePermissions(): HasMany
    {
        return $this->hasMany(AdvancedRolePermission::class, 'role_id');
    }

    /**
     * Get user role assignments
     */
    public function userRoles(): HasMany
    {
        return $this->hasMany(AdvancedUserRole::class, 'role_id');
    }

    /**
     * Get users with this role (polymorphic)
     */
    public function users(): MorphToMany
    {
        return $this->morphedByMany(User::class, 'user', 'advanced_user_roles')
            ->withPivot([
                'is_active',
                'priority',
                'assigned_at',
                'expires_at',
                'context',
                'context_data'
            ])
            ->withTimestamps();
    }

    /**
     * Get admins with this role (polymorphic)
     */
    public function admins(): MorphToMany
    {
        return $this->morphedByMany(Admin::class, 'user', 'advanced_user_roles')
            ->withPivot([
                'is_active',
                'priority',
                'assigned_at',
                'expires_at',
                'context',
                'context_data'
            ])
            ->withTimestamps();
    }

    /**
     * Get the admin who created this role
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * Get the admin who last updated this role
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'updated_by');
    }

    /**
     * Scope: Only active roles
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Only system roles
     */
    public function scopeSystem(Builder $query): Builder
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope: Filter by category
     */
    public function scopeInCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope: Filter by scope
     */
    public function scopeInScope(Builder $query, string $scope): Builder
    {
        return $query->where('scope', $scope);
    }

    /**
     * Scope: Top-level roles (no parent)
     */
    public function scopeTopLevel(Builder $query): Builder
    {
        return $query->whereNull('parent_role_id');
    }

    /**
     * Scope: Ordered for display
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('level')
            ->orderBy('sort_order')
            ->orderBy('display_name');
    }

    /**
     * Update hierarchy fields
     */
    protected function updateHierarchy(): void
    {
        if ($this->parent_role_id) {
            $parent = static::find($this->parent_role_id);
            if ($parent) {
                $this->level = $parent->level + 1;
                $this->hierarchy_path = $parent->hierarchy_path
                    ? $parent->hierarchy_path . '/' . $parent->id
                    : (string) $parent->id;
            }
        } else {
            $this->level = 0;
            $this->hierarchy_path = null;
        }
    }

    /**
     * Update children hierarchy when this role's hierarchy changes
     */
    protected function updateChildrenHierarchy(): void
    {
        $children = $this->childRoles;
        foreach ($children as $child) {
            $child->updateHierarchy();
            $child->save();
        }
    }

    /**
     * Get all permissions including inherited ones
     */
    public function getAllPermissions(): Collection
    {
        $permissions = collect();

        // Get direct permissions
        $directPermissions = $this->permissions()
            ->wherePivot('is_granted', true)
            ->get();

        $permissions = $permissions->merge($directPermissions);

        // Get inherited permissions if enabled
        if ($this->inherit_permissions && $this->parentRole) {
            $inheritedPermissions = $this->parentRole->getAllPermissions();
            $permissions = $permissions->merge($inheritedPermissions);
        }

        // Remove duplicates and sort by priority
        return $permissions->unique('id')->sortByDesc('pivot.priority');
    }

    /**
     * Check if role has a specific permission
     */
    public function hasPermission(string $permissionName): bool
    {
        return $this->getAllPermissions()
            ->contains('name', $permissionName);
    }

    /**
     * Grant a permission to this role
     */
    public function grantPermission(
        AdvancedPermission $permission,
        array $constraints = null,
        int $priority = 0
    ): void {
        $this->permissions()->syncWithoutDetaching([
            $permission->id => [
                'is_granted' => true,
                'constraints' => $constraints,
                'priority' => $priority,
                'granted_by' => auth()->id(),
                'granted_at' => now(),
            ]
        ]);
    }

    /**
     * Revoke a permission from this role
     */
    public function revokePermission(AdvancedPermission $permission): void
    {
        $this->permissions()->detach($permission->id);
    }

    /**
     * Get role hierarchy as array
     */
    public function getHierarchyArray(): array
    {
        $hierarchy = [];
        $current = $this;

        while ($current) {
            array_unshift($hierarchy, [
                'id' => $current->id,
                'name' => $current->name,
                'display_name' => $current->display_name,
                'level' => $current->level,
            ]);
            $current = $current->parentRole;
        }

        return $hierarchy;
    }

    /**
     * Check if role can be assigned to more users
     */
    public function canAssignToMoreUsers(): bool
    {
        if (!$this->max_users) {
            return true;
        }

        return $this->userRoles()->where('is_active', true)->count() < $this->max_users;
    }

    /**
     * Check if role is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get role status badge HTML
     */
    public function getStatusBadge(): string
    {
        if (!$this->is_active) {
            return '<span class="badge bg-danger">Inactive</span>';
        }

        if ($this->isExpired()) {
            return '<span class="badge bg-warning">Expired</span>';
        }

        return '<span class="badge bg-success">Active</span>';
    }

    /**
     * Find role by name
     */
    public static function findByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }

    /**
     * Get default role for new users
     */
    public static function getDefaultRole(): ?self
    {
        return static::where('is_default', true)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Create a new role with standard permissions
     */
    public static function createWithPermissions(
        string $name,
        string $displayName,
        array $permissionNames = [],
        array $attributes = []
    ): self {
        $role = static::create(array_merge([
            'name' => $name,
            'display_name' => $displayName,
        ], $attributes));

        // Assign permissions
        foreach ($permissionNames as $permissionName) {
            $permission = AdvancedPermission::findByName($permissionName);
            if ($permission) {
                $role->grantPermission($permission);
            }
        }

        return $role;
    }
}
