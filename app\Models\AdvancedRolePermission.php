<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * Advanced Role Permission Pivot Model
 * 
 * Represents the relationship between roles and permissions with additional constraints.
 * Supports temporal access control, priority-based conflict resolution, and conditional permissions.
 * 
 * @property int $id
 * @property int $role_id Reference to advanced_roles
 * @property int $permission_id Reference to advanced_permissions
 * @property array|null $constraints Additional constraints for permission
 * @property bool $is_granted Whether permission is granted or denied
 * @property int $priority Priority for conflict resolution
 * @property Carbon|null $valid_from Permission valid from date
 * @property Carbon|null $valid_until Permission valid until date
 * @property array|null $schedule Time-based schedule
 * @property int|null $granted_by Admin who granted permission
 * @property Carbon|null $granted_at When permission was granted
 * @property string|null $grant_reason Reason for granting/denying
 */
class AdvancedRolePermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'role_id',
        'permission_id',
        'constraints',
        'is_granted',
        'priority',
        'valid_from',
        'valid_until',
        'schedule',
        'granted_by',
        'granted_at',
        'grant_reason',
    ];

    protected $casts = [
        'constraints' => 'array',
        'schedule' => 'array',
        'is_granted' => 'boolean',
        'priority' => 'integer',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'granted_at' => 'datetime',
    ];

    protected $attributes = [
        'is_granted' => true,
        'priority' => 0,
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (auth()->check() && auth()->user() instanceof Admin) {
                $model->granted_by = auth()->id();
                $model->granted_at = now();
            }
        });
    }

    /**
     * Get the role
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(AdvancedRole::class, 'role_id');
    }

    /**
     * Get the permission
     */
    public function permission(): BelongsTo
    {
        return $this->belongsTo(AdvancedPermission::class, 'permission_id');
    }

    /**
     * Get the admin who granted this permission
     */
    public function grantedBy(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'granted_by');
    }

    /**
     * Scope: Only granted permissions
     */
    public function scopeGranted(Builder $query): Builder
    {
        return $query->where('is_granted', true);
    }

    /**
     * Scope: Only denied permissions
     */
    public function scopeDenied(Builder $query): Builder
    {
        return $query->where('is_granted', false);
    }

    /**
     * Scope: Currently valid permissions
     */
    public function scopeCurrentlyValid(Builder $query): Builder
    {
        $now = now();
        return $query->where(function ($q) use ($now) {
            $q->whereNull('valid_from')
              ->orWhere('valid_from', '<=', $now);
        })->where(function ($q) use ($now) {
            $q->whereNull('valid_until')
              ->orWhere('valid_until', '>=', $now);
        });
    }

    /**
     * Scope: Ordered by priority (highest first)
     */
    public function scopeByPriority(Builder $query): Builder
    {
        return $query->orderByDesc('priority');
    }

    /**
     * Check if permission is currently valid based on time constraints
     */
    public function isCurrentlyValid(): bool
    {
        $now = now();
        
        // Check date range
        if ($this->valid_from && $now->isBefore($this->valid_from)) {
            return false;
        }
        
        if ($this->valid_until && $now->isAfter($this->valid_until)) {
            return false;
        }
        
        // Check schedule if present
        if ($this->schedule) {
            return $this->isValidBySchedule($now);
        }
        
        return true;
    }

    /**
     * Check if permission is valid according to schedule
     */
    protected function isValidBySchedule(Carbon $dateTime): bool
    {
        if (!$this->schedule) {
            return true;
        }
        
        $schedule = $this->schedule;
        
        // Check day of week
        if (isset($schedule['days'])) {
            $currentDay = strtolower($dateTime->format('l'));
            if (!in_array($currentDay, $schedule['days'])) {
                return false;
            }
        }
        
        // Check time range
        if (isset($schedule['hours'])) {
            $currentTime = $dateTime->format('H:i');
            $startTime = $schedule['hours']['start'] ?? '00:00';
            $endTime = $schedule['hours']['end'] ?? '23:59';
            
            if ($currentTime < $startTime || $currentTime > $endTime) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check if constraints are satisfied
     */
    public function constraintsSatisfied(array $context = []): bool
    {
        if (!$this->constraints) {
            return true;
        }
        
        foreach ($this->constraints as $constraint => $value) {
            if (!$this->checkConstraint($constraint, $value, $context)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check individual constraint
     */
    protected function checkConstraint(string $constraint, $value, array $context): bool
    {
        switch ($constraint) {
            case 'ip_whitelist':
                return $this->checkIpWhitelist($value, $context);
                
            case 'max_amount':
                return $this->checkMaxAmount($value, $context);
                
            case 'required_fields':
                return $this->checkRequiredFields($value, $context);
                
            default:
                // Custom constraint checking can be added here
                return true;
        }
    }

    /**
     * Check IP whitelist constraint
     */
    protected function checkIpWhitelist(array $whitelist, array $context): bool
    {
        $userIp = $context['ip'] ?? request()->ip();
        
        foreach ($whitelist as $allowedIp) {
            if ($this->ipInRange($userIp, $allowedIp)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if IP is in range (supports CIDR notation)
     */
    protected function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $mask) = explode('/', $range);
        return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
    }

    /**
     * Check maximum amount constraint
     */
    protected function checkMaxAmount($maxAmount, array $context): bool
    {
        $amount = $context['amount'] ?? 0;
        return $amount <= $maxAmount;
    }

    /**
     * Check required fields constraint
     */
    protected function checkRequiredFields(array $requiredFields, array $context): bool
    {
        foreach ($requiredFields as $field) {
            if (!isset($context[$field]) || empty($context[$field])) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get constraint description for UI
     */
    public function getConstraintDescription(): string
    {
        if (!$this->constraints) {
            return 'No constraints';
        }
        
        $descriptions = [];
        
        foreach ($this->constraints as $constraint => $value) {
            switch ($constraint) {
                case 'ip_whitelist':
                    $descriptions[] = 'IP restricted to: ' . implode(', ', $value);
                    break;
                    
                case 'max_amount':
                    $descriptions[] = 'Maximum amount: ' . number_format($value);
                    break;
                    
                case 'time_restriction':
                    $days = implode(', ', $value['days'] ?? []);
                    $hours = ($value['hours']['start'] ?? '') . ' - ' . ($value['hours']['end'] ?? '');
                    $descriptions[] = "Time restricted: {$days} {$hours}";
                    break;
                    
                default:
                    $descriptions[] = ucfirst($constraint) . ': ' . json_encode($value);
            }
        }
        
        return implode('; ', $descriptions);
    }

    /**
     * Get validity period description
     */
    public function getValidityDescription(): string
    {
        if (!$this->valid_from && !$this->valid_until) {
            return 'Always valid';
        }
        
        $from = $this->valid_from ? $this->valid_from->format('Y-m-d H:i') : 'Beginning';
        $until = $this->valid_until ? $this->valid_until->format('Y-m-d H:i') : 'Forever';
        
        return "Valid from {$from} until {$until}";
    }
}
