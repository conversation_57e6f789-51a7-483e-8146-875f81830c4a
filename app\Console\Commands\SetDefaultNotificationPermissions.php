<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\NotificationPermission;
use App\Models\NotificationTemplate;
use Illuminate\Console\Command;

class SetDefaultNotificationPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:set-defaults
                            {--force : Force update existing permissions}
                            {--user-type= : Only update specific user type (user, agent, merchant)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set default notification permissions for users who don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        $userType = $this->option('user-type');

        $this->info('Setting default notification permissions...');

        // Get users without notification permissions or force update
        $query = User::query();

        if ($userType) {
            $query->where('type', $userType);
        }

        if (!$force) {
            $query->whereDoesntHave('notifypermission');
        }

        $users = $query->get();

        if ($users->isEmpty()) {
            $this->info('No users found that need notification permissions.');
            return;
        }

        $this->info("Found {$users->count()} users to update.");

        $progressBar = $this->output->createProgressBar($users->count());
        $progressBar->start();

        $successCount = 0;
        $errorCount = 0;

        foreach ($users as $user) {
            try {
                // Delete existing permissions if force update
                if ($force && $user->notifypermission) {
                    $user->notifypermission->delete();
                }

                $this->createDefaultNotificationPermissions($user);
                $successCount++;
            } catch (\Exception $e) {
                $this->error("\nFailed to create permissions for user {$user->id}: " . $e->getMessage());
                $errorCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();

        $this->newLine(2);
        $this->info("Completed! Success: {$successCount}, Errors: {$errorCount}");
    }

    /**
     * Create default notification permissions for a user
     */
    protected function createDefaultNotificationPermissions($user)
    {
        // Default user type to 'user' if not set
        $userType = $user->type ?? 'user';

        // Define which template types are allowed for each user role
        $allowedTypes = match ($userType) {
            'user' => [1, 2, 5, 6], // All, User, User+Agent, User+Merchant
            'agent' => [1, 3, 5, 7], // All, Agent, User+Agent, Agent+Merchant
            'merchant' => [1, 4, 6, 7], // All, Merchant, User+Merchant, Agent+Merchant
            default => [1, 2, 5, 6], // Default to user permissions
        };

        // Get all notification templates for this user type that have email enabled
        $emailTemplates = NotificationTemplate::where('notify_for', 0)
            ->whereIn('type', $allowedTypes)
            ->where('email', 1) // Only templates with email enabled
            ->pluck('template_key')
            ->unique()
            ->values()
            ->toArray();

        // Create notification permission record with all available email templates enabled
        NotificationPermission::create([
            'notifyable_id' => $user->id,
            'notifyable_type' => User::class,
            'template_email_key' => $emailTemplates,
            'template_sms_key' => [], // SMS disabled by default
            'template_push_key' => [], // Push disabled by default
            'template_in_app_key' => [], // In-app disabled by default
        ]);
    }
}
