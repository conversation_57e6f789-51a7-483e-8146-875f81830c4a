<?php

namespace Tests\Unit;

use App\Services\WebhookSecurityService;
use App\Models\User;
use Illuminate\Http\Request;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WebhookSecurityServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_generate_signature_with_array_payload()
    {
        $payload = ['event' => 'test', 'data' => ['id' => 123]];
        $secretKey = 'test_secret_key';
        
        $signature = WebhookSecurityService::generateSignature($payload, $secretKey);
        
        $this->assertNotNull($signature);
        $this->assertStringStartsWith('sha256=', $signature);
        $this->assertEquals(71, strlen($signature)); // 'sha256=' (7) + 64 hex chars
    }

    public function test_generate_signature_with_string_payload()
    {
        $payload = '{"event":"test","data":{"id":123}}';
        $secretKey = 'test_secret_key';
        
        $signature = WebhookSecurityService::generateSignature($payload, $secretKey);
        
        $this->assertNotNull($signature);
        $this->assertStringStartsWith('sha256=', $signature);
    }

    public function test_verify_signature_success()
    {
        $payload = ['event' => 'test', 'data' => ['id' => 123]];
        $secretKey = 'test_secret_key';
        
        $signature = WebhookSecurityService::generateSignature($payload, $secretKey);
        $isValid = WebhookSecurityService::verifySignature($payload, $signature, $secretKey);
        
        $this->assertTrue($isValid);
    }

    public function test_verify_signature_failure()
    {
        $payload = ['event' => 'test', 'data' => ['id' => 123]];
        $secretKey = 'test_secret_key';
        $wrongSignature = 'sha256=invalid_signature';
        
        $isValid = WebhookSecurityService::verifySignature($payload, $wrongSignature, $secretKey);
        
        $this->assertFalse($isValid);
    }

    public function test_extract_signature_from_headers()
    {
        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_WEBHOOK_SIGNATURE' => 'sha256=test_signature'
        ]);
        
        $signature = WebhookSecurityService::extractSignatureFromHeaders($request);
        
        $this->assertEquals('sha256=test_signature', $signature);
    }

    public function test_extract_signature_from_alternative_headers()
    {
        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_HUB_SIGNATURE_256' => 'sha256=test_signature'
        ]);
        
        $signature = WebhookSecurityService::extractSignatureFromHeaders($request);
        
        $this->assertEquals('sha256=test_signature', $signature);
    }

    public function test_extract_signature_not_found()
    {
        $request = Request::create('/webhook', 'POST');
        
        $signature = WebhookSecurityService::extractSignatureFromHeaders($request);
        
        $this->assertNull($signature);
    }

    public function test_get_api_key_for_user()
    {
        $user = User::factory()->create([
            'secret_key' => 'test_secret_key'
        ]);
        
        $apiKey = WebhookSecurityService::getApiKeyForUser($user);
        
        $this->assertEquals('test_secret_key', $apiKey);
    }

    public function test_get_api_key_for_user_without_secret()
    {
        $user = User::factory()->create([
            'secret_key' => null
        ]);
        
        $apiKey = WebhookSecurityService::getApiKeyForUser($user);
        
        $this->assertNull($apiKey);
    }

    public function test_validate_incoming_webhook_success()
    {
        $payload = '{"event":"test","data":{"id":123}}';
        $secretKey = 'test_secret_key';
        $signature = WebhookSecurityService::generateSignature($payload, $secretKey);
        
        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_WEBHOOK_SIGNATURE' => $signature
        ], $payload);
        
        $isValid = WebhookSecurityService::validateIncomingWebhook($request, $secretKey);
        
        $this->assertTrue($isValid);
    }

    public function test_validate_incoming_webhook_failure()
    {
        $payload = '{"event":"test","data":{"id":123}}';
        $secretKey = 'test_secret_key';
        
        $request = Request::create('/webhook', 'POST', [], [], [], [
            'HTTP_X_WEBHOOK_SIGNATURE' => 'sha256=invalid_signature'
        ], $payload);
        
        $isValid = WebhookSecurityService::validateIncomingWebhook($request, $secretKey);
        
        $this->assertFalse($isValid);
    }

    public function test_validate_incoming_webhook_no_signature()
    {
        $payload = '{"event":"test","data":{"id":123}}';
        $secretKey = 'test_secret_key';
        
        $request = Request::create('/webhook', 'POST', [], [], [], [], $payload);
        
        $isValid = WebhookSecurityService::validateIncomingWebhook($request, $secretKey);
        
        $this->assertFalse($isValid);
    }

    public function test_signature_consistency()
    {
        $payload = ['event' => 'TRANSFER_NOTIFICATION', 'timestamp' => '2025-07-10T12:00:00.000Z'];
        $secretKey = 'test_secret_key';
        
        $signature1 = WebhookSecurityService::generateSignature($payload, $secretKey);
        $signature2 = WebhookSecurityService::generateSignature($payload, $secretKey);
        
        $this->assertEquals($signature1, $signature2);
    }
}
