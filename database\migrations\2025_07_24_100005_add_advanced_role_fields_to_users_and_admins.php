<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Adds advanced role system fields to existing users and admins tables.
     * These fields support the new advanced permission system without breaking existing functionality.
     */
    public function up(): void
    {
        // Add fields to users table
        Schema::table('users', function (Blueprint $table) {
            // Advanced role system integration
            $table->boolean('use_advanced_roles')->default(false)->after('type')
                ->comment('Whether to use advanced role system for this user');
            $table->json('advanced_role_cache')->nullable()->after('use_advanced_roles')
                ->comment('Cached permissions for performance');
            $table->timestamp('role_cache_updated_at')->nullable()->after('advanced_role_cache')
                ->comment('When role cache was last updated');
            
            // Permission override capabilities
            $table->json('permission_overrides')->nullable()->after('role_cache_updated_at')
                ->comment('User-specific permission overrides');
            $table->boolean('can_override_permissions')->default(false)->after('permission_overrides')
                ->comment('Whether user can have permission overrides');
        });

        // Add fields to admins table
        Schema::table('admins', function (Blueprint $table) {
            // Check if role_id column exists (it might be missing from migration)
            if (!Schema::hasColumn('admins', 'role_id')) {
                $table->unsignedBigInteger('role_id')->nullable()->after('admin_access')
                    ->comment('Reference to basic roles table (existing system)');
                $table->foreign('role_id')->references('id')->on('roles')->onDelete('set null');
            }
            
            // Advanced role system integration
            $table->boolean('use_advanced_roles')->default(false)->after('role_id')
                ->comment('Whether to use advanced role system for this admin');
            $table->json('advanced_role_cache')->nullable()->after('use_advanced_roles')
                ->comment('Cached permissions for performance');
            $table->timestamp('role_cache_updated_at')->nullable()->after('advanced_role_cache')
                ->comment('When role cache was last updated');
            
            // Permission override capabilities
            $table->json('permission_overrides')->nullable()->after('role_cache_updated_at')
                ->comment('Admin-specific permission overrides');
            $table->boolean('can_override_permissions')->default(true)->after('permission_overrides')
                ->comment('Whether admin can have permission overrides');
            
            // Super admin capabilities
            $table->boolean('is_super_admin')->default(false)->after('can_override_permissions')
                ->comment('Super admin with all permissions (bypasses role checks)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'use_advanced_roles',
                'advanced_role_cache',
                'role_cache_updated_at',
                'permission_overrides',
                'can_override_permissions'
            ]);
        });

        Schema::table('admins', function (Blueprint $table) {
            $table->dropColumn([
                'use_advanced_roles',
                'advanced_role_cache',
                'role_cache_updated_at',
                'permission_overrides',
                'can_override_permissions',
                'is_super_admin'
            ]);
            
            // Only drop role_id if we added it
            if (Schema::hasColumn('admins', 'role_id')) {
                $table->dropForeign(['role_id']);
                $table->dropColumn('role_id');
            }
        });
    }
};
