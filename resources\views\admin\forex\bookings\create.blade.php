@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.bookings.index') }}">
                                    @lang('Forex Bookings')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Create Booking')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Create Forex Booking')</h1>
                    <p class="page-header-text">@lang('Create a new forex trading booking for clients')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.bookings.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Bookings')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Current Rate Alert -->
        @if($activeRate)
            <div class="alert alert-soft-info mb-4" role="alert">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="bi-info-circle"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('Current Exchange Rates')</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>@lang('Buy Rates (NGN to USD)'):</strong></p>
                                <p class="mb-0">
                                    <strong>@lang('CBN'):</strong> ₦{{ number_format($activeRate->cbn_rate, 2) }} |
                                    <strong>@lang('Parallel'):</strong> ₦{{ number_format($activeRate->parallel_rate, 2) }} |
                                    <strong>@lang('Customer'):</strong> ₦{{ number_format($activeRate->parallel_rate + ($activeRate->parallel_rate * $activeRate->markup_percentage / 100), 2) }}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>@lang('Sell Rates (USD to NGN)'):</strong></p>
                                <p class="mb-0">
                                    <strong>@lang('CBN'):</strong> ₦{{ number_format($activeRate->cbn_sell_rate, 2) }} |
                                    <strong>@lang('Parallel'):</strong> ₦{{ number_format($activeRate->parallel_sell_rate, 2) }} |
                                    <strong>@lang('Customer'):</strong> ₦{{ number_format($activeRate->parallel_sell_rate + ($activeRate->parallel_sell_rate * $activeRate->sell_markup_percentage / 100), 2) }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="alert alert-warning mb-4" role="alert">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="bi-exclamation-triangle"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('No Active Rate')</h4>
                        <p class="mb-0">@lang('Please set an active exchange rate before creating bookings.')</p>
                        <hr>
                        <div class="d-flex">
                            <a href="{{ route('admin.forex.rates.create') }}" class="btn btn-warning btn-sm">
                                <i class="bi-plus-circle me-1"></i> @lang('Set Exchange Rate')
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Error Messages -->
        @if($errors->any())
            <div class="alert alert-danger mb-4" role="alert">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="bi-exclamation-triangle"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('Validation Error')</h4>
                        <ul class="mb-0">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        <!-- Success/Error Flash Messages -->
        @if(session('error'))
            <div class="alert alert-danger mb-4" role="alert">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="bi-exclamation-triangle"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('Error')</h4>
                        <p class="mb-0">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        @endif

        @if(session('success'))
            <div class="alert alert-success mb-4" role="alert">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="bi-check-circle"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('Success')</h4>
                        <p class="mb-0">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        @endif

        <div class="row justify-content-lg-center">
            <div class="col-lg-10">
                <!-- Card -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Booking Information')</h4>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <form action="{{ route('admin.forex.bookings.store') }}" method="POST">
                            @csrf

                            <!-- Hidden fields for payment method -->
                            <input type="hidden" name="payment_method" id="paymentMethodInput" value="account_details">
                            <input type="hidden" name="wallet_currency_id" id="walletCurrencyIdInput" value="">

                            <!-- Client Type -->
                            <div class="row mb-4">
                                <label for="clientTypeLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Client Type') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('client_type') is-invalid @enderror"
                                                name="client_type" id="clientTypeLabel" required
                                                data-hs-tom-select-options='{"placeholder": "@lang('Select client type')"}'>
                                            <option value="">@lang('Select client type')</option>
                                            <option value="user" {{ old('client_type') == 'user' ? 'selected' : '' }}>@lang('Registered User')</option>
                                            <option value="merchant" {{ old('client_type') == 'merchant' ? 'selected' : '' }}>@lang('Merchant')</option>
                                            <option value="external" {{ old('client_type') == 'external' ? 'selected' : '' }}>@lang('External Client')</option>
                                        </select>
                                    </div>
                                    @error('client_type')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Client Type -->

                            <!-- User Selection (for registered users/merchants) -->
                            <div class="row mb-4" id="userSelectionRow" style="display: none;">
                                <label for="userLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Select User') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('user_id') is-invalid @enderror"
                                                name="user_id" id="userLabel"
                                                data-hs-tom-select-options='{"placeholder": "@lang('Search and select user')"}'>
                                            <option value="">@lang('Search and select user')</option>
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}"
                                                        data-name="{{ $user->firstname }} {{ $user->lastname }}"
                                                        data-email="{{ $user->email }}"
                                                        {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->firstname }} {{ $user->lastname }} ({{ $user->email }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('user_id')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End User Selection -->

                            <!-- Client Name -->
                            <div class="row mb-4">
                                <label for="clientNameLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Client Name') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control @error('client_name') is-invalid @enderror"
                                           name="client_name" id="clientNameLabel"
                                           placeholder="@lang('Full name of the client')"
                                           value="{{ old('client_name') }}" required>
                                    @error('client_name')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Client Name -->

                            <!-- Client Email -->
                            <div class="row mb-4">
                                <label for="clientEmailLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Client Email') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input type="email" class="form-control @error('client_email') is-invalid @enderror"
                                           name="client_email" id="clientEmailLabel"
                                           placeholder="@lang('<EMAIL>')"
                                           value="{{ old('client_email') }}" required>
                                    @error('client_email')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Client Email -->

                            <!-- Client Phone -->
                            <div class="row mb-4">
                                <label for="clientPhoneLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Client Phone')
                                </label>
                                <div class="col-sm-9">
                                    <input type="tel" class="form-control @error('client_phone') is-invalid @enderror"
                                           name="client_phone" id="clientPhoneLabel"
                                           placeholder="@lang('+234 xxx xxx xxxx')"
                                           value="{{ old('client_phone') }}">
                                    @error('client_phone')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Client Phone -->

                            <!-- Transaction Type -->
                            <div class="row mb-4">
                                <label for="transactionTypeLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Transaction Type') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('transaction_type') is-invalid @enderror"
                                                name="transaction_type" id="transactionTypeLabel" required
                                                data-hs-tom-select-options='{"placeholder": "@lang('Select transaction type')"}'>
                                            <option value="">@lang('Select transaction type')</option>
                                            <option value="buying" {{ old('transaction_type') == 'buying' ? 'selected' : '' }}>@lang('Buying USD (Client pays NGN, receives USD)')</option>
                                            <option value="selling" {{ old('transaction_type') == 'selling' ? 'selected' : '' }}>@lang('Selling USD (Client pays USD, receives NGN)')</option>
                                        </select>
                                    </div>
                                    @error('transaction_type')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Transaction Type -->

                            <!-- Currency and Amount -->
                            <div class="row mb-4">
                                <label for="currencyLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Currency & Amount') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <select class="form-select @error('currency') is-invalid @enderror" name="currency" id="currencyLabel" required>
                                                <option value="">@lang('Currency')</option>
                                                <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>USD</option>
                                                {{-- <option value="NGN" {{ old('currency') == 'NGN' ? 'selected' : '' }}>NGN</option> --}}
                                            </select>
                                        </div>
                                        <div class="col-sm-8">
                                            <div class="input-group">
                                                <span class="input-group-text" id="currencySymbol">$</span>
                                                <input type="number" class="form-control @error('amount') is-invalid @enderror"
                                                       name="amount" id="amountLabel" step="0.01" min="0.01"
                                                       placeholder="@lang('0.00')" value="{{ old('amount') }}" required>
                                            </div>
                                        </div>
                                    </div>
                                    @error('currency')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    @error('amount')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Currency and Amount -->

                            <!-- Target Account -->
                            <div class="row mb-4">
                                <label for="targetAccountLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Target Account') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('target_account_id') is-invalid @enderror"
                                                name="target_account_id" id="targetAccountLabel" required
                                                data-hs-tom-select-options='{"placeholder": "@lang('Select target account')"}'>
                                            <option value="">@lang('Select target account')</option>
                                            @foreach($accounts as $account)
                                                <option value="{{ $account->id }}" {{ old('target_account_id') == $account->id ? 'selected' : '' }}>
                                                    {{ $account->account_name }} ({{ $account->currency_code }} - {{ number_format($account->balance, 2) }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('target_account_id')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Target Account -->

                            <!-- Payment Method Selection -->
                            <div class="row mb-4" id="paymentMethodRow" style="display: none;">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Payment Method') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div id="paymentMethodOptions">
                                        <!-- Payment method options will be populated by JavaScript -->
                                    </div>
                                    @error('payment_method')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Payment Method Selection -->

                            <!-- Account Details -->
                            <div class="row mb-4" id="accountDetailsRow">
                                <label for="accountDetailsLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Account Details')
                                </label>
                                <div class="col-sm-9">
                                    <textarea class="form-control @error('account_details') is-invalid @enderror"
                                              name="account_details" id="accountDetailsLabel" rows="3"
                                              placeholder="@lang('Bank account details, wallet address, or payment information')">{{ old('account_details') }}</textarea>
                                    @error('account_details')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Account Details -->

                            <!-- Payment Instructions -->
                            <div class="row mb-4">
                                <label for="paymentInstructionsLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Payment Instructions')
                                </label>
                                <div class="col-sm-9">
                                    <textarea class="form-control @error('payment_instructions') is-invalid @enderror"
                                              name="payment_instructions" id="paymentInstructionsLabel" rows="3"
                                              placeholder="@lang('Special instructions for payment processing')">{{ old('payment_instructions') }}</textarea>
                                    @error('payment_instructions')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Payment Instructions -->

                            <!-- Rate Calculation Preview -->
                            <div class="row mb-4" id="ratePreview" style="display: none;">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Rate Calculation')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-success" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-calculator"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div id="calculationDetails">
                                                    <!-- Calculation will be populated by JavaScript -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Rate Calculation Preview -->

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end">
                                <div class="d-flex gap-3">
                                    <a class="btn btn-white" href="{{ route('admin.forex.bookings.index') }}">
                                        @lang('Cancel')
                                    </a>
                                    <button type="submit" class="btn btn-primary" {{ !$activeRate ? 'disabled' : '' }}>
                                        <i class="bi-check-circle me-1"></i> @lang('Create Booking')
                                    </button>
                                </div>
                            </div>
                            <!-- End Submit Buttons -->
                        </form>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Card -->
            </div>
        </div>
    </div>
@endsection

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
@endpush

@push('js-lib')
    <script src="{{ asset('assets/admin/js/tom-select.complete.min.js') }}"></script>
@endpush

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            HSCore.components.HSTomSelect.init('.js-select');

            // Auto-select target account based on transaction type
            $('#transactionTypeLabel').on('change', function() {
                const transactionType = $(this).val();
                const targetAccountSelect = $('#targetAccountLabel');

                if (transactionType === 'buying') {
                    // NGN to USD: Auto-select USD account
                    targetAccountSelect.find('option').each(function() {
                        const optionText = $(this).text();
                        if (optionText.includes('USD') && optionText.includes('USD')) {
                            $(this).prop('selected', true);
                            targetAccountSelect.trigger('change');
                            return false; // Break the loop
                        }
                    });
                } else if (transactionType === 'selling') {
                    // USD to NGN: Auto-select CBN account
                    targetAccountSelect.find('option').each(function() {
                        const optionText = $(this).text();
                        if (optionText.includes('CBN') || optionText.toLowerCase().includes('central bank')) {
                            $(this).prop('selected', true);
                            targetAccountSelect.trigger('change');
                            return false; // Break the loop
                        }
                    });
                } else {
                    // Clear selection if no transaction type selected
                    targetAccountSelect.val('').trigger('change');
                }
            });

            const activeRate = @json($activeRate);

            // Handle client type change
            $('#clientTypeLabel').on('change', function() {
                const clientType = $(this).val();
                if (clientType === 'user' || clientType === 'merchant') {
                    $('#userSelectionRow').show();
                    $('#userLabel').prop('required', true);
                } else {
                    $('#userSelectionRow').hide();
                    $('#userLabel').prop('required', false);
                    $('#userLabel').val('');
                }
            });

            // Handle user selection
            $('#userLabel').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                if (selectedOption.val()) {
                    $('#clientNameLabel').val(selectedOption.data('name'));
                    $('#clientEmailLabel').val(selectedOption.data('email'));
                    loadWalletInfo();
                } else {
                    hidePaymentMethodOptions();
                }
            });

            // Handle currency change
            $('#currencyLabel').on('change', function() {
                const currency = $(this).val();
                const symbol = currency === 'USD' ? '$' : '₦';
                $('#currencySymbol').text(symbol);
                calculateRates();
            });

            // Handle amount change
            $('#amountLabel').on('input', calculateRates);
            $('#transactionTypeLabel').on('change', calculateRates);

            function calculateRates() {
                if (!activeRate) return;

                const currency = $('#currencyLabel').val();
                const amount = parseFloat($('#amountLabel').val()) || 0;
                const transactionType = $('#transactionTypeLabel').val();

                if (!currency || !amount || !transactionType) {
                    $('#ratePreview').hide();
                    return;
                }

                let calculationHtml = '';

                if (transactionType === 'buying') {
                    // NGN to USD: Use buy rates
                    const cbnRate = activeRate.cbn_rate;
                    const parallelRate = activeRate.parallel_rate;
                    const markup = activeRate.markup_percentage;
                    let pRate = parseFloat(parallelRate)
                    let mUp = parseFloat(markup)
                    let cRate = parseFloat(cbnRate)
                    const customerRate = parseFloat((pRate + (pRate * mUp / 100)));

                    if (currency === 'USD') {
                        const ngnAmount = amount * customerRate;
                        calculationHtml = `
                            <div class="row">
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Pays')</span>
                                    <span class="text-danger">₦${ngnAmount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Receives')</span>
                                    <span class="text-success">$${amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                            </div>
                            <small class="text-muted">
                                @lang('Buy Rate'): ₦${customerRate.toFixed(2)} per $1
                                (Parallel: ₦${pRate.toFixed(2)} + ${mUp.toFixed(2)}% markup)
                            </small>
                        `;
                    } else {
                        const usdAmount = amount / customerRate;
                        calculationHtml = `
                            <div class="row">
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Pays')</span>
                                    <span class="text-danger">₦${amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Receives')</span>
                                    <span class="text-success">$${usdAmount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                            </div>
                            <small class="text-muted">
                                @lang('Customer Rate'): ₦${customerRate.toFixed(2)} per $1
                                (Parallel: ₦${pRate.toFixed(2)} + ${mUp.toFixed(2)}% markup)
                            </small>
                        `;
                    }
                } else {
                    // USD to NGN: Use sell rates
                    const cbnSellRate = activeRate.cbn_sell_rate;
                    const parallelSellRate = activeRate.parallel_sell_rate;
                    const sellMarkup = activeRate.sell_markup_percentage;
                    let psRate = parseFloat(parallelSellRate)
                    let smUp = parseFloat(sellMarkup)
                    let csRate = parseFloat(cbnSellRate)
                    const customerSellRate = psRate + (psRate * smUp / 100);

                    if (currency === 'USD') {
                        const ngnAmount = amount * customerSellRate;
                        calculationHtml = `
                            <div class="row">
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Pays')</span>
                                    <span class="text-danger">$${amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Receives')</span>
                                    <span class="text-success">₦${ngnAmount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                            </div>
                            <small class="text-muted">
                                @lang('Sell Rate'): ₦${customerSellRate.toFixed(2)} per $1
                                (Parallel: ₦${psRate.toFixed(2)} + ${smUp.toFixed(2)}% markup)
                            </small>
                        `;
                    } else {
                        const usdAmount = amount / customerSellRate;
                        calculationHtml = `
                            <div class="row">
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Pays')</span>
                                    <span class="text-danger">₦${amount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                                <div class="col-sm-6">
                                    <span class="d-block fw-semibold">@lang('Client Receives')</span>
                                    <span class="text-success">$${usdAmount.toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
                                </div>
                            </div>
                            <small class="text-muted">
                                @lang('Sell Rate'): ₦${customerSellRate.toFixed(2)} per $1
                                (Parallel: ₦${psRate.toFixed(2)} + ${smUp.toFixed(2)}% markup)
                            </small>
                        `;
                    }
                }

                $('#calculationDetails').html(calculationHtml);
                $('#ratePreview').show();
            }

            // Handle transaction type change to reload wallet info
            $('#transactionTypeLabel').on('change', function() {
                if ($('#userLabel').val()) {
                    loadWalletInfo();
                }
            });

            // Load wallet information for selected user
            function loadWalletInfo() {
                const userId = $('#userLabel').val();
                const transactionType = $('#transactionTypeLabel').val();
                const clientType = $('#clientTypeLabel').val();

                if (!userId || !transactionType || (clientType !== 'user' && clientType !== 'merchant')) {
                    hidePaymentMethodOptions();
                    return;
                }

                $.ajax({
                    url: '{{ route("admin.forex.bookings.user.wallet.info") }}',
                    method: 'GET',
                    data: {
                        user_id: userId,
                        transaction_type: transactionType
                    },
                    success: function(response) {
                        if (response.success) {
                            showPaymentMethodOptions(response.data);
                        } else {
                            hidePaymentMethodOptions();
                        }
                    },
                    error: function() {
                        hidePaymentMethodOptions();
                    }
                });
            }

            // Show payment method options
            function showPaymentMethodOptions(data) {
                let optionsHtml = '';

                Object.keys(data.payment_methods).forEach(function(method) {
                    const option = data.payment_methods[method];
                    const isWallet = method === 'wallet';
                    const checked = method === 'account_details' ? 'checked' : '';

                    optionsHtml += `
                        <div class="form-check mb-2">
                            <input class="form-check-input payment-method-radio" type="radio"
                                   name="payment_method_display" id="payment_${method}"
                                   value="${method}" ${checked}
                                   data-currency-id="${isWallet ? data.currency_id : ''}">
                            <label class="form-check-label" for="payment_${method}">
                                <strong>${option.label}</strong>
                                <br><small class="text-muted">${option.description}</small>
                            </label>
                        </div>
                    `;
                });

                $('#paymentMethodOptions').html(optionsHtml);
                $('#paymentMethodRow').show();

                // Handle payment method selection
                $('.payment-method-radio').on('change', function() {
                    const selectedMethod = $(this).val();
                    const currencyId = $(this).data('currency-id');

                    $('#paymentMethodInput').val(selectedMethod);
                    $('#walletCurrencyIdInput').val(currencyId || '');

                    if (selectedMethod === 'wallet') {
                        $('#accountDetailsRow').hide();
                        $('#accountDetailsLabel').prop('required', false);
                    } else {
                        $('#accountDetailsRow').show();
                        $('#accountDetailsLabel').prop('required', false); // Keep optional
                    }
                });

                // Trigger initial selection
                $('.payment-method-radio:checked').trigger('change');
            }

            // Hide payment method options
            function hidePaymentMethodOptions() {
                $('#paymentMethodRow').hide();
                $('#paymentMethodInput').val('account_details');
                $('#walletCurrencyIdInput').val('');
                $('#accountDetailsRow').show();
                $('#accountDetailsLabel').prop('required', false);
            }

            // Initial setup
            $('#clientTypeLabel').trigger('change');
        });
    </script>
@endpush
