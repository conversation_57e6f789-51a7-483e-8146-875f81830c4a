@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Balance Summary')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Balance Summary & Reports')</h1>
                    <p class="page-header-text">@lang('Comprehensive balance tracking and account analytics')</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Accounts')
                        </a>
                        <button class="btn btn-outline-success" id="exportReport">
                            <i class="bi-download me-1"></i> @lang('Export Report')
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Date Range Filter -->
        <div class="card mb-3 mb-lg-5">
            <div class="card-body">
                <form method="GET" class="row align-items-end">
                    <div class="col-sm-4">
                        <label for="dateFrom" class="form-label">@lang('Date From')</label>
                        <input type="date" class="form-control" id="dateFrom" name="date_from" value="{{ $dateFrom }}">
                    </div>
                    <div class="col-sm-4">
                        <label for="dateTo" class="form-label">@lang('Date To')</label>
                        <input type="date" class="form-control" id="dateTo" name="date_to" value="{{ $dateTo }}">
                    </div>
                    <div class="col-sm-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi-funnel me-1"></i> @lang('Update Report')
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- End Date Range Filter -->

        <!-- Current Balance Summary -->
        <div class="row mb-3 mb-lg-5">
            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('USD Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($currentSummary['available_usd'], 2) }}
                            </span>
                        </div>
                        @if($currentSummary['pending_usd'] > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($currentSummary['pending_usd'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($currentSummary['total_usd'], 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('EUR Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($currentSummary['available_eur'], 2) }}
                            </span>
                        </div>
                        @if($currentSummary['pending_eur'] > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($currentSummary['pending_eur'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($currentSummary['total_eur'], 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('GBP Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($currentSummary['available_gbp'], 2) }}
                            </span>
                        </div>
                        @if($currentSummary['pending_gbp'] > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($currentSummary['pending_gbp'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($currentSummary['total_gbp'], 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('NGN Balances')</h6>
                        <div class="mb-2">
                            <span class="d-block text-muted small">@lang('Available')</span>
                            <span class="js-counter display-6 text-success">
                                {{ number_format($currentSummary['available_ngn'], 2) }}
                            </span>
                        </div>
                        @if($currentSummary['pending_ngn'] > 0)
                            <div class="mb-2">
                                <span class="d-block text-muted small">@lang('Pending')</span>
                                <span class="text-warning fw-bold">
                                    {{ number_format($currentSummary['pending_ngn'], 2) }}
                                </span>
                            </div>
                        @endif
                        <div>
                            <span class="d-block text-muted small">@lang('Total')</span>
                            <span class="text-primary fw-bold">
                                {{ number_format($currentSummary['total_ngn'], 2) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Current Balance Summary -->

        <!-- Charts Row -->
        <div class="row mb-3 mb-lg-5">
            <div class="col-lg-8 mb-3 mb-lg-0">
                <!-- Historical Balance Trends -->
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Historical Balance Trends')</h4>
                    </div>
                    <div class="card-body">
                        <canvas id="balanceTrendsChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <!-- Transaction Volume -->
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transaction Types')</h4>
                    </div>
                    <div class="card-body">
                        <canvas id="transactionTypesChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Charts Row -->

        <!-- Account Type Breakdown -->
        <div class="card mb-3 mb-lg-5">
            <div class="card-header">
                <h4 class="card-header-title">@lang('Account Type Breakdown')</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                        <thead class="thead-light">
                        <tr>
                            <th>@lang('Account Type')</th>
                            <th>@lang('Currency')</th>
                            <th>@lang('Count')</th>
                            <th>@lang('Total Balance')</th>
                            <th>@lang('Pending Balance')</th>
                            <th>@lang('Available Balance')</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($accountTypeBreakdown as $breakdown)
                            <tr>
                                <td>{{ ucfirst($breakdown->account_type) }}</td>
                                <td>
                                    <span class="badge bg-soft-primary text-primary">{{ $breakdown->currency_code }}</span>
                                </td>
                                <td>{{ $breakdown->count }}</td>
                                <td class="fw-bold">{{ number_format($breakdown->total_balance, 2) }} {{ $breakdown->currency_code }}</td>
                                <td class="text-warning">{{ number_format($breakdown->total_pending, 2) }} {{ $breakdown->currency_code }}</td>
                                <td class="text-success">{{ number_format($breakdown->total_balance - $breakdown->total_pending, 2) }} {{ $breakdown->currency_code }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- End Account Type Breakdown -->

        <!-- Account Utilization -->
        <div class="card mb-3 mb-lg-5">
            <div class="card-header">
                <h4 class="card-header-title">@lang('Account Utilization Metrics')</h4>
                <small class="text-muted">@lang('Activity and utilization rates for the selected period')</small>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                        <thead class="thead-light">
                        <tr>
                            <th>@lang('Account')</th>
                            <th>@lang('Current Balance')</th>
                            <th>@lang('Transaction Count')</th>
                            <th>@lang('Transaction Volume')</th>
                            <th>@lang('Utilization Rate')</th>
                            <th>@lang('Last Activity')</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($utilizationMetrics as $metric)
                            <tr>
                                <td>
                                    <a href="{{ route('admin.forex.accounts.show', $metric['account']->id) }}">
                                        {{ $metric['account']->account_name }}
                                    </a>
                                    <small class="d-block text-muted">{{ $metric['account']->account_type }}</small>
                                </td>
                                <td class="fw-bold">{{ $metric['account']->formatted_balance }}</td>
                                <td>{{ number_format($metric['transaction_count']) }}</td>
                                <td>{{ number_format($metric['transaction_volume'], 2) }} {{ $metric['account']->currency_code }}</td>
                                <td>
                                    @php
                                        $rate = $metric['utilization_rate'];
                                        $class = $rate > 50 ? 'success' : ($rate > 20 ? 'warning' : 'secondary');
                                    @endphp
                                    <span class="badge bg-{{ $class }}">{{ number_format($rate, 1) }}%</span>
                                </td>
                                <td>
                                    @if($metric['last_activity'])
                                        {{ $metric['last_activity']->format('M d, Y H:i') }}
                                    @else
                                        <span class="text-muted">@lang('No activity')</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- End Account Utilization -->

        @if($reconciliation['accounts_with_discrepancies'] || $reconciliation['accounts_requiring_attention']->count() > 0)
            <!-- Reconciliation Alerts -->
            <div class="card mb-3 mb-lg-5">
                <div class="card-header">
                    <h4 class="card-header-title text-warning">
                        <i class="bi-exclamation-triangle me-1"></i> @lang('Accounts Requiring Attention')
                    </h4>
                </div>
                <div class="card-body">
                    @if($reconciliation['accounts_with_discrepancies'])
                        <div class="alert alert-warning">
                            <h6>@lang('Balance Discrepancies Found')</h6>
                            @foreach($reconciliation['accounts_with_discrepancies'] as $discrepancy)
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <strong>{{ $discrepancy['account']->account_name }}</strong>
                                        <small class="d-block text-muted">{{ $discrepancy['account']->account_type }}</small>
                                    </div>
                                    <div class="text-end">
                                        <div>@lang('Recorded'): {{ number_format($discrepancy['recorded_balance'], 2) }}</div>
                                        <div>@lang('Calculated'): {{ number_format($discrepancy['calculated_balance'], 2) }}</div>
                                        <div class="text-danger">@lang('Difference'): {{ number_format($discrepancy['discrepancy'], 2) }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    @if($reconciliation['accounts_requiring_attention']->count() > 0)
                        <div class="alert alert-info">
                            <h6>@lang('Accounts with Pending Balances or Negative Balances')</h6>
                            @foreach($reconciliation['accounts_requiring_attention'] as $account)
                                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                    <div>
                                        <a href="{{ route('admin.forex.accounts.show', $account->id) }}">
                                            {{ $account->account_name }}
                                        </a>
                                        <small class="d-block text-muted">{{ $account->account_type }}</small>
                                    </div>
                                    <div class="text-end">
                                        <div>@lang('Balance'): {{ $account->formatted_balance }}</div>
                                        @if($account->pending_balance > 0)
                                            <div class="text-warning">@lang('Pending'): {{ number_format($account->pending_balance, 2) }}</div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
            <!-- End Reconciliation Alerts -->
        @endif
    </div>
@endsection

@push('css-lib')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endpush

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Initialize counters
            HSCore.components.HSCounter.init('.js-counter');

            // Historical Balance Trends Chart
            const balanceTrendsCtx = document.getElementById('balanceTrendsChart').getContext('2d');
            const historicalData = @json($historicalData);

            new Chart(balanceTrendsCtx, {
                type: 'line',
                data: {
                    labels: historicalData.map(d => d.date),
                    datasets: [
                        {
                            label: 'USD',
                            data: historicalData.map(d => d.usd_balance),
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.1)',
                            tension: 0.1
                        },
                        {
                            label: 'EUR',
                            data: historicalData.map(d => d.eur_balance),
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.1)',
                            tension: 0.1
                        },
                        {
                            label: 'GBP',
                            data: historicalData.map(d => d.gbp_balance),
                            borderColor: 'rgb(54, 162, 235)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            tension: 0.1
                        },
                        {
                            label: 'NGN',
                            data: historicalData.map(d => d.ngn_balance),
                            borderColor: 'rgb(255, 205, 86)',
                            backgroundColor: 'rgba(255, 205, 86, 0.1)',
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat().format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + new Intl.NumberFormat().format(context.parsed.y);
                                }
                            }
                        }
                    }
                }
            });

            // Transaction Types Chart
            const transactionTypesCtx = document.getElementById('transactionTypesChart').getContext('2d');
            const transactionTypes = @json($transactionAnalysis['type_breakdown']);

            new Chart(transactionTypesCtx, {
                type: 'doughnut',
                data: {
                    labels: transactionTypes.map(t => t.transaction_type.replace('_', ' ').toUpperCase()),
                    datasets: [{
                        data: transactionTypes.map(t => t.count),
                        backgroundColor: [
                            'rgb(75, 192, 192)',
                            'rgb(255, 99, 132)',
                            'rgb(54, 162, 235)',
                            'rgb(255, 205, 86)',
                            'rgb(153, 102, 255)',
                            'rgb(255, 159, 64)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Export functionality
            $('#exportReport').on('click', function() {
                window.print();
            });
        });
    </script>
@endpush
