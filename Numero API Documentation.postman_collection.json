{"info": {"_postman_id": "d8febec0-6776-405d-b723-c38667f4892e", "name": "Numero API Documentation", "description": "## Authentication\n\nAll endpoints require an API key to be sent in the request headers.\n\nHeader Name: x-api-key\n\nHeader Value: Your API Key\n\n## Signature Generation\n\nPost request endpoints require a signature to be sent in the request headers.\n\nHeader Name: x-signature-key\n\nHeader Value: Your Request Signature\n\nFor endpoints requiring signature validation, follow these steps:1. Serialize your request body to JSON (using camelCase)\n\nGenerate an HMAC-SHA256 hash of the JSON string using your Public API Key\n\nConvert the hash to Base64 string\n\nAdd the signature to the request headers\n\n## **Webhook Notification**\n\nThis guide explains how to integrate with our webhook system to receive real-time notifications about events occurring within the Numero platform.\n\nWebhooks allow your application to receive push notifications when specific events happen, eliminating the need to poll our API for updates. This results in more efficient integrations and real-time data synchronization.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://www.postman.com/getnumero/numero-developer-documentation/collection/g1jfzp0/numero-api-documentation?action=share&source=collection_link&creator=********"}, "item": [{"name": "Generate Signature", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"narration\": \"test\",\r\n  \"amount\": 100,\r\n  \"destinationAccountNumber\": \"**********\",\r\n  \"destinationBankCode\": \"000016\",\r\n  \"destinationAccountName\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n  \"phoneNumber\": \"***********\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/generatesignature?publickey={{publicKey}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "generatesignature"], "query": [{"key": "publickey", "value": "{{publicKey}}"}]}}, "response": [{"name": "Generate Signature", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"narration\": \"test\",\r\n  \"amount\": 100,\r\n  \"destinationAccountNumber\": \"**********\",\r\n  \"destinationBankCode\": \"000016\",\r\n  \"destinationAccountName\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n  \"phoneNumber\": \"***********\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/generatesignature?publickey={{publicKey}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "generatesignature"], "query": [{"key": "publickey", "value": "{{publicKey}}"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "plain", "header": [{"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Content-Type", "value": "text/plain; charset=utf-8"}, {"key": "Server", "value": "Microsoft-IIS/10.0"}, {"key": "api-supported-versions", "value": "1.0"}, {"key": "Date", "value": "Mon, 03 Mar 2025 13:39:17 GMT"}], "cookie": [], "body": "4KkwgCUsJ5mDSYsdpK4YKHEfXXboTw0yw4rnV22ReX8="}]}, {"name": "Validate Account Number", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountNumber\": \"string\",\r\n  \"bankCode\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/validate", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "validate"]}}, "response": [{"name": "Validate Account Number", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountNumber\": \"string\",\r\n  \"bankCode\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api-dev.getnumero.co/numeroaccount/api/v1/business/validate", "protocol": "https", "host": ["api-dev", "getnumero", "co"], "path": ["numeroaccount", "api", "v1", "business", "validate"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"accountName\": \"string\",\r\n    \"accountNumber\": \"string\",\r\n    \"bankCode\": \"string\"\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Initiate Single Transfer", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"narration\": \"test\",\r\n  \"amount\": 100,\r\n  \"destinationAccountNumber\": \"**********\",\r\n  \"destinationBankCode\": \"000016\",\r\n  \"destinationAccountName\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n  \"phoneNumber\": \"***********\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/single", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "single"]}}, "response": [{"name": "Initiate Single Transfer", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"narration\": \"string\",\r\n  \"amount\": 0,\r\n  \"destinationAccountNumber\": \"string\",\r\n  \"destinationBankCode\": \"string\",\r\n  \"destinationAccountName\": \"string\",\r\n  \"phoneNumber\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api-dev.getnumero.co/numeroaccount/api/v1/business/single", "protocol": "https", "host": ["api-dev", "getnumero", "co"], "path": ["numeroaccount", "api", "v1", "business", "single"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"transferReference\": [\r\n      \"string\"\r\n    ]\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Initiate Bulk Transfer", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"transferRequest\": [\r\n    {\r\n      \"narration\": \"string\",\r\n      \"amount\": 0,\r\n      \"destinationAccountNumber\": \"string\",\r\n      \"destinationBankCode\": \"string\",\r\n      \"destinationAccountName\": \"string\"\r\n    }\r\n  ],\r\n  \"phoneNumber\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/bulk", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "bulk"]}}, "response": [{"name": "Initiate Bulk Transfer", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"transferRequest\": [\r\n    {\r\n      \"narration\": \"string\",\r\n      \"amount\": 0,\r\n      \"destinationAccountNumber\": \"string\",\r\n      \"destinationBankCode\": \"string\",\r\n      \"destinationAccountName\": \"string\"\r\n    }\r\n  ],\r\n  \"phoneNumber\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api-dev.getnumero.co/numeroaccount/api/v1/business/bulk", "protocol": "https", "host": ["api-dev", "getnumero", "co"], "path": ["numeroaccount", "api", "v1", "business", "bulk"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"transferReference\": [\r\n      \"string\"\r\n    ]\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Create Virtual Account Customer", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"firstName\": \"string\",\r\n  \"lastName\": \"string\",\r\n  \"email\": \"string\",\r\n  \"mobileNumber\": \"string\",\r\n  \"bvn\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/customer", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "customer"]}}, "response": [{"name": "Create Virtual Account Customer", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"transferRequest\": [\r\n    {\r\n      \"narration\": \"string\",\r\n      \"amount\": 0,\r\n      \"destinationAccountNumber\": \"string\",\r\n      \"destinationBankCode\": \"string\",\r\n      \"destinationAccountName\": \"string\"\r\n    }\r\n  ],\r\n  \"phoneNumber\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/bulk", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "bulk"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"reference\": \"string\",\r\n    \"accountName\": \"string\",\r\n    \"accountNumber\": \"string\",\r\n    \"bankName\": \"string\"\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Create Virtual Account Business", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"businessName\": \"string\",\r\n  \"rcNumber\": \"string\",\r\n  \"tin\": \"string\",\r\n  \"address\": \"string\",\r\n  \"firstName\": \"string\",\r\n  \"lastName\": \"string\",\r\n  \"email\": \"string\",\r\n  \"mobileNumber\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/business", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "business"]}}, "response": [{"name": "Create Virtual Account Business", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"transferRequest\": [\r\n    {\r\n      \"narration\": \"string\",\r\n      \"amount\": 0,\r\n      \"destinationAccountNumber\": \"string\",\r\n      \"destinationBankCode\": \"string\",\r\n      \"destinationAccountName\": \"string\"\r\n    }\r\n  ],\r\n  \"phoneNumber\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/business", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "business"]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"reference\": \"string\",\r\n    \"accountName\": \"string\",\r\n    \"accountNumber\": \"string\",\r\n    \"bankName\": \"string\"\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Transfer Status", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/business/status?reference=[Transaction Reference]", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "status"], "query": [{"key": "reference", "value": "[Transaction Reference]"}]}}, "response": [{"name": "Transfer Status", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "", "type": "text"}, {"key": "x-signature-key", "value": "", "type": "text"}], "url": {"raw": "https://api-dev.getnumero.co/numeroaccount/api/v1/business/status?reference=[Transaction Reference]", "protocol": "https", "host": ["api-dev", "getnumero", "co"], "path": ["numeroaccount", "api", "v1", "business", "status"], "query": [{"key": "reference", "value": "[Transaction Reference]"}]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"transactionRecord\": {\r\n      \"userId\": \"string\",\r\n      \"userRole\": \"string\",\r\n      \"userName\": \"string\",\r\n      \"trxAmount\": 0,\r\n      \"trxFee\": 0,\r\n      \"settledAmount\": 0,\r\n      \"userBusinessName\": \"string\",\r\n      \"businessCode\": \"string\",\r\n      \"reference\": \"string\",\r\n      \"type\": \"string\",\r\n      \"status\": \"string\",\r\n      \"channel\": \"string\",\r\n      \"postingType\": \"string\",\r\n      \"description\": \"string\",\r\n      \"service\": \"string\",\r\n      \"requestState\": \"string\",\r\n      \"requestStateDetails\": \"string\",\r\n      \"currency\": \"string\",\r\n      \"sessionId\": \"string\",\r\n      \"stan\": \"string\",\r\n      \"receiverBankCode\": \"string\",\r\n      \"receiverBankName\": \"string\",\r\n      \"receiverAccountNumber\": \"string\",\r\n      \"receiverAccountName\": \"string\"\r\n    }\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Get Transactions", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/business/transaction?startDate&endDate&searchBy&pageNumber&pageSize", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "transaction"], "query": [{"key": "startDate", "value": null}, {"key": "endDate", "value": null}, {"key": "searchBy", "value": null}, {"key": "pageNumber", "value": null}, {"key": "pageSize", "value": null}]}}, "response": [{"name": "Get Transactions", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "", "type": "text"}], "url": {"raw": "https://api-dev.getnumero.co/numeroaccount/api/v1/business/transaction?startDate&endDate&searchBy&pageNumber&pageSize", "protocol": "https", "host": ["api-dev", "getnumero", "co"], "path": ["numeroaccount", "api", "v1", "business", "transaction"], "query": [{"key": "startDate", "value": null}, {"key": "endDate", "value": null}, {"key": "searchBy", "value": null}, {"key": "pageNumber", "value": null}, {"key": "pageSize", "value": null}]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"transactions\": [\r\n      {\r\n        \"trxAmount\": 0,\r\n        \"trxFee\": 0,\r\n        \"settledAmount\": 0,\r\n        \"merchant\": \"string\",\r\n        \"businessCode\": \"string\",\r\n        \"reference\": \"string\",\r\n        \"sessionId\": \"string\",\r\n        \"vendorReference\": \"string\",\r\n        \"type\": \"string\",\r\n        \"status\": \"string\",\r\n        \"channel\": \"string\",\r\n        \"postingType\": \"string\",\r\n        \"description\": \"string\",\r\n        \"service\": \"string\",\r\n        \"requestState\": \"string\",\r\n        \"requestStateDetails\": \"string\",\r\n        \"beneficiaryName\": \"string\",\r\n        \"beneficiaryNumber\": \"string\",\r\n        \"beneficiaryBank\": \"string\",\r\n        \"dateCreated\": \"2024-12-10T18:56:32.433Z\",\r\n        \"dateModified\": \"2024-12-10T18:56:32.433Z\"\r\n      }\r\n    ],\r\n    \"pageNumber\": 0,\r\n    \"pageSize\": 0,\r\n    \"totalCount\": 0\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Get Virtual Account", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/reference?reference=[REFERENCE]", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "reference"], "query": [{"key": "reference", "value": "[REFERENCE]"}]}}, "response": [{"name": "Get Virtual Account", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/reference?reference=[REFERENCE]", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "reference"], "query": [{"key": "reference", "value": "[REFERENCE]"}]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"reference\": \"string\",\r\n    \"accountName\": \"string\",\r\n    \"accountNumber\": \"string\",\r\n    \"bankName\": \"string\",\r\n    \"details\": {\r\n      \"firstName\": \"string\",\r\n      \"lastName\": \"string\",\r\n      \"email\": \"string\",\r\n      \"phone\": \"string\",\r\n      \"category\": \"string\",\r\n      \"businessName\": \"string\",\r\n      \"tin\": \"string\",\r\n      \"rcNumber\": \"string\",\r\n      \"type\": \"string\"\r\n    }\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}, {"name": "Get All Virtual Accounts", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/all?startDate=[DATE]&endDate=[DATE]&searchBy=[SEARCH]&pageNumber=1&pageSize=50", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "all"], "query": [{"key": "startDate", "value": "[DATE]"}, {"key": "endDate", "value": "[DATE]"}, {"key": "searchBy", "value": "[SEARCH]"}, {"key": "pageNumber", "value": "1"}, {"key": "pageSize", "value": "50"}]}}, "response": [{"name": "Get All Virtual Accounts", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/business/virtualaccount/all?startDate=[DATE]&endDate=[DATE]&searchBy=[SEARCH]&pageNumber=1&pageSize=50", "host": ["{{baseUrl}}"], "path": ["api", "v1", "business", "virtualaccount", "all"], "query": [{"key": "startDate", "value": "[DATE]"}, {"key": "endDate", "value": "[DATE]"}, {"key": "searchBy", "value": "[SEARCH]"}, {"key": "pageNumber", "value": "1"}, {"key": "pageSize", "value": "50"}]}}, "_postman_previewlanguage": "Text", "header": [], "cookie": [], "body": "{\r\n  \"status\": true,\r\n  \"message\": \"string\",\r\n  \"code\": \"string\",\r\n  \"version\": \"string\",\r\n  \"reference\": \"string\",\r\n  \"data\": {\r\n    \"accounts\": [\r\n      {\r\n        \"reference\": \"string\",\r\n        \"accountName\": \"string\",\r\n        \"accountNumber\": \"string\",\r\n        \"bankName\": \"string\",\r\n        \"details\": {\r\n          \"firstName\": \"string\",\r\n          \"lastName\": \"string\",\r\n          \"email\": \"string\",\r\n          \"phone\": \"string\",\r\n          \"category\": \"string\",\r\n          \"businessName\": \"string\",\r\n          \"tin\": \"string\",\r\n          \"rcNumber\": \"string\",\r\n          \"type\": \"string\"\r\n        }\r\n      }\r\n    ],\r\n    \"pageNumber\": 0,\r\n    \"pageSize\": 0,\r\n    \"totalCount\": 0\r\n  },\r\n  \"error\": {\r\n    \"message\": \"string\"\r\n  }\r\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "https://api-dev.getnumero.co/numeroaccount", "type": "string"}]}