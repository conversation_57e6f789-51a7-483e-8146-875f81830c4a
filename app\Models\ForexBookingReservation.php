<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForexBookingReservation extends Model
{
    use HasFactory;

    protected $fillable = [
        'forex_booking_id',
        'forex_account_id',
        'reserved_amount',
        'status',
        'forex_transaction_id',
        'metadata'
    ];

    protected $casts = [
        'reserved_amount' => 'decimal:8',
        'metadata' => 'array',
    ];

    // Relationships
    public function booking()
    {
        return $this->belongsTo(ForexBooking::class, 'forex_booking_id');
    }

    public function account()
    {
        return $this->belongsTo(ForexAccount::class, 'forex_account_id');
    }

    public function transaction()
    {
        return $this->belongsTo(ForexTransaction::class, 'forex_transaction_id');
    }

    // Scopes
    public function scopeReserved($query)
    {
        return $query->where('status', 'reserved');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    public function scopeForBooking($query, $bookingId)
    {
        return $query->where('forex_booking_id', $bookingId);
    }

    public function scopeForAccount($query, $accountId)
    {
        return $query->where('forex_account_id', $accountId);
    }

    // Accessors
    public function getFormattedReservedAmountAttribute()
    {
        return number_format($this->reserved_amount, 2) . ' ' . $this->account->currency_code;
    }

    public function getStatusClassAttribute()
    {
        return [
            'reserved' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    // Business Logic Methods
    public function complete()
    {
        $this->update(['status' => 'completed']);
    }

    public function cancel()
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Get total reserved amount for a booking across all accounts
     */
    public static function getTotalReservedForBooking($bookingId, $status = 'reserved')
    {
        return static::forBooking($bookingId)
            ->where('status', $status)
            ->sum('reserved_amount');
    }

    /**
     * Get reservation breakdown for a booking
     */
    public static function getReservationBreakdown($bookingId)
    {
        return static::with(['account'])
            ->forBooking($bookingId)
            ->get()
            ->groupBy('status')
            ->map(function ($reservations) {
                return $reservations->map(function ($reservation) {
                    return [
                        'account_name' => $reservation->account->account_name,
                        'account_type' => $reservation->account->account_type,
                        'currency' => $reservation->account->currency_code,
                        'amount' => $reservation->reserved_amount,
                        'formatted_amount' => $reservation->formatted_reserved_amount,
                        'status' => $reservation->status,
                    ];
                });
            });
    }
}
