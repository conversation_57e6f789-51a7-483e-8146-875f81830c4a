<?php

namespace App\Services;

use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Models\ForexTransaction;
use App\Models\ForexRate;
use App\Models\OperationalCost;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ForexDashboardService
{
    /**
     * Get comprehensive dashboard data
     */
    public function getDashboardData(): array
    {
        return Cache::remember('forex_dashboard_data', 300, function () {
            return [
                'account_balances' => $this->getAccountBalances(),
                'booking_stats' => $this->getBookingStats('today'),
                'weekly_stats' => $this->getBookingStats('week'),
                'monthly_stats' => $this->getBookingStats('month'),
                'operational_costs' => $this->getOperationalCostSummary('week'),
                'recent_activity' => $this->getRecentActivity(),
                'active_rate' => ForexRate::getActiveRate(),
                'pending_bookings' => ForexBooking::pending()->count(),
                'total_accounts' => ForexAccount::active()->count(),
            ];
        });
    }

    /**
     * Get account balances with pending amounts
     */
    public function getAccountBalances(): array
    {
        return Cache::remember('forex_account_balances', 300, function () {
            $accounts = ForexAccount::active()->get();
            
            $balances = [];
            foreach ($accounts as $account) {
                $balances[$account->account_type] = [
                    'id' => $account->id,
                    'name' => $account->account_name,
                    'type' => $account->account_type,
                    'currency' => $account->currency_code,
                    'balance' => $account->balance,
                    'pending_balance' => $account->pending_balance,
                    'available_balance' => $account->available_balance,
                    'formatted_balance' => $account->formatted_balance,
                    'formatted_available' => $account->formatted_available_balance,
                ];
            }

            // Calculate totals
            $balances['totals'] = [
                'usd_total' => $accounts->where('account_type', 'USD')->sum('balance'),
                'ngn_total' => $accounts->whereIn('account_type', ['CBN', 'Difference', 'Investment'])->sum('balance'),
                'usd_pending' => $accounts->where('account_type', 'USD')->sum('pending_balance'),
                'ngn_pending' => $accounts->whereIn('account_type', ['CBN', 'Difference', 'Investment'])->sum('pending_balance'),
            ];

            return $balances;
        });
    }

    /**
     * Get booking statistics for a period
     */
    public function getBookingStats(string $period): array
    {
        $cacheKey = "forex_booking_stats_{$period}";
        
        return Cache::remember($cacheKey, 300, function () use ($period) {
            $query = ForexBooking::query();
            
            switch ($period) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', now()->month)
                          ->whereYear('created_at', now()->year);
                    break;
            }

            $bookings = $query->get();
            $completed = $bookings->where('status', 'completed');

            return [
                'total_bookings' => $bookings->count(),
                'completed_bookings' => $completed->count(),
                'pending_bookings' => $bookings->where('status', 'pending')->count(),
                'cancelled_bookings' => $bookings->where('status', 'cancelled')->count(),
                'completion_rate' => $bookings->count() > 0 ? 
                    round(($completed->count() / $bookings->count()) * 100, 2) : 0,
                'total_usd_volume' => $bookings->where('currency', 'USD')->sum('amount'),
                'total_ngn_volume' => $bookings->where('currency', 'NGN')->sum('amount'),
                'total_revenue' => $completed->sum('difference_amount'),
                'credit_transactions' => $bookings->where('transaction_type', 'credit')->count(),
                'debit_transactions' => $bookings->where('transaction_type', 'debit')->count(),
            ];
        });
    }

    /**
     * Get transaction chart data for visualization
     */
    public function getTransactionChartData(string $period): array
    {
        $cacheKey = "forex_transaction_chart_{$period}";
        
        return Cache::remember($cacheKey, 600, function () use ($period) {
            $days = match($period) {
                'week' => 7,
                'month' => 30,
                'year' => 365,
                default => 7
            };

            $startDate = now()->subDays($days);
            $endDate = now();

            $transactions = ForexTransaction::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('DATE(created_at) as date, transaction_type, COUNT(*) as count, SUM(amount) as total')
                ->groupBy(['date', 'transaction_type'])
                ->orderBy('date')
                ->get();

            $chartData = [
                'labels' => [],
                'datasets' => [
                    [
                        'label' => 'Credits',
                        'data' => [],
                        'backgroundColor' => 'rgba(40, 167, 69, 0.8)',
                    ],
                    [
                        'label' => 'Debits',
                        'data' => [],
                        'backgroundColor' => 'rgba(220, 53, 69, 0.8)',
                    ]
                ]
            ];

            // Generate date range
            $currentDate = $startDate->copy();
            while ($currentDate <= $endDate) {
                $dateStr = $currentDate->format('Y-m-d');
                $chartData['labels'][] = $currentDate->format('M d');
                
                $creditCount = $transactions->where('date', $dateStr)->where('transaction_type', 'credit')->sum('count');
                $debitCount = $transactions->where('date', $dateStr)->where('transaction_type', 'debit')->sum('count');
                
                $chartData['datasets'][0]['data'][] = $creditCount;
                $chartData['datasets'][1]['data'][] = $debitCount;
                
                $currentDate->addDay();
            }

            return $chartData;
        });
    }

    /**
     * Get revenue chart data
     */
    public function getRevenueChartData(string $period): array
    {
        $cacheKey = "forex_revenue_chart_{$period}";
        
        return Cache::remember($cacheKey, 600, function () use ($period) {
            $months = match($period) {
                'quarter' => 3,
                'year' => 12,
                default => 6
            };

            $startDate = now()->subMonths($months)->startOfMonth();
            $endDate = now()->endOfMonth();

            $bookings = ForexBooking::completed()
                ->whereBetween('completed_at', [$startDate, $endDate])
                ->selectRaw('YEAR(completed_at) as year, MONTH(completed_at) as month, SUM(difference_amount) as revenue')
                ->groupBy(['year', 'month'])
                ->orderBy('year')
                ->orderBy('month')
                ->get();

            $operationalCosts = OperationalCost::whereBetween('cost_date', [$startDate, $endDate])
                ->selectRaw('YEAR(cost_date) as year, MONTH(cost_date) as month, SUM(amount) as costs')
                ->groupBy(['year', 'month'])
                ->orderBy('year')
                ->orderBy('month')
                ->get();

            $chartData = [
                'labels' => [],
                'datasets' => [
                    [
                        'label' => 'Revenue',
                        'data' => [],
                        'backgroundColor' => 'rgba(40, 167, 69, 0.8)',
                    ],
                    [
                        'label' => 'Costs',
                        'data' => [],
                        'backgroundColor' => 'rgba(255, 193, 7, 0.8)',
                    ],
                    [
                        'label' => 'Net Profit',
                        'data' => [],
                        'backgroundColor' => 'rgba(0, 123, 255, 0.8)',
                    ]
                ]
            ];

            // Generate month range
            $currentDate = $startDate->copy();
            while ($currentDate <= $endDate) {
                $year = $currentDate->year;
                $month = $currentDate->month;
                
                $chartData['labels'][] = $currentDate->format('M Y');
                
                $revenue = $bookings->where('year', $year)->where('month', $month)->sum('revenue');
                $costs = $operationalCosts->where('year', $year)->where('month', $month)->sum('costs');
                $netProfit = $revenue - $costs;
                
                $chartData['datasets'][0]['data'][] = $revenue;
                $chartData['datasets'][1]['data'][] = $costs;
                $chartData['datasets'][2]['data'][] = $netProfit;
                
                $currentDate->addMonth();
            }

            return $chartData;
        });
    }

    /**
     * Get recent activity
     */
    public function getRecentActivity(): array
    {
        return Cache::remember('forex_recent_activity', 300, function () {
            $recentBookings = ForexBooking::with(['user', 'initiatedBy'])
                ->latest()
                ->limit(10)
                ->get();

            $recentTransactions = ForexTransaction::with(['forexAccount', 'createdBy'])
                ->latest()
                ->limit(10)
                ->get();

            return [
                'recent_bookings' => $recentBookings->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'reference' => $booking->booking_reference,
                        'client_name' => $booking->client_name,
                        'amount' => $booking->formatted_amount,
                        'status' => $booking->status,
                        'created_at' => $booking->created_at->diffForHumans(),
                        'url' => route('admin.forex.bookings.show', $booking->id),
                    ];
                }),
                'recent_transactions' => $recentTransactions->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'reference' => $transaction->transaction_reference,
                        'account' => $transaction->forexAccount->account_name,
                        'type' => $transaction->transaction_type,
                        'amount' => $transaction->formatted_amount,
                        'created_at' => $transaction->created_at->diffForHumans(),
                    ];
                }),
            ];
        });
    }

    /**
     * Get top clients by transaction volume
     */
    public function getTopClients(string $period, int $limit = 10): array
    {
        $cacheKey = "forex_top_clients_{$period}_{$limit}";
        
        return Cache::remember($cacheKey, 600, function () use ($period, $limit) {
            $query = ForexBooking::completed();
            
            switch ($period) {
                case 'week':
                    $query->whereBetween('completed_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('completed_at', now()->month)
                          ->whereYear('completed_at', now()->year);
                    break;
                case 'year':
                    $query->whereYear('completed_at', now()->year);
                    break;
            }

            return $query->selectRaw('client_name, client_email, COUNT(*) as booking_count, SUM(amount) as total_volume, SUM(difference_amount) as total_revenue')
                ->groupBy(['client_name', 'client_email'])
                ->orderBy('total_volume', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($client) {
                    return [
                        'name' => $client->client_name,
                        'email' => $client->client_email,
                        'booking_count' => $client->booking_count,
                        'total_volume' => number_format($client->total_volume, 2),
                        'total_revenue' => number_format($client->total_revenue, 2),
                    ];
                });
        });
    }

    /**
     * Get exchange rate history
     */
    public function getExchangeRateHistory(int $days = 30): array
    {
        $cacheKey = "forex_rate_history_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $rates = ForexRate::where('status', 'approved')
                ->where('created_at', '>=', now()->subDays($days))
                ->orderBy('created_at')
                ->get();

            return [
                'labels' => $rates->map(fn($rate) => $rate->created_at->format('M d')),
                'cbn_rates' => $rates->map(fn($rate) => $rate->cbn_rate),
                'parallel_rates' => $rates->map(fn($rate) => $rate->parallel_rate),
            ];
        });
    }

    /**
     * Get operational cost summary
     */
    public function getOperationalCostSummary(string $period): array
    {
        $cacheKey = "forex_operational_costs_{$period}";
        
        return Cache::remember($cacheKey, 600, function () use ($period) {
            $query = OperationalCost::query();
            
            switch ($period) {
                case 'week':
                    $query->whereBetween('cost_date', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('cost_date', now()->month)
                          ->whereYear('cost_date', now()->year);
                    break;
            }

            $costs = $query->get();

            return [
                'total_costs' => $costs->sum('amount'),
                'cost_count' => $costs->count(),
                'by_category' => $costs->groupBy('category')->map(function ($categoryCosts) {
                    return [
                        'total' => $categoryCosts->sum('amount'),
                        'count' => $categoryCosts->count(),
                    ];
                }),
            ];
        });
    }

    /**
     * Refresh all cached dashboard data
     */
    public function refreshCachedData(): void
    {
        $cacheKeys = [
            'forex_dashboard_data',
            'forex_account_balances',
            'forex_booking_stats_today',
            'forex_booking_stats_week',
            'forex_booking_stats_month',
            'forex_recent_activity',
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Refresh main dashboard data
        $this->getDashboardData();
    }
}
