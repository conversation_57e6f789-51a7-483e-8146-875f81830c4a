<?php

namespace Database\Seeders;

use App\Models\MerchantPayoutConfiguration;
use App\Models\PayoutMethod;
use App\Models\User;
use Illuminate\Database\Seeder;

class MerchantPayoutConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some test merchants and payout methods
        $merchants = User::where('type', 'merchant')->take(3)->get(); // Get first 3 merchants
        $payoutMethods = PayoutMethod::where('is_active', 1)->take(2)->get(); // Get first 2 active payout methods

        if ($merchants->isEmpty() || $payoutMethods->isEmpty()) {
            $this->command->info('No merchants or payout methods found. Skipping merchant payout configuration seeding.');
            return;
        }

        $configurations = [
            // Merchant 1: Custom limits and charges for first payout method
            [
                'merchant_id' => $merchants->first()->id,
                'payout_method_id' => $payoutMethods->first()->id,
                'min_limit' => 500.00,
                'max_limit' => 1000000.00,
                'percentage_charge' => 1.50,
                'fixed_charge' => 25.00,
                'is_active' => 1,
            ],
            
            // Merchant 1: Only custom limits for second payout method (charges use defaults)
            [
                'merchant_id' => $merchants->first()->id,
                'payout_method_id' => $payoutMethods->count() > 1 ? $payoutMethods->get(1)->id : $payoutMethods->first()->id,
                'min_limit' => 1000.00,
                'max_limit' => 500000.00,
                'percentage_charge' => null, // Use default
                'fixed_charge' => null, // Use default
                'is_active' => 1,
            ],
            
            // Merchant 2: Only custom charges (limits use defaults)
            [
                'merchant_id' => $merchants->count() > 1 ? $merchants->get(1)->id : $merchants->first()->id,
                'payout_method_id' => $payoutMethods->first()->id,
                'min_limit' => null, // Use default
                'max_limit' => null, // Use default
                'percentage_charge' => 0.75,
                'fixed_charge' => 10.00,
                'is_active' => 1,
            ],
            
            // Merchant 3: Mixed configuration
            [
                'merchant_id' => $merchants->count() > 2 ? $merchants->get(2)->id : $merchants->first()->id,
                'payout_method_id' => $payoutMethods->first()->id,
                'min_limit' => 200.00,
                'max_limit' => null, // Use default
                'percentage_charge' => 2.00,
                'fixed_charge' => null, // Use default
                'is_active' => 1,
            ],
        ];

        foreach ($configurations as $config) {
            MerchantPayoutConfiguration::updateOrCreate(
                [
                    'merchant_id' => $config['merchant_id'],
                    'payout_method_id' => $config['payout_method_id'],
                ],
                $config
            );
        }

        $this->command->info('Merchant payout configurations seeded successfully.');
        
        // Display what was created
        $this->command->info('Created configurations:');
        foreach ($configurations as $config) {
            $merchant = User::find($config['merchant_id']);
            $payoutMethod = PayoutMethod::find($config['payout_method_id']);
            
            $customFields = [];
            if (!is_null($config['min_limit'])) $customFields[] = "min_limit: {$config['min_limit']}";
            if (!is_null($config['max_limit'])) $customFields[] = "max_limit: {$config['max_limit']}";
            if (!is_null($config['percentage_charge'])) $customFields[] = "percentage_charge: {$config['percentage_charge']}%";
            if (!is_null($config['fixed_charge'])) $customFields[] = "fixed_charge: {$config['fixed_charge']}";
            
            $this->command->info("- {$merchant->name} -> {$payoutMethod->name}: " . implode(', ', $customFields));
        }
    }
}
