<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\PayoutMethod;

class NumeroPayoutSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if Numero already exists
        $existingNumero = PayoutMethod::where('code', 'numero')->first();
        
        if ($existingNumero) {
            $this->command->info('Numero payout method already exists. Updating...');
            
            $existingNumero->update([
                'name' => 'Numero',
                'description' => 'Fast Nigerian bank transfers via Numero API',
                'bank_name' => json_encode(['0' => ['NUMERO' => 'NUMERO']]),
                'banks' => json_encode(['NUMERO']),
                'parameters' => json_encode([
                    'api_key' => 'live_key_2wJVjV1WfDMbAtQzb0SsLAxPn',
                    'public_key' => 'a73c40b5055844aebf834b3f665f4733',
                    'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
                ]),
                'extra_parameters' => json_encode(['webhook' => 'payout']),
                'inputForm' => json_encode([
                    'account_number' => [
                        'field_name' => 'account_number',
                        'field_label' => 'Account Number',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'account_name' => [
                        'field_name' => 'account_name',
                        'field_label' => 'Account Name',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'bank_code' => [
                        'field_name' => 'bank_code',
                        'field_label' => 'Bank Code',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'narration' => [
                        'field_name' => 'narration',
                        'field_label' => 'Narration',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'phone_number' => [
                        'field_name' => 'phone_number',
                        'field_label' => 'Phone Number',
                        'type' => 'text',
                        'validation' => 'required'
                    ]
                ]),
                'currency_lists' => json_encode(['NGN' => 'NGN']),
                'supported_currency' => json_encode(['NGN']),
                'payout_currencies' => json_encode([
                    [
                        'name' => 'NGN',
                        'currency_symbol' => 'NGN',
                        'conversion_rate' => '1650',
                        'min_limit' => '100',
                        'max_limit' => '5000000',
                        'percentage_charge' => '0',
                        'fixed_charge' => '50'
                    ]
                ]),
                'is_active' => 1,
                'is_automatic' => 1,
                'is_sandbox' => 0,
                'environment' => 'live',
                'confirm_payout' => 1,
                'is_auto_update' => 1,
                'currency_type' => 1,
                'updated_at' => now()
            ]);
            
            $this->command->info('Numero payout method updated successfully!');
        } else {
            $this->command->info('Creating new Numero payout method...');
            
            PayoutMethod::create([
                'name' => 'Numero',
                'code' => 'numero',
                'description' => 'Fast Nigerian bank transfers via Numero API',
                'bank_name' => json_encode(['0' => ['NUMERO' => 'NUMERO']]),
                'banks' => json_encode(['NUMERO']),
                'parameters' => json_encode([
                    'api_key' => 'live_key_2wJVjV1WfDMbAtQzb0SsLAxPn',
                    'public_key' => 'a73c40b5055844aebf834b3f665f4733',
                    'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
                ]),
                'extra_parameters' => json_encode(['webhook' => 'payout']),
                'inputForm' => json_encode([
                    'account_number' => [
                        'field_name' => 'account_number',
                        'field_label' => 'Account Number',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'account_name' => [
                        'field_name' => 'account_name',
                        'field_label' => 'Account Name',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'bank_code' => [
                        'field_name' => 'bank_code',
                        'field_label' => 'Bank Code',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'narration' => [
                        'field_name' => 'narration',
                        'field_label' => 'Narration',
                        'type' => 'text',
                        'validation' => 'required'
                    ],
                    'phone_number' => [
                        'field_name' => 'phone_number',
                        'field_label' => 'Phone Number',
                        'type' => 'text',
                        'validation' => 'required'
                    ]
                ]),
                'currency_lists' => json_encode(['NGN' => 'NGN']),
                'supported_currency' => json_encode(['NGN']),
                'payout_currencies' => json_encode([
                    [
                        'name' => 'NGN',
                        'currency_symbol' => 'NGN',
                        'conversion_rate' => '1650',
                        'min_limit' => '100',
                        'max_limit' => '5000000',
                        'percentage_charge' => '0',
                        'fixed_charge' => '50'
                    ]
                ]),
                'is_active' => 1,
                'is_automatic' => 1,
                'is_sandbox' => 0,
                'environment' => 'live',
                'confirm_payout' => 1,
                'is_auto_update' => 1,
                'currency_type' => 1,
                'logo' => null,
                'driver' => 'local',
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            $this->command->info('Numero payout method created successfully!');
        }
    }
}
