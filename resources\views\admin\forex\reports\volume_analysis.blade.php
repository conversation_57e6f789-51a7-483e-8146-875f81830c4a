@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.index') }}">
                                    @lang('Forex Reports')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Volume Analysis')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">{{ $reportData['title'] }}</h1>
                    <p class="page-header-text">{{ $reportData['subtitle'] ?? '' }}</p>
                    <p class="page-header-text">@lang('Report Period'): {{ $reportData['period'] }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="btn-group" role="group">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.reports.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Reports')
                        </a>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="bi-printer me-1"></i> @lang('Print')
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi-download me-1"></i> @lang('Export')
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                    <i class="bi-file-earmark-excel me-2"></i> @lang('Excel')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                    <i class="bi-file-earmark-pdf me-2"></i> @lang('PDF')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                    <i class="bi-file-earmark-text me-2"></i> @lang('CSV')
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Volume Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Volume Overview')</h4>
                        <span class="badge bg-soft-primary text-primary">
                            <i class="bi-bar-chart me-1"></i> @lang('Volume Analytics')
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">${{ number_format($reportData['summary']['total_volume_usd'], 2) }}</span>
                                        <span class="d-block text-muted">@lang('Total USD Volume')</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">₦{{ number_format($reportData['summary']['total_volume_ngn'], 2) }}</span>
                                        <span class="d-block text-muted">@lang('Total NGN Volume')</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['total_transactions']) }}</span>
                                        <span class="d-block text-muted">@lang('Total Transactions')</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['completion_rate_by_volume'], 1) }}%</span>
                                        <span class="d-block text-muted">@lang('Volume Completion Rate')</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buy vs Sell Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Buy Transactions (NGN to USD)')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['buy_volume_usd'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('USD Volume')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['buy_count']) }}</span>
                                    <span class="d-block text-muted small">@lang('Transactions')</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">${{ number_format($reportData['summary']['average_buy_size_usd'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Average Transaction Size')</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">@lang('Volume Share')</span>
                                <span class="badge bg-soft-success text-success">{{ number_format($reportData['summary']['buy_volume_percentage'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Sell Transactions (USD to NGN)')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['sell_volume_usd'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('USD Volume')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['sell_count']) }}</span>
                                    <span class="d-block text-muted small">@lang('Transactions')</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">${{ number_format($reportData['summary']['average_sell_size_usd'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Average Transaction Size')</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">@lang('Volume Share')</span>
                                <span class="badge bg-soft-primary text-primary">{{ number_format($reportData['summary']['sell_volume_percentage'], 1) }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Size Distribution -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transaction Size Distribution')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($reportData['summary']['transaction_size_distribution'] as $size => $data)
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card card-sm">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $data['label'] }}</h6>
                                            <div class="row">
                                                <div class="col-6">
                                                    <span class="d-block h6 mb-0">{{ number_format($data['count']) }}</span>
                                                    <span class="d-block text-muted small">@lang('Transactions')</span>
                                                </div>
                                                <div class="col-6">
                                                    <span class="d-block h6 mb-0">{{ number_format($data['percentage'], 1) }}%</span>
                                                    <span class="d-block text-muted small">@lang('Share')</span>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <span class="d-block text-muted small">@lang('Volume'): ${{ number_format($data['volume'], 2) }}</span>
                                                <span class="d-block text-muted small">@lang('Revenue'): ${{ number_format($data['revenue'], 2) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Volume Performance Metrics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Volume Performance')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Peak Daily Volume')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['peak_daily_volume'], 2) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Average Daily Volume')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['average_daily_volume'], 2) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Volume Growth Rate')</span>
                                    <span class="badge bg-soft-{{ $reportData['summary']['volume_growth_rate'] >= 0 ? 'success' : 'danger' }} text-{{ $reportData['summary']['volume_growth_rate'] >= 0 ? 'success' : 'danger' }}">
                                        {{ number_format($reportData['summary']['volume_growth_rate'], 1) }}%
                                    </span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Large Transactions (>$10K)')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['large_transactions_count']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Client Distribution')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Total Unique Clients')</span>
                                    <span class="fw-bold">{{ $reportData['summary']['top_clients_by_volume']->count() }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Client Concentration (Top 5)')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['client_concentration_ratio'], 1) }}%</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Median Transaction Size')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['median_transaction_size'], 2) }}</span>
                                </div>
                            </div>
                        </div>
                        
                        @if($reportData['summary']['busiest_day'])
                            <div class="mt-3 pt-3 border-top">
                                <h6 class="mb-2">@lang('Busiest Day')</h6>
                                <p class="mb-1"><strong>{{ $reportData['summary']['busiest_day']['period'] }}</strong></p>
                                <p class="text-muted small mb-0">
                                    ${{ number_format($reportData['summary']['busiest_day']['total_volume_usd'], 2) }} 
                                    ({{ $reportData['summary']['busiest_day']['transaction_count'] }} @lang('transactions'))
                                </p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Clients by Volume -->
        @if($reportData['summary']['top_clients_by_volume']->count() > 0)
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-header-title">@lang('Top Clients by Volume')</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>@lang('Client Name')</th>
                                            <th>@lang('USD Volume')</th>
                                            <th>@lang('NGN Volume')</th>
                                            <th>@lang('Transactions')</th>
                                            <th>@lang('Avg Size')</th>
                                            <th>@lang('Revenue')</th>
                                            <th>@lang('Client Type')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reportData['summary']['top_clients_by_volume']->take(10) as $client)
                                            <tr>
                                                <td>{{ $client['client_name'] }}</td>
                                                <td>${{ number_format($client['total_volume_usd'], 2) }}</td>
                                                <td>₦{{ number_format($client['total_volume_ngn'], 2) }}</td>
                                                <td>{{ number_format($client['transaction_count']) }}</td>
                                                <td>${{ number_format($client['average_transaction_size'], 2) }}</td>
                                                <td>${{ number_format($client['total_revenue'], 2) }}</td>
                                                <td>
                                                    <span class="badge bg-soft-info text-info">{{ ucfirst($client['client_type']) }}</span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        
        function exportReport(format) {
            // Implementation for exporting volume reports
            alert('Export functionality will be implemented for ' + format + ' format');
        }
    </script>
@endpush
