# API Key Authentication

This document describes the new API Key authentication endpoint that allows users to authenticate using their `publicKey` and `secretKey` instead of username and password.

## Overview

The `/api/authenticate` endpoint provides an alternative authentication method for API users who prefer to use API keys rather than traditional username/password credentials. This is particularly useful for:

- Automated systems and integrations
- Third-party applications
- Secure API access without exposing user passwords
- Applications that need programmatic access

## Authentication Endpoint

### POST /api/authenticate

Authenticate a user using their API keys and receive a Bearer token for subsequent API calls.

**URL:** `POST /api/authenticate`

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Request Body:**
```json
{
    "publicKey": "your_public_key_here",
    "secretKey": "your_secret_key_here"
}
```

**Parameters:**
- `publicKey` (string, required): The user's public API key
- `secretKey` (string, required): The user's secret API key

### Success Response

**Status Code:** `200 OK`

```json
{
    "status": "success",
    "message": "User Authenticated Successfully",
    "token": "1|abcdef123456789...",
    "user": {
        "id": 123,
        "username": "john_doe",
        "email": "<EMAIL>",
        "type": "user",
        "mode": "Test"
    }
}
```

### Error Responses

**Invalid Credentials (200 OK):**
```json
{
    "status": "failed",
    "message": "Invalid API credentials. Public Key & Secret Key do not match with our records."
}
```

**Account Suspended (200 OK):**
```json
{
    "status": "failed",
    "message": "Your account has been suspended. Please contact support."
}
```

**Restricted User Type (200 OK):**
```json
{
    "status": "failed",
    "message": "Authentication restricted for [user_type]."
}
```

**Validation Error (200 OK):**
```json
{
    "status": "failed",
    "message": "The publicKey field is required."
}
```

## How to Get API Keys

### For Users:
1. Login to your account
2. Navigate to **Settings** → **API Keys**
3. Click **Generate API Key** (requires security PIN)
4. Copy your `publicKey` and `secretKey`

### For Merchants:
1. Login to your merchant account
2. Navigate to **Settings** → **API Keys**
3. Click **Generate API Key** (requires security PIN)
4. Copy your `publicKey` and `secretKey`

## Using the Token

Once you receive the authentication token, include it in the `Authorization` header for all subsequent API requests:

```bash
curl -X POST "https://your-domain.com/api/validate-account" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{"accountNumber":"**********","bankCode":"123"}'
```

## Example Usage

### cURL Example

```bash
# Step 1: Authenticate with API keys
curl -X POST "https://your-domain.com/api/authenticate" \
  -H "Content-Type: application/json" \
  -d '{
    "publicKey": "your_public_key_here",
    "secretKey": "your_secret_key_here"
  }'

# Step 2: Use the returned token for API calls
curl -X POST "https://your-domain.com/api/validate-account" \
  -H "Authorization: Bearer 1|abcdef123456789..." \
  -H "Content-Type: application/json" \
  -d '{
    "accountNumber": "**********",
    "bankCode": "123"
  }'
```

### PHP Example

```php
<?php

// Step 1: Authenticate
$authData = [
    'publicKey' => 'your_public_key_here',
    'secretKey' => 'your_secret_key_here'
];

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => 'https://your-domain.com/api/authenticate',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($authData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json'
    ]
]);

$response = curl_exec($curl);
$authResult = json_decode($response, true);
curl_close($curl);

if ($authResult['status'] === 'success') {
    $token = $authResult['token'];
    
    // Step 2: Use token for API calls
    $apiData = [
        'accountNumber' => '**********',
        'bankCode' => '123'
    ];
    
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => 'https://your-domain.com/api/validate-account',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($apiData),
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    $apiResponse = curl_exec($curl);
    curl_close($curl);
    
    echo $apiResponse;
}
?>
```

### JavaScript Example

```javascript
// Step 1: Authenticate
const authResponse = await fetch('https://your-domain.com/api/authenticate', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({
        publicKey: 'your_public_key_here',
        secretKey: 'your_secret_key_here'
    })
});

const authResult = await authResponse.json();

if (authResult.status === 'success') {
    const token = authResult.token;
    
    // Step 2: Use token for API calls
    const apiResponse = await fetch('https://your-domain.com/api/validate-account', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            accountNumber: '**********',
            bankCode: '123'
        })
    });
    
    const apiResult = await apiResponse.json();
    console.log(apiResult);
}
```

## Security Considerations

1. **Keep Secret Keys Secure**: Never expose your secret key in client-side code or public repositories
2. **Use HTTPS**: Always use HTTPS in production to protect API keys in transit
3. **Regenerate Keys**: Regularly regenerate your API keys for security
4. **Token Expiration**: Tokens don't expire by default, but you can revoke them by regenerating your API keys
5. **Environment Variables**: Store API keys in environment variables, not in code

## Comparison with Username/Password Authentication

| Feature | Username/Password (`/api/login`) | API Keys (`/api/authenticate`) |
|---------|----------------------------------|--------------------------------|
| **Credentials** | username, password | publicKey, secretKey |
| **Use Case** | User login, mobile apps | API integrations, automation |
| **Security** | User password required | API keys (can be regenerated) |
| **Token Response** | Same format | Same format |
| **Subsequent Calls** | Same Bearer token usage | Same Bearer token usage |

## Testing

Use the provided test script to verify the authentication endpoint:

```bash
php tests/test-api-key-authentication.php
```

## Support

For issues with API key authentication:
1. Verify your API keys are correct and not expired
2. Ensure your account is active and verified
3. Check that you're using the correct endpoint URL
4. Contact support if problems persist
