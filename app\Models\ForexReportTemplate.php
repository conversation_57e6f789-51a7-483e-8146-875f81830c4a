<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForexReportTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'report_type',
        'configuration',
        'is_public',
        'created_by',
        'usage_count'
    ];

    protected $casts = [
        'configuration' => 'array',
        'is_public' => 'boolean',
        'usage_count' => 'integer',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('report_type', $type);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('usage_count', 'desc');
    }

    // Methods
    public function incrementUsage()
    {
        $this->increment('usage_count');
    }

    public function getConfigurationAttribute($value)
    {
        $config = json_decode($value, true);
        
        // Ensure default values for missing keys
        return array_merge([
            'report_type' => 'bookings',
            'date_range_type' => 'monthly',
            'transaction_type' => 'both',
            'currency' => 'both',
            'selected_fields' => [],
            'field_labels' => [],
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
            'group_by' => '',
            'include_totals' => true,
            'include_counts' => true,
        ], $config ?: []);
    }

    // Static methods for common templates
    public static function getDefaultTemplates()
    {
        return [
            [
                'name' => 'CBN Compliance Report',
                'description' => 'Standard CBN compliance report with rates and markup percentages',
                'report_type' => 'cbn_focused',
                'configuration' => [
                    'report_type' => 'cbn_focused',
                    'selected_fields' => ['booking_reference', 'client_name', 'amount', 'cbn_rate', 'markup_percentage', 'customer_rate', 'status', 'created_at'],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'amount' => 'Amount',
                        'cbn_rate' => 'CBN Rate',
                        'markup_percentage' => 'Markup %',
                        'customer_rate' => 'Final Rate',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'sort_by' => 'created_at',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Transaction Volume Analysis',
                'description' => 'Volume analysis with NGN and USD breakdowns',
                'report_type' => 'volume',
                'configuration' => [
                    'report_type' => 'volume',
                    'selected_fields' => ['booking_reference', 'transaction_type', 'currency', 'amount', 'customer_total', 'status', 'created_at'],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'transaction_type' => 'Type',
                        'currency' => 'Currency',
                        'amount' => 'Amount',
                        'customer_total' => 'Total',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'group_by' => 'currency',
                    'sort_by' => 'amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true,
                    'include_averages' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Revenue Tracking Report',
                'description' => 'Revenue and markup tracking for performance analysis',
                'report_type' => 'performance',
                'configuration' => [
                    'report_type' => 'performance',
                    'selected_fields' => ['booking_reference', 'client_name', 'amount', 'markup_percentage', 'markup_amount', 'customer_payment_amount', 'status', 'created_at'],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'amount' => 'Amount',
                        'markup_percentage' => 'Markup %',
                        'markup_amount' => 'Revenue',
                        'customer_payment_amount' => 'Payment',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'sort_by' => 'markup_amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_percentages' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Buy Transactions Only',
                'description' => 'NGN to USD transactions analysis',
                'report_type' => 'buy_transactions',
                'configuration' => [
                    'report_type' => 'buy_transactions',
                    'transaction_type' => 'buying',
                    'selected_fields' => ['booking_reference', 'client_name', 'amount', 'cbn_rate', 'customer_rate', 'markup_amount', 'status', 'created_at'],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'amount' => 'USD Amount',
                        'cbn_rate' => 'CBN Rate',
                        'customer_rate' => 'Final Rate',
                        'markup_amount' => 'Revenue',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'sort_by' => 'amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Sell Transactions Only',
                'description' => 'USD to NGN transactions analysis',
                'report_type' => 'sell_transactions',
                'configuration' => [
                    'report_type' => 'sell_transactions',
                    'transaction_type' => 'selling',
                    'selected_fields' => ['booking_reference', 'client_name', 'amount', 'cbn_rate', 'customer_rate', 'markup_amount', 'status', 'created_at'],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'amount' => 'USD Amount',
                        'cbn_rate' => 'CBN Rate',
                        'customer_rate' => 'Final Rate',
                        'markup_amount' => 'Revenue',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'sort_by' => 'amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Client Activity Summary',
                'description' => 'Client activity and transaction patterns',
                'report_type' => 'bookings',
                'configuration' => [
                    'report_type' => 'bookings',
                    'selected_fields' => ['client_name', 'client_email', 'client_type', 'transaction_type', 'amount', 'status', 'created_at'],
                    'field_labels' => [
                        'client_name' => 'Client Name',
                        'client_email' => 'Email',
                        'client_type' => 'Type',
                        'transaction_type' => 'Transaction',
                        'amount' => 'Amount',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'group_by' => 'client_type',
                    'sort_by' => 'created_at',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true,
                    'include_averages' => true
                ],
                'is_public' => true
            ]
        ];
    }

    public static function createDefaultTemplates()
    {
        $templates = self::getDefaultTemplates();
        
        foreach ($templates as $template) {
            self::updateOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }
}
