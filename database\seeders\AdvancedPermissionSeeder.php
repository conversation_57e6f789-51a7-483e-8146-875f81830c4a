<?php

namespace Database\Seeders;

use App\Models\AdvancedPermission;
use App\Services\PermissionDiscoveryService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * Advanced Permission System Seeder
 * 
 * Seeds the database with comprehensive permissions for all system resources.
 */
class AdvancedPermissionSeeder extends Seeder
{
    /**
     * Permission discovery service
     */
    protected PermissionDiscoveryService $discoveryService;

    public function __construct()
    {
        $this->discoveryService = new PermissionDiscoveryService();
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Advanced Permission System Seeding...');

        DB::beginTransaction();
        try {
            // 1. Create core system permissions
            $this->createCorePermissions();

            // 2. Discover and create permissions from controllers
            $this->discoverControllerPermissions();

            // 3. Create resource-specific permissions
            $this->createResourcePermissions();

            // 4. Create admin and system permissions
            $this->createAdminPermissions();

            // 5. Create finance-specific permissions
            $this->createFinancePermissions();

            // 6. Create user management permissions
            $this->createUserManagementPermissions();

            // 7. Create compliance and audit permissions
            $this->createCompliancePermissions();

            DB::commit();

            $totalPermissions = AdvancedPermission::count();
            $this->command->info("✅ Successfully seeded {$totalPermissions} permissions!");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error("❌ Seeding failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create core system permissions
     */
    protected function createCorePermissions(): void
    {
        $this->command->info('📋 Creating core system permissions...');

        $corePermissions = [
            // Advanced Role System
            ['name' => 'advanced_roles.create', 'display_name' => 'Create Advanced Roles', 'description' => 'Create new advanced roles', 'resource' => 'advanced_roles', 'action' => 'create', 'category' => 'role_management'],
            ['name' => 'advanced_roles.read', 'display_name' => 'View Advanced Roles', 'description' => 'View advanced roles and their details', 'resource' => 'advanced_roles', 'action' => 'read', 'category' => 'role_management'],
            ['name' => 'advanced_roles.update', 'display_name' => 'Update Advanced Roles', 'description' => 'Edit and modify advanced roles', 'resource' => 'advanced_roles', 'action' => 'update', 'category' => 'role_management'],
            ['name' => 'advanced_roles.delete', 'display_name' => 'Delete Advanced Roles', 'description' => 'Delete advanced roles', 'resource' => 'advanced_roles', 'action' => 'delete', 'category' => 'role_management'],

            // Advanced Permissions
            ['name' => 'advanced_permissions.create', 'display_name' => 'Create Permissions', 'description' => 'Create new permissions', 'resource' => 'advanced_permissions', 'action' => 'create', 'category' => 'permission_management'],
            ['name' => 'advanced_permissions.read', 'display_name' => 'View Permissions', 'description' => 'View permissions and their details', 'resource' => 'advanced_permissions', 'action' => 'read', 'category' => 'permission_management'],
            ['name' => 'advanced_permissions.update', 'display_name' => 'Update Permissions', 'description' => 'Edit and modify permissions', 'resource' => 'advanced_permissions', 'action' => 'update', 'category' => 'permission_management'],
            ['name' => 'advanced_permissions.delete', 'display_name' => 'Delete Permissions', 'description' => 'Delete permissions', 'resource' => 'advanced_permissions', 'action' => 'delete', 'category' => 'permission_management'],
            ['name' => 'advanced_permissions.discover', 'display_name' => 'Discover Permissions', 'description' => 'Run permission discovery from controllers', 'resource' => 'advanced_permissions', 'action' => 'discover', 'category' => 'permission_management'],

            // User Role Assignments
            ['name' => 'user_roles.create', 'display_name' => 'Assign User Roles', 'description' => 'Assign roles to users', 'resource' => 'user_roles', 'action' => 'create', 'category' => 'user_management'],
            ['name' => 'user_roles.read', 'display_name' => 'View User Roles', 'description' => 'View user role assignments', 'resource' => 'user_roles', 'action' => 'read', 'category' => 'user_management'],
            ['name' => 'user_roles.update', 'display_name' => 'Update User Roles', 'description' => 'Modify user role assignments', 'resource' => 'user_roles', 'action' => 'update', 'category' => 'user_management'],
            ['name' => 'user_roles.delete', 'display_name' => 'Revoke User Roles', 'description' => 'Revoke roles from users', 'resource' => 'user_roles', 'action' => 'delete', 'category' => 'user_management'],

            // System Administration
            ['name' => 'system.admin', 'display_name' => 'System Administration', 'description' => 'Full system administration access', 'resource' => 'system', 'action' => 'admin', 'category' => 'system'],
            ['name' => 'system.settings', 'display_name' => 'System Settings', 'description' => 'Manage system settings and configuration', 'resource' => 'system', 'action' => 'settings', 'category' => 'system'],
            ['name' => 'system.logs', 'display_name' => 'System Logs', 'description' => 'View system logs and audit trails', 'resource' => 'system', 'action' => 'logs', 'category' => 'system'],
        ];

        foreach ($corePermissions as $permission) {
            AdvancedPermission::updateOrCreate(
                ['name' => $permission['name']],
                array_merge($permission, ['is_system' => true])
            );
        }

        $this->command->info('✓ Core permissions created');
    }

    /**
     * Discover permissions from controllers
     */
    protected function discoverControllerPermissions(): void
    {
        $this->command->info('🔍 Discovering permissions from controllers...');

        $result = $this->discoveryService->syncPermissionsToDatabase();
        
        $this->command->info("✓ Discovered {$result['total_discovered']} permissions");
        $this->command->info("  - Created: {$result['created']}");
        $this->command->info("  - Updated: {$result['updated']}");
        
        if (!empty($result['errors'])) {
            $this->command->warn("  - Errors: " . count($result['errors']));
        }
    }

    /**
     * Create resource-specific permissions
     */
    protected function createResourcePermissions(): void
    {
        $this->command->info('📦 Creating resource-specific permissions...');

        $resources = [
            'users' => 'user_management',
            'merchants' => 'merchant_management',
            'transactions' => 'finance',
            'wallets' => 'finance',
            'deposits' => 'finance',
            'payouts' => 'finance',
            'transfers' => 'finance',
            'currencies' => 'finance',
            'exchange_rates' => 'finance',
            'vouchers' => 'finance',
            'invoices' => 'finance',
            'escrows' => 'finance',
            'disputes' => 'support',
            'tickets' => 'support',
            'notifications' => 'communication',
            'templates' => 'communication',
            'reports' => 'reporting',
            'analytics' => 'reporting',
            'kyc' => 'compliance',
            'documents' => 'compliance',
        ];

        foreach ($resources as $resource => $category) {
            AdvancedPermission::createCrudPermissions($resource, $category);
        }

        $this->command->info('✓ Resource permissions created');
    }

    /**
     * Create admin-specific permissions
     */
    protected function createAdminPermissions(): void
    {
        $this->command->info('👑 Creating admin-specific permissions...');

        $adminPermissions = [
            // Admin Management
            ['name' => 'admins.create', 'display_name' => 'Create Admins', 'description' => 'Create new admin accounts', 'resource' => 'admins', 'action' => 'create', 'category' => 'admin_management'],
            ['name' => 'admins.read', 'display_name' => 'View Admins', 'description' => 'View admin accounts and details', 'resource' => 'admins', 'action' => 'read', 'category' => 'admin_management'],
            ['name' => 'admins.update', 'display_name' => 'Update Admins', 'description' => 'Edit admin accounts', 'resource' => 'admins', 'action' => 'update', 'category' => 'admin_management'],
            ['name' => 'admins.delete', 'display_name' => 'Delete Admins', 'description' => 'Delete admin accounts', 'resource' => 'admins', 'action' => 'delete', 'category' => 'admin_management'],
            ['name' => 'admins.impersonate', 'display_name' => 'Impersonate Users', 'description' => 'Login as other users for support', 'resource' => 'admins', 'action' => 'impersonate', 'category' => 'admin_management'],

            // Dashboard and Analytics
            ['name' => 'dashboard.admin', 'display_name' => 'Admin Dashboard', 'description' => 'Access admin dashboard', 'resource' => 'dashboard', 'action' => 'admin', 'category' => 'admin_management'],
            ['name' => 'dashboard.analytics', 'display_name' => 'Analytics Dashboard', 'description' => 'View analytics and metrics', 'resource' => 'dashboard', 'action' => 'analytics', 'category' => 'reporting'],

            // System Maintenance
            ['name' => 'maintenance.enable', 'display_name' => 'Enable Maintenance Mode', 'description' => 'Put system in maintenance mode', 'resource' => 'maintenance', 'action' => 'enable', 'category' => 'system'],
            ['name' => 'maintenance.disable', 'display_name' => 'Disable Maintenance Mode', 'description' => 'Take system out of maintenance mode', 'resource' => 'maintenance', 'action' => 'disable', 'category' => 'system'],

            // Cache Management
            ['name' => 'cache.clear', 'display_name' => 'Clear Cache', 'description' => 'Clear application cache', 'resource' => 'cache', 'action' => 'clear', 'category' => 'system'],
            ['name' => 'cache.view', 'display_name' => 'View Cache Status', 'description' => 'View cache statistics and status', 'resource' => 'cache', 'action' => 'view', 'category' => 'system'],
        ];

        foreach ($adminPermissions as $permission) {
            AdvancedPermission::updateOrCreate(
                ['name' => $permission['name']],
                array_merge($permission, ['is_system' => true])
            );
        }

        $this->command->info('✓ Admin permissions created');
    }

    /**
     * Create finance-specific permissions
     */
    protected function createFinancePermissions(): void
    {
        $this->command->info('💰 Creating finance-specific permissions...');

        $financePermissions = [
            // Forex Trading
            ['name' => 'forex.rates.read', 'display_name' => 'View Forex Rates', 'description' => 'View forex exchange rates', 'resource' => 'forex_rates', 'action' => 'read', 'category' => 'finance'],
            ['name' => 'forex.rates.update', 'display_name' => 'Update Forex Rates', 'description' => 'Update forex exchange rates', 'resource' => 'forex_rates', 'action' => 'update', 'category' => 'finance'],
            ['name' => 'forex.bookings.create', 'display_name' => 'Create Forex Bookings', 'description' => 'Create forex trading bookings', 'resource' => 'forex_bookings', 'action' => 'create', 'category' => 'finance'],
            ['name' => 'forex.bookings.read', 'display_name' => 'View Forex Bookings', 'description' => 'View forex trading bookings', 'resource' => 'forex_bookings', 'action' => 'read', 'category' => 'finance'],
            ['name' => 'forex.bookings.approve', 'display_name' => 'Approve Forex Bookings', 'description' => 'Approve forex trading bookings', 'resource' => 'forex_bookings', 'action' => 'approve', 'category' => 'finance'],
            ['name' => 'forex.bookings.cancel', 'display_name' => 'Cancel Forex Bookings', 'description' => 'Cancel forex trading bookings', 'resource' => 'forex_bookings', 'action' => 'cancel', 'category' => 'finance'],

            // Virtual Accounts
            ['name' => 'virtual_accounts.create', 'display_name' => 'Create Virtual Accounts', 'description' => 'Create virtual bank accounts', 'resource' => 'virtual_accounts', 'action' => 'create', 'category' => 'finance'],
            ['name' => 'virtual_accounts.read', 'display_name' => 'View Virtual Accounts', 'description' => 'View virtual bank accounts', 'resource' => 'virtual_accounts', 'action' => 'read', 'category' => 'finance'],
            ['name' => 'virtual_accounts.update', 'display_name' => 'Update Virtual Accounts', 'description' => 'Update virtual bank accounts', 'resource' => 'virtual_accounts', 'action' => 'update', 'category' => 'finance'],
            ['name' => 'virtual_accounts.delete', 'display_name' => 'Delete Virtual Accounts', 'description' => 'Delete virtual bank accounts', 'resource' => 'virtual_accounts', 'action' => 'delete', 'category' => 'finance'],

            // Financial Approvals
            ['name' => 'finance.approve_large', 'display_name' => 'Approve Large Transactions', 'description' => 'Approve transactions above threshold', 'resource' => 'finance', 'action' => 'approve_large', 'category' => 'finance'],
            ['name' => 'finance.approve_unlimited', 'display_name' => 'Unlimited Approval', 'description' => 'Approve transactions of any amount', 'resource' => 'finance', 'action' => 'approve_unlimited', 'category' => 'finance'],

            // Payout Methods
            ['name' => 'payout_methods.create', 'display_name' => 'Create Payout Methods', 'description' => 'Create new payout methods', 'resource' => 'payout_methods', 'action' => 'create', 'category' => 'finance'],
            ['name' => 'payout_methods.read', 'display_name' => 'View Payout Methods', 'description' => 'View payout methods', 'resource' => 'payout_methods', 'action' => 'read', 'category' => 'finance'],
            ['name' => 'payout_methods.update', 'display_name' => 'Update Payout Methods', 'description' => 'Update payout methods', 'resource' => 'payout_methods', 'action' => 'update', 'category' => 'finance'],
            ['name' => 'payout_methods.delete', 'display_name' => 'Delete Payout Methods', 'description' => 'Delete payout methods', 'resource' => 'payout_methods', 'action' => 'delete', 'category' => 'finance'],
        ];

        foreach ($financePermissions as $permission) {
            AdvancedPermission::updateOrCreate(
                ['name' => $permission['name']],
                array_merge($permission, ['is_system' => true])
            );
        }

        $this->command->info('✓ Finance permissions created');
    }

    /**
     * Create user management permissions
     */
    protected function createUserManagementPermissions(): void
    {
        $this->command->info('👥 Creating user management permissions...');

        $userPermissions = [
            // User Actions
            ['name' => 'users.activate', 'display_name' => 'Activate Users', 'description' => 'Activate user accounts', 'resource' => 'users', 'action' => 'activate', 'category' => 'user_management'],
            ['name' => 'users.deactivate', 'display_name' => 'Deactivate Users', 'description' => 'Deactivate user accounts', 'resource' => 'users', 'action' => 'deactivate', 'category' => 'user_management'],
            ['name' => 'users.suspend', 'display_name' => 'Suspend Users', 'description' => 'Suspend user accounts', 'resource' => 'users', 'action' => 'suspend', 'category' => 'user_management'],
            ['name' => 'users.verify', 'display_name' => 'Verify Users', 'description' => 'Verify user accounts', 'resource' => 'users', 'action' => 'verify', 'category' => 'user_management'],

            // Merchant Management
            ['name' => 'merchants.activate', 'display_name' => 'Activate Merchants', 'description' => 'Activate merchant accounts', 'resource' => 'merchants', 'action' => 'activate', 'category' => 'merchant_management'],
            ['name' => 'merchants.deactivate', 'display_name' => 'Deactivate Merchants', 'description' => 'Deactivate merchant accounts', 'resource' => 'merchants', 'action' => 'deactivate', 'category' => 'merchant_management'],
            ['name' => 'merchants.approve', 'display_name' => 'Approve Merchants', 'description' => 'Approve merchant applications', 'resource' => 'merchants', 'action' => 'approve', 'category' => 'merchant_management'],
            ['name' => 'merchants.reject', 'display_name' => 'Reject Merchants', 'description' => 'Reject merchant applications', 'resource' => 'merchants', 'action' => 'reject', 'category' => 'merchant_management'],
        ];

        foreach ($userPermissions as $permission) {
            AdvancedPermission::updateOrCreate(
                ['name' => $permission['name']],
                array_merge($permission, ['is_system' => true])
            );
        }

        $this->command->info('✓ User management permissions created');
    }

    /**
     * Create compliance and audit permissions
     */
    protected function createCompliancePermissions(): void
    {
        $this->command->info('🔍 Creating compliance and audit permissions...');

        $compliancePermissions = [
            // KYC Management
            ['name' => 'kyc.approve', 'display_name' => 'Approve KYC', 'description' => 'Approve KYC submissions', 'resource' => 'kyc', 'action' => 'approve', 'category' => 'compliance'],
            ['name' => 'kyc.reject', 'display_name' => 'Reject KYC', 'description' => 'Reject KYC submissions', 'resource' => 'kyc', 'action' => 'reject', 'category' => 'compliance'],
            ['name' => 'kyc.review', 'display_name' => 'Review KYC', 'description' => 'Review KYC submissions', 'resource' => 'kyc', 'action' => 'review', 'category' => 'compliance'],

            // Audit and Logs
            ['name' => 'audit.read', 'display_name' => 'View Audit Logs', 'description' => 'View system audit logs', 'resource' => 'audit', 'action' => 'read', 'category' => 'compliance'],
            ['name' => 'audit.export', 'display_name' => 'Export Audit Logs', 'description' => 'Export audit logs for compliance', 'resource' => 'audit', 'action' => 'export', 'category' => 'compliance'],

            // Compliance Reports
            ['name' => 'compliance.reports', 'display_name' => 'Compliance Reports', 'description' => 'Generate compliance reports', 'resource' => 'compliance', 'action' => 'reports', 'category' => 'compliance'],
            ['name' => 'compliance.aml', 'display_name' => 'AML Monitoring', 'description' => 'Anti-money laundering monitoring', 'resource' => 'compliance', 'action' => 'aml', 'category' => 'compliance'],
        ];

        foreach ($compliancePermissions as $permission) {
            AdvancedPermission::updateOrCreate(
                ['name' => $permission['name']],
                array_merge($permission, ['is_system' => true])
            );
        }

        $this->command->info('✓ Compliance permissions created');
    }
}
