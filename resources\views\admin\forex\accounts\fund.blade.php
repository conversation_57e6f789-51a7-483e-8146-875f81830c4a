@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.show', $account->id) }}">
                                    {{ $account->account_name }}
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Fund Account')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Fund Account')</h1>
                    <p class="page-header-text">@lang('Add funds to') {{ $account->account_name }}</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.show', $account->id) }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Account')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row justify-content-lg-center">
            <div class="col-lg-8">
                <!-- Account Info Card -->
                <div class="card mb-3 mb-lg-5">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Information')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Account Name'):</dt>
                                    <dd class="col-sm-7">{{ $account->account_name }}</dd>

                                    <dt class="col-sm-5">@lang('Account Type'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-soft-primary text-primary">{{ $account->account_type }}</span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Currency'):</dt>
                                    <dd class="col-sm-7">{{ $account->currency_code }}</dd>
                                </dl>
                            </div>
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-6">@lang('Current Balance'):</dt>
                                    <dd class="col-sm-6">
                                        <span class="fw-semibold text-success">
                                            {{ number_format($account->balance, 2) }} {{ $account->currency_code }}
                                        </span>
                                    </dd>

                                    @if($account->pending_balance > 0)
                                        <dt class="col-sm-6">@lang('Pending Balance'):</dt>
                                        <dd class="col-sm-6">
                                            <span class="fw-semibold text-warning">
                                                {{ number_format($account->pending_balance, 2) }} {{ $account->currency_code }}
                                            </span>
                                        </dd>
                                    @endif

                                    <dt class="col-sm-6">@lang('Total Balance'):</dt>
                                    <dd class="col-sm-6">
                                        <span class="fw-bold text-primary">
                                            {{ number_format($account->balance + $account->pending_balance, 2) }} {{ $account->currency_code }}
                                        </span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Account Info Card -->

                <!-- Fund Account Card -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Add Funds')</h4>
                        <span class="badge bg-soft-info text-info">
                            <i class="bi-info-circle me-1"></i> @lang('Funds will be added immediately')
                        </span>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <form action="{{ route('admin.forex.accounts.fund.process', $account->id) }}" method="POST">
                            @csrf

                            <!-- Amount -->
                            <div class="row mb-4">
                                <label for="amountLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Amount') <span class="text-danger">*</span>
                                    <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Amount to add to the account')"></i>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <span class="input-group-text">{{ $account->currency_code === 'USD' ? '$' : '₦' }}</span>
                                        <input type="number" class="form-control @error('amount') is-invalid @enderror"
                                               name="amount" id="amountLabel" step="0.01" min="0.01"
                                               placeholder="@lang('0.00')" value="{{ old('amount') }}" required>
                                        <span class="input-group-text">{{ $account->currency_code }}</span>
                                    </div>
                                    @error('amount')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Enter the amount to add to this account')
                                    </small>
                                </div>
                            </div>
                            <!-- End Amount -->

                            <!-- Description -->
                            <div class="row mb-4">
                                <label for="descriptionLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Description') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control @error('description') is-invalid @enderror"
                                           name="description" id="descriptionLabel"
                                           placeholder="@lang('e.g., Initial funding, Bank transfer, Cash deposit')"
                                           value="{{ old('description') }}" required>
                                    @error('description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Brief description of the funding source or purpose')
                                    </small>
                                </div>
                            </div>
                            <!-- End Description -->

                            <!-- Notes -->
                            <div class="row mb-4">
                                <label for="notesLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Notes')
                                </label>
                                <div class="col-sm-9">
                                    <textarea class="form-control @error('notes') is-invalid @enderror"
                                              name="notes" id="notesLabel" rows="3"
                                              placeholder="@lang('Additional notes or comments (optional)')">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Optional: Additional details about this funding transaction')
                                    </small>
                                </div>
                            </div>
                            <!-- End Notes -->

                            <!-- New Balance Preview -->
                            <div class="row mb-4">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('New Balance Preview')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-success" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-calculator"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <span class="d-block">@lang('Current Balance')</span>
                                                        <span class="fw-semibold">{{ number_format($account->balance, 2) }} {{ $account->currency_code }}</span>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <span class="d-block">@lang('New Balance')</span>
                                                        <span class="fw-bold text-success" id="newBalance">{{ number_format($account->balance, 2) }} {{ $account->currency_code }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End New Balance Preview -->

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end">
                                <div class="d-flex gap-3">
                                    <a class="btn btn-white" href="{{ route('admin.forex.accounts.show', $account->id) }}">
                                        @lang('Cancel')
                                    </a>
                                    <button type="submit" class="btn btn-success">
                                        <i class="bi-plus-circle me-1"></i> @lang('Add Funds')
                                    </button>
                                </div>
                            </div>
                            <!-- End Submit Buttons -->
                        </form>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Fund Account Card -->
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            const currentBalance = {{ $account->balance }};
            const currencyCode = '{{ $account->currency_code }}';

            // Update new balance preview
            function updateNewBalance() {
                const amount = parseFloat($('#amountLabel').val()) || 0;
                const newBalance = currentBalance + amount;
                $('#newBalance').text(newBalance.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + ' ' + currencyCode);
            }

            // Bind calculation to amount input changes
            $('#amountLabel').on('input', updateNewBalance);

            // Initial calculation
            updateNewBalance();
        });
    </script>
@endpush
