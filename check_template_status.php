<?php

require 'vendor/autoload.php';

use App\Models\NotificationTemplate;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== CHECKING TEMPLATE STATUS ===\n\n";

$template = NotificationTemplate::where('template_key', 'FOREX_BOOKING_CONFIRMATION')->first();

if ($template) {
    echo "Template found: {$template->name}\n";
    echo "Status: " . json_encode($template->status) . "\n";
    echo "Mail enabled: " . ($template->status['mail'] ? 'Yes' : 'No') . "\n";
} else {
    echo "Template not found\n";
}

echo "\n=== DONE ===\n";
