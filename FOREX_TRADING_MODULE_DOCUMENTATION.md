# Forex Trading Module Documentation

## Overview

The Forex Trading Module is a comprehensive foreign exchange and currency trading system that handles the complete lifecycle of forex transactions from onboarding through transaction completion, with full regulatory compliance reporting and comprehensive financial tracking.

**Important Note:** This module is completely separate from the existing wallet exchange system and operates independently.

## Features Implemented

### 1. Exchange Rate Management
- **Rate Types**: CBN (Central Bank of Nigeria) Rate and Parallel Market Rate
- **Markup Percentage**: Configurable markup for rates
- **Active Rate System**: Only one rate set can be active at a time
- **Rate History Tracking**: Complete audit trail of all rate changes
- **Approval Workflow**: Rate validation and approval system
- **Dashboard Display**: Current rates displayed on admin dashboard

### 2. Account Balance Management (Admin Only)
- **Account Types**:
  - USD Account: Tracks USD balances
  - CBN Account: Naira for CBN rate transactions
  - Difference Account: Naira for rate differentials/premiums
  - Investment Account: Naira for internal transfers
- **Features**:
  - Real-time balance tracking
  - Account-to-account transfers
  - Balance history and audit trail
  - Multi-currency support (USD/NGN)
  - Pending balance management

### 3. Transaction/Booking System
- **Transaction Types**: Credit (funding), Debit (exchange)
- **Comprehensive Fields**:
  - Merchant/user information (ID, name, email, phone)
  - Currency (USD/NGN)
  - Amount, CBN rate, parallel rate
  - Calculated totals (CBN total, parallel total, difference)
  - Account details for payments
  - Status tracking (pending, completed, cancelled)
  - Status notes and timestamps
- **Complete Workflow**:
  - Booking creation with automatic rate calculations
  - Email notifications with payment instructions
  - Payment processing and completion
  - Automatic balance updates
  - Transaction record generation

### 4. Fund Management
- **USD Funding**: Manual credit to USD accounts
- **Naira Funding**: Manual credit to Naira accounts
- **Features**:
  - Account funding with validation
  - Real-time balance updates
  - Funding history tracking
  - Rate-based conversions

### 5. Email Notification System
- **Email Templates**:
  - Booking confirmation with payment instructions
  - Payment reminders for overdue bookings
  - Transaction completion notifications
- **Professional Design**: Company branding and responsive templates
- **Queue System**: Background email processing with retry logic
- **Email Tracking**: Delivery status and error tracking

### 6. Comprehensive Reporting System
- **CBN Compliance Reports**:
  - Daily Report: All transactions for specific date
  - Weekly Report: Weekly transaction summary
  - Monthly Report: Monthly transaction summary
  - Annual Report: Yearly transaction summary
  - Monthly Revenue Report: Revenue analysis
- **Excel Export**: All reports exportable using PhpSpreadsheet
- **Report Filters**: Date ranges, client types, transaction status
- **Access Control**: Role-based report access

### 7. Operational Cost Management
- **Cost Tracking**: Record business operational expenses
- **Cost Categories**: Various expense types (Office Rent, Utilities, Staff, etc.)
- **Comprehensive Fields**: Name, description, amount, date, recorded_by
- **File Attachments**: Support for receipts/invoices
- **Cost Reports**: Weekly, monthly operational cost summaries

### 8. Dashboard & Analytics
- **Key Metrics**:
  - USD Available Balance
  - USD Pending Balance
  - CBN Account Balance
  - Difference Account Balance
  - Investment Account Balance
  - Bookings Today/This Week
  - Completed Bookings Today/This Week
  - Operational Costs This Week
- **Real-time Updates**: Live balance calculations
- **Interactive Charts**: Transaction overview and revenue charts
- **Recent Activity**: Latest bookings and transactions
- **Quick Actions**: Fast access to common operations

## Technical Implementation

### Database Schema
The module includes 7 main database tables:

1. **forex_rates**: Exchange rate management
2. **forex_accounts**: Account balance tracking
3. **forex_bookings**: Booking/transaction requests
4. **forex_transactions**: Individual transaction records
5. **forex_account_transfers**: Inter-account transfers
6. **operational_costs**: Business expense tracking
7. **forex_email_logs**: Email notification tracking

### Laravel Features Utilized
- **Models**: Eloquent models with relationships and business logic
- **Controllers**: Resource controllers with proper validation
- **Middleware**: Role-based access control
- **Requests**: Form request validation classes
- **Jobs**: Email sending and background processing
- **Services**: Business logic separation
- **Notifications**: Email notifications for bookings
- **Caching**: Performance optimization
- **Pagination**: Laravel pagination for all listings
- **Seeders**: Default admin user and account balances

### Business Logic Implementation
- **Rate Calculations**: Automatic calculation of CBN total, parallel total, and difference
- **Balance Management**: Real-time balance updates across all accounts
- **Transaction Workflow**: Complete booking process with email notifications
- **Account Transfers**: Inter-account transfer functionality
- **Audit Trail**: Complete transaction history and user activity logging
- **Data Validation**: Comprehensive validation for all financial transactions
- **Security**: Role-based permissions and secure file handling

## File Structure

```
app/
├── Http/Controllers/Admin/
│   ├── ForexRateController.php
│   ├── ForexAccountController.php
│   ├── ForexBookingController.php
│   ├── ForexReportController.php
│   ├── ForexDashboardController.php
│   └── OperationalCostController.php
├── Http/Controllers/Api/
│   └── ForexController.php
├── Models/
│   ├── ForexRate.php
│   ├── ForexAccount.php
│   ├── ForexBooking.php
│   ├── ForexTransaction.php
│   ├── ForexAccountTransfer.php
│   ├── ForexEmailLog.php
│   └── OperationalCost.php
├── Services/
│   ├── ForexBookingService.php
│   ├── ForexReportService.php
│   └── ForexDashboardService.php

database/
├── migrations/
│   ├── 2025_07_03_100000_create_forex_rates_table.php
│   ├── 2025_07_03_100001_create_forex_accounts_table.php
│   ├── 2025_07_03_100002_create_forex_bookings_table.php
│   ├── 2025_07_03_100003_create_forex_transactions_table.php
│   ├── 2025_07_03_100004_create_forex_account_transfers_table.php
│   ├── 2025_07_03_100005_create_operational_costs_table.php
│   └── 2025_07_03_100006_create_forex_email_logs_table.php
└── seeders/
    └── ForexAccountSeeder.php

resources/views/
├── admin/forex/
│   ├── dashboard/index.blade.php
│   └── rates/index.blade.php


routes/
├── admin.php (forex routes added)
└── api.php (forex API routes added)
```

## Installation & Setup

### 1. Database Migration
```bash
php artisan migrate
```

### 2. Seed Default Accounts
```bash
php artisan db:seed --class=ForexAccountSeeder
```

### 3. Queue Configuration
Ensure your queue system is configured and running:
```bash
php artisan queue:work
```

### 4. Operational Costs Management
The operational costs system is fully integrated into the dashboard for on-the-fly recording:

**Features:**
- Record costs directly through the admin dashboard
- Add categories dynamically (no need to pre-configure)
- Upload attachments (receipts, invoices, documents)
- Filter and search by category, currency, date range
- Generate cost summaries and reports
- Export data in multiple formats

**Access Points:**
- Main operational costs page: `/admin/forex/costs`
- Quick record cost: `/admin/forex/costs/create`
- Cost summary: `/admin/forex/costs/summary`
- Quick actions available on the forex dashboard

**File Storage:**
File attachments are automatically stored in `assets/upload/operational-costs/` directory.

### 5. Email Configuration
Ensure your email configuration is set up in `.env` file for notifications.

## Usage Guide

### Admin Access
1. Navigate to **Admin Panel > Forex Trading**
2. Access the forex trading dashboard for overview
3. Manage exchange rates, accounts, bookings, and reports

### Setting Exchange Rates
1. Go to **Forex Trading > Exchange Rates**
2. Click **Add New Rate**
3. Enter CBN rate, parallel rate, and markup percentage
4. Submit for approval
5. Approve the rate to make it active

### Creating Bookings
1. Go to **Forex Trading > Bookings**
2. Click **Create New Booking**
3. Fill in client details and transaction information
4. System automatically calculates totals based on active rates
5. Booking is created and email notification sent

### Managing Accounts
1. Go to **Forex Trading > Accounts**
2. View all account balances and pending amounts
3. Fund accounts manually
4. Transfer funds between accounts
5. View transaction history

### Generating Reports
1. Go to **Forex Trading > Reports**
2. Select report type (Daily, Weekly, Monthly, Annual)
3. Choose date range and filters
4. Generate report in HTML, Excel, or PDF format

## API Documentation

### Base URL
```
/api/forex/
```

### Endpoints

#### Get Current Exchange Rates
```
GET /api/forex/rates
```

#### Calculate Exchange Amount
```
POST /api/forex/calculate
Body: {
    "amount": 100,
    "from_currency": "USD",
    "to_currency": "NGN"
}
```

#### Create Booking
```
POST /api/forex/booking
Body: {
    "transaction_type": "debit",
    "currency": "USD",
    "amount": 100,
    "client_name": "John Doe",
    "client_email": "<EMAIL>",
    "client_phone": "+**********"
}
```

#### Get Booking Status
```
GET /api/forex/booking/{reference}/status
```

#### Get Booking History
```
GET /api/forex/bookings/history?client_email=<EMAIL>
```

## Testing Guide

### Unit Testing
Create tests for:
1. **Model Relationships**: Test all Eloquent relationships
2. **Business Logic**: Test rate calculations and balance updates
3. **Service Classes**: Test booking workflow and email services
4. **API Endpoints**: Test all API responses and validation

### Integration Testing
Test complete workflows:
1. **Booking Creation**: From creation to completion
2. **Email Notifications**: Ensure emails are sent and logged
3. **Balance Updates**: Verify account balances are updated correctly
4. **Report Generation**: Test report generation and export

### Manual Testing Scenarios

#### Scenario 1: Complete Booking Workflow
1. Set an active exchange rate
2. Create a new booking
3. Verify email notification is sent
4. Complete the booking
5. Check account balances are updated
6. Verify completion email is sent

#### Scenario 2: Account Management
1. Fund a USD account
2. Transfer funds between accounts
3. Verify transaction history
4. Check balance calculations

#### Scenario 3: Reporting
1. Create several bookings with different dates
2. Generate daily, weekly, and monthly reports
3. Export reports to Excel
4. Verify data accuracy

#### Scenario 4: API Testing
1. Test all API endpoints with valid data
2. Test validation with invalid data
3. Test authentication for protected endpoints
4. Verify response formats

## Security Considerations

1. **Role-based Access**: Only authorized admins can access forex functions
2. **Input Validation**: All inputs are validated and sanitized
3. **SQL Injection Protection**: Using Eloquent ORM prevents SQL injection
4. **CSRF Protection**: All forms include CSRF tokens
5. **File Upload Security**: Operational cost attachments are validated
6. **API Rate Limiting**: Consider implementing rate limiting for API endpoints

## Performance Optimization

1. **Database Indexing**: All foreign keys and frequently queried columns are indexed
2. **Caching**: Dashboard data and account balances are cached
3. **Queue Processing**: Email notifications are processed in background
4. **Pagination**: Large datasets are paginated
5. **Eager Loading**: Related models are eager loaded to prevent N+1 queries

## Maintenance

### Regular Tasks
1. **Monitor Queue**: Ensure email queue is processing correctly
2. **Check Logs**: Review application logs for errors
3. **Database Cleanup**: Archive old transactions if needed
4. **Report Generation**: Generate regular compliance reports

### Backup Considerations
1. **Database Backup**: Regular backup of forex-related tables
2. **File Backup**: Backup operational cost attachments
3. **Configuration Backup**: Backup exchange rate configurations

## Support & Troubleshooting

### Common Issues
1. **No Active Rate**: Ensure an exchange rate is set and approved
2. **Email Not Sending**: Check queue configuration and email settings
3. **Balance Discrepancies**: Review transaction logs and audit trail
4. **Report Generation Errors**: Check date ranges and data availability

### Logging
The system logs all important operations:
- Booking creation and completion
- Email sending status
- Account balance changes
- Rate approvals and changes

### Contact
For technical support or questions about the forex trading module, refer to the development team or system administrator.

---

**Version**: 1.0  
**Last Updated**: July 3, 2025  
**Author**: Augment Agent
