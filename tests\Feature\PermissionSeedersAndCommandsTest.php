<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\AdvancedPermission;
use App\Models\AdvancedRole;
use App\Models\AdvancedUserRole;
use App\Models\User;
use App\Models\Admin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

/**
 * Permission Seeders and Commands Tests
 * 
 * Tests the seeding functionality and management commands.
 */
class PermissionSeedersAndCommandsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_seed_advanced_permissions()
    {
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder'])
            ->assertExitCode(0);

        // Check that permissions were created
        $this->assertGreaterThan(0, AdvancedPermission::count());

        // Check for specific core permissions
        $this->assertDatabaseHas('advanced_permissions', [
            'name' => 'advanced_roles.create',
            'category' => 'role_management',
            'is_system' => true,
        ]);

        $this->assertDatabaseHas('advanced_permissions', [
            'name' => 'users.create',
            'category' => 'user_management',
        ]);

        // Check permission categories
        $categories = AdvancedPermission::distinct()->pluck('category')->filter();
        $this->assertContains('role_management', $categories);
        $this->assertContains('user_management', $categories);
        $this->assertContains('finance', $categories);
        $this->assertContains('compliance', $categories);
    }

    /** @test */
    public function it_can_seed_advanced_roles()
    {
        // First seed permissions
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        
        // Then seed roles
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder'])
            ->assertExitCode(0);

        // Check that roles were created
        $this->assertGreaterThan(0, AdvancedRole::count());

        // Check for specific template roles
        $this->assertDatabaseHas('advanced_roles', [
            'name' => 'super_admin',
            'display_name' => 'Super Administrator',
        ]);

        $this->assertDatabaseHas('advanced_roles', [
            'name' => 'finance_manager',
            'category' => 'finance',
        ]);

        // Check that roles have permissions assigned
        $superAdmin = AdvancedRole::findByName('super_admin');
        $this->assertNotNull($superAdmin);
        $this->assertGreaterThan(0, $superAdmin->permissions()->count());

        // Check role hierarchy
        $financeHead = AdvancedRole::findByName('finance_department_head');
        $financeAnalyst = AdvancedRole::findByName('finance_analyst');
        
        if ($financeHead && $financeAnalyst) {
            $this->assertEquals($financeHead->id, $financeAnalyst->parent_role_id);
        }
    }

    /** @test */
    public function it_can_discover_permissions_via_command()
    {
        $this->artisan('permissions:discover', ['--sync'])
            ->assertExitCode(0);

        // Check that permissions were discovered and created
        $this->assertGreaterThan(0, AdvancedPermission::count());

        // Check for system permissions that should be discovered
        $systemPermissions = AdvancedPermission::where('is_system', true)->count();
        $this->assertGreaterThan(0, $systemPermissions);
    }

    /** @test */
    public function it_can_show_system_status()
    {
        // Seed some data first
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        $this->artisan('permissions:manage', ['action' => 'status'])
            ->expectsOutput('📊 System Status')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_list_roles_and_permissions()
    {
        // Seed some data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        // Test list roles
        $this->artisan('permissions:manage', ['action' => 'list-roles'])
            ->assertExitCode(0);

        // Test list permissions
        $this->artisan('permissions:manage', ['action' => 'list-permissions'])
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_assign_and_revoke_roles()
    {
        // Seed data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        // Create a test user
        $user = User::factory()->create(['use_advanced_roles' => true]);

        // Assign role
        $this->artisan('permissions:manage', [
            'action' => 'assign-role',
            '--user' => $user->id,
            '--role' => 'basic_user',
        ])->assertExitCode(0);

        // Check assignment was created
        $this->assertDatabaseHas('advanced_user_roles', [
            'user_id' => $user->id,
            'user_type' => User::class,
            'is_active' => true,
        ]);

        // Revoke role
        $this->artisan('permissions:manage', [
            'action' => 'revoke-role',
            '--user' => $user->id,
            '--role' => 'basic_user',
        ])->assertExitCode(0);

        // Check assignment was revoked
        $this->assertDatabaseHas('advanced_user_roles', [
            'user_id' => $user->id,
            'user_type' => User::class,
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_can_show_user_info()
    {
        // Seed data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        // Create user with role
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::findByName('basic_user');
        $user->assignAdvancedRole($role);

        $this->artisan('permissions:manage', [
            'action' => 'user-info',
            '--user' => $user->id,
        ])
            ->expectsOutput("👤 User Information: {$user->name} ({$user->email})")
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_validate_system_integrity()
    {
        // Seed clean data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        $this->artisan('permissions:manage', ['action' => 'validate'])
            ->expectsOutput('✅ System integrity validated - no issues found')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_cleanup_system()
    {
        // Seed data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);

        // Create an unused permission
        AdvancedPermission::create([
            'name' => 'unused.permission',
            'display_name' => 'Unused Permission',
            'resource' => 'unused',
            'action' => 'permission',
            'category' => 'test',
            'is_system' => false,
        ]);

        // Create an expired assignment
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        AdvancedUserRole::create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'role_id' => $role->id,
            'expires_at' => now()->subDay(),
            'is_active' => true,
        ]);

        $this->artisan('permissions:manage', ['action' => 'cleanup'])
            ->assertExitCode(0);

        // Check that unused permission was removed
        $this->assertDatabaseMissing('advanced_permissions', [
            'name' => 'unused.permission',
        ]);

        // Check that expired assignment was deactivated
        $this->assertDatabaseHas('advanced_user_roles', [
            'user_id' => $user->id,
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_can_run_health_check()
    {
        // Seed data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        $this->artisan('permissions:health')
            ->expectsOutput('🏥 Advanced Permission System Health Check')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_run_health_check_with_alerts_only()
    {
        // Seed data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);

        $this->artisan('permissions:health', ['--alerts'])
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_create_roles_from_templates()
    {
        // Seed permissions first
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);

        $this->artisan('permissions:templates', [
            'action' => 'create',
            'template' => 'basic_user',
        ])->assertExitCode(0);

        // Check that role was created
        $this->assertDatabaseHas('advanced_roles', [
            'name' => 'basic_user',
            'display_name' => 'Basic User',
        ]);

        // Check that role has permissions
        $role = AdvancedRole::findByName('basic_user');
        $this->assertNotNull($role);
        $this->assertGreaterThan(0, $role->permissions()->count());
    }

    /** @test */
    public function it_can_list_available_templates()
    {
        $this->artisan('permissions:templates', ['action' => 'list'])
            ->expectsOutput('📋 Available Permission Templates')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_create_all_template_roles()
    {
        // Seed permissions first
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);

        $this->artisan('permissions:templates', ['action' => 'create-all'])
            ->assertExitCode(0);

        // Check that multiple template roles were created
        $this->assertDatabaseHas('advanced_roles', ['name' => 'super_admin']);
        $this->assertDatabaseHas('advanced_roles', ['name' => 'admin']);
        $this->assertDatabaseHas('advanced_roles', ['name' => 'finance_manager']);
        $this->assertDatabaseHas('advanced_roles', ['name' => 'basic_user']);
    }

    /** @test */
    public function it_handles_dry_run_mode()
    {
        $this->artisan('permissions:discover', ['--dry-run'])
            ->expectsOutput('🔥 DRY RUN MODE - No changes were made to the database')
            ->assertExitCode(0);

        // No permissions should be created in dry-run mode
        $this->assertEquals(0, AdvancedPermission::count());
    }

    /** @test */
    public function it_can_reset_system_with_force()
    {
        // Seed some data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        $this->assertGreaterThan(0, AdvancedPermission::count());
        $this->assertGreaterThan(0, AdvancedRole::count());

        // Reset with force
        $this->artisan('permissions:manage', [
            'action' => 'reset',
            '--force',
        ])->assertExitCode(0);

        // Check that everything was reset
        $this->assertEquals(0, AdvancedPermission::count());
        $this->assertEquals(0, AdvancedRole::count());
        $this->assertEquals(0, AdvancedUserRole::count());
    }

    /** @test */
    public function it_shows_help_for_invalid_actions()
    {
        $this->artisan('permissions:manage', ['action' => 'invalid'])
            ->expectsOutput('🔧 Advanced Permission System Manager')
            ->expectsOutput('Available actions:')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_handles_missing_parameters_gracefully()
    {
        $this->artisan('permissions:manage', [
            'action' => 'assign-role',
            // Missing --user and --role parameters
        ])->expectsOutput('❌ Both --user and --role are required')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_can_export_health_report()
    {
        $filename = storage_path('test_health_report.json');
        
        // Clean up any existing file
        if (file_exists($filename)) {
            unlink($filename);
        }

        $this->artisan('permissions:health', ['--export' => $filename])
            ->assertExitCode(0);

        // Check that file was created
        $this->assertFileExists($filename);

        // Check file content
        $content = json_decode(file_get_contents($filename), true);
        $this->assertArrayHasKey('timestamp', $content);
        $this->assertArrayHasKey('health_data', $content);
        $this->assertArrayHasKey('alerts', $content);

        // Clean up
        unlink($filename);
    }

    /** @test */
    public function it_validates_user_existence_for_role_operations()
    {
        $this->artisan('permissions:manage', [
            'action' => 'user-info',
            '--user' => 99999, // Non-existent user
        ])->expectsOutput('❌ User with ID 99999 not found')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_validates_role_existence_for_assignment()
    {
        $user = User::factory()->create();

        $this->artisan('permissions:manage', [
            'action' => 'assign-role',
            '--user' => $user->id,
            '--role' => 'non_existent_role',
        ])->expectsOutput("❌ Role 'non_existent_role' not found")
            ->assertExitCode(1);
    }
}
