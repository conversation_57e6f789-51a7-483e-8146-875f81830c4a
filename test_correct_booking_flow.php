<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Models\ForexTransaction;
use App\Services\ForexBookingService;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== CORRECT BOOKING FLOW TEST ===\n\n";

$usdAccount = ForexAccount::where('currency_code', 'USD')->first();
$initialBalance = $usdAccount->balance;

echo "Initial Account State:\n";
echo "- Total Balance: $" . number_format($initialBalance, 2) . "\n";
echo "- Pending Balance: $" . number_format($usdAccount->pending_balance, 2) . "\n";
echo "- Available Balance: $" . number_format($usdAccount->available_balance, 2) . "\n\n";

$bookingService = app(ForexBookingService::class);

// Test: Complete Booking Flow
echo "=== COMPLETE BOOKING FLOW TEST ===\n";

$bookingData = [
    'client_name' => 'Test Client',
    'client_email' => '<EMAIL>',
    'client_phone' => '**********',
    'client_type' => 'individual',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 100,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test account'
];

try {
    // Step 1: Create Booking
    echo "Step 1: Creating booking...\n";
    $booking = $bookingService->createBooking($bookingData, 1);
    echo "✅ Booking created: {$booking->booking_reference}\n";
    
    $usdAccount->refresh();
    $balanceAfterBooking = $usdAccount->balance;
    
    echo "Account after booking creation:\n";
    echo "- Total Balance: $" . number_format($balanceAfterBooking, 2) . 
         " (Change: $" . number_format($balanceAfterBooking - $initialBalance, 2) . ")\n";
    echo "- Pending Balance: $" . number_format($usdAccount->pending_balance, 2) . "\n";
    echo "- Available Balance: $" . number_format($usdAccount->available_balance, 2) . "\n";
    
    // Check booked transaction
    $bookedTransaction = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_subtype', 'booked')
        ->first();
    
    echo "Booked Transaction:\n";
    echo "- Type: {$bookedTransaction->transaction_type}\n";
    echo "- Subtype: {$bookedTransaction->transaction_subtype}\n";
    echo "- Amount: $" . number_format($bookedTransaction->amount, 2) . "\n";
    echo "- Is Completed: " . ($bookedTransaction->is_completed ? 'Yes' : 'No') . "\n\n";
    
    // Step 2: Complete Booking
    echo "Step 2: Completing booking...\n";
    $bookingService->completeBooking($booking, 1, 'Payment received');
    echo "✅ Booking completed\n";
    
    $usdAccount->refresh();
    $balanceAfterCompletion = $usdAccount->balance;
    
    echo "Account after completion:\n";
    echo "- Total Balance: $" . number_format($balanceAfterCompletion, 2) . 
         " (Change from initial: $" . number_format($balanceAfterCompletion - $initialBalance, 2) . ")\n";
    echo "- Pending Balance: $" . number_format($usdAccount->pending_balance, 2) . "\n";
    echo "- Available Balance: $" . number_format($usdAccount->available_balance, 2) . "\n";
    
    // Check all transactions for this booking
    $allTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->orderBy('id')
        ->get();
    
    echo "All transactions for this booking:\n";
    foreach ($allTransactions as $transaction) {
        $balanceChange = $transaction->balance_after - $transaction->balance_before;
        echo "- {$transaction->transaction_type} ({$transaction->transaction_subtype}): " .
             "$" . number_format($transaction->amount, 2) . 
             " | Balance change: $" . number_format($balanceChange, 2) . 
             " | Completed: " . ($transaction->is_completed ? 'Yes' : 'No') . "\n";
    }
    
    echo "\n=== EXPECTED RESULTS ===\n";
    echo "✅ Total balance should be reduced by exactly $100 (single debit)\n";
    echo "✅ Should have 2 transactions: 1 debit (booked) + 1 record (completed)\n";
    echo "✅ Only the booked transaction should affect account balance\n";
    echo "✅ Completion transaction should be record-only with no balance change\n";
    echo "✅ Pending balance should be $0 after completion\n\n";
    
    // Verify results
    $actualBalanceChange = $balanceAfterCompletion - $initialBalance;
    $expectedBalanceChange = -100;
    
    if (abs($actualBalanceChange - $expectedBalanceChange) < 0.01) {
        echo "✅ PERFECT! Balance change is exactly -$100 (single debit only)\n";
    } else {
        echo "❌ ERROR! Expected balance change: $" . number_format($expectedBalanceChange, 2) . 
             ", Actual: $" . number_format($actualBalanceChange, 2) . "\n";
    }
    
    if ($usdAccount->pending_balance == 0) {
        echo "✅ PERFECT! Pending balance cleared after completion\n";
    } else {
        echo "❌ ERROR! Pending balance should be $0, but is $" . 
             number_format($usdAccount->pending_balance, 2) . "\n";
    }
    
    if ($allTransactions->count() == 2) {
        echo "✅ PERFECT! Exactly 2 transactions created\n";
        
        $debitTransactions = $allTransactions->where('transaction_type', 'debit')->count();
        $recordTransactions = $allTransactions->where('transaction_type', 'record')->count();
        
        if ($debitTransactions == 1 && $recordTransactions == 1) {
            echo "✅ PERFECT! 1 debit transaction + 1 record transaction\n";
        } else {
            echo "❌ ERROR! Expected 1 debit + 1 record, got {$debitTransactions} debit + {$recordTransactions} record\n";
        }
    } else {
        echo "❌ ERROR! Expected 2 transactions, got " . $allTransactions->count() . "\n";
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TRANSACTION FLOW SUMMARY ===\n";
echo "✅ Booking Creation: Creates 'booked' debit transaction (affects balance)\n";
echo "✅ Booking Completion: Creates 'completed' record transaction (no balance change)\n";
echo "✅ Pending Balance: Tracks uncompleted booked transactions\n";
echo "✅ Available Balance: Total balance minus pending balance\n";
echo "✅ No Double Debiting: Only one actual debit per booking\n";

echo "\n=== TEST COMPLETE ===\n";
?>
