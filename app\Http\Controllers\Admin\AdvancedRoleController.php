<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Models\AdvancedUserRole;
use App\Services\PermissionTemplateService;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

/**
 * Advanced Role Management Controller
 * 
 * Handles CRUD operations for advanced roles with permission assignment,
 * hierarchy management, and bulk operations.
 */
class AdvancedRoleController extends Controller
{
    use HasAdvancedPermissions;

    protected PermissionTemplateService $templateService;

    public function __construct(PermissionTemplateService $templateService)
    {
        $this->templateService = $templateService;
        
        // Apply advanced permission middleware
        $this->middleware('advanced.permission:advanced_roles.read')->only(['index', 'show']);
        $this->middleware('advanced.permission:advanced_roles.create')->only(['create', 'store']);
        $this->middleware('advanced.permission:advanced_roles.update')->only(['edit', 'update']);
        $this->middleware('advanced.permission:advanced_roles.delete')->only(['destroy']);
    }

    /**
     * Display a listing of advanced roles
     */
    public function index(Request $request): View
    {
        $query = AdvancedRole::with(['parentRole', 'childRoles', 'permissions'])
            ->withCount(['userRoles', 'permissions']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'system') {
                $query->where('is_system', true);
            }
        }

        if ($request->filled('parent_role')) {
            $query->where('parent_role_id', $request->get('parent_role'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'level');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['name', 'display_name', 'category', 'level', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $roles = $query->paginate(20)->withQueryString();

        // Get filter options
        $categories = AdvancedRole::distinct()->pluck('category')->filter()->sort();
        $parentRoles = AdvancedRole::whereNull('parent_role_id')
            ->where('is_active', true)
            ->pluck('display_name', 'id');

        return view('admin.advanced-roles.index', compact(
            'roles',
            'categories',
            'parentRoles'
        ));
    }

    /**
     * Show the form for creating a new role
     */
    public function create(Request $request): View
    {
        $parentRoles = AdvancedRole::where('is_active', true)
            ->orderBy('level')
            ->orderBy('display_name')
            ->get();

        $permissions = AdvancedPermission::active()
            ->ordered()
            ->get()
            ->groupBy('category');

        $templates = $this->templateService->getAvailableTemplates();

        // If template is specified, pre-fill form
        $selectedTemplate = null;
        if ($request->filled('template')) {
            $selectedTemplate = $this->templateService->getTemplate($request->get('template'));
        }

        return view('admin.advanced-roles.create', compact(
            'parentRoles',
            'permissions',
            'templates',
            'selectedTemplate'
        ));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:advanced_roles,name|regex:/^[a-z_]+$/',
            'display_name' => 'required|string|max:150',
            'description' => 'nullable|string|max:1000',
            'category' => 'nullable|string|max:50',
            'scope' => 'required|string|in:global,department,project',
            'parent_role_id' => 'nullable|exists:advanced_roles,id',
            'inherit_permissions' => 'boolean',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'max_users' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'permissions' => 'array',
            'permissions.*' => 'exists:advanced_permissions,id',
            'permission_constraints' => 'array',
            'template' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();
        try {
            // Create from template if specified
            if ($request->filled('template')) {
                $role = $this->templateService->createRoleFromTemplate(
                    $request->get('template'),
                    $request->only([
                        'name', 'display_name', 'description', 'category',
                        'scope', 'parent_role_id', 'inherit_permissions',
                        'is_active', 'max_users', 'expires_at', 'color'
                    ])
                );
            } else {
                // Create role manually
                $role = AdvancedRole::create($request->only([
                    'name', 'display_name', 'description', 'category',
                    'scope', 'parent_role_id', 'inherit_permissions',
                    'is_active', 'is_default', 'max_users', 'expires_at', 'color'
                ]));

                // Assign permissions
                if ($request->filled('permissions')) {
                    $this->assignPermissionsToRole($role, $request->get('permissions'), $request->get('permission_constraints', []));
                }
            }

            DB::commit();

            $this->logPermissionEvent(
                'role_created',
                'advanced_roles.create',
                "Created role: {$role->display_name}",
                ['role_id' => $role->id]
            );

            return redirect()->route('admin.advanced-roles.show', $role)
                ->with('success', "Role '{$role->display_name}' created successfully.");

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to create role: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified role
     */
    public function show(AdvancedRole $role): View
    {
        $role->load([
            'parentRole',
            'childRoles.userRoles',
            'permissions' => function ($query) {
                $query->orderBy('category')->orderBy('resource')->orderBy('action');
            },
            'rolePermissions.permission',
            'userRoles.user',
            'creator',
            'updater'
        ]);

        // Get role statistics
        $stats = [
            'total_permissions' => $role->permissions()->count(),
            'inherited_permissions' => $role->inherit_permissions && $role->parentRole 
                ? $role->parentRole->getAllPermissions()->count() 
                : 0,
            'active_users' => $role->userRoles()->where('is_active', true)->count(),
            'child_roles' => $role->childRoles()->count(),
        ];

        // Get permission breakdown by category
        $permissionsByCategory = $role->permissions()
            ->select('category', DB::raw('count(*) as count'))
            ->groupBy('category')
            ->pluck('count', 'category');

        // Get recent assignments
        $recentAssignments = $role->userRoles()
            ->with('user')
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.advanced-roles.show', compact(
            'role',
            'stats',
            'permissionsByCategory',
            'recentAssignments'
        ));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(AdvancedRole $role): View
    {
        $this->authorize('update', $role);

        $role->load(['permissions', 'rolePermissions.permission']);

        $parentRoles = AdvancedRole::where('is_active', true)
            ->where('id', '!=', $role->id)
            ->orderBy('level')
            ->orderBy('display_name')
            ->get();

        $permissions = AdvancedPermission::active()
            ->ordered()
            ->get()
            ->groupBy('category');

        // Get current permission assignments with constraints
        $currentPermissions = $role->rolePermissions->keyBy('permission_id');

        return view('admin.advanced-roles.edit', compact(
            'role',
            'parentRoles',
            'permissions',
            'currentPermissions'
        ));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, AdvancedRole $role): RedirectResponse
    {
        $this->authorize('update', $role);

        $validator = Validator::make($request->all(), [
            'name' => "required|string|max:100|unique:advanced_roles,name,{$role->id}|regex:/^[a-z_]+$/",
            'display_name' => 'required|string|max:150',
            'description' => 'nullable|string|max:1000',
            'category' => 'nullable|string|max:50',
            'scope' => 'required|string|in:global,department,project',
            'parent_role_id' => 'nullable|exists:advanced_roles,id',
            'inherit_permissions' => 'boolean',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'max_users' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'permissions' => 'array',
            'permissions.*' => 'exists:advanced_permissions,id',
            'permission_constraints' => 'array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Prevent circular hierarchy
        if ($request->filled('parent_role_id')) {
            $parentId = $request->get('parent_role_id');
            if ($this->wouldCreateCircularHierarchy($role->id, $parentId)) {
                return back()->withErrors(['parent_role_id' => 'This would create a circular hierarchy.'])->withInput();
            }
        }

        DB::beginTransaction();
        try {
            $role->update($request->only([
                'name', 'display_name', 'description', 'category',
                'scope', 'parent_role_id', 'inherit_permissions',
                'is_active', 'is_default', 'max_users', 'expires_at', 'color'
            ]));

            // Update permissions
            if ($request->has('permissions')) {
                $this->syncRolePermissions($role, $request->get('permissions'), $request->get('permission_constraints', []));
            }

            DB::commit();

            $this->logPermissionEvent(
                'role_updated',
                'advanced_roles.update',
                "Updated role: {$role->display_name}",
                ['role_id' => $role->id]
            );

            return redirect()->route('admin.advanced-roles.show', $role)
                ->with('success', "Role '{$role->display_name}' updated successfully.");

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update role: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified role
     */
    public function destroy(AdvancedRole $role): RedirectResponse
    {
        $this->authorize('delete', $role);

        try {
            $roleName = $role->display_name;
            $role->delete();

            $this->logPermissionEvent(
                'role_deleted',
                'advanced_roles.delete',
                "Deleted role: {$roleName}",
                ['role_name' => $roleName]
            );

            return redirect()->route('admin.advanced-roles.index')
                ->with('success', "Role '{$roleName}' deleted successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete role: ' . $e->getMessage()]);
        }
    }

    /**
     * Assign permissions to role
     */
    protected function assignPermissionsToRole(AdvancedRole $role, array $permissionIds, array $constraints = []): void
    {
        foreach ($permissionIds as $permissionId) {
            $permissionConstraints = $constraints[$permissionId] ?? null;
            
            $permission = AdvancedPermission::find($permissionId);
            if ($permission) {
                $role->grantPermission($permission, $permissionConstraints);
            }
        }
    }

    /**
     * Sync role permissions
     */
    protected function syncRolePermissions(AdvancedRole $role, array $permissionIds, array $constraints = []): void
    {
        // Remove existing permissions
        $role->permissions()->detach();

        // Add new permissions
        $this->assignPermissionsToRole($role, $permissionIds, $constraints);
    }

    /**
     * Check if parent assignment would create circular hierarchy
     */
    protected function wouldCreateCircularHierarchy(int $roleId, int $parentId): bool
    {
        $current = AdvancedRole::find($parentId);
        
        while ($current) {
            if ($current->id === $roleId) {
                return true;
            }
            $current = $current->parentRole;
        }

        return false;
    }
}
