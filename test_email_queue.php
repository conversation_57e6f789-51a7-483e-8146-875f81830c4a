<?php

require 'vendor/autoload.php';

use App\Traits\Notify;
use Illuminate\Support\Facades\DB;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== TESTING EMAIL QUEUE ===\n\n";

// Check initial queue count
$initialCount = DB::table('jobs')->count();
echo "Initial queue jobs: {$initialCount}\n";

// Test class with Notify trait
class TestEmailQueue {
    use Notify;
}

$emailTest = new TestEmailQueue();

// Create test user object
$testUser = new stdClass();
$testUser->email = '<EMAIL>';
$testUser->username = 'Queue Test User';
$testUser->language_id = 1;
$testUser->notifypermission = new stdClass();
$testUser->notifypermission->template_email_key = [
    'FOREX_BOOKING_CONFIRMATION',
    'FOREX_BOOKING_COMPLETION', 
    'FOREX_PAYMENT_REMINDER'
];

// Test parameters
$params = [
    'client_name' => 'Queue Test Client',
    'booking_reference' => 'QUEUE123',
    'transaction_type' => 'Buying',
    'amount' => '100.00',
    'currency' => 'USD',
    'customer_rate' => '1500.00',
    'customer_total' => '150000.00',
    'status' => 'Pending',
    'payment_instructions' => 'Test payment instructions for queue'
];

echo "\nSending test email...\n";
try {
    $result = $emailTest->mail($testUser, 'FOREX_BOOKING_CONFIRMATION', $params);
    echo "Email result: " . ($result ? 'Success' : 'Failed') . "\n";
    
    // Check queue count after
    $finalCount = DB::table('jobs')->count();
    echo "Final queue jobs: {$finalCount}\n";
    echo "Jobs added: " . ($finalCount - $initialCount) . "\n";
    
    if ($finalCount > $initialCount) {
        echo "✅ Email successfully queued!\n";
        echo "\nTo process the email, run:\n";
        echo "php artisan queue:work --once\n";
    } else {
        echo "❌ No job was queued. Check logs for errors.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
