


<?php if(session()->has('success')): ?>
    <script>
        Notiflix.Notify.success("<?php echo app('translator')->get(session('success')); ?>");
    </script>
<?php endif; ?>

<?php if(session()->has('error')): ?>
    <script>
        Notiflix.Notify.failure("<?php echo app('translator')->get(session('error')); ?>");
    </script>
<?php endif; ?>

<?php if(session()->has('warning')): ?>
    <script>
        Notiflix.Notify.warning("<?php echo app('translator')->get(session('warning')); ?>");
    </script>
<?php endif; ?>

<?php if(session()->has('info')): ?>
    <script>
        Notiflix.Notify.info("<?php echo app('translator')->get(session('info')); ?>");
    </script>
<?php endif; ?>

<?php echo $__env->yieldPushContent('notify'); ?>
<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/components/notify.blade.php ENDPATH**/ ?>