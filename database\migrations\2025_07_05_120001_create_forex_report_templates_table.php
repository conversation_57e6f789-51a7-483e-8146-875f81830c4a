<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_report_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->text('description')->nullable();
            $table->enum('report_type', ['cbn_focused', 'bookings', 'volume', 'performance', 'buy_transactions', 'sell_transactions']);
            $table->json('configuration')->comment('Report configuration including fields, filters, sorting, etc.');
            $table->boolean('is_public')->default(false)->comment('Whether template is available to all users');
            $table->foreignId('created_by')->nullable()->constrained('admins')->onDelete('set null');
            $table->integer('usage_count')->default(0)->comment('Number of times template has been used');
            $table->timestamps();
            
            // Indexes
            $table->index(['report_type', 'is_public']);
            $table->index(['is_public', 'usage_count']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_report_templates');
    }
};
