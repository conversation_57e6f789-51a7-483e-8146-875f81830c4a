<?php

// Test script to verify rate calculations
require_once 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexRate;

// Test data
$cbnRate = 1500;
$parallelRate = 1850;
$markupPercentage = 2; // 2%
$amount = 100; // $100 USD

echo "=== FOREX RATE CALCULATION TEST ===\n";
echo "CBN Rate: ₦{$cbnRate}\n";
echo "Parallel Rate: ₦{$parallelRate}\n";
echo "Markup: {$markupPercentage}%\n";
echo "Amount: \${$amount}\n\n";

// Calculate expected values
$expectedCustomerRate = $parallelRate + ($parallelRate * $markupPercentage / 100);
$expectedCustomerTotal = $amount * $expectedCustomerRate;
$expectedCbnTotal = $amount * $cbnRate;
$expectedParallelTotal = $amount * $parallelRate;
$expectedMarkupAmount = $expectedCustomerTotal - $expectedParallelTotal;
$expectedDifferenceAmount = $expectedParallelTotal - $expectedCbnTotal;

echo "=== EXPECTED CALCULATIONS ===\n";
echo "Customer Rate: ₦{$expectedCustomerRate}\n";
echo "Customer Total (what client pays): ₦{$expectedCustomerTotal}\n";
echo "CBN Total: ₦{$expectedCbnTotal}\n";
echo "Parallel Total: ₦{$expectedParallelTotal}\n";
echo "Markup Amount: ₦{$expectedMarkupAmount}\n";
echo "Difference Amount: ₦{$expectedDifferenceAmount}\n\n";

// Test with ForexBooking model
$booking = new ForexBooking();
$booking->transaction_type = 'buying';
$booking->currency = 'USD';
$booking->amount = $amount;

$booking->calculateTotals($cbnRate, $parallelRate, $markupPercentage);

echo "=== ACTUAL MODEL CALCULATIONS ===\n";
echo "Customer Rate: ₦{$booking->customer_rate}\n";
echo "Customer Total: ₦{$booking->customer_total}\n";
echo "Customer Payment Amount: ₦{$booking->customer_payment_amount}\n";
echo "CBN Total: ₦{$booking->cbn_total}\n";
echo "Parallel Total: ₦{$booking->parallel_total}\n";
echo "Markup Amount: ₦{$booking->markup_amount}\n";
echo "Difference Amount: ₦{$booking->difference_amount}\n\n";

// Verify calculations
$errors = [];

if ($booking->customer_rate != $expectedCustomerRate) {
    $errors[] = "Customer Rate mismatch: Expected ₦{$expectedCustomerRate}, Got ₦{$booking->customer_rate}";
}

if ($booking->customer_total != $expectedCustomerTotal) {
    $errors[] = "Customer Total mismatch: Expected ₦{$expectedCustomerTotal}, Got ₦{$booking->customer_total}";
}

if ($booking->customer_payment_amount != $expectedCustomerTotal) {
    $errors[] = "Customer Payment Amount mismatch: Expected ₦{$expectedCustomerTotal}, Got ₦{$booking->customer_payment_amount}";
}

if ($booking->markup_amount != $expectedMarkupAmount) {
    $errors[] = "Markup Amount mismatch: Expected ₦{$expectedMarkupAmount}, Got ₦{$booking->markup_amount}";
}

if ($booking->difference_amount != $expectedDifferenceAmount) {
    $errors[] = "Difference Amount mismatch: Expected ₦{$expectedDifferenceAmount}, Got ₦{$booking->difference_amount}";
}

if (empty($errors)) {
    echo "✅ ALL CALCULATIONS CORRECT!\n";
    echo "\nFor a \${$amount} USD purchase:\n";
    echo "- Client pays: ₦{$booking->customer_payment_amount}\n";
    echo "- CBN Account gets: ₦" . ($booking->cbn_total + $booking->markup_amount) . " (CBN + Markup)\n";
    echo "- Difference Account gets: ₦{$booking->difference_amount} (Parallel - CBN profit)\n";
    echo "- USD Account debited: \${$booking->amount}\n";
} else {
    echo "❌ CALCULATION ERRORS FOUND:\n";
    foreach ($errors as $error) {
        echo "- {$error}\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
?>
