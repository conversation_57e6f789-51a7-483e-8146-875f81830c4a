<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\PayoutMethod;
use App\Services\Payout\numero\Card as NumeroCard;
use App\Traits\ApiValidation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class WebhookController extends Controller
{
    use ApiValidation;

    /**
     * Subscribe to webhook notifications
     */
    public function subscribeWebhook(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'webhook_url' => 'required|url',
                'events' => 'array',
                'provider' => 'required|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $webhookUrl = $request->webhook_url;
            $events = $request->events ?? [];
            $provider = $request->provider;

            // Currently only supporting Numero
            if ($provider === 'numero') {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return response()->json($this->withErrors('Numero payout method not found'));
                }

                $result = NumeroCard::subscribeWebhook($webhookUrl, $events, $method);
                
                if ($result['status'] === 'success') {
                    return response()->json($this->withSuccess($result));
                } else {
                    return response()->json($this->withErrors($result['data']));
                }
            }

            return response()->json($this->withErrors('Unsupported provider'));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Get webhook secret token
     */
    public function getWebhookSecret(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'provider' => 'required|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $provider = $request->provider;

            // Currently only supporting Numero
            if ($provider === 'numero') {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return response()->json($this->withErrors('Numero payout method not found'));
                }

                $result = NumeroCard::getWebhookSecret($method);
                
                if ($result['status'] === 'success') {
                    return response()->json($this->withSuccess($result));
                } else {
                    return response()->json($this->withErrors($result['data']));
                }
            }

            return response()->json($this->withErrors('Unsupported provider'));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Get all webhooks
     */
    public function getAllWebhooks(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'provider' => 'required|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $provider = $request->provider;

            // Currently only supporting Numero
            if ($provider === 'numero') {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return response()->json($this->withErrors('Numero payout method not found'));
                }

                $result = NumeroCard::getAllWebhooks($method);
                
                if ($result['status'] === 'success') {
                    return response()->json($this->withSuccess($result));
                } else {
                    return response()->json($this->withErrors($result['data']));
                }
            }

            return response()->json($this->withErrors('Unsupported provider'));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Unsubscribe from webhook notifications
     */
    public function unsubscribeWebhook(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'webhook_id' => 'required|string',
                'provider' => 'required|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $webhookId = $request->webhook_id;
            $provider = $request->provider;

            // Currently only supporting Numero
            if ($provider === 'numero') {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return response()->json($this->withErrors('Numero payout method not found'));
                }

                $result = NumeroCard::unsubscribeWebhook($webhookId, $method);

                if ($result['status'] === 'success') {
                    return response()->json($this->withSuccess($result));
                } else {
                    return response()->json($this->withErrors($result['data']));
                }
            }

            return response()->json($this->withErrors('Unsupported provider'));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Update webhook configuration
     */
    public function updateWebhook(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'webhook_id' => 'required|string',
                'webhook_url' => 'required|url',
                'events' => 'array',
                'provider' => 'required|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $webhookId = $request->webhook_id;
            $webhookUrl = $request->webhook_url;
            $events = $request->events ?? [];
            $provider = $request->provider;

            // Currently only supporting Numero
            if ($provider === 'numero') {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return response()->json($this->withErrors('Numero payout method not found'));
                }

                $result = NumeroCard::updateWebhook($webhookId, $webhookUrl, $events, $method);

                if ($result['status'] === 'success') {
                    return response()->json($this->withSuccess($result));
                } else {
                    return response()->json($this->withErrors($result['data']));
                }
            }

            return response()->json($this->withErrors('Unsupported provider'));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Get supported webhook providers
     */
    public function getSupportedProviders()
    {
        try {
            $providers = [
                [
                    'code' => 'numero',
                    'name' => 'Numero',
                    'description' => 'Numero webhook management',
                    'supported_events' => [
                        'transfer.success',
                        'transfer.failed',
                        'transfer.pending'
                    ]
                ]
            ];

            return response()->json($this->withSuccess([
                'providers' => $providers,
                'message' => 'Supported webhook providers retrieved successfully'
            ]));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }
}
