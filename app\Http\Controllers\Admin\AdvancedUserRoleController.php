<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Admin;
use App\Models\AdvancedRole;
use App\Models\AdvancedUserRole;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

/**
 * Advanced User Role Assignment Controller
 * 
 * Handles assignment and management of advanced roles to users and admins.
 */
class AdvancedUserRoleController extends Controller
{
    use HasAdvancedPermissions;

    public function __construct()
    {
        // Apply advanced permission middleware
        $this->middleware('advanced.permission:user_roles.read')->only(['index', 'show']);
        $this->middleware('advanced.permission:user_roles.create')->only(['create', 'store']);
        $this->middleware('advanced.permission:user_roles.update')->only(['edit', 'update']);
        $this->middleware('advanced.permission:user_roles.delete')->only(['destroy']);
    }

    /**
     * Display a listing of user role assignments
     */
    public function index(Request $request): View
    {
        $query = AdvancedUserRole::with(['role', 'user', 'assignedBy'])
            ->latest();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQ) use ($search) {
                    $userQ->where('name', 'like', "%{$search}%")
                          ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('role', function ($roleQ) use ($search) {
                    $roleQ->where('name', 'like', "%{$search}%")
                          ->orWhere('display_name', 'like', "%{$search}%");
                });
            });
        }

        if ($request->filled('role_id')) {
            $query->where('role_id', $request->get('role_id'));
        }

        if ($request->filled('user_type')) {
            $query->where('user_type', $request->get('user_type'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'expired') {
                $query->where('expires_at', '<', now());
            }
        }

        if ($request->filled('context')) {
            $query->where('context', $request->get('context'));
        }

        $assignments = $query->paginate(25)->withQueryString();

        // Get filter options
        $roles = AdvancedRole::active()->orderBy('display_name')->pluck('display_name', 'id');
        $userTypes = AdvancedUserRole::distinct()->pluck('user_type');
        $contexts = AdvancedUserRole::distinct()->whereNotNull('context')->pluck('context');

        return view('admin.advanced-user-roles.index', compact(
            'assignments',
            'roles',
            'userTypes',
            'contexts'
        ));
    }

    /**
     * Show the form for creating a new role assignment
     */
    public function create(Request $request): View
    {
        $roles = AdvancedRole::active()->orderBy('display_name')->get();
        
        // Pre-select user if provided
        $selectedUser = null;
        if ($request->filled('user_id') && $request->filled('user_type')) {
            $userClass = $request->get('user_type');
            if (in_array($userClass, [User::class, Admin::class])) {
                $selectedUser = $userClass::find($request->get('user_id'));
            }
        }

        return view('admin.advanced-user-roles.create', compact(
            'roles',
            'selectedUser'
        ));
    }

    /**
     * Store a newly created role assignment
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer',
            'user_type' => 'required|in:' . User::class . ',' . Admin::class,
            'role_id' => 'required|exists:advanced_roles,id',
            'priority' => 'nullable|integer|min:0|max:100',
            'context' => 'nullable|string|max:100',
            'context_data' => 'nullable|array',
            'expires_at' => 'nullable|date|after:now',
            'assignment_reason' => 'nullable|string|max:500',
            'schedule' => 'nullable|array',
            'schedule.days' => 'nullable|array',
            'schedule.days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'schedule.hours.start' => 'nullable|date_format:H:i',
            'schedule.hours.end' => 'nullable|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Validate user exists
        $userClass = $request->get('user_type');
        $user = $userClass::find($request->get('user_id'));
        if (!$user) {
            return back()->withErrors(['user_id' => 'Selected user not found.'])->withInput();
        }

        // Check if role can be assigned to more users
        $role = AdvancedRole::find($request->get('role_id'));
        if (!$role->canAssignToMoreUsers()) {
            return back()->withErrors(['role_id' => 'Role has reached maximum user limit.'])->withInput();
        }

        // Check for existing assignment
        $existingAssignment = AdvancedUserRole::where([
            'user_id' => $user->id,
            'user_type' => get_class($user),
            'role_id' => $role->id,
            'context' => $request->get('context'),
            'is_active' => true,
        ])->first();

        if ($existingAssignment) {
            return back()->withErrors(['role_id' => 'User already has this role in the specified context.'])->withInput();
        }

        try {
            $assignment = AdvancedUserRole::create([
                'user_id' => $user->id,
                'user_type' => get_class($user),
                'role_id' => $role->id,
                'priority' => $request->get('priority', 0),
                'context' => $request->get('context'),
                'context_data' => $request->get('context_data'),
                'expires_at' => $request->get('expires_at'),
                'assignment_reason' => $request->get('assignment_reason'),
                'schedule' => $request->get('schedule'),
                'assigned_at' => now(),
            ]);

            // Clear user permission cache
            if (method_exists($user, 'clearPermissionCache')) {
                $user->clearPermissionCache();
            }

            $this->logPermissionEvent(
                'role_assigned',
                'user_roles.create',
                "Assigned role '{$role->display_name}' to user '{$user->email}'",
                [
                    'user_id' => $user->id,
                    'user_type' => get_class($user),
                    'role_id' => $role->id,
                    'assignment_id' => $assignment->id,
                ]
            );

            return redirect()->route('admin.advanced-user-roles.show', $assignment)
                ->with('success', "Role '{$role->display_name}' assigned to user successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to assign role: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified role assignment
     */
    public function show(AdvancedUserRole $assignment): View
    {
        $assignment->load([
            'role.permissions',
            'user',
            'assignedBy',
            'revokedBy'
        ]);

        // Get effective permissions for this assignment
        $effectivePermissions = $assignment->role->getAllPermissions();

        return view('admin.advanced-user-roles.show', compact(
            'assignment',
            'effectivePermissions'
        ));
    }

    /**
     * Show the form for editing the specified assignment
     */
    public function edit(AdvancedUserRole $assignment): View
    {
        $assignment->load(['role', 'user']);

        return view('admin.advanced-user-roles.edit', compact('assignment'));
    }

    /**
     * Update the specified role assignment
     */
    public function update(Request $request, AdvancedUserRole $assignment): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'priority' => 'nullable|integer|min:0|max:100',
            'context' => 'nullable|string|max:100',
            'context_data' => 'nullable|array',
            'expires_at' => 'nullable|date|after:now',
            'assignment_reason' => 'nullable|string|max:500',
            'schedule' => 'nullable|array',
            'schedule.days' => 'nullable|array',
            'schedule.days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'schedule.hours.start' => 'nullable|date_format:H:i',
            'schedule.hours.end' => 'nullable|date_format:H:i',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $assignment->update($request->only([
                'priority',
                'context',
                'context_data',
                'expires_at',
                'assignment_reason',
                'schedule'
            ]));

            // Clear user permission cache
            $user = $assignment->user;
            if ($user && method_exists($user, 'clearPermissionCache')) {
                $user->clearPermissionCache();
            }

            $this->logPermissionEvent(
                'role_assignment_updated',
                'user_roles.update',
                "Updated role assignment for user '{$user->email}'",
                ['assignment_id' => $assignment->id]
            );

            return redirect()->route('admin.advanced-user-roles.show', $assignment)
                ->with('success', 'Role assignment updated successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update assignment: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Revoke the specified role assignment
     */
    public function destroy(AdvancedUserRole $assignment): RedirectResponse
    {
        try {
            $user = $assignment->user;
            $role = $assignment->role;
            
            $assignment->revoke('Revoked by admin');

            // Clear user permission cache
            if ($user && method_exists($user, 'clearPermissionCache')) {
                $user->clearPermissionCache();
            }

            $this->logPermissionEvent(
                'role_revoked',
                'user_roles.delete',
                "Revoked role '{$role->display_name}' from user '{$user->email}'",
                ['assignment_id' => $assignment->id]
            );

            return redirect()->route('admin.advanced-user-roles.index')
                ->with('success', 'Role assignment revoked successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to revoke assignment: ' . $e->getMessage()]);
        }
    }

    /**
     * Reactivate a revoked role assignment
     */
    public function reactivate(AdvancedUserRole $assignment): RedirectResponse
    {
        $this->authorizePermission('user_roles.update');

        if ($assignment->is_active) {
            return back()->withErrors(['error' => 'Assignment is already active.']);
        }

        try {
            $assignment->reactivate();

            // Clear user permission cache
            $user = $assignment->user;
            if ($user && method_exists($user, 'clearPermissionCache')) {
                $user->clearPermissionCache();
            }

            $this->logPermissionEvent(
                'role_reactivated',
                'user_roles.update',
                "Reactivated role assignment for user '{$user->email}'",
                ['assignment_id' => $assignment->id]
            );

            return back()->with('success', 'Role assignment reactivated successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to reactivate assignment: ' . $e->getMessage()]);
        }
    }

    /**
     * Extend assignment expiration
     */
    public function extend(Request $request, AdvancedUserRole $assignment): RedirectResponse
    {
        $this->authorizePermission('user_roles.update');

        $validator = Validator::make($request->all(), [
            'new_expiration' => 'required|date|after:now',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            $assignment->extend($request->date('new_expiration'));

            $this->logPermissionEvent(
                'role_assignment_extended',
                'user_roles.update',
                "Extended role assignment expiration for user '{$assignment->user->email}'",
                [
                    'assignment_id' => $assignment->id,
                    'new_expiration' => $request->get('new_expiration'),
                ]
            );

            return back()->with('success', 'Assignment expiration extended successfully.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to extend assignment: ' . $e->getMessage()]);
        }
    }

    /**
     * Search users for role assignment
     */
    public function searchUsers(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $userType = $request->get('user_type', User::class);

        if (!in_array($userType, [User::class, Admin::class])) {
            return response()->json(['error' => 'Invalid user type'], 400);
        }

        $users = $userType::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->limit(20)
            ->get(['id', 'name', 'email']);

        return response()->json($users);
    }

    /**
     * Get user's current roles
     */
    public function getUserRoles(Request $request): JsonResponse
    {
        $userId = $request->get('user_id');
        $userType = $request->get('user_type', User::class);

        if (!in_array($userType, [User::class, Admin::class])) {
            return response()->json(['error' => 'Invalid user type'], 400);
        }

        $user = $userType::find($userId);
        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        $assignments = AdvancedUserRole::forUser($user)
            ->with('role')
            ->get()
            ->map(function ($assignment) {
                return [
                    'id' => $assignment->id,
                    'role' => [
                        'id' => $assignment->role->id,
                        'name' => $assignment->role->name,
                        'display_name' => $assignment->role->display_name,
                    ],
                    'is_active' => $assignment->is_active,
                    'status' => $assignment->getStatus(),
                    'expires_at' => $assignment->expires_at?->toISOString(),
                    'context' => $assignment->context,
                ];
            });

        return response()->json($assignments);
    }
}
