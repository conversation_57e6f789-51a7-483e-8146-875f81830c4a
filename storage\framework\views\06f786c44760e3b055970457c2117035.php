<!-- Navbar Vertical -->
<aside
    class="js-navbar-vertical-aside navbar navbar-vertical-aside navbar-vertical navbar-vertical-fixed navbar-expand-xl navbar-vertical-aside-initialized
    <?php echo e(in_array(session()->get('themeMode'), [null, 'auto'] )?  'navbar-dark bg-dark ' : 'navbar-light bg-white'); ?>">
    <div class="navbar-vertical-container">
        <div class="navbar-vertical-footer-offset">
            <!-- Logo -->
            <a class="navbar-brand" href="<?php echo e(route('admin.dashboard')); ?>" aria-label="<?php echo e($basicControl->site_title); ?>">
                <img class="navbar-brand-logo navbar-brand-logo-auto"
                     src="<?php echo e(getFile(session()->get('themeMode') == 'auto'?$basicControl->admin_dark_mode_logo_driver : $basicControl->admin_logo_driver, session()->get('themeMode') == 'auto'?$basicControl->admin_dark_mode_logo:$basicControl->admin_logo, true)); ?>"
                     alt="<?php echo e($basicControl->site_title); ?> Logo"
                     data-hs-theme-appearance="default">

                <img class="navbar-brand-logo"
                     src="<?php echo e(getFile($basicControl->admin_dark_mode_logo_driver, $basicControl->admin_dark_mode_logo, true)); ?>"
                     alt="<?php echo e($basicControl->site_title); ?> Logo"
                     data-hs-theme-appearance="dark">

                <img class="navbar-brand-logo-mini"
                     src="<?php echo e(getFile($basicControl->favicon_driver, $basicControl->favicon, true)); ?>"
                     alt="<?php echo e($basicControl->site_title); ?> Logo"
                     data-hs-theme-appearance="default">
                <img class="navbar-brand-logo-mini"
                     src="<?php echo e(getFile($basicControl->favicon_driver, $basicControl->favicon, true)); ?>"
                     alt="Logo"
                     data-hs-theme-appearance="dark">
            </a>
            <!-- End Logo -->

            <!-- Navbar Vertical Toggle -->
            <button type="button" class="js-navbar-vertical-aside-toggle-invoker navbar-aside-toggler">
                <i class="bi-arrow-bar-left navbar-toggler-short-align"
                   data-bs-template='<div class="tooltip d-none d-md-block" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
                   data-bs-toggle="tooltip"
                   data-bs-placement="right"
                   title="Collapse">
                </i>
                <i
                    class="bi-arrow-bar-right navbar-toggler-full-align"
                    data-bs-template='<div class="tooltip d-none d-md-block" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
                    data-bs-toggle="tooltip"
                    data-bs-placement="right"
                    title="Expand"
                ></i>
            </button>
            <!-- End Navbar Vertical Toggle -->


            <!-- Content -->
            <div class="navbar-vertical-content">
                <div id="navbarVerticalMenu" class="nav nav-pills nav-vertical card-navbar-nav">

                    <div class="nav-item">
                        <a class="nav-link <?php echo e(menuActive(['admin.dashboard','admin.wallet.summary'])); ?>"
                           href="<?php echo e(route('admin.dashboard')); ?>">
                            <i class="bi-house-door nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get("Dashboard"); ?></span>
                        </a>
                    </div>


                    <span class="dropdown-header mt-2"><?php echo app('translator')->get('Transactions'); ?></span>
                    <small class="bi-three-dots nav-subtitle-replacer"></small>
                    <?php if($basic->transfer): ?>
                        <?php if(checkPermission(2)): ?>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.transfer.index'])); ?>"
                                   href="<?php echo e(route('admin.transfer.index')); ?>" data-placement="left">
                                    <i class="fa-light fa-users nav-icon"></i>
                                    <span class="nav-link-title"><?php echo app('translator')->get("Transfer List"); ?></span>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->request): ?>
                        <?php if(checkPermission(15)): ?>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.requestMoney.index'])); ?>"
                                   href="<?php echo e(route('admin.requestMoney.index')); ?>" data-placement="left">
                                    <i class="fa-light fa-search-dollar nav-icon"></i>
                                    <span class="nav-link-title"><?php echo app('translator')->get("Request Money List"); ?></span>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if($basic->exchange): ?>
                        <?php if(checkPermission(3)): ?>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.exchange.index'])); ?>"
                                   href="<?php echo e(route('admin.exchange.index')); ?>" data-placement="left">
                                    <i class="fa-light fa-exchange-alt nav-icon"></i>
                                    <span class="nav-link-title"><?php echo app('translator')->get("Exchange List"); ?></span>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if(checkPermission(18)): ?>
                    <div class="nav-item">
                        <a class="nav-link <?php echo e(menuActive(['admin.transaction'])); ?>"
                           href="<?php echo e(route('admin.transaction')); ?>" data-placement="left">
                            <i class="bi bi-send nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get("Transaction"); ?></span>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if($basic->payout): ?>
                        <?php if(checkPermission(8)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.payout.log'])); ?>"
                               href="<?php echo e(route('admin.payout.log')); ?>" data-placement="left">
                                <i class="bi bi-wallet2 nav-icon "></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Withdraw Log"); ?></span>
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->deposit): ?>
                        <?php if(checkPermission(1)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.payment.log'])); ?>"
                               href="<?php echo e(route('admin.payment.log')); ?>" data-placement="left">
                                <i class="bi bi-credit-card-2-front nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Payment Log"); ?></span>
                            </a>
                        </div>
                        <div class="nav-item ">
                            <a class="nav-link <?php echo e(menuActive(['admin.payment.pending'])); ?> "
                               href="<?php echo e(route('admin.payment.pending')); ?>" data-placement="left">
                                <i class="bi bi-cash nav-icon"></i>
                                <?php echo app('translator')->get("Payment Request"); ?>
                                <?php if($sidebarCounts->deposit_pending > 0): ?>
                                    <span class="badge bg-primary rounded-pill ms-5"><?php echo e($sidebarCounts->deposit_pending); ?></span>
                                <?php endif; ?>
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <span class="dropdown-header mt-2"><?php echo app('translator')->get('Forex Trading'); ?></span>
                    <small class="bi-three-dots nav-subtitle-replacer"></small>

                    
                    <div class="nav-item">
                        <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.forex.dashboard'])); ?>"
                           href="#navbarVerticalForexMenu" role="button" data-bs-toggle="unfold"
                           data-bs-target="#navbarVerticalForexMenu" aria-expanded="false"
                           aria-controls="navbarVerticalForexMenu">
                            <i class="fa-light fa-chart-line nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get("Forex Trading"); ?></span>
                        </a>

                        <div id="navbarVerticalForexMenu" class="nav-collapse <?php echo e(menuActive(['admin.forex.*'], 3)); ?>"
                             data-bs-parent="#navbarVerticalMenuPrimary">
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.forex.dashboard'])); ?>"
                                   href="<?php echo e(route('admin.forex.dashboard')); ?>">
                                    <span class="nav-link-title"><?php echo app('translator')->get("Dashboard"); ?></span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.forex.rates.*'])); ?>"
                                   href="<?php echo e(route('admin.forex.rates.index')); ?>">
                                    <span class="nav-link-title"><?php echo app('translator')->get("Exchange Rates"); ?></span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.forex.accounts.*'])); ?>"
                                   href="<?php echo e(route('admin.forex.accounts.index')); ?>">
                                    <span class="nav-link-title"><?php echo app('translator')->get("Accounts"); ?></span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.forex.bookings.*'])); ?>"
                                   href="<?php echo e(route('admin.forex.bookings.index')); ?>">
                                    <span class="nav-link-title"><?php echo app('translator')->get("Bookings"); ?></span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.forex.costs.*'])); ?>"
                                   href="<?php echo e(route('admin.forex.costs.index')); ?>">
                                    <span class="nav-link-title"><?php echo app('translator')->get("Operational Costs"); ?></span>
                                </a>
                            </div>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.forex.reports.*'])); ?>"
                                   href="<?php echo e(route('admin.forex.reports.index')); ?>">
                                    <span class="nav-link-title"><?php echo app('translator')->get("Reports"); ?></span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <span class="dropdown-header mt-2"><?php echo app('translator')->get('Payment Services'); ?></span>
                    <small class="bi-three-dots nav-subtitle-replacer"></small>

                    
                    <div class="nav-item">
                        <a class="nav-link <?php echo e(menuActive(['admin.virtual.accounts.index','admin.virtual.accounts.create','admin.virtual.accounts.show','admin.virtual.accounts.edit'])); ?>"
                           href="<?php echo e(route('admin.virtual.accounts.index')); ?>">
                            <i class="bi-bank nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get("Virtual Accounts"); ?></span>
                        </a>
                    </div>

                    <?php if($basic->redeem): ?>
                        <?php if(checkPermission(4)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.redeem.index'])); ?>"
                               href="<?php echo e(route('admin.redeem.index')); ?>" data-placement="left">
                                <i class="fa-light fa-gift nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Redeem Code List"); ?></span>
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->escrow): ?>
                        <?php if(checkPermission(5)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.escrow.index'])); ?>"
                               href="<?php echo e(route('admin.escrow.index')); ?>" data-placement="left">
                                <i class="fa-light fa-handshake nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Escrow List"); ?></span>
                            </a>
                        </div>
                        <?php endif; ?>
                        <?php if(checkPermission(16)): ?>
                            <div class="nav-item">
                                <a class="nav-link <?php echo e(menuActive(['admin.dispute.index','admin.dispute.view'])); ?>"
                                   href="<?php echo e(route('admin.dispute.index')); ?>" data-placement="left">
                                    <i class="fa-light fa-gavel nav-icon"></i>
                                    <span class="nav-link-title"><?php echo app('translator')->get("Dispute List"); ?></span>
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->qr_payment): ?>
                        <?php if(checkPermission(17)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.qr.payment'])); ?>"
                               href="<?php echo e(route('admin.qr.payment')); ?>" data-placement="left">
                                <i class="fa-light fa-qrcode nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("QR Payment"); ?></span>
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if($basic->voucher): ?>
                        <?php if(checkPermission(6)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.voucher.index'])); ?>"
                               href="<?php echo e(route('admin.voucher.index')); ?>" data-placement="left">
                                <i class="fa-light fa-file-invoice-dollar nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Voucher List"); ?></span>
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if($basic->invoice): ?>
                        <?php if(checkPermission(9)): ?>
                        <div class="nav-item">
                            <a class="nav-link <?php echo e(menuActive(['admin.invoice.index'])); ?>"
                               href="<?php echo e(route('admin.invoice.index')); ?>" data-placement="left">
                                <i class="fa-light fa-file-invoice nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Invoice List"); ?></span>
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->bill_payment): ?>
                        <?php if(checkPermission(11)): ?>
                        <div class="nav-item">
                            <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.bill.pay.list','admin.bill.pay.view'], 3)); ?>"
                               href="#navbarVerticalBillPayMenu"
                               role="button"
                               data-bs-toggle="collapse"
                               data-bs-target="#navbarVerticalBillPayMenu"
                               aria-expanded="false"
                               aria-controls="navbarVerticalBillPayMenu">
                                <i class="fa-light fa-credit-card nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Bill History"); ?></span>
                            </a>
                            <div id="navbarVerticalBillPayMenu"
                                 class="nav-collapse collapse <?php echo e(menuActive(['admin.bill.pay.list','admin.bill.pay.view'], 2)); ?>"
                                 data-bs-parent="#navbarVerticalBillPayMenu">
                                <a class="nav-link <?php echo e(menuActive(['admin.bill.pay.view'])); ?> <?php echo e((collect(request()->segments())->last() == 'all' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.bill.pay.list','all')); ?>">
                                    <span><?php echo app('translator')->get("All Bills"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Bill History > All Bills"); ?></small>
                                </a>

                                <a class="nav-link <?php echo e((collect(request()->segments())->last() == 'pending' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.bill.pay.list','pending')); ?>">
                                    <span><?php echo app('translator')->get("Pending Bills"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Bill History > Pending Bills"); ?></small>
                                </a>

                                <a class="nav-link <?php echo e((collect(request()->segments())->last() == 'completed' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.bill.pay.list','completed')); ?>">
                                    <span><?php echo app('translator')->get("Complete Bills"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Bill History > Complete Bills"); ?></small>
                                </a>

                                <a class="nav-link <?php echo e((collect(request()->segments())->last() == 'return' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.bill.pay.list','return')); ?>">
                                    <span><?php echo app('translator')->get("Return Bills"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Bill History > Return Bills"); ?></small>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->store): ?>
                        <?php if(checkPermission(7)): ?>
                        <span class="dropdown-header mt-2"> <?php echo app('translator')->get("Store Settings"); ?></span>
                        <div class="nav-item">
                            <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.store.list', 'admin.store.view', 'admin.product.list',
                               'admin.product.view','admin.order.list','admin.order.view','admin.contact.list'], 3)); ?>"
                               href="#navbarVerticalStoreMenu" role="button"
                               data-bs-toggle="collapse" data-bs-target="#navbarVerticalStoreMenu"
                               aria-expanded="false" aria-controls="navbarVerticalStoreMenu">
                                <i class="fa-light fa-store nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Store Management"); ?></span>
                            </a>
                            <div id="navbarVerticalStoreMenu" class="nav-collapse collapse
                                 <?php echo e(menuActive(['admin.store.list', 'admin.store.view', 'admin.product.list',
                                    'admin.product.view','admin.order.list','admin.order.view','admin.contact.list'], 2)); ?>"
                                 data-bs-parent="#navbarVerticalStoreMenu">
                                <a class="nav-link <?php echo e(menuActive(['admin.store.list','admin.store.view'])); ?>"
                                   href="<?php echo e(route('admin.store.list')); ?>">
                                    <span><?php echo app('translator')->get("Store List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Store Management > Store List"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e(menuActive(['admin.product.list','admin.product.view'])); ?>"
                                   href="<?php echo e(route('admin.product.list')); ?>">
                                    <span><?php echo app('translator')->get("Product List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Store Management > Product List"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e(menuActive(['admin.order.list','admin.order.view'])); ?>"
                                   href="<?php echo e(route('admin.order.list')); ?>">
                                    <span><?php echo app('translator')->get("Order List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Store Management > Order List"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e(menuActive('admin.contact.list')); ?>"
                                   href="<?php echo e(route('admin.contact.list')); ?>">
                                    <span><?php echo app('translator')->get("Contact List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Store Management > Contact List"); ?></small>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    

                    

                    <?php if(checkPermission(14)): ?>
                    <span class="dropdown-header mt-2"> <?php echo app('translator')->get('Kyc Management'); ?></span>
                    <small class="bi-three-dots nav-subtitle-replacer"></small>
                    <div class="nav-item">
                        <a class="nav-link <?php echo e(menuActive(['admin.kyc.form.list','admin.kyc.edit','admin.kyc.create'])); ?>"
                           href="<?php echo e(route('admin.kyc.form.list')); ?>" data-placement="left">
                            <i class="bi-stickies nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get('KYC Setting'); ?></span>
                            <small class="d-none"><?php echo app('translator')->get("Kyc Management > KYC Setting"); ?></small>
                        </a>
                    </div>

                    <div class="nav-item" <?php echo e(menuActive(['admin.kyc.list*','admin.kyc.view'], 3)); ?>>
                        <a class="nav-link dropdown-toggle collapsed" href="#navbarVerticalKycRequestMenu"
                           role="button"
                           data-bs-toggle="collapse" data-bs-target="#navbarVerticalKycRequestMenu"
                           aria-expanded="false"
                           aria-controls="navbarVerticalKycRequestMenu">
                            <i class="bi bi-person-lines-fill nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get("KYC Request"); ?></span>
                        </a>
                        <div id="navbarVerticalKycRequestMenu"
                             class="nav-collapse collapse <?php echo e(menuActive(['admin.kyc.list*','admin.kyc.view'], 2)); ?>"
                             data-bs-parent="#navbarVerticalKycRequestMenu">
                            <a class="nav-link d-flex justify-content-between <?php echo e(Request::is('admin/kyc/pending') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.kyc.list', 'pending')); ?>">
                                <span><?php echo app('translator')->get("Pending KYC"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("Kyc Management > Pending KYC"); ?></small>
                                <?php if($sidebarCounts->kyc_pending > 0): ?>
                                    <span class="badge bg-primary rounded-pill "><?php echo e($sidebarCounts->kyc_pending); ?></span>
                                <?php endif; ?>
                            </a>
                            <a class="nav-link d-flex justify-content-between <?php echo e(Request::is('admin/kyc/approve') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.kyc.list', 'approve')); ?>">
                                <span><?php echo app('translator')->get("Approved KYC"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("Kyc Management > Approved KYC"); ?></small>
                            </a>
                            <a class="nav-link <?php echo e(Request::is('admin/kyc/rejected') ? 'active' : ''); ?>"
                               href="<?php echo e(route('admin.kyc.list', 'rejected')); ?>">
                                <span><?php echo app('translator')->get("Rejected KYC"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("Kyc Management > Rejected KYC"); ?></small>
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php if(checkPermission(13)): ?>
                    <span class="dropdown-header mt-2"> <?php echo app('translator')->get("User Panel"); ?></span>
                    <small class="bi-three-dots nav-subtitle-replacer"></small>
                    <div class="nav-item">
                        <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.users'], 3)); ?>"
                           href="#navbarVerticalUserPanelMenu"
                           role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalUserPanelMenu"
                           aria-expanded="false" aria-controls="navbarVerticalUserPanelMenu">
                            <i class="bi-people nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get('User Management'); ?></span>
                        </a>
                        <div id="navbarVerticalUserPanelMenu" class="nav-collapse collapse
                             <?php echo e(menuActive(['admin.mail.all.user','admin.users'], 2)); ?>"
                             data-bs-parent="#navbarVerticalUserPanelMenu">

                            <a class="nav-link <?php echo e(request()->is('admin/users') ? 'active' : ''); ?>" href="<?php echo e(route('admin.users')); ?>">
                                <span><?php echo app('translator')->get("All User"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("User Management > All User"); ?></small>
                            </a>

                            <a href="<?php echo e(route('admin.users','active-users')); ?>" class="nav-link d-flex justify-content-between
                                <?php echo e(request()->is('admin/users/active-users') ? 'active' : ''); ?>">
                                <span><?php echo app('translator')->get("Active Users"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("User Management > Active Users"); ?></small>

                                <?php if($sidebarCounts->active_users > 0): ?>
                                    <span class="badge bg-primary rounded-pill "><?php echo e($sidebarCounts->active_users); ?></span>
                                <?php endif; ?>
                            </a>
                            <a href="<?php echo e(route('admin.users','blocked-users')); ?>" class="nav-link d-flex justify-content-between
                                <?php echo e(request()->is('admin/users/blocked-users') ? 'active' : ''); ?>">
                                <span><?php echo app('translator')->get("Blocked Users"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("User Management > Blocked Users"); ?></small>
                                <?php if($sidebarCounts->blocked_users > 0): ?>
                                    <span class="badge bg-primary rounded-pill "><?php echo e($sidebarCounts->blocked_users); ?></span>
                                <?php endif; ?>
                            </a>
                            <a href="<?php echo e(route('admin.users','email-unverified')); ?>" class="nav-link d-flex justify-content-between
                               <?php echo e(request()->is('admin/users/email-unverified') ? 'active' : ''); ?>">
                                <span><?php echo app('translator')->get("Email Unverified"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("User Management > Email Unverified"); ?></small>
                                <?php if($sidebarCounts->email_unverified > 0): ?>
                                    <span class="badge bg-primary rounded-pill "><?php echo e($sidebarCounts->email_unverified); ?></span>
                                <?php endif; ?>
                            </a>
                            <a href="<?php echo e(route('admin.users','sms-unverified')); ?>" class="nav-link d-flex justify-content-between
                               <?php echo e(request()->is('admin/users/sms-unverified') ? 'active' : ''); ?>">
                                <span><?php echo app('translator')->get("Sms Unverified"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("User Management > Sms Unverified"); ?></small>
                                <?php if($sidebarCounts->sms_unverified > 0): ?>
                                    <span class="badge bg-primary rounded-pill "><?php echo e($sidebarCounts->sms_unverified); ?></span>
                                <?php endif; ?>
                            </a>

                            <a class="nav-link <?php echo e(menuActive(['admin.mail.all.user'])); ?>" href="<?php echo e(route("admin.mail.all.user")); ?>">
                                <span><?php echo app('translator')->get("Mail To Users"); ?></span>
                                <small class="d-none"><?php echo app('translator')->get("User Management > Mail To Users"); ?></small>
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php echo $__env->yieldPushContent('extra_menu'); ?>

                    

                    

                    <?php if(any_modules_exist()): ?>
                        
                    <?php endif; ?>

                    <?php if(checkPermission(12)): ?>
                        
                    <?php endif; ?>

                    <?php if($basic->virtual_card): ?>
                        <?php if(checkPermission(10)): ?>
                        <div class="nav-item">
                            <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.virtual.card','admin.virtual.cardOrder',
                                        'admin.virtual.cardOrderDetail', 'admin.virtual.cardList','admin.virtual.cardView','admin.virtual.cardTransaction'], 3)); ?>"
                               href="#navbarVerticalVirtualMenu"
                               role="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalVirtualMenu"
                               aria-expanded="false" aria-controls="navbarVerticalVirtualMenu">
                                <i class="fa-light fa-address-card nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Virtual Card"); ?></span>
                            </a>
                            <div id="navbarVerticalVirtualMenu" class="nav-collapse collapse
                                 <?php echo e(menuActive(['admin.virtual.card','admin.virtual.cardEdit','admin.virtual.cardOrder','admin.virtual.cardOrderDetail',
                                    'admin.virtual.cardList','admin.virtual.cardView','admin.virtual.cardTransaction'], 2)); ?>"
                                 data-bs-parent="#navbarVerticalVirtualMenu">
                                <a class="nav-link <?php echo e(menuActive(['admin.virtual.card','admin.virtual.cardEdit'])); ?>"
                                   href="<?php echo e(route('admin.virtual.card')); ?>">
                                    <span><?php echo app('translator')->get("Available Methods"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Virtual Card > Available Methods"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e(menuActive(['admin.virtual.cardOrder','admin.virtual.cardOrderDetail'])); ?>"
                                   href="<?php echo e(route('admin.virtual.cardOrder')); ?>">
                                    <span><?php echo app('translator')->get("Request List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Virtual Card > Request List"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e(menuActive(['admin.virtual.cardView','admin.virtual.cardTransaction'])); ?> <?php echo e((collect(request()->segments())->last() == 'all' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.virtual.cardList','all')); ?>">
                                    <span><?php echo app('translator')->get("Card List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Virtual Card > Card List"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e((collect(request()->segments())->last() == 'add-fund' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.virtual.cardList','add-fund')); ?>">
                                    <span><?php echo app('translator')->get("Add Fund Request"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Virtual Card > Add Fund Request"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e((collect(request()->segments())->last() == 'block' ? 'active':'')); ?>"
                                   href="<?php echo e(route('admin.virtual.cardList','block')); ?>">
                                    <span><?php echo app('translator')->get("Block Request"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Virtual Card > Block Request"); ?></small>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if($basic->bill_payment): ?>
                        <?php if(checkPermission(11)): ?>
                        <div class="nav-item">
                            <a class="nav-link dropdown-toggle <?php echo e(menuActive(['admin.bill.method.list','admin.bill.method.edit','admin.bill.fetch.service',
                               'admin.bill.service.list'], 3)); ?>"
                               href="#navbarVerticalBillMethodMenu"
                               role="button"
                               data-bs-toggle="collapse"
                               data-bs-target="#navbarVerticalBillMethodMenu"
                               aria-expanded="false"
                               aria-controls="navbarVerticalBillMethodMenu">
                                <i class="fa-light fa-money-bill nav-icon"></i>
                                <span class="nav-link-title"><?php echo app('translator')->get("Bill Setting"); ?></span>
                            </a>
                            <div id="navbarVerticalBillMethodMenu"
                                 class="nav-collapse collapse <?php echo e(menuActive(['admin.bill.method.list','admin.bill.method.edit','admin.bill.fetch.service',
                                    'admin.bill.service.list'], 2)); ?>"
                                 data-bs-parent="#navbarVerticalBillMethodMenu">
                                <a class="nav-link <?php echo e(menuActive(['admin.bill.method.list','admin.bill.method.edit','admin.bill.fetch.service'])); ?>"
                                   href="<?php echo e(route('admin.bill.method.list')); ?>">
                                    <span><?php echo app('translator')->get("Available Methods"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Bill Settings > Available Methods"); ?></small>
                                </a>
                                <a class="nav-link <?php echo e(menuActive(['admin.bill.service.list'])); ?>"
                                   href="<?php echo e(route('admin.bill.service.list')); ?>">
                                    <span><?php echo app('translator')->get("Service List"); ?></span>
                                    <small class="d-none"><?php echo app('translator')->get("Bill Settings > Service List"); ?></small>
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>


                    

                    

                    

                    <div class="nav-item">
                        <a class="nav-link"
                           href="<?php echo e(route('clear')); ?>" data-placement="left">
                            <i class="bi bi-radioactive nav-icon"></i>
                            <span class="nav-link-title"><?php echo app('translator')->get('Clear Cache'); ?></span>
                        </a>
                    </div>

                    <?php $__currentLoopData = collect(config('generalsettings.settings')); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $setting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="nav-item d-none">
                            <a class="nav-link  <?php echo e(isMenuActive($setting['route'])); ?>"
                               href="<?php echo e(getRoute($setting['route'], $setting['route_segment'] ?? null)); ?>">
                                <i class="<?php echo e($setting['icon']); ?> nav-icon"></i>
                                <span class="nav-link-title"><?php echo e(__(getTitle($key.' '.'Settings'))); ?></span>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                </div>

                <div class="navbar-vertical-footer">
                    <ul class="navbar-vertical-footer-list">
                        <li class="navbar-vertical-footer-list-item">
                            
                        </li>
                        <li class="navbar-vertical-footer-list-item">
                            <div class="dropdown dropup">
                                <button type="button" class="btn btn-ghost-secondary btn-icon rounded-circle"
                                        id="selectThemeDropdown" data-bs-toggle="dropdown" aria-expanded="false"
                                        data-bs-dropdown-animation></button>
                                <div class="dropdown-menu navbar-dropdown-menu navbar-dropdown-menu-borderless"
                                     aria-labelledby="selectThemeDropdown">
                                    <a class="dropdown-item" href="javascript:void(0)" data-icon="bi-moon-stars"
                                       data-value="auto">
                                        <i class="bi-moon-stars me-2"></i>
                                        <span class="text-truncate"
                                              title="Auto (system default)"><?php echo app('translator')->get("Default"); ?></span>
                                    </a>
                                    <a class="dropdown-item" href="javascript:void(0)" data-icon="bi-brightness-high"
                                       data-value="default">
                                        <i class="bi-brightness-high me-2"></i>
                                        <span class="text-truncate"
                                              title="Default (light mode)"><?php echo app('translator')->get("Light Mode"); ?></span>
                                    </a>
                                    <a class="dropdown-item active" href="javascript:void(0)" data-icon="bi-moon"
                                       data-value="dark">
                                        <i class="bi-moon me-2"></i>
                                        <span class="text-truncate" title="Dark"><?php echo app('translator')->get("Dark Mode"); ?></span>
                                    </a>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</aside>




<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>