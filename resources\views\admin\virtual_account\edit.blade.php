@extends('admin.layouts.app')
@section('page_title', __('Edit Virtual Account'))

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.virtual.accounts.index') }}">
                                    @lang('Virtual Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.virtual.accounts.show', $virtualAccount->id) }}">
                                    @lang('Account Details')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Edit')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Edit Virtual Account')</h1>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-ghost-secondary" href="{{ route('admin.virtual.accounts.show', $virtualAccount->id) }}">
                        <i class="bi-chevron-left me-1"></i> @lang('Back')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row justify-content-lg-center">
            <div class="col-lg-8">
                <!-- Card -->
                <div class="card">
                    <!-- Header -->
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Information')</h4>
                    </div>
                    <!-- End Header -->

                    <!-- Body -->
                    <div class="card-body">
                        <!-- Account Details (Read-only) -->
                        <div class="row mb-4">
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label">@lang('Account Number')</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $virtualAccount->account_number }}" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="bi-lock"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">@lang('Account Name')</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $virtualAccount->account_name }}" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="bi-lock"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">@lang('Bank Name')</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $virtualAccount->bank_name }}" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="bi-lock"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="mb-3">
                                    <label class="form-label">@lang('Provider')</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ ucfirst($virtualAccount->provider) }}" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="bi-lock"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">@lang('Currency')</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ $virtualAccount->currency }}" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="bi-lock"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">@lang('Account Type')</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" value="{{ ucfirst($virtualAccount->type) }}" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">
                                                <i class="bi-lock"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Editable Form -->
                        <form action="{{ route('admin.virtual.accounts.update', $virtualAccount->id) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="row">
                                <div class="col-sm-6">
                                    <!-- Account Owner (Read-only) -->
                                    <div class="mb-4">
                                        <label class="form-label">@lang('Account Owner')</label>
                                        <div class="d-flex align-items-center p-3 border rounded">
                                            <div class="flex-shrink-0">
                                                {!! $virtualAccount->user->profilePicture() !!}
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">{{ $virtualAccount->user->name }}</h6>
                                                <span class="d-block text-muted small">{{ $virtualAccount->user->email }}</span>
                                                <span class="d-block text-muted small">{{ $virtualAccount->user->username }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <!-- Status (Editable) -->
                                    <div class="mb-4">
                                        <label class="form-label">@lang('Account Status')</label>
                                        <div class="tom-select-custom">
                                            <select class="js-select form-select" name="is_active" required>
                                                <option value="1" {{ $virtualAccount->is_active ? 'selected' : '' }}>
                                                    @lang('Active')
                                                </option>
                                                <option value="0" {{ !$virtualAccount->is_active ? 'selected' : '' }}>
                                                    @lang('Inactive')
                                                </option>
                                            </select>
                                        </div>
                                        @error('is_active')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-flex justify-content-end gap-3">
                                <a href="{{ route('admin.virtual.accounts.show', $virtualAccount->id) }}" class="btn btn-white">
                                    @lang('Cancel')
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi-check-lg me-1"></i> @lang('Update Account')
                                </button>
                            </div>
                        </form>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Card -->
            </div>
        </div>
    </div>
@endsection

<x-assets :tomselect="true"/>
