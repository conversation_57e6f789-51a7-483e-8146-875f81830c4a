<?php

namespace Database\Factories;

use App\Models\PayoutMethod;
use Illuminate\Database\Eloquent\Factories\Factory;

class PayoutMethodFactory extends Factory
{
    protected $model = PayoutMethod::class;

    public function definition()
    {
        return [
            'name' => $this->faker->company(),
            'code' => $this->faker->slug(),
            'description' => $this->faker->sentence(),
            'image' => $this->faker->imageUrl(),
            'is_active' => 1,
            'currency_type' => 0,
            'parameters' => (object) [
                'api_key' => $this->faker->uuid(),
                'secret_key' => $this->faker->uuid(),
            ],
            'payout_currencies' => [
                [
                    'name' => 'NGN',
                    'currency_symbol' => 'NGN',
                    'min_limit' => 100,
                    'max_limit' => 1000000,
                    'percentage_charge' => 1.5,
                    'fixed_charge' => 50
                ]
            ],
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
