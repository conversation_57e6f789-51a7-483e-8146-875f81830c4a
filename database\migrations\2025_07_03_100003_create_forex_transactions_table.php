<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_reference')->unique();
            $table->foreignId('forex_booking_id')->nullable()->constrained('forex_bookings')->onDelete('cascade');
            $table->foreignId('forex_account_id')->constrained('forex_accounts')->onDelete('cascade');
            $table->enum('transaction_type', ['credit', 'debit', 'transfer']);
            $table->string('currency', 3);
            $table->decimal('amount', 18, 8);
            $table->decimal('balance_before', 18, 8);
            $table->decimal('balance_after', 18, 8);
            $table->text('description');
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('admins')->onDelete('cascade');
            $table->json('metadata')->nullable()->comment('Additional transaction data');
            $table->timestamps();
            
            $table->index(['forex_account_id', 'created_at']);
            $table->index(['transaction_type', 'created_at']);
            $table->index('forex_booking_id');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_transactions');
    }
};
