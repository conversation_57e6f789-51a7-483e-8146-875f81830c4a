<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Admin;
use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Models\AdvancedUserRole;
use App\Models\AdvancedRolePermission;
use App\Models\AdvancedPermissionAudit;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

/**
 * Advanced Permission System Unit Tests
 * 
 * Tests all models, relationships, and core functionality of the advanced permission system.
 */
class AdvancedPermissionSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_create_advanced_permissions()
    {
        $permission = AdvancedPermission::create([
            'name' => 'users.create',
            'display_name' => 'Create Users',
            'description' => 'Allow creating new users',
            'resource' => 'users',
            'action' => 'create',
            'category' => 'admin',
        ]);

        $this->assertDatabaseHas('advanced_permissions', [
            'name' => 'users.create',
            'resource' => 'users',
            'action' => 'create',
        ]);

        $this->assertTrue($permission->is_active);
        $this->assertFalse($permission->is_system);
    }

    /** @test */
    public function it_validates_permission_name_format()
    {
        $this->assertTrue(AdvancedPermission::isValidPermissionName('users.create'));
        $this->assertTrue(AdvancedPermission::isValidPermissionName('forex.bookings.read'));
        $this->assertFalse(AdvancedPermission::isValidPermissionName('invalid'));
        $this->assertFalse(AdvancedPermission::isValidPermissionName(''));
    }

    /** @test */
    public function it_can_create_crud_permissions_for_resource()
    {
        $permissions = AdvancedPermission::createCrudPermissions('transactions', 'finance');

        $this->assertCount(4, $permissions);
        $this->assertDatabaseHas('advanced_permissions', ['name' => 'transactions.create']);
        $this->assertDatabaseHas('advanced_permissions', ['name' => 'transactions.read']);
        $this->assertDatabaseHas('advanced_permissions', ['name' => 'transactions.update']);
        $this->assertDatabaseHas('advanced_permissions', ['name' => 'transactions.delete']);

        foreach ($permissions as $permission) {
            $this->assertEquals('finance', $permission->category);
            $this->assertTrue($permission->is_system);
        }
    }

    /** @test */
    public function it_can_create_advanced_roles()
    {
        $role = AdvancedRole::create([
            'name' => 'finance_manager',
            'display_name' => 'Finance Manager',
            'description' => 'Manages financial operations',
            'category' => 'finance',
        ]);

        $this->assertDatabaseHas('advanced_roles', [
            'name' => 'finance_manager',
            'display_name' => 'Finance Manager',
        ]);

        $this->assertTrue($role->is_active);
        $this->assertEquals(0, $role->level);
        $this->assertTrue($role->inherit_permissions);
    }

    /** @test */
    public function it_supports_role_hierarchy()
    {
        $parentRole = AdvancedRole::create([
            'name' => 'admin',
            'display_name' => 'Administrator',
        ]);

        $childRole = AdvancedRole::create([
            'name' => 'finance_admin',
            'display_name' => 'Finance Administrator',
            'parent_role_id' => $parentRole->id,
        ]);

        $this->assertEquals(1, $childRole->level);
        $this->assertEquals((string) $parentRole->id, $childRole->hierarchy_path);
        $this->assertEquals($parentRole->id, $childRole->parent_role_id);
    }

    /** @test */
    public function it_can_assign_permissions_to_roles()
    {
        $role = AdvancedRole::create([
            'name' => 'user_manager',
            'display_name' => 'User Manager',
        ]);

        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $role->grantPermission($permission, ['ip_whitelist' => ['***********/24']], 10);

        $this->assertDatabaseHas('advanced_role_permissions', [
            'role_id' => $role->id,
            'permission_id' => $permission->id,
            'is_granted' => true,
            'priority' => 10,
        ]);

        $this->assertTrue($role->hasPermission('users.read'));
    }

    /** @test */
    public function it_can_assign_roles_to_users()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'basic_user',
            'display_name' => 'Basic User',
        ]);

        $assignment = $user->assignAdvancedRole($role, [
            'priority' => 5,
            'context' => 'project_alpha',
        ]);

        $this->assertDatabaseHas('advanced_user_roles', [
            'user_id' => $user->id,
            'user_type' => User::class,
            'role_id' => $role->id,
            'is_active' => true,
            'priority' => 5,
            'context' => 'project_alpha',
        ]);

        $this->assertTrue($user->hasAdvancedRole('basic_user'));
    }

    /** @test */
    public function it_can_assign_roles_to_admins()
    {
        $admin = Admin::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'system_admin',
            'display_name' => 'System Administrator',
        ]);

        $assignment = $admin->assignAdvancedRole($role);

        $this->assertDatabaseHas('advanced_user_roles', [
            'user_id' => $admin->id,
            'user_type' => Admin::class,
            'role_id' => $role->id,
            'is_active' => true,
        ]);

        $this->assertTrue($admin->hasAdvancedRole('system_admin'));
    }

    /** @test */
    public function it_supports_permission_inheritance()
    {
        // Create parent role with permission
        $parentRole = AdvancedRole::create([
            'name' => 'admin',
            'display_name' => 'Administrator',
        ]);

        $permission = AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
        ]);

        $parentRole->grantPermission($permission);

        // Create child role that inherits
        $childRole = AdvancedRole::create([
            'name' => 'finance_admin',
            'display_name' => 'Finance Administrator',
            'parent_role_id' => $parentRole->id,
            'inherit_permissions' => true,
        ]);

        // Child should have parent's permissions
        $this->assertTrue($childRole->hasPermission('users.read'));
        $this->assertContains($permission->id, $childRole->getAllPermissions()->pluck('id'));
    }

    /** @test */
    public function it_supports_super_admin_functionality()
    {
        $admin = Admin::factory()->create([
            'use_advanced_roles' => true,
            'is_super_admin' => true,
        ]);

        // Super admin should have all permissions
        $this->assertTrue($admin->isSuperAdmin());
        $this->assertTrue($admin->hasAdvancedPermission('any.permission'));
        
        // Should return all active permissions
        $permissions = $admin->getAdvancedPermissions();
        $this->assertEquals(AdvancedPermission::active()->count(), $permissions->count());
    }

    /** @test */
    public function it_caches_user_permissions()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create(['name' => 'test_role', 'display_name' => 'Test Role']);
        $permission = AdvancedPermission::create([
            'name' => 'test.permission',
            'display_name' => 'Test Permission',
            'resource' => 'test',
            'action' => 'permission',
        ]);

        $role->grantPermission($permission);
        $user->assignAdvancedRole($role);

        // First call should cache permissions
        $permissions1 = $user->getAdvancedPermissions();
        $this->assertNotNull($user->fresh()->advanced_role_cache);
        $this->assertNotNull($user->fresh()->role_cache_updated_at);

        // Second call should use cache
        $permissions2 = $user->getAdvancedPermissions();
        $this->assertEquals($permissions1->count(), $permissions2->count());
    }

    /** @test */
    public function it_logs_permission_checks()
    {
        $user = User::factory()->create();
        $role = AdvancedRole::create(['name' => 'test_role', 'display_name' => 'Test Role']);
        $permission = AdvancedPermission::create([
            'name' => 'test.read',
            'display_name' => 'Test Read',
            'resource' => 'test',
            'action' => 'read',
        ]);

        $audit = AdvancedPermissionAudit::logPermissionCheck(
            'test.read',
            true,
            $user,
            $role,
            $permission,
            'User has required role',
            ['test_context' => 'value'],
            150
        );

        $this->assertDatabaseHas('advanced_permission_audit', [
            'event_type' => AdvancedPermissionAudit::EVENT_ACCESS_GRANTED,
            'action' => 'test.read',
            'user_id' => $user->id,
            'was_granted' => true,
            'check_duration_ms' => 150,
        ]);
    }

    /** @test */
    public function it_supports_temporal_role_assignments()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create(['name' => 'temp_role', 'display_name' => 'Temporary Role']);

        $assignment = $user->assignAdvancedRole($role, [
            'expires_at' => now()->addDays(7),
        ]);

        $this->assertTrue($assignment->isCurrentlyValid());

        // Test expired assignment
        $assignment->update(['expires_at' => now()->subDay()]);
        $this->assertFalse($assignment->fresh()->isCurrentlyValid());
        $this->assertEquals('expired', $assignment->fresh()->getStatus());
    }

    /** @test */
    public function it_supports_permission_constraints()
    {
        $role = AdvancedRole::create(['name' => 'constrained_role', 'display_name' => 'Constrained Role']);
        $permission = AdvancedPermission::create([
            'name' => 'finance.approve',
            'display_name' => 'Approve Finance',
            'resource' => 'finance',
            'action' => 'approve',
        ]);

        $role->grantPermission($permission, [
            'max_amount' => 10000,
            'ip_whitelist' => ['***********/24'],
        ]);

        $rolePermission = $role->rolePermissions()->first();
        
        // Test constraint satisfaction
        $this->assertTrue($rolePermission->constraintsSatisfied(['amount' => 5000, 'ip' => '*************']));
        $this->assertFalse($rolePermission->constraintsSatisfied(['amount' => 15000, 'ip' => '*************']));
        $this->assertFalse($rolePermission->constraintsSatisfied(['amount' => 5000, 'ip' => '********']));
    }

    /** @test */
    public function it_prevents_deletion_of_system_permissions()
    {
        $permission = AdvancedPermission::create([
            'name' => 'system.permission',
            'display_name' => 'System Permission',
            'resource' => 'system',
            'action' => 'permission',
            'is_system' => true,
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('System permissions cannot be deleted.');
        
        $permission->delete();
    }

    /** @test */
    public function it_prevents_deletion_of_assigned_roles()
    {
        $role = AdvancedRole::create(['name' => 'assigned_role', 'display_name' => 'Assigned Role']);
        $user = User::factory()->create(['use_advanced_roles' => true]);
        
        $user->assignAdvancedRole($role);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Cannot delete role that is assigned to users.');
        
        $role->delete();
    }
}
