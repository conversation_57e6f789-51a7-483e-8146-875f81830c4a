<?php

use App\Http\Controllers\Admin\AdvancedUserManagementController;
use App\Http\Controllers\Admin\AdvancedRoleManagementController;
use App\Http\Controllers\Admin\AdvancedPermissionManagementController;
use App\Http\Controllers\Admin\AdvancedUserRoleController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Advanced Permission System Admin Routes
|--------------------------------------------------------------------------
|
| These routes handle the advanced permission system administration
| and integrate with the existing admin dashboard structure.
|
*/

Route::prefix('admin')->name('admin.')->middleware(['auth:admin', 'advanced.permission:system.admin'])->group(function () {
    
    // User & Role Management Dashboard
    Route::get('user-role-management', [AdvancedUserManagementController::class, 'dashboard'])
        ->name('user-role-management.dashboard')
        ->middleware('advanced.permission:user_roles.read');

    // Enhanced User Management with Advanced Roles
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [AdvancedUserManagementController::class, 'index'])
            ->name('index')
            ->middleware('advanced.permission:users.read');
            
        Route::get('create', [AdvancedUserManagementController::class, 'create'])
            ->name('create')
            ->middleware('advanced.permission:users.create');
            
        Route::post('/', [AdvancedUserManagementController::class, 'store'])
            ->name('store')
            ->middleware('advanced.permission:users.create');
            
        Route::get('{user}', [AdvancedUserManagementController::class, 'show'])
            ->name('show')
            ->middleware('advanced.permission:users.read');
            
        Route::get('{user}/edit', [AdvancedUserManagementController::class, 'edit'])
            ->name('edit')
            ->middleware('advanced.permission:users.update');
            
        Route::put('{user}', [AdvancedUserManagementController::class, 'update'])
            ->name('update')
            ->middleware('advanced.permission:users.update');
            
        Route::delete('{user}', [AdvancedUserManagementController::class, 'destroy'])
            ->name('destroy')
            ->middleware('advanced.permission:users.delete');

        // Advanced role assignment routes
        Route::post('{user}/assign-role', [AdvancedUserManagementController::class, 'assignRole'])
            ->name('assign-role')
            ->middleware('advanced.permission:user_roles.create');
            
        Route::delete('{user}/revoke-role/{role}', [AdvancedUserManagementController::class, 'revokeRole'])
            ->name('revoke-role')
            ->middleware('advanced.permission:user_roles.delete');
            
        Route::post('{user}/toggle-advanced-roles', [AdvancedUserManagementController::class, 'toggleAdvancedRoles'])
            ->name('toggle-advanced-roles')
            ->middleware('advanced.permission:users.update');
    });

    // Advanced Role Management
    Route::prefix('roles')->name('roles.')->group(function () {
        Route::get('/', [AdvancedRoleManagementController::class, 'index'])
            ->name('index')
            ->middleware('advanced.permission:advanced_roles.read');
            
        Route::get('create', [AdvancedRoleManagementController::class, 'create'])
            ->name('create')
            ->middleware('advanced.permission:advanced_roles.create');
            
        Route::post('/', [AdvancedRoleManagementController::class, 'store'])
            ->name('store')
            ->middleware('advanced.permission:advanced_roles.create');
            
        Route::get('{role}', [AdvancedRoleManagementController::class, 'show'])
            ->name('show')
            ->middleware('advanced.permission:advanced_roles.read');
            
        Route::get('{role}/edit', [AdvancedRoleManagementController::class, 'edit'])
            ->name('edit')
            ->middleware('advanced.permission:advanced_roles.update');
            
        Route::put('{role}', [AdvancedRoleManagementController::class, 'update'])
            ->name('update')
            ->middleware('advanced.permission:advanced_roles.update');
            
        Route::delete('{role}', [AdvancedRoleManagementController::class, 'destroy'])
            ->name('destroy')
            ->middleware('advanced.permission:advanced_roles.delete');

        // Permission assignment routes
        Route::post('{role}/assign-permission', [AdvancedRoleManagementController::class, 'assignPermission'])
            ->name('assign-permission')
            ->middleware('advanced.permission:advanced_roles.update');
            
        Route::delete('{role}/revoke-permission/{permission}', [AdvancedRoleManagementController::class, 'revokePermission'])
            ->name('revoke-permission')
            ->middleware('advanced.permission:advanced_roles.update');
            
        Route::post('{role}/bulk-assign-permissions', [AdvancedRoleManagementController::class, 'bulkAssignPermissions'])
            ->name('bulk-assign-permissions')
            ->middleware('advanced.permission:advanced_roles.update');

        // Role hierarchy management
        Route::post('{role}/set-parent', [AdvancedRoleManagementController::class, 'setParent'])
            ->name('set-parent')
            ->middleware('advanced.permission:advanced_roles.update');
    });

    // Permission Management
    Route::prefix('permissions')->name('permissions.')->group(function () {
        Route::get('/', [AdvancedPermissionManagementController::class, 'index'])
            ->name('index')
            ->middleware('advanced.permission:advanced_permissions.read');
            
        Route::get('create', [AdvancedPermissionManagementController::class, 'create'])
            ->name('create')
            ->middleware('advanced.permission:advanced_permissions.create');
            
        Route::post('/', [AdvancedPermissionManagementController::class, 'store'])
            ->name('store')
            ->middleware('advanced.permission:advanced_permissions.create');
            
        Route::get('{permission}', [AdvancedPermissionManagementController::class, 'show'])
            ->name('show')
            ->middleware('advanced.permission:advanced_permissions.read');
            
        Route::get('{permission}/edit', [AdvancedPermissionManagementController::class, 'edit'])
            ->name('edit')
            ->middleware('advanced.permission:advanced_permissions.update');
            
        Route::put('{permission}', [AdvancedPermissionManagementController::class, 'update'])
            ->name('update')
            ->middleware('advanced.permission:advanced_permissions.update');
            
        Route::delete('{permission}', [AdvancedPermissionManagementController::class, 'destroy'])
            ->name('destroy')
            ->middleware('advanced.permission:advanced_permissions.delete');

        // Discovery and bulk operations
        Route::post('discover', [AdvancedPermissionManagementController::class, 'discover'])
            ->name('discover')
            ->middleware('advanced.permission:advanced_permissions.create');
            
        Route::post('bulk-action', [AdvancedPermissionManagementController::class, 'bulkAction'])
            ->name('bulk-action')
            ->middleware('advanced.permission:advanced_permissions.update');
    });

    // User-Role Assignment Management
    Route::prefix('user-roles')->name('user-roles.')->group(function () {
        Route::get('/', [AdvancedUserRoleController::class, 'index'])
            ->name('index')
            ->middleware('advanced.permission:user_roles.read');
            
        Route::get('create', [AdvancedUserRoleController::class, 'create'])
            ->name('create')
            ->middleware('advanced.permission:user_roles.create');
            
        Route::post('/', [AdvancedUserRoleController::class, 'store'])
            ->name('store')
            ->middleware('advanced.permission:user_roles.create');
            
        Route::get('{assignment}', [AdvancedUserRoleController::class, 'show'])
            ->name('show')
            ->middleware('advanced.permission:user_roles.read');
            
        Route::get('{assignment}/edit', [AdvancedUserRoleController::class, 'edit'])
            ->name('edit')
            ->middleware('advanced.permission:user_roles.update');
            
        Route::put('{assignment}', [AdvancedUserRoleController::class, 'update'])
            ->name('update')
            ->middleware('advanced.permission:user_roles.update');
            
        Route::delete('{assignment}', [AdvancedUserRoleController::class, 'destroy'])
            ->name('destroy')
            ->middleware('advanced.permission:user_roles.delete');

        // Assignment management
        Route::post('{assignment}/reactivate', [AdvancedUserRoleController::class, 'reactivate'])
            ->name('reactivate')
            ->middleware('advanced.permission:user_roles.update');
            
        Route::post('{assignment}/extend', [AdvancedUserRoleController::class, 'extend'])
            ->name('extend')
            ->middleware('advanced.permission:user_roles.update');
    });

    // AJAX endpoints for dynamic loading
    Route::prefix('ajax')->name('ajax.')->group(function () {
        Route::get('users/search', [AdvancedUserManagementController::class, 'searchUsers'])
            ->name('users.search')
            ->middleware('advanced.permission:users.read');
            
        Route::get('roles/search', [AdvancedRoleManagementController::class, 'searchRoles'])
            ->name('roles.search')
            ->middleware('advanced.permission:advanced_roles.read');
            
        Route::get('permissions/search', [AdvancedPermissionManagementController::class, 'searchPermissions'])
            ->name('permissions.search')
            ->middleware('advanced.permission:advanced_permissions.read');
            
        Route::get('user/{user}/roles', [AdvancedUserManagementController::class, 'getUserRoles'])
            ->name('user.roles')
            ->middleware('advanced.permission:user_roles.read');
            
        Route::get('role/{role}/permissions', [AdvancedRoleManagementController::class, 'getRolePermissions'])
            ->name('role.permissions')
            ->middleware('advanced.permission:advanced_roles.read');
    });
});
