@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.rates.index') }}">
                                    @lang('Forex Rates')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Edit Rate')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Edit Exchange Rate')</h1>
                    <p class="page-header-text">@lang('Update CBN and parallel market exchange rates')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.rates.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Rates')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row justify-content-lg-center">
            <div class="col-lg-8">
                <!-- Card -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Exchange Rate Information')</h4>
                        <div class="d-flex gap-2">
                            <span class="badge bg-soft-{{ $rate->status === 'approved' ? 'success' : ($rate->status === 'rejected' ? 'danger' : 'warning') }} text-{{ $rate->status === 'approved' ? 'success' : ($rate->status === 'rejected' ? 'danger' : 'warning') }}">
                                {{ ucfirst($rate->status) }}
                            </span>
                            <span class="badge bg-soft-info text-info">
                                @lang('Created'): {{ $rate->created_at->format('M d, Y H:i') }}
                            </span>
                        </div>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <form action="{{ route('admin.forex.rates.update', $rate->id) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <!-- Rate Type -->
                            <div class="row mb-4">
                                <label for="rateTypeLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Rate Type') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control @error('rate_type') is-invalid @enderror"
                                           name="rate_type" id="rateTypeLabel"
                                           placeholder="@lang('e.g., Daily Rate, Weekly Rate, Special Rate')"
                                           value="{{ old('rate_type', $rate->rate_type) }}" required>
                                    @error('rate_type')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Rate Type -->

                            <!-- CBN Rate -->
                            <div class="row mb-4">
                                <label for="cbnRateLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('CBN Rate') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <span class="input-group-text">₦</span>
                                        <input type="number" class="form-control @error('cbn_rate') is-invalid @enderror"
                                               name="cbn_rate" id="cbnRateLabel" step="0.01" min="0"
                                               placeholder="@lang('0.00')" value="{{ old('cbn_rate', $rate->cbn_rate) }}" required>
                                        <span class="input-group-text">/ $1</span>
                                    </div>
                                    @error('cbn_rate')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End CBN Rate -->

                            <!-- Parallel Rate -->
                            <div class="row mb-4">
                                <label for="parallelRateLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Parallel Rate') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <span class="input-group-text">₦</span>
                                        <input type="number" class="form-control @error('parallel_rate') is-invalid @enderror"
                                               name="parallel_rate" id="parallelRateLabel" step="0.01" min="0"
                                               placeholder="@lang('0.00')" value="{{ old('parallel_rate', $rate->parallel_rate) }}" required>
                                        <span class="input-group-text">/ $1</span>
                                    </div>
                                    @error('parallel_rate')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Parallel Rate -->

                            <!-- Rate Difference Display -->
                            <div class="row mb-4">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Rate Difference')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-info" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-calculator"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <span class="fw-semibold">@lang('Calculated Difference:')</span>
                                                <span id="rateDifference">₦{{ number_format($rate->parallel_rate - $rate->cbn_rate, 2) }}</span>
                                                <br>
                                                <small class="text-muted">@lang('This is the profit margin per USD')</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Rate Difference Display -->

                            <!-- Buy Markup Percentage -->
                            <div class="row mb-4">
                                <label for="markupLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Buy Markup %')
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('markup_percentage') is-invalid @enderror"
                                               name="markup_percentage" id="markupLabel" step="0.01" min="0" max="100"
                                               placeholder="@lang('0.00')" value="{{ old('markup_percentage', $rate->markup_percentage) }}">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    @error('markup_percentage')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Buy Markup Percentage -->

                            <!-- Effective Buy Rate Display -->
                            <div class="row mb-4">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Effective Buy Rate')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-success" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-currency-exchange"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <span class="fw-semibold">@lang('Final Buy Rate with Markup:')</span>
                                                <span id="effectiveRate">₦{{ number_format($rate->parallel_rate + ($rate->parallel_rate * $rate->markup_percentage / 100), 2) }} / $1</span>
                                                <br>
                                                <small class="text-muted">@lang('This is the buy rate customers will see (NGN to USD)')</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Effective Buy Rate Display -->

                            <!-- Sell Rates Section -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="card-header-title">@lang('Sell Rates') <small class="text-muted">(@lang('USD to NGN'))</small></h5>
                                    <hr class="my-3">
                                </div>
                            </div>

                            <!-- CBN Sell Rate -->
                            <div class="row mb-4">
                                <label for="cbnSellRateLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('CBN Sell Rate') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <span class="input-group-text">₦</span>
                                        <input type="number" class="form-control @error('cbn_sell_rate') is-invalid @enderror"
                                               name="cbn_sell_rate" id="cbnSellRateLabel" step="0.01" min="0"
                                               placeholder="@lang('0.00')" value="{{ old('cbn_sell_rate', $rate->cbn_sell_rate) }}" required>
                                        <span class="input-group-text">/ $1</span>
                                    </div>
                                    @error('cbn_sell_rate')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End CBN Sell Rate -->

                            <!-- Parallel Sell Rate -->
                            <div class="row mb-4">
                                <label for="parallelSellRateLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Parallel Sell Rate') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <span class="input-group-text">₦</span>
                                        <input type="number" class="form-control @error('parallel_sell_rate') is-invalid @enderror"
                                               name="parallel_sell_rate" id="parallelSellRateLabel" step="0.01" min="0"
                                               placeholder="@lang('0.00')" value="{{ old('parallel_sell_rate', $rate->parallel_sell_rate) }}" required>
                                        <span class="input-group-text">/ $1</span>
                                    </div>
                                    @error('parallel_sell_rate')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Parallel Sell Rate -->

                            <!-- Sell Rate Difference Display -->
                            <div class="row mb-4">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Sell Rate Difference')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-info" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-calculator"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <span class="fw-semibold">@lang('Calculated Sell Difference:')</span>
                                                <span id="sellRateDifference">₦{{ number_format(($rate->parallel_sell_rate ?? 0) - ($rate->cbn_sell_rate ?? 0), 2) }}</span>
                                                <br>
                                                <small class="text-muted">@lang('This is the sell profit margin per USD')</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Sell Rate Difference Display -->

                            <!-- Sell Markup Percentage -->
                            <div class="row mb-4">
                                <label for="sellMarkupLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Sell Markup %')
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('sell_markup_percentage') is-invalid @enderror"
                                               name="sell_markup_percentage" id="sellMarkupLabel" step="0.01" min="0" max="100"
                                               placeholder="@lang('0.00')" value="{{ old('sell_markup_percentage', $rate->sell_markup_percentage) }}">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    @error('sell_markup_percentage')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Sell Markup Percentage -->

                            <!-- Effective Sell Rate Display -->
                            <div class="row mb-4">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Effective Sell Rate')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-success" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-currency-exchange"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <span class="fw-semibold">@lang('Final Sell Rate with Markup:')</span>
                                                <span id="effectiveSellRate">₦{{ number_format(($rate->parallel_sell_rate ?? 0) + (($rate->parallel_sell_rate ?? 0) * ($rate->sell_markup_percentage ?? 0) / 100), 2) }} / $1</span>
                                                <br>
                                                <small class="text-muted">@lang('This is the sell rate customers will see (USD to NGN)')</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Effective Sell Rate Display -->

                            <!-- Active Status -->
                            <div class="row mb-4">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Status')
                                </label>
                                <div class="col-sm-9">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_active" id="isActiveCheck"
                                               value="1" {{ old('is_active', $rate->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="isActiveCheck">
                                            @lang('Set as active rate (requires approval)')
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        @lang('Changes will reset status to pending and require re-approval')
                                    </small>
                                </div>
                            </div>
                            <!-- End Active Status -->

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end">
                                <div class="d-flex gap-3">
                                    <a class="btn btn-white" href="{{ route('admin.forex.rates.index') }}">
                                        @lang('Cancel')
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi-check-circle me-1"></i> @lang('Update Exchange Rate')
                                    </button>
                                </div>
                            </div>
                            <!-- End Submit Buttons -->
                        </form>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Card -->
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Calculate buy and sell rates
            function calculateRates() {
                // Buy rates calculation
                const cbnRate = parseFloat($('#cbnRateLabel').val()) || 0;
                const parallelRate = parseFloat($('#parallelRateLabel').val()) || 0;
                const markup = parseFloat($('#markupLabel').val()) || 0;

                // Calculate buy difference
                const difference = parallelRate - cbnRate;
                $('#rateDifference').text('₦' + difference.toFixed(2));

                // Calculate effective buy rate with markup
                const effectiveRate = parallelRate + (parallelRate * markup / 100);
                $('#effectiveRate').text('₦' + effectiveRate.toFixed(2) + ' / $1');

                // Sell rates calculation
                const cbnSellRate = parseFloat($('#cbnSellRateLabel').val()) || 0;
                const parallelSellRate = parseFloat($('#parallelSellRateLabel').val()) || 0;
                const sellMarkup = parseFloat($('#sellMarkupLabel').val()) || 0;

                // Calculate sell difference
                const sellDifference = parallelSellRate - cbnSellRate;
                $('#sellRateDifference').text('₦' + sellDifference.toFixed(2));

                // Calculate effective sell rate with markup
                const effectiveSellRate = parallelSellRate + (parallelSellRate * sellMarkup / 100);
                $('#effectiveSellRate').text('₦' + effectiveSellRate.toFixed(2) + ' / $1');
            }

            // Bind calculation to input changes for both buy and sell rates
            $('#cbnRateLabel, #parallelRateLabel, #markupLabel, #cbnSellRateLabel, #parallelSellRateLabel, #sellMarkupLabel').on('input', calculateRates);

            // Initial calculation
            calculateRates();
        });
    </script>
@endpush
