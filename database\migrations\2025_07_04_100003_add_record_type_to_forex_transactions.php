<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'record' to the transaction_type enum
        DB::statement("ALTER TABLE forex_transactions MODIFY COLUMN transaction_type ENUM('credit', 'debit', 'transfer', 'record')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'record' from the transaction_type enum
        DB::statement("ALTER TABLE forex_transactions MODIFY COLUMN transaction_type ENUM('credit', 'debit', 'transfer')");
    }
};
