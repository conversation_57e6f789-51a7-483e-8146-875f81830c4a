<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ForexBooking;
use App\Models\ForexTransaction;
use App\Models\ForexAccount;
use App\Models\OperationalCost;
use App\Models\ForexReportTemplate;
use App\Services\ForexReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ForexReportController extends Controller
{
    protected $reportService;

    public function __construct(ForexReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    public function index()
    {
        $data['pageTitle'] = 'Forex Reports';
        $data['stats'] = [
            'total_bookings' => ForexBooking::count(),
            'pending_bookings' => ForexBooking::pending()->count(),
            'completed_bookings' => ForexBooking::completed()->count(),
            'total_transactions' => ForexTransaction::count(),
            'total_accounts' => ForexAccount::count(),
        ];
        return view('admin.forex.reports.index', $data);
    }

    public function cbnReports()
    {
        $data['pageTitle'] = 'CBN Compliance Reports';
        return view('admin.forex.reports.cbn', $data);
    }

    public function dailyReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date' => 'required|date|before_or_equal:today',
            'format' => 'nullable|in:html,excel,pdf',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $date = $request->date;
        $format = $request->format ?? 'html';

        try {
            $reportData = $this->reportService->generateDailyReport($date);

            if ($format === 'excel') {
                return $this->reportService->exportDailyReportToExcel($reportData, $date);
            } elseif ($format === 'pdf') {
                return $this->reportService->exportDailyReportToPdf($reportData, $date);
            }

            $data['pageTitle'] = 'Daily Report - ' . date('M d, Y', strtotime($date));
            $data['reportData'] = $reportData;
            $data['date'] = $date;
            return view('admin.forex.reports.daily', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate report: ' . $e->getMessage())->withInput();
        }
    }

    public function weeklyReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'nullable|in:html,excel,pdf',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $format = $request->format ?? 'html';

        try {
            $reportData = $this->reportService->generateWeeklyReport($startDate, $endDate);

            if ($format === 'excel') {
                return $this->reportService->exportWeeklyReportToExcel($reportData, $startDate, $endDate);
            } elseif ($format === 'pdf') {
                return $this->reportService->exportWeeklyReportToPdf($reportData, $startDate, $endDate);
            }

            $data['pageTitle'] = 'Weekly Report - ' . date('M d', strtotime($startDate)) . ' to ' . date('M d, Y', strtotime($endDate));
            $data['reportData'] = $reportData;
            $data['startDate'] = $startDate;
            $data['endDate'] = $endDate;
            return view('admin.forex.reports.weekly', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate report: ' . $e->getMessage())->withInput();
        }
    }

    public function monthlyReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'month' => 'required|integer|between:1,12',
            'year' => 'required|integer|min:2020|max:' . (date('Y') + 1),
            'format' => 'nullable|in:html,excel,pdf',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $month = $request->month;
        $year = $request->year;
        $format = $request->format ?? 'html';

        try {
            $reportData = $this->reportService->generateMonthlyReport($month, $year);

            if ($format === 'excel') {
                return $this->reportService->exportMonthlyReportToExcel($reportData, $month, $year);
            } elseif ($format === 'pdf') {
                return $this->reportService->exportMonthlyReportToPdf($reportData, $month, $year);
            }

            $data['pageTitle'] = 'Monthly Report - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
            $data['reportData'] = $reportData;
            $data['month'] = $month;
            $data['year'] = $year;
            return view('admin.forex.reports.monthly', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate report: ' . $e->getMessage())->withInput();
        }
    }

    public function annualReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'year' => 'required|integer|min:2020|max:' . (date('Y') + 1),
            'format' => 'nullable|in:html,excel,pdf',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $year = $request->year;
        $format = $request->format ?? 'html';

        try {
            $reportData = $this->reportService->generateAnnualReport($year);

            if ($format === 'excel') {
                return $this->reportService->exportAnnualReportToExcel($reportData, $year);
            } elseif ($format === 'pdf') {
                return $this->reportService->exportAnnualReportToPdf($reportData, $year);
            }

            $data['pageTitle'] = 'Annual Report - ' . $year;
            $data['reportData'] = $reportData;
            $data['year'] = $year;
            return view('admin.forex.reports.annual', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate report: ' . $e->getMessage())->withInput();
        }
    }

    public function revenueReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'nullable|in:html,excel,pdf',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $format = $request->format ?? 'html';

        try {
            $reportData = $this->reportService->generateRevenueReport($startDate, $endDate);

            if ($format === 'excel') {
                return $this->reportService->exportRevenueReportToExcel($reportData, $startDate, $endDate);
            } elseif ($format === 'pdf') {
                return $this->reportService->exportRevenueReportToPdf($reportData, $startDate, $endDate);
            }

            $data['pageTitle'] = 'Revenue Report - ' . date('M d', strtotime($startDate)) . ' to ' . date('M d, Y', strtotime($endDate));
            $data['reportData'] = $reportData;
            $data['startDate'] = $startDate;
            $data['endDate'] = $endDate;
            return view('admin.forex.reports.revenue', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate report: ' . $e->getMessage())->withInput();
        }
    }

    public function operationalCostReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'category' => 'nullable|string',
            'format' => 'nullable|in:html,excel,pdf',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $category = $request->category;
        $format = $request->format ?? 'html';

        try {
            $reportData = $this->reportService->generateOperationalCostReport($startDate, $endDate, $category);

            if ($format === 'excel') {
                return $this->reportService->exportOperationalCostReportToExcel($reportData, $startDate, $endDate);
            } elseif ($format === 'pdf') {
                return $this->reportService->exportOperationalCostReportToPdf($reportData, $startDate, $endDate);
            }

            $data['pageTitle'] = 'Operational Cost Report - ' . date('M d', strtotime($startDate)) . ' to ' . date('M d, Y', strtotime($endDate));
            $data['reportData'] = $reportData;
            $data['startDate'] = $startDate;
            $data['endDate'] = $endDate;
            $data['category'] = $category;
            $data['categories'] = OperationalCost::getAvailableCategories();
            return view('admin.forex.reports.operational_costs', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate report: ' . $e->getMessage())->withInput();
        }
    }

    public function customReport(Request $request)
    {
        $data['pageTitle'] = 'Custom Report Builder';
        $data['accounts'] = ForexAccount::active()->latest()->get();
        $data['categories'] = OperationalCost::getAvailableCategories();
        $data['templates'] = $this->reportService->getReportTemplates(true); // Get public templates
        return view('admin.forex.reports.custom', $data);
    }

    public function generateCustomReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'report_type' => 'required|in:bookings,transactions,performance,volume,cbn_focused',
            'date_range_type' => 'required|in:daily,weekly,monthly,custom',
            'start_date' => 'required_if:date_range_type,custom,weekly|date',
            'end_date' => 'required_if:date_range_type,custom,weekly|date|after_or_equal:start_date',
            'specific_date' => 'required_if:date_range_type,daily|date',
            'month' => 'required_if:date_range_type,monthly|integer|between:1,12',
            'year' => 'required_if:date_range_type,monthly|integer|min:2020|max:' . (date('Y') + 1),
            'transaction_type' => 'nullable|in:buying,selling,both',
            'currency' => 'nullable|in:USD,NGN,both',
            'selected_fields' => 'nullable|array',
            'field_labels' => 'nullable|array',
            'format' => 'nullable|in:html,excel,pdf,csv',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $reportData = $this->reportService->generateCustomReport($request->all());

            $format = $request->format ?? 'html';

            if ($format !== 'html') {
                return $this->reportService->exportCustomReport($reportData, $request->all(), $format);
            }

            $data['pageTitle'] = 'Custom Report - ' . ucfirst($request->report_type);
            $data['reportData'] = $reportData;
            $data['reportConfig'] = $request->all();
            return view('admin.forex.reports.custom_result', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate custom report: ' . $e->getMessage())->withInput();
        }
    }

    public function saveTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_name' => 'required|string|max:255|unique:forex_report_templates,name',
            'template_description' => 'nullable|string|max:500',
            'is_public' => 'boolean',
            'configuration' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $template = $this->reportService->saveReportTemplate(
                $request->configuration,
                $request->template_name,
                $request->template_description ?? '',
                $request->boolean('is_public'),
                auth('admin')->id()
            );

            return response()->json([
                'success' => true,
                'message' => 'Template saved successfully',
                'template' => $template
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to save template: ' . $e->getMessage()], 500);
        }
    }

    public function loadTemplate(Request $request, $templateId)
    {
        try {
            $configuration = $this->reportService->loadReportTemplate($templateId);

            return response()->json([
                'success' => true,
                'configuration' => $configuration
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to load template: ' . $e->getMessage()], 500);
        }
    }

    public function deleteTemplate(Request $request, $templateId)
    {
        try {
            $template = ForexReportTemplate::findOrFail($templateId);

            // Check if user can delete this template
            if (!$template->is_public && $template->created_by !== auth('admin')->id()) {
                return response()->json(['success' => false, 'message' => 'Unauthorized to delete this template'], 403);
            }

            $template->delete();

            return response()->json([
                'success' => true,
                'message' => 'Template deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to delete template: ' . $e->getMessage()], 500);
        }
    }

    public function getTemplates(Request $request)
    {
        try {
            $templates = $this->reportService->getReportTemplates($request->boolean('public_only', true));

            return response()->json([
                'success' => true,
                'templates' => $templates
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to load templates: ' . $e->getMessage()], 500);
        }
    }

    public function cbnFocusedReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date_range_type' => 'required|in:daily,weekly,monthly,custom',
            'start_date' => 'required_if:date_range_type,custom,weekly|date',
            'end_date' => 'required_if:date_range_type,custom,weekly|date|after_or_equal:start_date',
            'specific_date' => 'required_if:date_range_type,daily|date',
            'month' => 'required_if:date_range_type,monthly|integer|between:1,12',
            'year' => 'required_if:date_range_type,monthly|integer|min:2020|max:' . (date('Y') + 1),
            'transaction_type' => 'nullable|in:buying,selling,both',
            'format' => 'nullable|in:html,excel,pdf,csv',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Force CBN-focused configuration
            $config = array_merge($request->all(), [
                'report_type' => 'cbn_focused',
                'selected_fields' => [
                    'booking_reference', 'client_name', 'transaction_type', 'currency',
                    'amount', 'cbn_rate', 'markup_percentage', 'customer_rate',
                    'cbn_total', 'markup_amount', 'status', 'created_at'
                ],
                'include_advanced_stats' => true,
                'exclude_parallel_data' => true, // Explicitly exclude parallel market data
            ]);

            $reportData = $this->reportService->generateCustomReport($config);

            $format = $request->format ?? 'html';

            if ($format !== 'html') {
                return $this->reportService->exportCustomReport($reportData, $config, $format);
            }

            $data['pageTitle'] = 'CBN Compliance Report';
            $data['reportData'] = $reportData;
            $data['reportConfig'] = $config;
            return view('admin.forex.reports.cbn_focused', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate CBN report: ' . $e->getMessage())->withInput();
        }
    }

    public function seedCbnTemplates(Request $request)
    {
        try {
            // Log the attempt
            \Log::info('Attempting to seed CBN templates');

            // Create CBN-focused templates
            $this->reportService->createCbnFocusedTemplates();

            // Create default templates
            ForexReportTemplate::createDefaultTemplates();

            $templatesCount = ForexReportTemplate::count();

            \Log::info('CBN templates seeded successfully', ['count' => $templatesCount]);

            return response()->json([
                'success' => true,
                'message' => 'CBN templates seeded successfully',
                'templates_count' => $templatesCount
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to seed CBN templates', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to seed templates: ' . $e->getMessage()
            ], 500);
        }
    }

    public function volumeAnalysisReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date_range_type' => 'required|in:daily,weekly,monthly,custom',
            'start_date' => 'required_if:date_range_type,custom,weekly|date',
            'end_date' => 'required_if:date_range_type,custom,weekly|date|after_or_equal:start_date',
            'specific_date' => 'required_if:date_range_type,daily|date',
            'month' => 'required_if:date_range_type,monthly|integer|between:1,12',
            'year' => 'required_if:date_range_type,monthly|integer|min:2020|max:' . (date('Y') + 1),
            'transaction_type' => 'nullable|in:buying,selling,both',
            'currency' => 'nullable|in:USD,NGN,both',
            'format' => 'nullable|in:html,excel,pdf,csv',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Force volume report configuration
            $config = array_merge($request->all(), [
                'report_type' => 'volume',
                'selected_fields' => [
                    'booking_reference', 'client_name', 'transaction_type', 'currency',
                    'amount', 'customer_total', 'status', 'created_at'
                ],
                'include_volume_analytics' => true,
                'include_client_distribution' => true,
                'include_size_distribution' => true,
            ]);

            $reportData = $this->reportService->generateCustomReport($config);

            $format = $request->format ?? 'html';

            if ($format !== 'html') {
                return $this->reportService->exportCustomReport($reportData, $config, $format);
            }

            $data['pageTitle'] = 'Transaction Volume Analysis';
            $data['reportData'] = $reportData;
            $data['reportConfig'] = $config;
            return view('admin.forex.reports.volume_analysis', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate volume analysis report: ' . $e->getMessage())->withInput();
        }
    }

    public function performanceAnalyticsReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'date_range_type' => 'required|in:daily,weekly,monthly,custom',
            'start_date' => 'required_if:date_range_type,custom,weekly|date',
            'end_date' => 'required_if:date_range_type,custom,weekly|date|after_or_equal:start_date',
            'specific_date' => 'required_if:date_range_type,daily|date',
            'month' => 'required_if:date_range_type,monthly|integer|between:1,12',
            'year' => 'required_if:date_range_type,monthly|integer|min:2020|max:' . (date('Y') + 1),
            'transaction_type' => 'nullable|in:buying,selling,both',
            'currency' => 'nullable|in:USD,NGN,both',
            'format' => 'nullable|in:html,excel,pdf,csv',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Force performance report configuration
            $config = array_merge($request->all(), [
                'report_type' => 'performance',
                'selected_fields' => [
                    'booking_reference', 'client_name', 'transaction_type', 'amount',
                    'markup_percentage', 'markup_amount', 'customer_payment_amount',
                    'status', 'created_at', 'completed_at'
                ],
                'include_performance_analytics' => true,
                'include_efficiency_metrics' => true,
                'include_profitability_analysis' => true,
                'include_forecasting' => true,
            ]);

            $reportData = $this->reportService->generateCustomReport($config);

            $format = $request->format ?? 'html';

            if ($format !== 'html') {
                return $this->reportService->exportCustomReport($reportData, $config, $format);
            }

            $data['pageTitle'] = 'Performance Analytics Report';
            $data['reportData'] = $reportData;
            $data['reportConfig'] = $config;
            return view('admin.forex.reports.performance_analytics', $data);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate performance analytics report: ' . $e->getMessage())->withInput();
        }
    }
}
