<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * Advanced Permission Facade
 * 
 * Provides easy access to permission checking functionality throughout the application.
 * 
 * @method static bool check($user, string $permission, array $context = [])
 * @method static array checkDetailed($user, string $permission, array $context = [])
 * @method static bool checkMultiple($user, array $permissions, string $logic = 'and', array $context = [])
 * @method static array getUserPermissions($user)
 * @method static array getUserRoles($user)
 * @method static bool hasRole($user, string $roleName)
 * @method static bool hasAnyRole($user, array $roleNames)
 * @method static void logAccess(string $action, bool $granted, $user = null, array $context = [])
 * @method static array getResourcePermissions(string $resource)
 * @method static array getPermissionConstraints($user, string $permission)
 * @method static bool validateConstraints(array $constraints, array $context)
 */
class AdvancedPermission extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'advanced-permission';
    }
}
