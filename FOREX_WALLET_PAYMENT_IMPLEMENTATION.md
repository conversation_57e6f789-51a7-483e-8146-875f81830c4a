# Forex Wallet Payment Feature Implementation

## Overview

This document outlines the complete implementation of the wallet payment feature for forex trading, allowing registered users and merchants to receive payments directly into their wallets instead of traditional bank account details.

## Feature Description

### Core Functionality
- **For registered users/merchants**: When creating a forex booking, the system checks if they have the appropriate wallet (NGN for USD→NGN, USD for NGN→USD)
- **Payment method selection**: If wallet exists, show two options:
  - "Pay to Wallet" 
  - "Add Account Details" (traditional bank details)
- **If no wallet**: Only show account details field
- **Upon completion**: If user chose wallet payment, fund their wallet and create transaction records tied to the booking

### Transaction Logic
- **NGN to USD (Buying)**: User receives USD in their USD wallet
- **USD to NGN (Selling)**: User receives NGN in their NGN wallet

## Implementation Details

### 1. Database Schema Changes

**File**: `database/migrations/2025_07_06_000001_add_wallet_payment_fields_to_forex_bookings_table.php`

Added two new fields to the `forex_bookings` table:
- `payment_method`: ENUM('account_details', 'wallet') - tracks payment method
- `wallet_currency_id`: BIGINT NULLABLE - references currencies table for wallet funding

```sql
ALTER TABLE forex_bookings 
ADD COLUMN payment_method ENUM('account_details', 'wallet') NOT NULL DEFAULT 'account_details',
ADD COLUMN wallet_currency_id BIGINT UNSIGNED NULL;
```

### 2. Model Updates

**File**: `app/Models/ForexBooking.php`

**New Fields Added to Fillable:**
- `payment_method`
- `wallet_currency_id`

**New Relationships:**
```php
public function walletCurrency()
{
    return $this->belongsTo(Currency::class, 'wallet_currency_id');
}
```

**New Helper Methods:**
```php
public function isWalletPayment(): bool
{
    return $this->payment_method === 'wallet';
}

public function getWalletCurrencyCode(): ?string
{
    return $this->walletCurrency?->code;
}
```

### 3. Service Layer

**File**: `app/Services/ForexWalletService.php`

New service class to handle all wallet-related operations:

**Key Methods:**
- `userHasWallet($userId, $currencyCode)`: Check if user has specific currency wallet
- `getWalletAvailability($userId)`: Get wallet availability for both NGN and USD
- `getTargetWalletCurrency($transactionType)`: Determine which wallet to fund
- `getPaymentMethodOptions($userId, $transactionType)`: Get available payment methods
- `fundWalletFromBooking($booking, $adminId)`: Fund user wallet upon booking completion
- `validateWalletPayment($userId, $paymentMethod, $walletCurrencyId)`: Validate wallet selection

**Wallet Funding Logic:**
```php
private function calculateWalletFundAmount(ForexBooking $booking): float
{
    if ($booking->transaction_type === 'buying') {
        // NGN to USD: User receives USD amount
        return $booking->amount;
    } else {
        // USD to NGN: User receives NGN amount (customer_total)
        return $booking->customer_total;
    }
}
```

### 4. Controller Updates

**File**: `app/Http/Controllers/Admin/ForexBookingController.php`

**Changes Made:**
- Added `ForexWalletService` dependency injection
- Updated validation rules to include `payment_method` and `wallet_currency_id`
- Added wallet validation logic before booking creation
- Added `getUserWalletInfo()` endpoint for AJAX wallet information retrieval
- Updated booking creation to include new wallet fields
- Enhanced booking listing to show payment method badges

**New Validation Rules:**
```php
'payment_method' => 'nullable|in:account_details,wallet',
'wallet_currency_id' => 'nullable|exists:currencies,id',
```

**File**: `app/Http/Controllers/Api/ForexController.php`

**API Updates:**
- Added wallet payment support to booking creation
- Added `getWalletPaymentOptions()` endpoint for authenticated users
- Enhanced validation for API wallet payments

### 5. Service Integration

**File**: `app/Services/ForexBookingService.php`

**Updated Completion Logic:**
```php
public function completeBooking(ForexBooking $booking, int $adminId, ?string $notes = null): void
{
    DB::transaction(function () use ($booking, $adminId, $notes) {
        // ... existing logic ...
        
        // Handle wallet funding if payment method is wallet
        if ($booking->isWalletPayment()) {
            $this->walletService->fundWalletFromBooking($booking, $adminId);
        }
        
        // ... rest of completion logic ...
    });
}
```

### 6. Frontend Implementation

**File**: `resources/views/admin/forex/bookings/create.blade.php`

**New UI Elements:**
- Payment method selection radio buttons
- Dynamic wallet availability checking via AJAX
- Conditional display of account details field
- Real-time wallet balance display

**JavaScript Features:**
- `loadWalletInfo()`: Fetches wallet availability for selected user
- `showPaymentMethodOptions()`: Displays available payment methods
- `hidePaymentMethodOptions()`: Hides payment options for external clients
- Dynamic form validation based on payment method selection

**Payment Method Options Display:**
```javascript
function showPaymentMethodOptions(data) {
    let optionsHtml = '';
    
    Object.keys(data.payment_methods).forEach(function(method) {
        const option = data.payment_methods[method];
        const isWallet = method === 'wallet';
        
        optionsHtml += `
            <div class="form-check mb-2">
                <input class="form-check-input payment-method-radio" type="radio" 
                       name="payment_method_display" id="payment_${method}" 
                       value="${method}" data-currency-id="${isWallet ? data.currency_id : ''}">
                <label class="form-check-label" for="payment_${method}">
                    <strong>${option.label}</strong>
                    <br><small class="text-muted">${option.description}</small>
                </label>
            </div>
        `;
    });
    
    $('#paymentMethodOptions').html(optionsHtml);
}
```

### 7. Admin Interface Updates

**File**: `resources/views/admin/forex/bookings/show.blade.php`

**Enhanced Booking Display:**
- Payment method information with badges
- Wallet currency display for wallet payments
- Clear indication of payment destination

**Payment Method Display:**
```php
@if($booking->payment_method === 'wallet')
    <div class="d-flex align-items-center">
        <span class="badge bg-primary me-2">@lang('Wallet Payment')</span>
        @if($booking->walletCurrency)
            <span class="text-muted">{{ $booking->walletCurrency->code }} Wallet</span>
        @endif
    </div>
@else
    <span class="badge bg-secondary">@lang('Bank Account Details')</span>
@endif
```

**Updated Booking Listing:**
- Payment method badges in client info column
- Visual distinction between wallet and bank payments

### 8. API Routes

**File**: `routes/admin.php`
```php
Route::get('/user-wallet-info', [ForexBookingController::class, 'getUserWalletInfo'])->name('user.wallet.info');
```

**File**: `routes/api.php`
```php
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/wallet-payment-options', [ForexController::class, 'getWalletPaymentOptions'])->name('wallet.payment.options');
});
```

### 9. Transaction Recording

**Wallet Funding Process:**
1. Upon booking completion, if `payment_method === 'wallet'`
2. Calculate fund amount based on transaction type
3. Update user's wallet balance using `updateWallet()` helper
4. Create transaction record with proper metadata:

```php
Transaction::create([
    'user_id' => $booking->user_id,
    'currency_id' => $currencyId,
    'amount' => $fundAmount,
    'charge' => 0,
    'trx_type' => '+',
    'remarks' => "Forex wallet funding - {$booking->booking_reference}",
    'trx_id' => $this->generateTransactionId(),
    'transactional_type' => ForexBooking::class,
    'transactional_id' => $booking->id,
]);
```

## Testing Implementation

### Test Files Created

1. **`tests/Feature/ForexWalletPaymentTest.php`**
   - Unit tests for ForexWalletService methods
   - Wallet availability checking
   - Payment method validation
   - Fund amount calculations

2. **`tests/Feature/ForexBookingWalletControllerTest.php`**
   - Controller endpoint testing
   - Admin booking creation with wallet payment
   - API wallet payment options
   - Validation testing

3. **`tests/Feature/ForexWalletIntegrationTest.php`**
   - End-to-end workflow testing
   - Complete booking lifecycle with wallet payment
   - Transaction recording verification
   - Edge case handling

### Test Coverage

**Scenarios Tested:**
- ✅ User wallet availability checking
- ✅ Payment method option generation
- ✅ Wallet payment validation
- ✅ Booking creation with wallet payment
- ✅ Booking completion with wallet funding
- ✅ Transaction record creation
- ✅ Fund amount calculations for both transaction types
- ✅ API endpoint functionality
- ✅ Admin interface integration
- ✅ Edge cases (missing wallets, external clients)

## Usage Examples

### 1. Admin Creating Booking with Wallet Payment

```javascript
// User selects registered user
$('#userLabel').change() // Triggers wallet info loading

// System shows payment options
{
    "has_wallet": true,
    "target_currency": "USD",
    "payment_methods": {
        "account_details": {
            "label": "Bank Account Details",
            "description": "Provide bank account details for payment"
        },
        "wallet": {
            "label": "Pay to USD Wallet",
            "description": "Fund your USD wallet (Balance: 100.00 USD)",
            "wallet_balance": 100.00
        }
    }
}
```

### 2. API Usage for Wallet Payment

```javascript
// Get wallet payment options
GET /api/forex/wallet-payment-options?transaction_type=buying
Authorization: Bearer {token}

// Create booking with wallet payment
POST /api/forex/booking
{
    "transaction_type": "buying",
    "currency": "USD",
    "amount": 100.00,
    "client_name": "John Doe",
    "client_email": "<EMAIL>",
    "payment_method": "wallet",
    "wallet_currency_id": 1
}
```

### 3. Booking Completion Flow

```php
// Admin completes booking
$bookingService->completeBooking($booking, $adminId, 'Payment processed');

// System automatically:
// 1. Processes forex account transactions
// 2. Funds user wallet if payment_method === 'wallet'
// 3. Creates transaction record
// 4. Sends completion email
```

## Security Considerations

1. **Wallet Validation**: System validates user owns the specified wallet before allowing wallet payment
2. **Authentication**: API endpoints require proper authentication
3. **Authorization**: Only admins can create bookings, users can only view their own wallet options
4. **Transaction Integrity**: All wallet funding happens within database transactions
5. **Input Validation**: Comprehensive validation of all payment method inputs

## Performance Considerations

1. **Efficient Queries**: Wallet availability checked with optimized database queries
2. **Caching**: Wallet information can be cached for better performance
3. **Batch Operations**: Transaction creation and wallet updates happen in single database transaction
4. **Minimal API Calls**: Frontend loads wallet info only when needed

## Backward Compatibility

- ✅ Existing bookings continue to work with `account_details` payment method
- ✅ External clients automatically use account details (no wallet option)
- ✅ All existing forex functionality remains unchanged
- ✅ Database migration is non-destructive with default values

## Future Enhancements

1. **Multi-Currency Wallets**: Support for additional currencies
2. **Wallet Notifications**: Real-time notifications for wallet funding
3. **Wallet Limits**: Configurable wallet funding limits
4. **Audit Trail**: Enhanced audit logging for wallet transactions
5. **Mobile API**: Dedicated mobile endpoints for wallet management

## Conclusion

The forex wallet payment feature has been successfully implemented with comprehensive testing, following existing patterns and maintaining backward compatibility. The implementation provides a seamless user experience while maintaining security and data integrity.
