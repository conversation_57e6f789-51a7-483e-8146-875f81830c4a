<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\WebhookForwardingService;
use App\Traits\ApiValidation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class WebhookTestController extends Controller
{
    use ApiValidation;

    /**
     * Test webhook URL configuration
     */
    public function testWebhook(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'webhook_url' => 'required|url',
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $user = Auth::user();
            $webhookUrl = $request->webhook_url;

            // Test the webhook
            $success = WebhookForwardingService::testWebhook($webhookUrl, $user->id);

            if ($success) {
                return response()->json($this->withSuccess('Webhook test successful. Check your endpoint for the test notification.'));
            } else {
                return response()->json($this->withErrors('Webhook test failed. Please check your endpoint URL and ensure it returns HTTP 200-299 status codes.'));
            }

        } catch (\Exception $e) {
            return response()->json($this->withErrors('Webhook test failed: ' . $e->getMessage()));
        }
    }

    /**
     * Get webhook configuration for current user
     */
    public function getWebhookConfig(Request $request)
    {
        try {
            $user = Auth::user();
            $webhookUrl = null;

            if ($user->type === 'merchant') {
                $merchantSetting = \Modules\Merchant\Models\MerchantSetting::where('merchant_id', $user->id)->first();
                $webhookUrl = $merchantSetting?->webhook_url;
            } else {
                $webhookUrl = $user->webhook_url;
            }

            return response()->json($this->withSuccess([
                'webhook_url' => $webhookUrl,
                'user_type' => $user->type,
                'configured' => !empty($webhookUrl)
            ]));

        } catch (\Exception $e) {
            return response()->json($this->withErrors('Failed to get webhook configuration: ' . $e->getMessage()));
        }
    }
}
