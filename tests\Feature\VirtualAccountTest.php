<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\VirtualAccount;
use App\Models\Currency;
use App\Models\Kyc;
use App\Models\UserKyc;
use App\Services\Payout\numero\Card as NumeroCard;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;

class VirtualAccountTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $currency;
    protected $kyc;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'status' => 1,
            'email_verification' => 1
        ]);

        // Create test currency
        $this->currency = Currency::factory()->create([
            'code' => 'NGN',
            'name' => 'Nigerian Naira',
            'is_active' => 1
        ]);

        // Create KYC record
        $this->kyc = Kyc::create([
            'name' => 'Customer Information',
            'slug' => 'customer-information',
            'input_form' => json_encode([
                'Email' => [
                    'field_name' => 'Email',
                    'field_label' => 'Email Address',
                    'type' => 'email',
                    'validation' => 'required'
                ],
                'FirstName' => [
                    'field_name' => 'FirstName',
                    'field_label' => 'First Name',
                    'type' => 'text',
                    'validation' => 'required'
                ],
                'LastName' => [
                    'field_name' => 'LastName',
                    'field_label' => 'Last Name',
                    'type' => 'text',
                    'validation' => 'required'
                ],
                'MobileNumber' => [
                    'field_name' => 'MobileNumber',
                    'field_label' => 'Mobile Number',
                    'type' => 'text',
                    'validation' => 'required'
                ],
                'BankVerificationNumber' => [
                    'field_name' => 'BankVerificationNumber',
                    'field_label' => 'Bank Verification Number (BVN)',
                    'type' => 'text',
                    'validation' => 'required'
                ]
            ]),
            'status' => 1
        ]);

        // Create user KYC submission
        UserKyc::create([
            'user_id' => $this->user->id,
            'kyc_id' => $this->kyc->id,
            'kyc_info' => (object) [
                'Email' => (object) ['field_value' => $this->user->email],
                'FirstName' => (object) ['field_value' => 'John'],
                'LastName' => (object) ['field_value' => 'Doe'],
                'MobileNumber' => (object) ['field_value' => '+*************'],
                'BankVerificationNumber' => (object) ['field_value' => '***********']
            ],
            'status' => 1
        ]);
    }

    /** @test */
    public function user_can_check_virtual_account_eligibility()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('user.virtual.accounts.check.eligibility'));

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'can_create' => true,
                        'has_existing_account' => false,
                        'provider' => 'numero',
                        'currency' => 'NGN',
                        'type' => 'customer'
                    ]
                ]);
    }

    /** @test */
    public function user_cannot_create_duplicate_virtual_account()
    {
        // Create existing virtual account
        VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'NGN',
            'type' => 'customer',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => true
        ]);

        $this->actingAs($this->user);

        $response = $this->get(route('user.virtual.accounts.check.eligibility'));

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'can_create' => false,
                        'has_existing_account' => true
                    ]
                ]);
    }

    /** @test */
    public function user_gets_customer_account_by_default()
    {
        $this->actingAs($this->user);

        // Check eligibility for customer account (default)
        $response = $this->get(route('user.virtual.accounts.check.eligibility'));
        $response->assertJson([
            'status' => 'success',
            'data' => [
                'can_create' => true,
                'type' => 'customer',
                'currency' => 'NGN'
            ]
        ]);

        // Create customer account
        VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'NGN',
            'type' => 'customer',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => true
        ]);

        // Should not be able to create another customer account
        $response = $this->get(route('user.virtual.accounts.check.eligibility'));
        $response->assertJson(['status' => 'success', 'data' => ['can_create' => false]]);
    }

    /** @test */
    public function virtual_account_model_relationships_work()
    {
        $virtualAccount = VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'NGN',
            'type' => 'customer',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => true
        ]);

        $this->assertInstanceOf(User::class, $virtualAccount->user);
        $this->assertEquals($this->user->id, $virtualAccount->user->id);
    }

    /** @test */
    public function virtual_account_scopes_work()
    {
        // Create active account
        $activeAccount = VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'NGN',
            'type' => 'customer',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => true
        ]);

        // Create inactive account
        VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'USD',
            'type' => 'individual',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => false
        ]);

        // Test active scope
        $activeAccounts = VirtualAccount::active()->get();
        $this->assertCount(1, $activeAccounts);
        $this->assertEquals($activeAccount->id, $activeAccounts->first()->id);

        // Test provider scope
        $numeroAccounts = VirtualAccount::byProvider('numero')->get();
        $this->assertCount(2, $numeroAccounts);

        // Test currency scope
        $ngnAccounts = VirtualAccount::byCurrency('NGN')->get();
        $this->assertCount(1, $ngnAccounts);
        $this->assertEquals($activeAccount->id, $ngnAccounts->first()->id);
    }

    /** @test */
    public function find_by_account_number_works()
    {
        $virtualAccount = VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'NGN',
            'type' => 'customer',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => true
        ]);

        $foundAccount = VirtualAccount::findByAccountNumber('**********');
        $this->assertNotNull($foundAccount);
        $this->assertEquals($virtualAccount->id, $foundAccount->id);

        // Test with provider filter
        $foundAccountWithProvider = VirtualAccount::findByAccountNumber('**********', 'numero');
        $this->assertNotNull($foundAccountWithProvider);
        $this->assertEquals($virtualAccount->id, $foundAccountWithProvider->id);

        // Test with wrong provider
        $notFoundAccount = VirtualAccount::findByAccountNumber('**********', 'other');
        $this->assertNull($notFoundAccount);
    }

    /** @test */
    public function user_has_account_method_works()
    {
        $this->assertFalse(VirtualAccount::userHasAccount($this->user->id, 'numero', 'NGN', 'customer'));

        VirtualAccount::create([
            'user_id' => $this->user->id,
            'provider' => 'numero',
            'currency' => 'NGN',
            'type' => 'customer',
            'account_number' => '**********',
            'account_name' => 'John Doe',
            'bank_name' => 'Test Bank',
            'bank_code' => '123',
            'is_active' => true
        ]);

        $this->assertTrue(VirtualAccount::userHasAccount($this->user->id, 'numero', 'NGN', 'customer'));
        $this->assertFalse(VirtualAccount::userHasAccount($this->user->id, 'numero', 'NGN', 'business'));
        $this->assertFalse(VirtualAccount::userHasAccount($this->user->id, 'numero', 'USD', 'customer'));
    }

    /** @test */
    public function kyc_data_extraction_works()
    {
        $kycData = NumeroCard::getKycDataForUser($this->user);

        $this->assertNotNull($kycData);
        $this->assertEquals($this->user->email, $kycData['Email']);
        $this->assertEquals('John', $kycData['FirstName']);
        $this->assertEquals('Doe', $kycData['LastName']);
        $this->assertEquals('+*************', $kycData['MobileNumber']);
        $this->assertEquals('***********', $kycData['BankVerificationNumber']);
    }

    /** @test */
    public function kyc_data_extraction_returns_null_for_incomplete_kyc()
    {
        // Create user without KYC
        $userWithoutKyc = User::factory()->create();

        $kycData = NumeroCard::getKycDataForUser($userWithoutKyc);
        $this->assertNull($kycData);
    }

    /** @test */
    public function admin_can_view_virtual_accounts_index()
    {
        $admin = User::factory()->create(['type' => 'admin']);

        $this->actingAs($admin, 'admin');

        $response = $this->get(route('admin.virtual.accounts.index'));
        $response->assertStatus(200);
        $response->assertViewIs('admin.virtual_account.index');
    }

    /** @test */
    public function merchant_can_check_virtual_account_eligibility()
    {
        $merchant = User::factory()->create(['type' => 'merchant']);

        $this->actingAs($merchant);

        $response = $this->get(route('merchant.virtual.accounts.check.eligibility', [
            'type' => 'business'
        ]));

        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'data' => [
                        'can_create' => true,
                        'has_existing_account' => false,
                        'provider' => 'numero',
                        'currency' => 'NGN',
                        'type' => 'business'
                    ]
                ]);
    }
}
