<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OperationalCost extends Model
{
    use HasFactory;

    protected $fillable = [
        'cost_name',
        'description',
        'amount',
        'currency',
        'category',
        'cost_date',
        'recorded_by',
        'notes',
        'attachments'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'cost_date' => 'date',
        'attachments' => 'array',
    ];

    // Relationships
    public function recordedBy()
    {
        return $this->belongsTo(Admin::class, 'recorded_by');
    }

    // Scopes
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('cost_date', [$startDate, $endDate]);
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('cost_date', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('cost_date', now()->month)
                    ->whereYear('cost_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('cost_date', now()->year);
    }

    // Accessors
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    public function getFormattedCostDateAttribute()
    {
        return $this->cost_date->format('M d, Y');
    }

    // Business Logic Methods
    public static function getTotalCosts($startDate = null, $endDate = null, $category = null)
    {
        $query = static::query();

        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        if ($category) {
            $query->byCategory($category);
        }

        return $query->sum('amount');
    }

    public static function getCostsByCategory($startDate = null, $endDate = null)
    {
        $query = static::query();

        if ($startDate && $endDate) {
            $query->byDateRange($startDate, $endDate);
        }

        return $query->selectRaw('category, SUM(amount) as total_amount, COUNT(*) as cost_count')
                    ->groupBy('category')
                    ->orderBy('total_amount', 'desc')
                    ->get();
    }

    public static function getWeeklySummary()
    {
        return [
            'total_costs' => static::thisWeek()->sum('amount'),
            'cost_count' => static::thisWeek()->count(),
            'categories' => static::thisWeek()->distinct('category')->pluck('category')->count(),
            'by_category' => static::thisWeek()->selectRaw('category, SUM(amount) as total')
                                  ->groupBy('category')
                                  ->pluck('total', 'category'),
        ];
    }

    public static function getMonthlySummary()
    {
        return [
            'total_costs' => static::thisMonth()->sum('amount'),
            'cost_count' => static::thisMonth()->count(),
            'categories' => static::thisMonth()->distinct('category')->pluck('category')->count(),
            'by_category' => static::thisMonth()->selectRaw('category, SUM(amount) as total')
                                   ->groupBy('category')
                                   ->pluck('total', 'category'),
        ];
    }

    public static function getAvailableCategories()
    {
        return static::distinct('category')->whereNotNull('category')->pluck('category')->sort();
    }
}
