<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\VirtualAccount;
use App\Models\Currency;
use App\Services\Payout\numero\Card as NumeroCard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class VirtualAccountController extends Controller
{
    /**
     * Check if user can create virtual account (defaults to customer type and NGN currency)
     */
    public function checkEligibility(Request $request)
    {
        try {
            $user = Auth::user();
            $currency = 'NGN'; // Default to NGN for users
            $type = 'customer'; // Default to customer for users
            $provider = 'numero';

            $hasAccount = VirtualAccount::userHasAccount($user->id, $provider, $currency, $type);

            return response()->json([
                'status' => 'success',
                'data' => [
                    'can_create' => !$hasAccount,
                    'has_existing_account' => $hasAccount,
                    'provider' => $provider,
                    'currency' => $currency,
                    'type' => $type
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create a new virtual account (defaults to customer type and NGN currency)
     */
    public function create(Request $request)
    {
        try {
            $user = Auth::user();
            $currency = 'NGN'; // Default to NGN for users
            $type = 'customer'; // Default to customer for users
            $provider = 'numero';

            // Check if user already has a virtual account for this provider, currency and type
            if (VirtualAccount::userHasAccount($user->id, $provider, $currency, $type)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'You already have a customer virtual account'
                ]);
            }

            // Create virtual account using the specified provider
            switch ($provider) {
                case 'numero':
                    $result = NumeroCard::createVirtualAccount($user, $currency, null, $type);
                    break;
                default:
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unsupported provider'
                    ]);
            }

            if ($result['status'] !== 'success') {
                return response()->json([
                    'status' => 'error',
                    'message' => $result['data']
                ]);
            }

            // Save virtual account to database
            $providerData = $result['data'];
            $virtualAccount = VirtualAccount::create([
                'user_id' => $user->id,
                'provider' => $provider,
                'currency' => $currency,
                'type' => $type,
                'account_number' => $providerData['account_number'] ?? $providerData['accountNumber'],
                'account_name' => $providerData['account_name'] ?? $providerData['accountName'],
                'bank_name' => $providerData['bank_name'] ?? $providerData['bankName'],
                //'bank_code' => $providerData['bank_code'] ?? $providerData['bankCode'],
                'provider_data' => $providerData,
                'kyc_data_used' => NumeroCard::getKycDataForUser($user),
                'provider_reference' => $providerData['reference'] ?? $providerData['id'] ?? null,
                'is_active' => true,
                'created_at_provider' => $providerData['created_at'] ?? now()
            ]);

            $virtualAccount->load('currency');

            return response()->json([
                'status' => 'success',
                'message' => 'Virtual account created successfully',
                'data' => [
                    'id' => $virtualAccount->id,
                    'provider' => ucfirst($virtualAccount->provider),
                    'currency' => $virtualAccount->currency,
                    'type' => ucfirst($virtualAccount->type),
                    'account_number' => $virtualAccount->account_number,
                    'account_name' => $virtualAccount->account_name,
                    'bank_name' => $virtualAccount->bank_name,
                    'bank_code' => $virtualAccount->bank_code,
                    'created_at' => $virtualAccount->created_at,
                    'formatted_details' => $virtualAccount->formatted_details
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Virtual account creation failed: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Virtual account creation failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user's virtual accounts
     */
    public function index()
    {
        try {
            $user = Auth::user();

            $virtualAccounts = VirtualAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->with('currency')
                ->get();

            $data['virtualAccounts'] = $virtualAccounts;
            $data['basic'] = basicControl();

            return view('user.virtual_account.index', $data);
        } catch (\Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Get virtual account details by ID
     */
    public function show($id)
    {
        try {
            $user = Auth::user();

            $virtualAccount = VirtualAccount::where('id', $id)
                ->where('user_id', $user->id)
                ->where('is_active', true)
                ->with('currency')
                ->first();

            if (!$virtualAccount) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Virtual account not found'
                ]);
            }

            return response()->json([
                'status' => 'success',
                'data' => [
                    'id' => $virtualAccount->id,
                    'provider' => ucfirst($virtualAccount->provider),
                    'currency' => $virtualAccount->currency,
                    'account_number' => $virtualAccount->account_number,
                    'account_name' => $virtualAccount->account_name,
                    'bank_name' => $virtualAccount->bank_name,
                    'bank_code' => $virtualAccount->bank_code,
                    'created_at' => $virtualAccount->created_at,
                    'formatted_details' => $virtualAccount->formatted_details
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
}
