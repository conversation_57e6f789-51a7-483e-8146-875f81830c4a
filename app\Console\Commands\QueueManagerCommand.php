<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

class QueueManagerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:manage {action : Action to perform (status|clear|restart|stats)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage Laravel queue system - check status, clear jobs, restart workers, and view statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->showQueueStatus();
                break;
            case 'clear':
                $this->clearQueue();
                break;
            case 'restart':
                $this->restartQueue();
                break;
            case 'stats':
                $this->showQueueStats();
                break;
            default:
                $this->error("Invalid action. Available actions: status, clear, restart, stats");
                return 1;
        }

        return 0;
    }

    private function showQueueStatus()
    {
        $this->info('Queue System Status');
        $this->line('==================');

        // Check queue connection
        $connection = config('queue.default');
        $this->line("Queue Connection: {$connection}");

        // Count pending jobs
        $pendingJobs = DB::table('jobs')->count();
        $this->line("Pending Jobs: {$pendingJobs}");

        // Count failed jobs
        $failedJobs = DB::table('failed_jobs')->count();
        $this->line("Failed Jobs: {$failedJobs}");

        // Show recent jobs
        if ($pendingJobs > 0) {
            $this->line("\nRecent Pending Jobs:");
            $recentJobs = DB::table('jobs')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['queue', 'attempts', 'created_at']);

            $this->table(['Queue', 'Attempts', 'Created At'], $recentJobs->toArray());
        }
    }

    private function clearQueue()
    {
        if ($this->confirm('Are you sure you want to clear all pending jobs?')) {
            $deletedJobs = DB::table('jobs')->delete();
            $this->info("Cleared {$deletedJobs} pending jobs from the queue.");
        }

        if ($this->confirm('Do you want to clear failed jobs as well?')) {
            $deletedFailedJobs = DB::table('failed_jobs')->delete();
            $this->info("Cleared {$deletedFailedJobs} failed jobs.");
        }
    }

    private function restartQueue()
    {
        $this->info('Restarting queue workers...');
        Artisan::call('queue:restart');
        $this->info('Queue restart signal sent. Workers will restart after completing current jobs.');
    }

    private function showQueueStats()
    {
        $this->info('Queue Statistics');
        $this->line('================');

        // Jobs by queue
        $jobsByQueue = DB::table('jobs')
            ->select('queue', DB::raw('count(*) as count'))
            ->groupBy('queue')
            ->get();

        if ($jobsByQueue->isNotEmpty()) {
            $this->line("\nJobs by Queue:");
            $this->table(['Queue Name', 'Job Count'], $jobsByQueue->toArray());
        }

        // Failed jobs by date
        $failedJobsByDate = DB::table('failed_jobs')
            ->select(DB::raw('DATE(failed_at) as date'), DB::raw('count(*) as count'))
            ->groupBy(DB::raw('DATE(failed_at)'))
            ->orderBy('date', 'desc')
            ->limit(7)
            ->get();

        if ($failedJobsByDate->isNotEmpty()) {
            $this->line("\nFailed Jobs (Last 7 Days):");
            $this->table(['Date', 'Failed Count'], $failedJobsByDate->toArray());
        }

        // Job batches if available
        if (DB::getSchemaBuilder()->hasTable('job_batches')) {
            $batchStats = DB::table('job_batches')
                ->select(DB::raw('count(*) as total_batches'))
                ->first();

            $this->line("\nBatch Statistics:");
            $this->line("Total Batches: {$batchStats->total_batches}");
        }
    }
}
