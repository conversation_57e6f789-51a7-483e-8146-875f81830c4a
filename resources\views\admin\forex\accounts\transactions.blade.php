@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.show', $account->id) }}">
                                    {{ $account->account_name }}
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Transaction History')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Transaction History')</h1>
                    <p class="page-header-text">{{ $account->account_name }} - @lang('Complete transaction history')</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.show', $account->id) }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Account')
                        </a>
                        <a class="btn btn-success" href="{{ route('admin.forex.accounts.fund', $account->id) }}">
                            <i class="bi-plus-circle me-1"></i> @lang('Fund Account')
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Account Summary -->
        <div class="row mb-3 mb-lg-5">
            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Current Balance')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="display-6 text-dark">
                                    {{ number_format($account->balance, 2) }}
                                </span>
                                <span class="text-body fs-6 ms-1">{{ $account->currency_code }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Available Balance')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="display-6 text-success">
                                    {{ number_format($account->available_balance, 2) }}
                                </span>
                                <span class="text-body fs-6 ms-1">{{ $account->currency_code }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Transactions')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="display-6 text-dark">
                                    {{ number_format($summary['total_transactions']) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Net Amount')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="display-6 text-info">
                                    {{ number_format($summary['total_credits'] - $summary['total_debits'], 2) }}
                                </span>
                                <span class="text-body fs-6 ms-1">{{ $account->currency_code }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Account Summary -->



        <!-- Transaction History -->
        <div class="card">
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <div class="input-group input-group-merge input-group-flush">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input id="datatableSearch" type="search" class="form-control" placeholder="@lang('Search transactions')" aria-label="@lang('Search transactions')">
                    </div>
                </div>

                <div class="d-grid d-sm-flex gap-2">
                    <div class="dropdown">
                        <button type="button" class="btn btn-white btn-sm dropdown-toggle w-100" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi-filter me-1"></i> @lang('Filter')
                        </button>
                        <div class="dropdown-menu dropdown-menu-sm-end dropdown-card card-dropdown-filter-centered" aria-labelledby="filterDropdown">
                            <div class="card card-sm">
                                <div class="card-body card-body-centered">
                                    <form>
                                        <div class="row">
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('Date Range')</span>
                                                <input type="text" class="form-control" id="filter_date_range" autocomplete="off" placeholder="@lang('Select date range')">
                                            </div>
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('Transaction Type')</span>
                                                <select class="form-select" id="filter_transaction_type">
                                                    <option value="">@lang('All Types')</option>
                                                    @foreach($transactionTypes as $key => $value)
                                                        <option value="{{ $key }}">{{ $value }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row gx-2 mt-4">
                                            <div class="col">
                                                <button type="button" id="clear_filter" class="btn btn-white w-100">@lang('Clear Filters')</button>
                                            </div>
                                            <div class="col">
                                                <button type="button" class="btn btn-primary w-100" id="filter_button">
                                                    <i class="bi-search"></i> @lang('Apply')
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive datatable-custom">
                <table id="datatable"
                       class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                       "columnDefs": [{
                          "targets": [0, 8],
                          "orderable": false
                        }],
                       "order": [],
                       "info": {
                         "totalQty": "#datatableWithPaginationInfoTotalQty"
                       },
                       "search": "#datatableSearch",
                       "entries": "#datatableEntries",
                       "pageLength": 15,
                       "isResponsive": false,
                       "isShowPaging": false,
                       "pagination": "datatablePagination"
                     }'>
                    <thead class="thead-light">
                    <tr>
                        <th>@lang('Date')</th>
                        <th>@lang('Reference')</th>
                        <th>@lang('Type')</th>
                        <th>@lang('Amount')</th>
                        <th>@lang('Balance After')</th>
                        <th>@lang('Description')</th>
                        <th>@lang('Booking')</th>
                        <th>@lang('Created By')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing:')</span>
                            <!-- Select -->
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto"
                                        autocomplete="off"
                                        data-hs-tom-select-options='{
                                            "searchInDropdown": false,
                                            "hideSearch": true
                                          }'>
                                    <option value="10">10</option>
                                    <option value="25" selected>25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <!-- End Select -->
                            <span class="text-secondary me-2">@lang('of')</span>
                            <!-- Dynamic Data -->
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <!-- End Col -->

                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <!-- Pagination -->
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Transaction History -->
    </div>
@endsection

<x-assets :datatable="true" :counter="true" :flatpickr="true"/>

@push('script')
    <script>
        $(document).on('ready', function () {
            initDataTable("{{ route('admin.forex.accounts.transactions.search', $account->id) }}",
                {!! json_encode(['date', 'reference', 'type', 'amount', 'balance_after', 'description', 'booking', 'created_by', 'action']) !!}
            );

            document.getElementById("filter_button").addEventListener("click", function () {
                applyFilter("{{ route('admin.forex.accounts.transactions.search', $account->id) }}", {
                    filterTransactionType: $('#filter_transaction_type').val(),
                    filterDate: $('#filter_date_range').val()
                });
            });

            document.getElementById("clear_filter").addEventListener("click", function () {
                $('#filter_transaction_type').val('').trigger('change');
                $('#filter_date_range').val('');
                applyFilter("{{ route('admin.forex.accounts.transactions.search', $account->id) }}", {});
            });

            // Initialize date range picker
            $('#filter_date_range').flatpickr({
                mode: "range",
                dateFormat: "Y-m-d",
                placeholder: "@lang('Select date range')"
            });
        });
    </script>
@endpush
