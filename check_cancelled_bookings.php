<?php

require 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexTransaction;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Checking cancelled bookings...\n";

$cancelledBookings = ForexBooking::where('status', 'cancelled')->latest()->take(3)->get();
echo "Found " . $cancelledBookings->count() . " cancelled bookings\n\n";

foreach($cancelledBookings as $booking) {
    echo "Booking: {$booking->booking_reference} (ID: {$booking->id})\n";
    echo "Transaction Type: {$booking->transaction_type}\n";
    echo "Amount: {$booking->amount} {$booking->currency}\n";
    
    $pendingTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_type', 'pending')
        ->get();
    echo "Pending transactions: " . $pendingTransactions->count() . "\n";
    
    $releaseTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_type', 'pending_release')
        ->get();
    echo "Release transactions: " . $releaseTransactions->count() . "\n";
    
    $allTransactions = ForexTransaction::where('forex_booking_id', $booking->id)->get();
    echo "All transactions: " . $allTransactions->count() . "\n";
    
    foreach($allTransactions as $transaction) {
        echo "  - Type: {$transaction->transaction_type}, Amount: {$transaction->amount}, Account: {$transaction->forex_account_id}\n";
    }
    
    echo "---\n";
}
?>
