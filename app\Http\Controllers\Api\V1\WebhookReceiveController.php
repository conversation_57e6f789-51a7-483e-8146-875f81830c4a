<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\WebhookSecurityService;
use App\Traits\ApiValidation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookReceiveController extends Controller
{
    use ApiValidation;

    /**
     * Example webhook endpoint that demonstrates signature verification
     * This endpoint can be used by external services to send webhooks to your system
     */
    public function receiveWebhook(Request $request)
    {
        try {
            // Get the secret key for verification (this would typically come from your configuration)
            $secretKey = config('app.webhook_secret') ?: 'your_webhook_secret_key';
            
            // Validate the incoming webhook signature
            $isValid = WebhookSecurityService::validateIncomingWebhook($request, $secretKey);
            
            if (!$isValid) {
                Log::warning('WebhookReceiveController: Invalid webhook signature received', [
                    'url' => $request->url(),
                    'headers' => $request->headers->all(),
                    'ip' => $request->ip()
                ]);
                
                return response()->json([
                    'error' => 'Webhook signature verification failed',
                    'message' => 'Invalid or missing signature'
                ], 401);
            }

            // Parse the webhook payload
            $payload = json_decode($request->getContent(), true);
            
            if (!$payload) {
                return response()->json([
                    'error' => 'Invalid JSON payload'
                ], 400);
            }

            // Process the webhook based on event type
            $eventType = $payload['event'] ?? 'unknown';
            
            Log::info('WebhookReceiveController: Valid webhook received', [
                'event' => $eventType,
                'timestamp' => $payload['timestamp'] ?? null,
                'data_keys' => array_keys($payload['data'] ?? [])
            ]);

            // Handle different webhook events
            switch ($eventType) {
                case 'TRANSFER_NOTIFICATION':
                    $this->handleTransferNotification($payload);
                    break;
                    
                case 'FUNDING_NOTIFICATION':
                    $this->handleFundingNotification($payload);
                    break;
                    
                case 'TEST_NOTIFICATION':
                    $this->handleTestNotification($payload);
                    break;
                    
                default:
                    Log::warning('WebhookReceiveController: Unknown webhook event type', [
                        'event' => $eventType,
                        'payload' => $payload
                    ]);
                    break;
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Webhook processed successfully',
                'event' => $eventType
            ]);

        } catch (\Exception $e) {
            Log::error('WebhookReceiveController: Exception processing webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'error' => 'Internal server error',
                'message' => 'Failed to process webhook'
            ], 500);
        }
    }

    /**
     * Handle transfer notification webhooks
     */
    private function handleTransferNotification($payload)
    {
        $data = $payload['data'] ?? [];
        
        Log::info('WebhookReceiveController: Processing transfer notification', [
            'transaction_id' => $data['transaction_id'] ?? null,
            'status' => $data['status'] ?? null,
            'amount' => $data['amount'] ?? null
        ]);
        
        // Add your transfer notification processing logic here
        // For example:
        // - Update transaction status in database
        // - Send notifications to users
        // - Trigger business logic based on status
    }

    /**
     * Handle funding notification webhooks
     */
    private function handleFundingNotification($payload)
    {
        $data = $payload['data'] ?? [];
        
        Log::info('WebhookReceiveController: Processing funding notification', [
            'transaction_id' => $data['transaction_id'] ?? null,
            'amount' => $data['amount'] ?? null,
            'user_id' => $data['user_id'] ?? null
        ]);
        
        // Add your funding notification processing logic here
        // For example:
        // - Credit user wallet
        // - Send confirmation notifications
        // - Update account balances
    }

    /**
     * Handle test notification webhooks
     */
    private function handleTestNotification($payload)
    {
        $data = $payload['data'] ?? [];
        
        Log::info('WebhookReceiveController: Processing test notification', [
            'message' => $data['message'] ?? null,
            'user_id' => $data['user_id'] ?? null
        ]);
        
        // Test notifications don't require any special processing
        // They're just used to verify webhook configuration
    }

    /**
     * Get webhook configuration and security information
     */
    public function getWebhookInfo(Request $request)
    {
        return response()->json([
            'webhook_security' => [
                'signature_header' => 'X-Webhook-Signature',
                'signature_format' => 'sha256=<hex_digest>',
                'algorithm' => 'HMAC-SHA256',
                'verification_required' => true
            ],
            'supported_events' => [
                'TRANSFER_NOTIFICATION' => 'Sent when payout transaction status changes',
                'FUNDING_NOTIFICATION' => 'Sent when deposit/funding occurs',
                'TEST_NOTIFICATION' => 'Sent when testing webhook configuration'
            ],
            'response_requirements' => [
                'status_codes' => '200-299 for success',
                'timeout' => '30 seconds',
                'content_type' => 'application/json',
                'https_required' => true
            ]
        ]);
    }
}
