<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendMail extends Mailable
{
	use Queueable, SerializesModels;

	public $from_email;
	public $site_title;
	public $subject;
	public $message;

	/**
	 * Create a new message instance.
	 *
	 * @return void
	 */
	public function __construct($email_from, $subject, $message, $fromName = null)
	{
		$basic = basicControl();
		$this->from_email = $email_from;
		$this->site_title = ($fromName) ?: $basic->site_title;
		$this->subject = $subject;
		$this->message = $this->formatEmailMessage($message);
	}

	/**
	 * Format email message by converting line breaks to HTML
	 * This handles the \r\n line breaks from notification templates
	 *
	 * @param string $message
	 * @return string
	 */
	private function formatEmailMessage($message)
	{
		// Convert various line break formats to HTML <br> tags
		// Handle \r\n (Windows), \n (Unix), and \r (Mac) line endings
		$message = str_replace(["\r\n", "\r", "\n"], "<br>", $message);

		// Clean up multiple consecutive <br> tags (more than 2)
		$message = preg_replace('/(<br\s*\/?>){3,}/i', '<br><br>', $message);

		return $message;
	}

	/**
	 * Build the message.
	 *
	 * @return $this
	 */
	public function build()
	{
		return $this->from($this->from_email, $this->site_title)->view('layouts.mail')->with('msg', $this->message);
	}
}
