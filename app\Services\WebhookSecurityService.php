<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class WebhookSecurityService
{
    /**
     * Generate HMAC-SHA256 signature for webhook payload
     *
     * @param array|string $payload The webhook payload
     * @param string $secretKey The secret key to use for signing
     * @return string The generated signature in format 'sha256=<hex_digest>'
     */
    public static function generateSignature($payload, $secretKey)
    {
        try {
            // Convert payload to JSON string if it's an array
            $payloadString = is_array($payload) ? json_encode($payload) : $payload;

            // Generate HMAC-SHA256 hash
            $hash = hash_hmac('sha256', $payloadString, $secretKey);

            Log::info('WebhookSecurityService: Generated signature', [
                'payload' => $payloadString,
                'signature' => $hash,
                'secret' => $secretKey
            ]);

            return $hash;

            // Return in standard format
            //return 'sha256=' . $hash;

        } catch (\Exception $e) {
            Log::error('WebhookSecurityService: Failed to generate signature', [
                'error' => $e->getMessage(),
                'payload_type' => gettype($payload)
            ]);
            return null;
        }
    }

    /**
     * Verify HMAC-SHA256 signature for webhook payload
     *
     * @param array|string $payload The webhook payload
     * @param string $signature The signature to verify (format: 'sha256=<hex_digest>')
     * @param string $secretKey The secret key used for signing
     * @return bool True if signature is valid, false otherwise
     */
    public static function verifySignature($payload, $signature, $secretKey)
    {
        try {
            // Generate expected signature
            $expectedSignature = self::generateSignature($payload, $secretKey);

            if (!$expectedSignature) {
                Log::warning('WebhookSecurityService: Failed to generate expected signature for verification');
                return false;
            }

            // Use hash_equals for timing-safe comparison
            $isValid = hash_equals($expectedSignature, $signature);

            Log::info('WebhookSecurityService: Signature verification', [
                'signature_provided' => $signature,
                'signature_expected' => $expectedSignature,
                'is_valid' => $isValid
            ]);

            return $isValid;

        } catch (\Exception $e) {
            Log::error('WebhookSecurityService: Failed to verify signature', [
                'error' => $e->getMessage(),
                'signature' => $signature
            ]);
            return false;
        }
    }

    /**
     * Extract signature from webhook headers
     * Supports multiple header formats commonly used for webhook signatures
     *
     * @param \Illuminate\Http\Request $request The incoming request
     * @return string|null The extracted signature or null if not found
     */
    public static function extractSignatureFromHeaders($request)
    {
        // Check common webhook signature headers
        $signatureHeaders = [
            'X-Webhook-Signature',
            'X-Hub-Signature-256',
            'X-Signature-256',
            'Webhook-Signature'
        ];

        foreach ($signatureHeaders as $header) {
            $signature = $request->header($header);
            if ($signature) {
                Log::info('WebhookSecurityService: Found signature in header', [
                    'header' => $header,
                    'signature' => $signature
                ]);
                return $signature;
            }
        }

        Log::warning('WebhookSecurityService: No signature found in webhook headers', [
            'headers' => $request->headers->all()
        ]);

        return null;
    }

    /**
     * Get the appropriate API key for signature verification based on user type
     *
     * @param \App\Models\User $user The user object
     * @return string|null The secret key to use for verification
     */
    public static function getApiKeyForUser($user)
    {
        if (!$user || !$user->secret_key) {
            Log::warning('WebhookSecurityService: User or secret key not found', [
                'user_id' => $user?->id,
                'has_secret_key' => $user?->secret_key ? true : false
            ]);
            return null;
        }

        return $user->secret_key;
    }

    /**
     * Validate webhook signature for incoming webhook requests
     *
     * @param \Illuminate\Http\Request $request The incoming webhook request
     * @param string $secretKey The secret key to use for verification
     * @return bool True if signature is valid, false otherwise
     */
    public static function validateIncomingWebhook($request, $secretKey)
    {
        try {
            // Extract signature from headers
            $signature = self::extractSignatureFromHeaders($request);

            if (!$signature) {
                Log::warning('WebhookSecurityService: No signature provided in webhook request');
                return false;
            }

            // Get raw request body for verification
            $payload = $request->getContent();

            // Verify signature
            return self::verifySignature($payload, $signature, $secretKey);

        } catch (\Exception $e) {
            Log::error('WebhookSecurityService: Failed to validate incoming webhook', [
                'error' => $e->getMessage(),
                'url' => $request->url()
            ]);
            return false;
        }
    }
}
