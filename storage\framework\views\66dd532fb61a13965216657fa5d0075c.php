<!-- ========== HEADER ========== -->
<header id="header"
        class="navbar navbar-expand-lg navbar-fixed navbar-height navbar-container navbar-bordered bg-white">
    <div class="navbar-nav-wrap">
        <a class="navbar-brand" href="<?php echo e(route('admin.dashboard')); ?>" aria-label="<?php echo e($basicControl->site_title); ?>">
            <img class="navbar-brand-logo"
                 src="<?php echo e(getFile($basicControl->admin_logo_driver, $basicControl->admin_logo, true)); ?>"
                 alt="<?php echo e($basicControl->site_title); ?> Logo"
                 data-hs-theme-appearance="default">
            <img class="navbar-brand-logo"
                 src="<?php echo e(getFile($basicControl->admin_logo_driver, $basicControl->admin_logo, true)); ?>"
                 alt="<?php echo e($basicControl->site_title); ?> Logo"
                 data-hs-theme-appearance="dark">
            <img class="navbar-brand-logo-mini"
                 src="<?php echo e(getFile($basicControl->admin_logo_driver, $basicControl->admin_logo, true)); ?>"
                 alt="<?php echo e($basicControl->site_title); ?> Logo"
                 data-hs-theme-appearance="default">
            <img class="navbar-brand-logo-mini"
                 src="<?php echo e(getFile($basicControl->admin_logo_driver, $basicControl->admin_logo, true)); ?>"
                 alt="Logo"
                 data-hs-theme-appearance="dark">
        </a>

        


        <!-- Notification -->
        <div class="navbar-nav-wrap-content-end" id="pushNotificationArea">
            <ul class="navbar-nav">

                

                <?php if(basicControl()->in_app_notification): ?>
                    <li class="nav-item d- d-sm-inline-block">
                        <div class="dropdown">
                            <button type="button" class="btn btn-ghost-secondary btn-icon rounded-circle"
                                    id="navbarNotificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false"
                                    data-bs-auto-close="outside">
                                <i class="bi-bell"></i>
                                <span class="btn-status btn-sm-status btn-status-danger" v-if="items.length > 0"
                                      v-cloak></span>
                            </button>
                            <div
                                class="dropdown-menu dropdown-menu-end dropdown-card navbar-dropdown-menu navbar-dropdown-menu-borderless navbarNotificationsDropdown data-bs-dropdown-animation"
                                aria-labelledby="navbarNotificationsDropdown">
                                <div class="card ">
                                    <div class="card-header card-header-content-between">
                                        <h4 class="card-title mb-0"><?php echo app('translator')->get('Notifications'); ?></h4>
                                    </div>
                                    <div class="card-body-height">
                                        <div id="notificationTabContent">
                                            <ul class="list-group list-group-flush navbar-card-list-group">
                                                <li class="list-group-item form-check-select"
                                                    v-for="(item, index) in items">
                                                    <div class="row">
                                                        <div class="col-auto">
                                                            <div class="d-flex align-items-center">
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="checkbox"
                                                                           id="notificationCheck6">
                                                                    <label class="form-check-label"
                                                                           for="notificationCheck6"></label>
                                                                    <span class="form-check-stretched-bg"></span>
                                                                </div>
                                                                <div class="avatar avatar-sm avatar-circle">
                                                                    <img class="avatar-img"
                                                                         :src="item.description.image"
                                                                         alt="Image Description">
                                                                </div>

                                                            </div>
                                                        </div>
                                                        <div class="col ms-n2">
                                                            <h5 class="mb-1">{{ item.description.name }}</h5>
                                                            <p class="text-body fs-5">{{ item.description.text }}</p>
                                                            <small class="col-auto text-muted text-cap" v-cloak>{{
                                                                item.formatted_date }}</small>
                                                        </div>
                                                    </div>
                                                    <a class="stretched-link" :href="item.description.link"></a>
                                                </li>
                                            </ul>
                                            <div class="text-center p-4" v-if="items.length < 1">
                                                <img class="dataTables-image mb-3"
                                                     src="<?php echo e(asset('assets/admin/img/oc-error.svg')); ?>"
                                                     alt="Image Description" data-hs-theme-appearance="default">
                                                <img class="dataTables-image mb-3"
                                                     src="<?php echo e(asset('assets/admin/img/oc-error-light.svg')); ?>"
                                                     alt="Image Description" data-hs-theme-appearance="dark">
                                                <p class="mb-0"><?php echo app('translator')->get("No Notifications Found"); ?></p>
                                            </div>
                                        </div>

                                    </div>
                                    <a class="card-footer text-center" href="javascript:void(0)" v-if="items.length > 0"
                                       @click.prevent="readAll">
                                        <?php echo app('translator')->get("Clear all notifications"); ?> <i class="bi-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>
                <?php endif; ?>


                <li class="nav-item">
                    <div class="dropdown">
                        <a class="navbar-dropdown-account-wrapper" href="javascript:void(0)" id="accountNavbarDropdown"
                           data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside"
                           data-bs-dropdown-animation>
                            <div class="avatar avatar-sm avatar-circle">
                                <img class="avatar-img"
                                     src="<?php echo e(getFile(Auth::guard('admin')->user()->image_driver, Auth::guard('admin')->user()->image)); ?>"
                                     alt="Image Description">
                                <span class="avatar-status avatar-sm-status avatar-status-success"></span>
                            </div>
                        </a>

                        <div
                            class="dropdown-menu dropdown-menu-end navbar-dropdown-menu navbar-dropdown-menu-borderless navbar-dropdown-account admin_dropdown_account"
                            aria-labelledby="accountNavbarDropdown">
                            <div class="dropdown-item-text">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm avatar-circle">
                                        <img class="avatar-img"
                                             src="<?php echo e(getFile(Auth::guard('admin')->user()->image_driver, Auth::guard('admin')->user()->image)); ?>"
                                             alt="Image Description">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="mb-0"><?php echo e(auth()->user()->name); ?></h5>
                                        <p class="card-text text-body"><?php echo e(auth()->user()->email); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="dropdown-divider"></div>

                            <a class="dropdown-item"
                               href="<?php echo e(route("admin.profile")); ?>"><?php echo app('translator')->get("Profile &amp; account"); ?></a>

                            <div class="dropdown-divider"></div>

                            <a class="dropdown-item" href="<?php echo e(route('admin.logout')); ?>"
                               onclick="event.preventDefault();document.getElementById('logout-form').submit();">
                                <?php echo app('translator')->get("Sign out"); ?>
                            </a>
                            <form id="logout-form" action="<?php echo e(route('admin.logout')); ?>" method="POST" class="d-none">
                                <?php echo csrf_field(); ?>
                            </form>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</header>
<!-- ========== END HEADER ========== -->


<?php $__env->startPush('script'); ?>
    <script>
        'use strict'
        let pushNotificationArea = new Vue({
            el: "#pushNotificationArea",
            data: {
                items: [],
            },
            mounted() {
                this.getNotifications();
                this.pushNewItem();
            },
            methods: {
                getNotifications() {
                    let app = this;
                    axios.get("<?php echo e(route('admin.push.notification.show')); ?>")
                        .then(function (res) {
                            app.items = res.data;
                        })
                },
                readAt(id, link) {
                    let app = this;
                    let url = "<?php echo e(route('admin.push.notification.readAt', 0)); ?>";
                    url = url.replace(/.$/, id);
                    axios.get(url)
                        .then(function (res) {
                            if (res.status) {
                                app.getNotifications();
                                if (link !== '#') {
                                    window.location.href = link
                                }
                            }
                        })
                },
                readAll() {
                    let app = this;
                    let url = "<?php echo e(route('admin.push.notification.readAll')); ?>";
                    axios.get(url)
                        .then(function (res) {
                            if (res.status) {
                                app.items = [];
                            }
                        })
                },
                pushNewItem() {
                    let app = this;
                    Pusher.logToConsole = false;
                    let pusher = new Pusher("<?php echo e(env('PUSHER_APP_KEY')); ?>", {
                        encrypted: true,
                        cluster: "<?php echo e(env('PUSHER_APP_CLUSTER')); ?>"
                    });
                    let channel = pusher.subscribe('admin-notification.' + "<?php echo e(Auth::id()); ?>");
                    channel.bind('App\\Events\\AdminNotification', function (data) {
                        app.items.unshift(data.message);
                    });
                    channel.bind('App\\Events\\UpdateAdminNotification', function (data) {
                        app.getNotifications();
                    });
                }
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/admin/layouts/header.blade.php ENDPATH**/ ?>