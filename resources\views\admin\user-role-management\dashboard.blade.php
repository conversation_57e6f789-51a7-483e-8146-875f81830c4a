@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">User & Role Management Dashboard</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">User & Role Management</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">Total Users</p>
                            <h4 class="mb-0">{{ number_format($stats['total_users']) }}</h4>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-users font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">
                            {{ number_format($stats['users_with_advanced_roles']) }}
                        </span>
                        with advanced roles
                    </p>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">Total Admins</p>
                            <h4 class="mb-0">{{ number_format($stats['total_admins']) }}</h4>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-user-shield font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <span class="text-success me-1">
                            {{ number_format($stats['admins_with_advanced_roles']) }}
                        </span>
                        with advanced roles
                    </p>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">Total Roles</p>
                            <h4 class="mb-0">{{ number_format($stats['total_roles']) }}</h4>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-user-tag font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <a href="{{ route('admin.roles.index') }}" class="text-decoration-none">
                            Manage Roles <i class="fas fa-arrow-right"></i>
                        </a>
                    </p>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-1 overflow-hidden">
                            <p class="text-truncate font-size-14 mb-2">Active Assignments</p>
                            <h4 class="mb-0">{{ number_format($stats['active_assignments']) }}</h4>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-link font-size-24"></i>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <a href="{{ route('admin.user-roles.index') }}" class="text-decoration-none">
                            View All <i class="fas fa-arrow-right"></i>
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @canAdvanced('users.create')
                            <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Create New User
                            </a>
                        @endcanAdvanced

                        @canAdvanced('advanced_roles.create')
                            <a href="{{ route('admin.roles.create') }}" class="btn btn-success">
                                <i class="fas fa-plus-circle me-2"></i>Create New Role
                            </a>
                        @endcanAdvanced

                        @canAdvanced('user_roles.create')
                            <a href="{{ route('admin.user-roles.create') }}" class="btn btn-info">
                                <i class="fas fa-link me-2"></i>Assign Role to User
                            </a>
                        @endcanAdvanced

                        @canAdvanced('advanced_permissions.discover')
                            <button type="button" class="btn btn-warning" onclick="discoverPermissions()">
                                <i class="fas fa-search me-2"></i>Discover Permissions
                            </button>
                        @endcanAdvanced
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Distribution -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Role Distribution
                    </h4>
                </div>
                <div class="card-body">
                    @if($roleDistribution->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-nowrap mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Role</th>
                                        <th>Category</th>
                                        <th>Users</th>
                                        <th>Distribution</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($roleDistribution as $role)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($role->color)
                                                        <div class="avatar-xs me-2">
                                                            <div class="avatar-title rounded-circle" 
                                                                 style="background-color: {{ $role->color }};">
                                                            </div>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        <h6 class="mb-0">{{ $role->display_name }}</h6>
                                                        <small class="text-muted">{{ $role->name }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($role->category)
                                                    <span class="badge bg-secondary">
                                                        {{ ucwords(str_replace('_', ' ', $role->category)) }}
                                                    </span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $role->user_roles_count }}</span>
                                            </td>
                                            <td>
                                                @php
                                                    $percentage = $stats['active_assignments'] > 0 
                                                        ? round(($role->user_roles_count / $stats['active_assignments']) * 100, 1)
                                                        : 0;
                                                @endphp
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar" 
                                                         style="width: {{ $percentage }}%; background-color: {{ $role->color ?? '#6c757d' }};">
                                                    </div>
                                                </div>
                                                <small class="text-muted">{{ $percentage }}%</small>
                                            </td>
                                            <td>
                                                @canAdvanced('advanced_roles.read')
                                                    <a href="{{ route('admin.roles.show', $role) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcanAdvanced
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No role assignments found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Assignments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>Recent Role Assignments
                        </h4>
                        @canAdvanced('user_roles.read')
                            <a href="{{ route('admin.user-roles.index') }}" class="btn btn-sm btn-outline-primary">
                                View All Assignments
                            </a>
                        @endcanAdvanced
                    </div>
                </div>
                <div class="card-body">
                    @if($stats['recent_assignments']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-nowrap mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>User</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Assigned</th>
                                        <th>Expires</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($stats['recent_assignments'] as $assignment)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-xs me-2">
                                                        <div class="avatar-title rounded-circle bg-primary">
                                                            {{ substr($assignment->user->name, 0, 1) }}
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">{{ $assignment->user->name }}</h6>
                                                        <small class="text-muted">{{ $assignment->user->email }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($assignment->role->color)
                                                        <div class="avatar-xs me-2">
                                                            <div class="avatar-title rounded-circle" 
                                                                 style="background-color: {{ $assignment->role->color }};">
                                                            </div>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        <span class="fw-semibold">{{ $assignment->role->display_name }}</span>
                                                        @if($assignment->context)
                                                            <br><small class="text-muted">Context: {{ $assignment->context }}</small>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                {!! $assignment->getStatusBadge() !!}
                                            </td>
                                            <td>
                                                <span class="text-muted">{{ $assignment->created_at->format('M j, Y') }}</span>
                                                <br><small class="text-muted">{{ $assignment->created_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                @if($assignment->expires_at)
                                                    <span class="text-warning">{{ $assignment->expires_at->format('M j, Y') }}</span>
                                                    <br><small class="text-muted">{{ $assignment->expires_at->diffForHumans() }}</small>
                                                @else
                                                    <span class="text-muted">Never</span>
                                                @endif
                                            </td>
                                            <td>
                                                @canAdvanced('user_roles.read')
                                                    <a href="{{ route('admin.user-roles.show', $assignment) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcanAdvanced
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent assignments found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
function discoverPermissions() {
    if (confirm('This will discover and create new permissions from your controllers. Continue?')) {
        fetch('{{ route("admin.permissions.discover") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                toastr.error(data.message || 'Permission discovery failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('An error occurred during permission discovery');
        });
    }
}
</script>
@endpush

@push('style')
<style>
.avatar-xs {
    height: 2rem;
    width: 2rem;
}

.avatar-title {
    align-items: center;
    background-color: #556ee6;
    color: #fff;
    display: flex;
    font-weight: 500;
    height: 100%;
    justify-content: center;
    width: 100%;
}

.progress {
    background-color: #f6f6f6;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}
</style>
@endpush
