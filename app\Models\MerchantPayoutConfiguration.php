<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MerchantPayoutConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'merchant_id',
        'payout_method_id',
        'currency',
        'min_limit',
        'max_limit',
        'percentage_charge',
        'fixed_charge',
        'is_active'
    ];

    protected $casts = [
        'min_limit' => 'decimal:8',
        'max_limit' => 'decimal:8',
        'percentage_charge' => 'decimal:2',
        'fixed_charge' => 'decimal:8',
        'is_active' => 'boolean'
    ];

    /**
     * Get the merchant that owns this configuration
     */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'merchant_id');
    }

    /**
     * Get the payout method for this configuration
     */
    public function payoutMethod(): BelongsTo
    {
        return $this->belongsTo(PayoutMethod::class, 'payout_method_id');
    }

    /**
     * Get effective configuration by merging custom settings with defaults
     * 
     * @param string $currency The currency code to get default config for
     * @return array Effective configuration with fallback to defaults
     */
    public function getEffectiveConfig(string $currency = 'NGN'): array
    {
        $payoutMethod = $this->payoutMethod;
        
        // Get default configuration for the specified currency
        $defaultConfig = collect($payoutMethod->payout_currencies)
            ->firstWhere('name', $currency) 
            ?? collect($payoutMethod->payout_currencies)
                ->firstWhere('currency_symbol', $currency)
            ?? collect($payoutMethod->payout_currencies)->first();

        if (!$defaultConfig) {
            throw new \Exception("No default configuration found for currency: {$currency}");
        }

        return [
            'min_limit' => $this->min_limit ?? $defaultConfig['min_limit'],
            'max_limit' => $this->max_limit ?? $defaultConfig['max_limit'],
            'percentage_charge' => $this->percentage_charge ?? $defaultConfig['percentage_charge'],
            'fixed_charge' => $this->fixed_charge ?? $defaultConfig['fixed_charge'],
            'conversion_rate' => $defaultConfig['conversion_rate'],
            'currency_symbol' => $defaultConfig['currency_symbol'] ?? $defaultConfig['name'],
            'has_custom_config' => true,
            'custom_fields' => $this->getCustomFields()
        ];
    }

    /**
     * Get list of fields that have custom values (not using defaults)
     * 
     * @return array List of custom field names
     */
    public function getCustomFields(): array
    {
        $customFields = [];
        
        if (!is_null($this->min_limit)) $customFields[] = 'min_limit';
        if (!is_null($this->max_limit)) $customFields[] = 'max_limit';
        if (!is_null($this->percentage_charge)) $customFields[] = 'percentage_charge';
        if (!is_null($this->fixed_charge)) $customFields[] = 'fixed_charge';
        
        return $customFields;
    }

    /**
     * Check if this configuration has any custom settings
     * 
     * @return bool True if any field is customized
     */
    public function hasAnyCustomSettings(): bool
    {
        return !is_null($this->min_limit) 
            || !is_null($this->max_limit) 
            || !is_null($this->percentage_charge) 
            || !is_null($this->fixed_charge);
    }

    /**
     * Static method to get effective configuration for a merchant and payout method
     * 
     * @param int $merchantId
     * @param int $payoutMethodId
     * @param string $currency
     * @return array Effective configuration
     */
    public static function getEffectiveConfigFor(int $merchantId, int $payoutMethodId, string $currency = 'NGN'): array
    {
        $merchantConfig = static::where([
            'merchant_id' => $merchantId,
            'payout_method_id' => $payoutMethodId,
            'currency' => $currency,
            'is_active' => 1
        ])->with('payoutMethod')->first();

        if ($merchantConfig) {
            return $merchantConfig->getEffectiveConfig($currency);
        }

        // No custom config, return default configuration
        $payoutMethod = PayoutMethod::find($payoutMethodId);
        if (!$payoutMethod) {
            throw new \Exception("Payout method not found: {$payoutMethodId}");
        }

        $defaultConfig = collect($payoutMethod->payout_currencies)
            ->firstWhere('name', $currency) 
            ?? collect($payoutMethod->payout_currencies)
                ->firstWhere('currency_symbol', $currency)
            ?? collect($payoutMethod->payout_currencies)->first();

        if (!$defaultConfig) {
            throw new \Exception("No default configuration found for currency: {$currency}");
        }

        return array_merge($defaultConfig, [
            'has_custom_config' => false,
            'custom_fields' => []
        ]);
    }

    /**
     * Validation rules for merchant payout configuration
     * 
     * @return array Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'merchant_id' => 'required|exists:users,id',
            'payout_method_id' => 'required|exists:payout_methods,id',
            'currency' => 'required|string|max:10',
            'min_limit' => 'nullable|numeric|min:0',
            'max_limit' => 'nullable|numeric|min:0|gt:min_limit',
            'percentage_charge' => 'nullable|numeric|min:0|max:100',
            'fixed_charge' => 'nullable|numeric|min:0',
            'is_active' => 'boolean'
        ];
    }
}
