@extends(userLayout())
@section('title', trans('Webhook Settings'))

@section('content')
    <div class="content {{ containerClass() }}">
        <x-page-header menu="Webhook Settings">
            <x-slot name="push_header_button">
                <button type="submit" form="webhookForm" class="btn btn-primary">@lang('Save Webhook Settings')</button>
            </x-slot>
        </x-page-header>

        <form method="POST" action="{{ route('merchant.webhook.settings.update') }}" id="webhookForm">
            @csrf
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">@lang('Webhook Configuration')</h4>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <label class="form-label" for="webhook_url">@lang('Webhook URL')</label>
                                <input type="url" class="form-control" name="webhook_url" id="webhook_url"
                                       value="{{ old('webhook_url', $setting?->webhook_url) }}"
                                       placeholder="https://your-domain.com/webhook">
                                <div class="form-text">
                                    @lang('Enter a webhook URL to receive real-time notifications for transaction events. Leave empty to disable webhooks.')
                                </div>
                                @error('webhook_url')
                                <span class="text-danger">{{$message}}</span>
                                @enderror
                            </div>

                            <div class="alert alert-soft-info" role="alert">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi-info-circle"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-2">
                                        <h5 class="alert-heading">@lang('Webhook Information')</h5>
                                        <p class="mb-2">@lang('Webhooks allow you to receive real-time notifications when transaction events occur. Configure your endpoint to handle the following events:')</p>
                                        <ul class="mb-2">
                                            <li><strong>TRANSFER_NOTIFICATION:</strong> @lang('Sent when payout transaction status changes')</li>
                                            <li><strong>FUNDING_NOTIFICATION:</strong> @lang('Sent when deposit/funding occurs')</li>
                                            <li><strong>TEST_NOTIFICATION:</strong> @lang('Sent when testing webhook configuration')</li>
                                        </ul>
                                        <p class="mb-0">@lang('All webhooks include an HMAC-SHA256 signature in the X-Webhook-Signature header for security verification.')</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">@lang('Webhook Testing')</h4>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">@lang('Test your webhook configuration to ensure it\'s working correctly.')</p>
                            
                            <button type="button" class="btn btn-outline-primary w-100" id="testWebhookBtn">
                                <i class="bi-send"></i> @lang('Test Webhook')
                            </button>
                            
                            <div class="mt-3" id="testResult" style="display: none;">
                                <div class="alert" id="testAlert"></div>
                            </div>

                            <hr>

                            <h6>@lang('Quick Actions')</h6>
                            <div class="d-grid gap-2">
                                <a href="{{ route('merchant.api.docx') }}" class="btn btn-outline-info btn-sm">
                                    <i class="bi-book"></i> @lang('API Documentation')
                                </a>
                                <a href="{{ route('merchant.api.key') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi-key"></i> @lang('API Keys')
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">@lang('Security Requirements')</h4>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="bi-check-circle text-success me-2"></i>
                                    @lang('Use HTTPS URLs only')
                                </li>
                                <li class="mb-2">
                                    <i class="bi-check-circle text-success me-2"></i>
                                    @lang('Verify HMAC-SHA256 signatures')
                                </li>
                                <li class="mb-2">
                                    <i class="bi-check-circle text-success me-2"></i>
                                    @lang('Return HTTP 200-299 status codes')
                                </li>
                                <li class="mb-2">
                                    <i class="bi-check-circle text-success me-2"></i>
                                    @lang('Respond within 30 seconds')
                                </li>
                                <li class="mb-0">
                                    <i class="bi-check-circle text-success me-2"></i>
                                    @lang('Handle duplicate notifications')
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('script')
<script>
    'use strict';
    
    document.getElementById('testWebhookBtn').addEventListener('click', function() {
        const webhookUrl = document.getElementById('webhook_url').value;
        const testBtn = this;
        const testResult = document.getElementById('testResult');
        const testAlert = document.getElementById('testAlert');
        
        if (!webhookUrl) {
            testAlert.className = 'alert alert-warning';
            testAlert.textContent = '@lang("Please enter a webhook URL first")';
            testResult.style.display = 'block';
            return;
        }
        
        testBtn.disabled = true;
        testBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>@lang("Testing...")';
        
        fetch('/api/webhook/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer {{ auth()->user()->createToken("webhook-test")->plainTextToken ?? "" }}',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                webhook_url: webhookUrl
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                testAlert.className = 'alert alert-success';
                testAlert.textContent = data.message || '@lang("Webhook test successful!")';
            } else {
                testAlert.className = 'alert alert-danger';
                testAlert.textContent = data.message || '@lang("Webhook test failed")';
            }
            testResult.style.display = 'block';
        })
        .catch(error => {
            testAlert.className = 'alert alert-danger';
            testAlert.textContent = '@lang("Error testing webhook: ") ' + error.message;
            testResult.style.display = 'block';
        })
        .finally(() => {
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="bi-send"></i> @lang("Test Webhook")';
        });
    });
</script>
@endpush
