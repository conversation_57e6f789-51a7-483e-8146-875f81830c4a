@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">@lang('Forex Reports')</h1>
                    <p class="page-header-text">@lang('Generate comprehensive forex trading reports and analytics')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-primary" href="{{ route('admin.forex.reports.custom') }}">
                        <i class="bi-graph-up me-1"></i> @lang('Custom Report')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['total_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Bookings')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-primary text-primary">
                                    <i class="bi-journal-text"></i> @lang('Total')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Completed Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['completed_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Completed')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi-check-circle"></i> @lang('Completed')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Pending Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['pending_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Pending')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-warning text-warning">
                                    <i class="bi-clock"></i> @lang('Pending')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Accounts')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['total_accounts'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Accounts')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-info text-info">
                                    <i class="bi-bank"></i> @lang('Accounts')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Stats Cards -->

        <!-- CBN Compliance Section -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card border-success">
                    <div class="card-header bg-soft-success">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4 class="card-header-title text-success">
                                    <i class="bi-shield-check me-2"></i>@lang('CBN Compliance Reports')
                                </h4>
                                <p class="card-text mb-0">@lang('Generate regulatory-compliant reports using only CBN official rates')</p>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-success">@lang('Regulatory Compliant')</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-2">@lang('Key Features:')</h6>
                                <ul class="list-unstyled mb-3">
                                    <li><i class="bi-check-circle text-success me-2"></i>@lang('Excludes parallel market data')</li>
                                    <li><i class="bi-check-circle text-success me-2"></i>@lang('CBN official rates only')</li>
                                    <li><i class="bi-check-circle text-success me-2"></i>@lang('Approved markup percentages')</li>
                                    <li><i class="bi-check-circle text-success me-2"></i>@lang('Regulatory compliance metrics')</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2">
                                    <a href="{{ route('admin.forex.reports.cbn.focused') }}" class="btn btn-success">
                                        <i class="bi-file-earmark-check me-1"></i> @lang('Generate CBN Report')
                                    </a>
                                    <button type="button" class="btn btn-outline-success" onclick="loadCbnTemplates()" id="loadCbnTemplatesBtn">
                                        <i class="bi-bookmark me-1"></i> @lang('Load CBN Templates')
                                    </button>
                                    <!-- Fallback form for non-JS users -->
                                    <form action="{{ route('admin.forex.reports.templates.seed.cbn') }}" method="POST" style="display: none;" id="cbnTemplatesForm">
                                        @csrf
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Types -->
        <div class="row">
            <!-- Daily Report -->
            <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Daily Report')</h4>
                        <span class="badge bg-soft-primary text-primary">
                            <i class="bi-calendar-day"></i> @lang('Daily')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate detailed daily forex trading reports with transaction summaries and account balances.')</p>

                        <form action="{{ route('admin.forex.reports.daily') }}" method="POST" class="daily-report-form">
                            @csrf
                            <div class="mb-3">
                                <label for="dailyDate" class="form-label">@lang('Select Date')</label>
                                <input type="date" class="form-control" name="date" id="dailyDate"
                                       value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="dailyFormat" class="form-label">@lang('Format')</label>
                                <select class="form-select" name="format" id="dailyFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi-file-earmark-text me-1"></i> @lang('Generate Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Weekly Report -->
            <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Weekly Report')</h4>
                        <span class="badge bg-soft-success text-success">
                            <i class="bi-calendar-week"></i> @lang('Weekly')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate weekly forex trading reports with trend analysis and performance metrics.')</p>

                        <form action="{{ route('admin.forex.reports.weekly') }}" method="POST" class="weekly-report-form">
                            @csrf
                            <div class="mb-3">
                                <label for="weeklyStartDate" class="form-label">@lang('Start Date')</label>
                                <input type="date" class="form-control" name="start_date" id="weeklyStartDate"
                                       value="{{ date('Y-m-d', strtotime('monday this week')) }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="weeklyEndDate" class="form-label">@lang('End Date')</label>
                                <input type="date" class="form-control" name="end_date" id="weeklyEndDate"
                                       value="{{ date('Y-m-d', strtotime('sunday this week')) }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="weeklyFormat" class="form-label">@lang('Format')</label>
                                <select class="form-select" name="format" id="weeklyFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi-file-earmark-bar-graph me-1"></i> @lang('Generate Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Monthly Report -->
            <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Monthly Report')</h4>
                        <span class="badge bg-soft-warning text-warning">
                            <i class="bi-calendar-month"></i> @lang('Monthly')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate comprehensive monthly reports with detailed analytics and profit summaries.')</p>

                        <form action="{{ route('admin.forex.reports.monthly') }}" method="POST" class="monthly-report-form">
                            @csrf
                            <div class="mb-3">
                                <label for="monthlyMonth" class="form-label">@lang('Month')</label>
                                <select class="form-select" name="month" id="monthlyMonth" required>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ date('n') == $i ? 'selected' : '' }}>
                                            {{ date('F', mktime(0, 0, 0, $i, 1)) }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="monthlyYear" class="form-label">@lang('Year')</label>
                                <select class="form-select" name="year" id="monthlyYear" required>
                                    @for($year = date('Y'); $year >= 2020; $year--)
                                        <option value="{{ $year }}" {{ date('Y') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="monthlyFormat" class="form-label">@lang('Format')</label>
                                <select class="form-select" name="format" id="monthlyFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="bi-file-earmark-spreadsheet me-1"></i> @lang('Generate Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Annual Report -->
            <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Annual Report')</h4>
                        <span class="badge bg-soft-info text-info">
                            <i class="bi-calendar"></i> @lang('Annual')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate comprehensive annual reports with yearly performance analysis and trends.')</p>

                        <form action="{{ route('admin.forex.reports.annual') }}" method="POST" class="annual-report-form">
                            @csrf
                            <div class="mb-3">
                                <label for="annualYear" class="form-label">@lang('Year')</label>
                                <select class="form-select" name="year" id="annualYear" required>
                                    @for($year = date('Y'); $year >= 2020; $year--)
                                        <option value="{{ $year }}" {{ date('Y') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="annualFormat" class="form-label">@lang('Format')</label>
                                <select class="form-select" name="format" id="annualFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-info w-100">
                                <i class="bi-file-earmark-pdf me-1"></i> @lang('Generate Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Revenue Report -->
            <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Revenue Report')</h4>
                        <span class="badge bg-soft-success text-success">
                            <i class="bi-currency-dollar"></i> @lang('Revenue')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate detailed revenue reports with profit analysis and commission breakdowns.')</p>

                        <form action="{{ route('admin.forex.reports.revenue') }}" method="POST" class="revenue-report-form">
                            @csrf
                            <div class="mb-3">
                                <label for="revenueStartDate" class="form-label">@lang('Start Date')</label>
                                <input type="date" class="form-control" name="start_date" id="revenueStartDate"
                                       value="{{ date('Y-m-01') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="revenueEndDate" class="form-label">@lang('End Date')</label>
                                <input type="date" class="form-control" name="end_date" id="revenueEndDate"
                                       value="{{ date('Y-m-d') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="revenueFormat" class="form-label">@lang('Format')</label>
                                <select class="form-select" name="format" id="revenueFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi-graph-up me-1"></i> @lang('Generate Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Operational Cost Report -->
            <div class="col-sm-6 col-lg-4 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Operational Costs')</h4>
                        <span class="badge bg-soft-danger text-danger">
                            <i class="bi-receipt"></i> @lang('Costs')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate operational cost reports with category breakdowns and expense analysis.')</p>

                        <form action="{{ route('admin.forex.reports.operational.costs') }}" method="POST" class="cost-report-form">
                            @csrf
                            <div class="mb-3">
                                <label for="costStartDate" class="form-label">@lang('Start Date')</label>
                                <input type="date" class="form-control" name="start_date" id="costStartDate"
                                       value="{{ date('Y-m-01') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="costEndDate" class="form-label">@lang('End Date')</label>
                                <input type="date" class="form-control" name="end_date" id="costEndDate"
                                       value="{{ date('Y-m-d') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="costFormat" class="form-label">@lang('Format')</label>
                                <select class="form-select" name="format" id="costFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="bi-receipt-cutoff me-1"></i> @lang('Generate Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Report Types -->

        <!-- Quick Links -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Quick Links')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a href="{{ route('admin.forex.reports.cbn') }}" class="btn btn-outline-primary w-100">
                                    <i class="bi-bank me-1"></i> @lang('CBN Compliance Reports')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a href="{{ route('admin.forex.reports.volume.analysis') }}" class="btn btn-outline-primary w-100">
                                    <i class="bi-bar-chart me-1"></i> @lang('Volume Analysis')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a href="{{ route('admin.forex.reports.performance.analytics') }}" class="btn btn-outline-success w-100">
                                    <i class="bi-graph-up me-1"></i> @lang('Performance Analytics')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a href="{{ route('admin.forex.reports.custom') }}" class="btn btn-outline-secondary w-100">
                                    <i class="bi-sliders me-1"></i> @lang('Custom Report Builder')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a href="{{ route('admin.forex.dashboard') }}" class="btn btn-outline-info w-100">
                                    <i class="bi-speedometer2 me-1"></i> @lang('Forex Dashboard')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a href="{{ route('admin.forex.costs.summary') }}" class="btn btn-outline-warning w-100">
                                    <i class="bi-graph-up me-1"></i> @lang('Cost Summary')
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Quick Links -->
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            // Initialize counter animations
            HSCore.components.HSCounter.init('.js-counter');

            // Handle date validation for weekly reports
            $('#weeklyStartDate').on('change', function() {
                const startDate = new Date($(this).val());
                const endDate = new Date($('#weeklyEndDate').val());

                if (startDate > endDate) {
                    const newEndDate = new Date(startDate);
                    newEndDate.setDate(newEndDate.getDate() + 6);
                    $('#weeklyEndDate').val(newEndDate.toISOString().split('T')[0]);
                }
            });

            // Handle date validation for revenue reports
            $('#revenueStartDate, #costStartDate').on('change', function() {
                const startDateId = $(this).attr('id');
                const endDateId = startDateId.replace('Start', 'End');
                const startDate = new Date($(this).val());
                const endDate = new Date($('#' + endDateId).val());

                if (startDate > endDate) {
                    $('#' + endDateId).val($(this).val());
                }
            });
            // CBN Templates button click handler
            $('#loadCbnTemplatesBtn').on('click', function(e) {
                e.preventDefault();
                console.log('CBN Templates button clicked');
                loadCbnTemplates();
            });

            // Test if function is available
            if (typeof loadCbnTemplates === 'function') {
                console.log('loadCbnTemplates function is properly defined');
            } else {
                console.error('loadCbnTemplates function is not defined');
            }
        });

        // CBN Templates functionality - Define globally
        function loadCbnTemplates() {
            console.log('loadCbnTemplates function called');
            $.ajax({
                url: '{{ route("admin.forex.reports.templates.seed.cbn") }}',
                method: 'POST',
                data: { _token: '{{ csrf_token() }}' },
                beforeSend: function() {
                    // Show loading state
                    $('#loadCbnTemplatesBtn').prop('disabled', true).html('<i class="bi-arrow-clockwise me-1"></i> @lang("Loading...")');
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        const alertHtml = `
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi-check-circle"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <strong>CBN Templates Loaded Successfully!</strong><br>
                                        ${response.templates_count} templates are now available in the custom report builder.
                                    </div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        `;

                        // Insert alert at the top of the page
                        $('.content').prepend(alertHtml);

                        // Auto-dismiss after 5 seconds
                        setTimeout(function() {
                            $('.alert').fadeOut();
                        }, 5000);
                    } else {
                        alert('@lang("Failed to load CBN templates. Please try again.")');
                    }
                },
                error: function(xhr) {
                    console.error('Error loading CBN templates:', xhr);
                    let errorMessage = '@lang("Failed to load CBN templates. Please try again.")';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.status === 419) {
                        errorMessage = '@lang("Session expired. Please refresh the page and try again.")';
                    } else if (xhr.status === 404) {
                        errorMessage = '@lang("Template seeding endpoint not found. Please contact support.")';
                    }

                    alert(errorMessage);
                },
                complete: function() {
                    // Reset button state
                    $('#loadCbnTemplatesBtn').prop('disabled', false).html('<i class="bi-bookmark me-1"></i> @lang("Load CBN Templates")');
                }
            });
        }
    </script>
@endpush
