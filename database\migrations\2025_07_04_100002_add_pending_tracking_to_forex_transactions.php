<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forex_transactions', function (Blueprint $table) {
            // Add field to track if booked transaction has been completed
            $table->boolean('is_completed')->default(false)->after('metadata')
                ->comment('Whether this booked transaction has been completed (debited/credited)');
            
            // Add field to link completion transaction to original booked transaction
            $table->unsignedBigInteger('related_transaction_id')->nullable()->after('is_completed')
                ->comment('ID of the completion/cancellation transaction related to this booked transaction');
            
            // Add field to track transaction subtype for better categorization
            $table->string('transaction_subtype', 50)->nullable()->after('transaction_type')
                ->comment('Subtype: booked, completed, cancelled_refund, etc.');
            
            // Add index for performance
            $table->index(['forex_booking_id', 'transaction_subtype']);
            $table->index(['is_completed', 'transaction_subtype']);
            
            // Add foreign key for related transaction
            $table->foreign('related_transaction_id')->references('id')->on('forex_transactions')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forex_transactions', function (Blueprint $table) {
            $table->dropForeign(['related_transaction_id']);
            $table->dropIndex(['forex_booking_id', 'transaction_subtype']);
            $table->dropIndex(['is_completed', 'transaction_subtype']);
            $table->dropColumn([
                'is_completed',
                'related_transaction_id', 
                'transaction_subtype'
            ]);
        });
    }
};
