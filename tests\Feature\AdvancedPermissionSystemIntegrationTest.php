<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\AdvancedPermission;
use App\Models\AdvancedRole;
use App\Models\AdvancedUserRole;
use App\Models\User;
use App\Models\Admin;
use App\Services\AdvancedPermissionService;
use App\Services\PermissionDiscoveryService;
use App\Services\PermissionTemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;

/**
 * Advanced Permission System Integration Test
 * 
 * Tests the complete integration of all system components.
 */
class AdvancedPermissionSystemIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    /** @test */
    public function it_can_run_complete_system_integration()
    {
        // 1. Test migrations ran successfully
        $this->assertTrue(\Schema::hasTable('advanced_permissions'));
        $this->assertTrue(\Schema::hasTable('advanced_roles'));
        $this->assertTrue(\Schema::hasTable('advanced_user_roles'));
        $this->assertTrue(\Schema::hasTable('advanced_role_permissions'));
        $this->assertTrue(\Schema::hasTable('advanced_permission_audit'));

        // 2. Test seeders work
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder'])
            ->assertExitCode(0);
        
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder'])
            ->assertExitCode(0);

        $this->assertGreaterThan(0, AdvancedPermission::count());
        $this->assertGreaterThan(0, AdvancedRole::count());

        // 3. Test service instantiation
        $permissionService = app(AdvancedPermissionService::class);
        $this->assertInstanceOf(AdvancedPermissionService::class, $permissionService);

        $discoveryService = app(PermissionDiscoveryService::class);
        $this->assertInstanceOf(PermissionDiscoveryService::class, $discoveryService);

        $templateService = app(PermissionTemplateService::class);
        $this->assertInstanceOf(PermissionTemplateService::class, $templateService);

        // 4. Test user model integration
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $this->assertTrue($user->usesAdvancedRoles());
        $this->assertTrue(method_exists($user, 'hasAdvancedPermission'));
        $this->assertTrue(method_exists($user, 'assignAdvancedRole'));

        // 5. Test admin model integration
        $admin = Admin::factory()->create(['use_advanced_roles' => true]);
        $this->assertTrue($admin->usesAdvancedRoles());
        $this->assertTrue(method_exists($admin, 'hasAdvancedPermission'));
        $this->assertTrue(method_exists($admin, 'assignAdvancedRole'));

        // 6. Test role assignment
        $role = AdvancedRole::findByName('basic_user');
        if (!$role) {
            $role = AdvancedRole::create([
                'name' => 'basic_user',
                'display_name' => 'Basic User',
                'category' => 'user',
            ]);
        }

        $user->assignAdvancedRole($role);
        $this->assertTrue($user->hasAdvancedRole('basic_user'));

        // 7. Test permission checking
        $permission = AdvancedPermission::findByName('users.read');
        if (!$permission) {
            $permission = AdvancedPermission::create([
                'name' => 'users.read',
                'display_name' => 'Read Users',
                'resource' => 'users',
                'action' => 'read',
                'category' => 'user_management',
            ]);
        }

        $role->grantPermission($permission);
        $this->assertTrue($user->hasAdvancedPermission('users.read'));

        // 8. Test service methods
        $this->assertTrue($permissionService->check($user, 'users.read'));
        $this->assertFalse($permissionService->check($user, 'nonexistent.permission'));

        // 9. Test commands work
        $this->artisan('permissions:manage', ['action' => 'status'])
            ->assertExitCode(0);

        $this->artisan('permissions:health')
            ->assertExitCode(0);

        $this->artisan('permissions:templates', ['action' => 'list'])
            ->assertExitCode(0);

        // 10. Test middleware integration
        $middleware = app(\App\Http\Middleware\AdvancedPermissionMiddleware::class);
        $this->assertInstanceOf(\App\Http\Middleware\AdvancedPermissionMiddleware::class, $middleware);

        // 11. Test blade directives are registered
        $this->assertTrue(app('blade.compiler')->getCustomDirectives()['canAdvanced'] ?? false);
        $this->assertTrue(app('blade.compiler')->getCustomDirectives()['hasRole'] ?? false);
        $this->assertTrue(app('blade.compiler')->getCustomDirectives()['isSuperAdmin'] ?? false);
    }

    /** @test */
    public function it_can_handle_permission_discovery()
    {
        $this->artisan('permissions:discover', ['--dry-run'])
            ->assertExitCode(0);

        $this->artisan('permissions:discover', ['--sync'])
            ->assertExitCode(0);

        // Should have discovered some permissions
        $this->assertGreaterThan(0, AdvancedPermission::count());
    }

    /** @test */
    public function it_can_handle_template_operations()
    {
        // Seed permissions first
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);

        // Test template listing
        $this->artisan('permissions:templates', ['action' => 'list'])
            ->assertExitCode(0);

        // Test template creation
        $this->artisan('permissions:templates', [
            'action' => 'create',
            'template' => 'basic_user',
        ])->assertExitCode(0);

        $this->assertDatabaseHas('advanced_roles', ['name' => 'basic_user']);
    }

    /** @test */
    public function it_can_handle_role_management()
    {
        // Seed data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        $user = User::factory()->create(['use_advanced_roles' => true]);

        // Test role assignment via command
        $this->artisan('permissions:manage', [
            'action' => 'assign-role',
            '--user' => $user->id,
            '--role' => 'basic_user',
        ])->assertExitCode(0);

        // Test user info command
        $this->artisan('permissions:manage', [
            'action' => 'user-info',
            '--user' => $user->id,
        ])->assertExitCode(0);

        // Test role revocation
        $this->artisan('permissions:manage', [
            'action' => 'revoke-role',
            '--user' => $user->id,
            '--role' => 'basic_user',
        ])->assertExitCode(0);
    }

    /** @test */
    public function it_can_handle_system_validation()
    {
        // Seed clean data
        $this->artisan('db:seed', ['--class' => 'AdvancedPermissionSeeder']);
        $this->artisan('db:seed', ['--class' => 'AdvancedRoleSeeder']);

        // Test system validation
        $this->artisan('permissions:manage', ['action' => 'validate'])
            ->assertExitCode(0);

        // Test cleanup
        $this->artisan('permissions:manage', ['action' => 'cleanup'])
            ->assertExitCode(0);
    }

    /** @test */
    public function it_can_handle_super_admin_functionality()
    {
        $admin = Admin::factory()->create([
            'use_advanced_roles' => true,
            'is_super_admin' => true,
        ]);

        $this->assertTrue($admin->isSuperAdmin());

        $permissionService = app(AdvancedPermissionService::class);
        
        // Super admin should have all permissions
        $this->assertTrue($permissionService->check($admin, 'any.permission'));
        $this->assertTrue($permissionService->check($admin, 'nonexistent.permission'));
    }

    /** @test */
    public function it_can_handle_constraint_validation()
    {
        $user = User::factory()->create(['use_advanced_roles' => true]);
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
        ]);
        
        $permission = AdvancedPermission::create([
            'name' => 'test.permission',
            'display_name' => 'Test Permission',
            'resource' => 'test',
            'action' => 'permission',
        ]);

        $user->assignAdvancedRole($role);
        $role->grantPermission($permission, [
            'constraints' => [
                'max_amount' => 1000,
                'ip_whitelist' => ['127.0.0.1'],
            ]
        ]);

        $permissionService = app(AdvancedPermissionService::class);

        // Should pass with valid context
        $this->assertTrue($permissionService->check($user, 'test.permission', [
            'amount' => 500,
            'ip' => '127.0.0.1',
        ]));

        // Should fail with invalid context
        $this->assertFalse($permissionService->check($user, 'test.permission', [
            'amount' => 2000,
            'ip' => '127.0.0.1',
        ]));
    }

    /** @test */
    public function it_can_handle_error_conditions_gracefully()
    {
        // Test with non-existent user
        $this->artisan('permissions:manage', [
            'action' => 'user-info',
            '--user' => 99999,
        ])->assertExitCode(1);

        // Test with non-existent role
        $user = User::factory()->create();
        $this->artisan('permissions:manage', [
            'action' => 'assign-role',
            '--user' => $user->id,
            '--role' => 'nonexistent_role',
        ])->assertExitCode(1);

        // Test invalid command action
        $this->artisan('permissions:manage', ['action' => 'invalid'])
            ->assertExitCode(0); // Should show help
    }
}
