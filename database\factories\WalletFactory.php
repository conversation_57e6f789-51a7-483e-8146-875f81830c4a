<?php

namespace Database\Factories;

use App\Models\Wallet;
use App\Models\User;
use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

class WalletFactory extends Factory
{
    protected $model = Wallet::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'currency_id' => Currency::factory(),
            'balance' => $this->faker->randomFloat(2, 0, 100000),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
