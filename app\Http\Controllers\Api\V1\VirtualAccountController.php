<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\VirtualAccount;
use App\Models\Currency;
use App\Services\Payout\numero\Card as NumeroCard;
use App\Traits\ApiValidation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class VirtualAccountController extends Controller
{
    use ApiValidation;

    /**
     * Get user's virtual accounts
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();

            $virtualAccounts = VirtualAccount::where('user_id', $user->id)
                ->where('is_active', true)
                ->with('currency')
                ->get();

            $data = $virtualAccounts->map(function ($account) {
                return [
                    'id' => $account->id,
                    'provider' => ucfirst($account->provider),
                    'currency' => $account->currency,
                    'account_number' => $account->account_number,
                    'account_name' => $account->account_name,
                    'bank_name' => $account->bank_name,
                    'bank_code' => $account->bank_code,
                    'created_at' => $account->created_at,
                    'formatted_details' => $account->formatted_details
                ];
            });

            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Create a new virtual account
     */
    public function create(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'currency' => 'required|string|size:3|exists:currencies,code',
                'provider' => 'sometimes|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $user = Auth::user();
            $currency = $request->currency;
            $provider = $request->provider ?? 'numero';

            // Check if user already has a virtual account for this provider and currency
            if (VirtualAccount::userHasAccount($user->id, $provider, $currency)) {
                return response()->json($this->withErrors('You already have a virtual account for this currency and provider'));
            }

            // Create virtual account using the specified provider
            switch ($provider) {
                case 'numero':
                    $result = NumeroCard::createVirtualAccount($user, $currency);
                    break;
                default:
                    return response()->json($this->withErrors('Unsupported provider'));
            }

            if ($result['status'] !== 'success') {
                return response()->json($this->withErrors($result['data']));
            }

            // Save virtual account to database
            $providerData = $result['data'];
            $virtualAccount = VirtualAccount::create([
                'user_id' => $user->id,
                'provider' => $provider,
                'currency' => $currency,
                'account_number' => $providerData['account_number'] ?? $providerData['accountNumber'],
                'account_name' => $providerData['account_name'] ?? $providerData['accountName'],
                'bank_name' => $providerData['bank_name'] ?? $providerData['bankName'],
                //'bank_code' => $providerData['bank_code'] ?? $providerData['bankCode'],
                'provider_data' => $providerData,
                'kyc_data_used' => NumeroCard::getKycDataForUser($user),
                'provider_reference' => $providerData['reference'] ?? $providerData['id'] ?? null,
                'is_active' => true,
                'created_at_provider' => $providerData['created_at'] ?? now()
            ]);

            $virtualAccount->load('currency');

            $responseData = [
                'id' => $virtualAccount->id,
                'provider' => ucfirst($virtualAccount->provider),
                'currency' => $virtualAccount->currency,
                'account_number' => $virtualAccount->account_number,
                'account_name' => $virtualAccount->account_name,
                'bank_name' => $virtualAccount->bank_name,
                'bank_code' => $virtualAccount->bank_code,
                'created_at' => $virtualAccount->created_at,
                'formatted_details' => $virtualAccount->formatted_details
            ];

            return response()->json($this->withSuccess($responseData));

        } catch (\Exception $e) {
            Log::error('Virtual account creation failed: ' . $e->getMessage());
            return response()->json($this->withErrors('Virtual account creation failed: ' . $e->getMessage()));
        }
    }

    /**
     * Get available currencies for virtual account creation
     */
    public function availableCurrencies(Request $request)
    {
        try {
            $provider = $request->provider ?? 'numero';

            // For now, Numero supports NGN and USD
            $supportedCurrencies = ['NGN', 'USD'];

            $currencies = Currency::whereIn('code', $supportedCurrencies)
                ->where('is_active', 1)
                ->select('id', 'code', 'name', 'symbol')
                ->get();

            return response()->json($this->withSuccess($currencies));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Check if user can create virtual account for a currency
     */
    public function checkEligibility(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'currency' => 'required|string|size:3|exists:currencies,code',
                'provider' => 'sometimes|string|in:numero'
            ]);

            if ($validator->fails()) {
                return response()->json($this->withErrors(collect($validator->errors())->collapse()));
            }

            $user = Auth::user();
            $currency = $request->currency;
            $provider = $request->provider ?? 'numero';

            $hasAccount = VirtualAccount::userHasAccount($user->id, $provider, $currency);

            $data = [
                'can_create' => !$hasAccount,
                'has_existing_account' => $hasAccount,
                'provider' => $provider,
                'currency' => $currency
            ];

            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }

    /**
     * Get virtual account details by ID
     */
    public function show($id)
    {
        try {
            $user = Auth::user();

            $virtualAccount = VirtualAccount::where('id', $id)
                ->where('user_id', $user->id)
                ->where('is_active', true)
                ->with('currency')
                ->first();

            if (!$virtualAccount) {
                return response()->json($this->withErrors('Virtual account not found'));
            }

            $data = [
                'id' => $virtualAccount->id,
                'provider' => ucfirst($virtualAccount->provider),
                'currency' => $virtualAccount->currency,
                'account_number' => $virtualAccount->account_number,
                'account_name' => $virtualAccount->account_name,
                'bank_name' => $virtualAccount->bank_name,
                'bank_code' => $virtualAccount->bank_code,
                'created_at' => $virtualAccount->created_at,
                'formatted_details' => $virtualAccount->formatted_details
            ];

            return response()->json($this->withSuccess($data));
        } catch (\Exception $e) {
            return response()->json($this->withErrors($e->getMessage()));
        }
    }
}
