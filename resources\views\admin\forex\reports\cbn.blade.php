@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.index') }}">
                                    @lang('Forex Reports')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('CBN Compliance')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('CBN Compliance Reports')</h1>
                    <p class="page-header-text">@lang('Generate reports for Central Bank of Nigeria compliance and regulatory requirements')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.reports.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Reports')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- CBN Compliance Info -->
        <div class="alert alert-soft-info mb-4" role="alert">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="bi-info-circle"></i>
                </div>
                <div class="flex-grow-1 ms-3">
                    <h4 class="alert-heading">@lang('CBN Compliance Requirements')</h4>
                    <p class="mb-0">@lang('These reports are designed to meet Central Bank of Nigeria regulatory requirements for forex trading operations. All reports include transaction details, exchange rates, and compliance metrics required for regulatory submissions.')</p>
                </div>
            </div>
        </div>

        <!-- Report Types -->
        <div class="row">
            <!-- Daily CBN Report -->
            <div class="col-sm-6 col-lg-6 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Daily CBN Report')</h4>
                        <span class="badge bg-soft-primary text-primary">
                            <i class="bi-calendar-day"></i> @lang('Daily')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate daily CBN compliance reports with all forex transactions, exchange rates used, and regulatory metrics.')</p>
                        
                        <div class="mb-3">
                            <h6>@lang('Report Includes:')</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi-check text-success me-1"></i> @lang('All forex transactions for the day')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('CBN vs Parallel rate comparisons')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Transaction volumes by currency')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Client transaction summaries')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Compliance status indicators')</li>
                            </ul>
                        </div>
                        
                        <form action="{{ route('admin.forex.reports.daily') }}" method="POST" class="cbn-daily-form">
                            @csrf
                            <input type="hidden" name="report_type" value="cbn_compliance">
                            <div class="mb-3">
                                <label for="cbnDailyDate" class="form-label">@lang('Report Date')</label>
                                <input type="date" class="form-control" name="date" id="cbnDailyDate" 
                                       value="{{ date('Y-m-d') }}" max="{{ date('Y-m-d') }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="cbnDailyFormat" class="form-label">@lang('Export Format')</label>
                                <select class="form-select" name="format" id="cbnDailyFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi-file-earmark-text me-1"></i> @lang('Generate Daily CBN Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Monthly CBN Report -->
            <div class="col-sm-6 col-lg-6 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Monthly CBN Report')</h4>
                        <span class="badge bg-soft-success text-success">
                            <i class="bi-calendar-month"></i> @lang('Monthly')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text">@lang('Generate comprehensive monthly CBN compliance reports with detailed analytics and regulatory summaries.')</p>
                        
                        <div class="mb-3">
                            <h6>@lang('Report Includes:')</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi-check text-success me-1"></i> @lang('Monthly transaction summaries')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Exchange rate trend analysis')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Volume and value statistics')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Client activity reports')</li>
                                <li><i class="bi-check text-success me-1"></i> @lang('Regulatory compliance metrics')</li>
                            </ul>
                        </div>
                        
                        <form action="{{ route('admin.forex.reports.monthly') }}" method="POST" class="cbn-monthly-form">
                            @csrf
                            <input type="hidden" name="report_type" value="cbn_compliance">
                            <div class="mb-3">
                                <label for="cbnMonthlyMonth" class="form-label">@lang('Month')</label>
                                <select class="form-select" name="month" id="cbnMonthlyMonth" required>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ date('n') == $i ? 'selected' : '' }}>
                                            {{ date('F', mktime(0, 0, 0, $i, 1)) }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="cbnMonthlyYear" class="form-label">@lang('Year')</label>
                                <select class="form-select" name="year" id="cbnMonthlyYear" required>
                                    @for($year = date('Y'); $year >= 2020; $year--)
                                        <option value="{{ $year }}" {{ date('Y') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="cbnMonthlyFormat" class="form-label">@lang('Export Format')</label>
                                <select class="form-select" name="format" id="cbnMonthlyFormat">
                                    <option value="html">@lang('View Online')</option>
                                    <option value="excel">@lang('Excel Download')</option>
                                    <option value="pdf">@lang('PDF Download')</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="bi-file-earmark-spreadsheet me-1"></i> @lang('Generate Monthly CBN Report')
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom CBN Report -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Custom CBN Compliance Report')</h4>
                        <span class="badge bg-soft-warning text-warning">
                            <i class="bi-sliders"></i> @lang('Custom')
                        </span>
                    </div>
                    <div class="card-body">
                        <p class="card-text mb-4">@lang('Generate custom CBN compliance reports for specific date ranges with detailed filtering options.')</p>
                        
                        <form action="{{ route('admin.forex.reports.custom') }}" method="GET" class="custom-cbn-form">
                            <input type="hidden" name="report_type" value="cbn_compliance">
                            <div class="row">
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customStartDate" class="form-label">@lang('Start Date')</label>
                                    <input type="date" class="form-control" name="start_date" id="customStartDate" 
                                           value="{{ date('Y-m-01') }}" required>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customEndDate" class="form-label">@lang('End Date')</label>
                                    <input type="date" class="form-control" name="end_date" id="customEndDate" 
                                           value="{{ date('Y-m-d') }}" required>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customTransactionType" class="form-label">@lang('Transaction Type')</label>
                                    <select class="form-select" name="transaction_type" id="customTransactionType">
                                        <option value="">@lang('All Transactions')</option>
                                        <option value="credit">@lang('Buy USD')</option>
                                        <option value="debit">@lang('Sell USD')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customFormat" class="form-label">@lang('Export Format')</label>
                                    <select class="form-select" name="format" id="customFormat">
                                        <option value="html">@lang('View Online')</option>
                                        <option value="excel">@lang('Excel Download')</option>
                                        <option value="pdf">@lang('PDF Download')</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customMinAmount" class="form-label">@lang('Minimum Amount (USD)')</label>
                                    <input type="number" class="form-control" name="min_amount" id="customMinAmount" 
                                           step="0.01" min="0" placeholder="@lang('0.00')">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customMaxAmount" class="form-label">@lang('Maximum Amount (USD)')</label>
                                    <input type="number" class="form-control" name="max_amount" id="customMaxAmount" 
                                           step="0.01" min="0" placeholder="@lang('No limit')">
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customClientType" class="form-label">@lang('Client Type')</label>
                                    <select class="form-select" name="client_type" id="customClientType">
                                        <option value="">@lang('All Client Types')</option>
                                        <option value="user">@lang('Registered Users')</option>
                                        <option value="merchant">@lang('Merchants')</option>
                                        <option value="external">@lang('External Clients')</option>
                                    </select>
                                </div>
                                <div class="col-sm-6 col-lg-3 mb-3">
                                    <label for="customStatus" class="form-label">@lang('Booking Status')</label>
                                    <select class="form-select" name="status" id="customStatus">
                                        <option value="">@lang('All Statuses')</option>
                                        <option value="completed">@lang('Completed Only')</option>
                                        <option value="pending">@lang('Pending Only')</option>
                                        <option value="cancelled">@lang('Cancelled Only')</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="include_rate_analysis" id="includeRateAnalysis" value="1" checked>
                                        <label class="form-check-label" for="includeRateAnalysis">
                                            @lang('Include exchange rate analysis and CBN compliance metrics')
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="include_client_summary" id="includeClientSummary" value="1" checked>
                                        <label class="form-check-label" for="includeClientSummary">
                                            @lang('Include client transaction summaries')
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" name="include_volume_analysis" id="includeVolumeAnalysis" value="1" checked>
                                        <label class="form-check-label" for="includeVolumeAnalysis">
                                            @lang('Include transaction volume and trend analysis')
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi-file-earmark-bar-graph me-1"></i> @lang('Generate Custom CBN Report')
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- CBN Guidelines -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('CBN Compliance Guidelines')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6 col-lg-4 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
                                            <span class="avatar-initials">
                                                <i class="bi-shield-check"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6>@lang('Transaction Reporting')</h6>
                                        <p class="text-muted small mb-0">@lang('All forex transactions must be reported to CBN with accurate exchange rates and client information.')</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-lg-4 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-success avatar-circle">
                                            <span class="avatar-initials">
                                                <i class="bi-graph-up"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6>@lang('Rate Compliance')</h6>
                                        <p class="text-muted small mb-0">@lang('Exchange rates must be within acceptable variance from CBN official rates.')</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 col-lg-4 mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <div class="avatar avatar-sm avatar-soft-warning avatar-circle">
                                            <span class="avatar-initials">
                                                <i class="bi-calendar-check"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6>@lang('Regular Reporting')</h6>
                                        <p class="text-muted small mb-0">@lang('Monthly and quarterly reports must be submitted to CBN within specified deadlines.')</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        
        $(document).ready(function () {
            // Handle date validation for custom reports
            $('#customStartDate').on('change', function() {
                const startDate = new Date($(this).val());
                const endDate = new Date($('#customEndDate').val());
                
                if (startDate > endDate) {
                    $('#customEndDate').val($(this).val());
                }
            });

            // Handle amount validation
            $('#customMinAmount').on('change', function() {
                const minAmount = parseFloat($(this).val()) || 0;
                const maxAmount = parseFloat($('#customMaxAmount').val()) || 0;
                
                if (maxAmount > 0 && minAmount > maxAmount) {
                    $('#customMaxAmount').val($(this).val());
                }
            });
        });
    </script>
@endpush
