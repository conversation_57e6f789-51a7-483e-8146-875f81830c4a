<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\PermissionDiscoveryService;
use App\Services\PermissionTemplateService;
use App\Models\AdvancedPermission;
use App\Models\AdvancedRole;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\File;

/**
 * Permission Discovery System Tests
 * 
 * Tests the permission discovery service and template management.
 */
class PermissionDiscoveryTest extends TestCase
{
    use RefreshDatabase;

    protected PermissionDiscoveryService $discoveryService;
    protected PermissionTemplateService $templateService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->artisan('migrate');
        $this->discoveryService = new PermissionDiscoveryService();
        $this->templateService = new PermissionTemplateService();
    }

    /** @test */
    public function it_can_discover_permissions_from_controllers()
    {
        $permissions = $this->discoveryService->discoverFromControllers();
        
        $this->assertIsArray($permissions);
        $this->assertNotEmpty($permissions);
        
        // Check that we found some expected permissions
        $permissionNames = array_column($permissions, 'name');
        $this->assertContains('users.read', $permissionNames);
        $this->assertContains('admins.read', $permissionNames);
    }

    /** @test */
    public function it_can_discover_permissions_from_routes()
    {
        $permissions = $this->discoveryService->discoverFromRoutes();
        
        $this->assertIsArray($permissions);
        // Routes might be empty in test environment, so just check structure
        if (!empty($permissions)) {
            $this->assertArrayHasKey('name', $permissions[0]);
            $this->assertArrayHasKey('resource', $permissions[0]);
            $this->assertArrayHasKey('action', $permissions[0]);
        }
    }

    /** @test */
    public function it_generates_standard_resource_permissions()
    {
        $permissions = $this->discoveryService->generateStandardResourcePermissions();
        
        $this->assertIsArray($permissions);
        $this->assertNotEmpty($permissions);
        
        // Check CRUD permissions for users
        $userPermissions = array_filter($permissions, fn($p) => $p['resource'] === 'users');
        $this->assertCount(4, $userPermissions); // create, read, update, delete
        
        $actions = array_column($userPermissions, 'action');
        $this->assertContains('create', $actions);
        $this->assertContains('read', $actions);
        $this->assertContains('update', $actions);
        $this->assertContains('delete', $actions);
    }

    /** @test */
    public function it_can_sync_permissions_to_database()
    {
        $permissions = [
            [
                'name' => 'test.create',
                'display_name' => 'Create Test',
                'description' => 'Test permission',
                'resource' => 'test',
                'action' => 'create',
                'category' => 'test',
                'is_system' => true,
            ],
            [
                'name' => 'test.read',
                'display_name' => 'Read Test',
                'description' => 'Test permission',
                'resource' => 'test',
                'action' => 'read',
                'category' => 'test',
                'is_system' => true,
            ]
        ];

        $result = $this->discoveryService->syncPermissionsToDatabase($permissions);

        $this->assertEquals(2, $result['total_discovered']);
        $this->assertEquals(2, $result['created']);
        $this->assertEquals(0, $result['updated']);
        $this->assertEmpty($result['errors']);

        $this->assertDatabaseHas('advanced_permissions', ['name' => 'test.create']);
        $this->assertDatabaseHas('advanced_permissions', ['name' => 'test.read']);
    }

    /** @test */
    public function it_provides_permission_statistics()
    {
        // Create some test permissions
        AdvancedPermission::create([
            'name' => 'existing.permission',
            'display_name' => 'Existing Permission',
            'resource' => 'existing',
            'action' => 'permission',
            'category' => 'test',
        ]);

        $stats = $this->discoveryService->getPermissionStatistics();

        $this->assertArrayHasKey('total_discovered', $stats);
        $this->assertArrayHasKey('total_existing', $stats);
        $this->assertArrayHasKey('by_category', $stats);
        $this->assertArrayHasKey('by_resource', $stats);
        $this->assertArrayHasKey('by_action', $stats);

        $this->assertEquals(1, $stats['total_existing']);
        $this->assertIsArray($stats['by_category']);
        $this->assertIsArray($stats['by_resource']);
        $this->assertIsArray($stats['by_action']);
    }

    /** @test */
    public function it_correctly_maps_controller_methods_to_actions()
    {
        $reflection = new \ReflectionClass($this->discoveryService);
        $method = $reflection->getMethod('mapMethodToAction');
        $method->setAccessible(true);

        $this->assertEquals('read', $method->invoke($this->discoveryService, 'index'));
        $this->assertEquals('read', $method->invoke($this->discoveryService, 'show'));
        $this->assertEquals('create', $method->invoke($this->discoveryService, 'create'));
        $this->assertEquals('create', $method->invoke($this->discoveryService, 'store'));
        $this->assertEquals('update', $method->invoke($this->discoveryService, 'edit'));
        $this->assertEquals('update', $method->invoke($this->discoveryService, 'update'));
        $this->assertEquals('delete', $method->invoke($this->discoveryService, 'destroy'));
        $this->assertEquals('approve', $method->invoke($this->discoveryService, 'approve'));
    }

    /** @test */
    public function it_extracts_resource_names_from_controllers()
    {
        $reflection = new \ReflectionClass($this->discoveryService);
        $method = $reflection->getMethod('extractResourceFromController');
        $method->setAccessible(true);

        $this->assertEquals('users', $method->invoke($this->discoveryService, 'App\\Http\\Controllers\\UserController'));
        $this->assertEquals('users', $method->invoke($this->discoveryService, 'App\\Http\\Controllers\\Admin\\UsersController'));
        $this->assertEquals('forex_rates', $method->invoke($this->discoveryService, 'App\\Http\\Controllers\\ForexRateController'));
    }

    /** @test */
    public function it_can_list_available_templates()
    {
        $templates = $this->templateService->getAvailableTemplates();

        $this->assertIsArray($templates);
        $this->assertArrayHasKey('super_admin', $templates);
        $this->assertArrayHasKey('admin', $templates);
        $this->assertArrayHasKey('finance_manager', $templates);
        $this->assertArrayHasKey('basic_user', $templates);

        foreach ($templates as $template) {
            $this->assertArrayHasKey('name', $template);
            $this->assertArrayHasKey('display_name', $template);
            $this->assertArrayHasKey('description', $template);
            $this->assertArrayHasKey('category', $template);
            $this->assertArrayHasKey('permissions', $template);
        }
    }

    /** @test */
    public function it_can_create_role_from_template()
    {
        // First create some permissions for the template to use
        AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
            'category' => 'user_management',
        ]);

        AdvancedPermission::create([
            'name' => 'users.create',
            'display_name' => 'Create Users',
            'resource' => 'users',
            'action' => 'create',
            'category' => 'user_management',
        ]);

        $role = $this->templateService->createRoleFromTemplate('basic_user');

        $this->assertInstanceOf(AdvancedRole::class, $role);
        $this->assertEquals('basic_user', $role->name);
        $this->assertEquals('Basic User', $role->display_name);
        $this->assertDatabaseHas('advanced_roles', ['name' => 'basic_user']);
    }

    /** @test */
    public function it_can_export_role_as_template()
    {
        $role = AdvancedRole::create([
            'name' => 'test_role',
            'display_name' => 'Test Role',
            'description' => 'Test role for export',
            'category' => 'test',
        ]);

        $permission = AdvancedPermission::create([
            'name' => 'test.permission',
            'display_name' => 'Test Permission',
            'resource' => 'test',
            'action' => 'permission',
            'category' => 'test',
        ]);

        $role->grantPermission($permission);

        $template = $this->templateService->exportRoleAsTemplate($role);

        $this->assertIsArray($template);
        $this->assertEquals('test_role', $template['name']);
        $this->assertEquals('Test Role', $template['display_name']);
        $this->assertEquals('Test role for export', $template['description']);
        $this->assertEquals('test', $template['category']);
        $this->assertContains('test.permission', $template['permissions']);
        $this->assertArrayHasKey('exported_at', $template);
    }

    /** @test */
    public function it_validates_template_structure()
    {
        $validTemplate = [
            'name' => 'valid_template',
            'display_name' => 'Valid Template',
            'description' => 'A valid template',
            'category' => 'test',
            'permissions' => ['test.permission'],
        ];

        $invalidTemplate = [
            'name' => 'invalid template', // Invalid name format
            'display_name' => 'Invalid Template',
            // Missing description, category, permissions
        ];

        $validErrors = $this->templateService->validateTemplate($validTemplate);
        $invalidErrors = $this->templateService->validateTemplate($invalidTemplate);

        $this->assertEmpty($validErrors);
        $this->assertNotEmpty($invalidErrors);
        $this->assertContains('Missing required field: description', $invalidErrors);
        $this->assertContains('Missing required field: category', $invalidErrors);
        $this->assertContains('Missing required field: permissions', $invalidErrors);
    }

    /** @test */
    public function it_can_get_permission_suggestions_for_category()
    {
        AdvancedPermission::create([
            'name' => 'finance.read',
            'display_name' => 'Read Finance',
            'resource' => 'finance',
            'action' => 'read',
            'category' => 'finance',
        ]);

        AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
            'category' => 'user_management',
        ]);

        $suggestions = $this->templateService->getPermissionSuggestions('finance');

        $this->assertGreaterThan(0, $suggestions->count());
        $this->assertTrue($suggestions->contains('name', 'finance.read'));
        // Should also include related category permissions (read-only)
        $this->assertTrue($suggestions->contains('name', 'users.read'));
    }

    /** @test */
    public function it_handles_wildcard_permissions_in_templates()
    {
        // Create some permissions
        AdvancedPermission::create([
            'name' => 'users.create',
            'display_name' => 'Create Users',
            'resource' => 'users',
            'action' => 'create',
            'category' => 'user_management',
        ]);

        AdvancedPermission::create([
            'name' => 'users.read',
            'display_name' => 'Read Users',
            'resource' => 'users',
            'action' => 'read',
            'category' => 'user_management',
        ]);

        // Test wildcard template
        $role = $this->templateService->createRoleFromTemplate('super_admin');

        // Super admin should have all permissions
        $this->assertEquals(AdvancedPermission::count(), $role->permissions()->count());
    }
}
