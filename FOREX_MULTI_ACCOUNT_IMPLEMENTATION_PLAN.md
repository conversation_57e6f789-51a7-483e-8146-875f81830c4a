# Forex Multi-Account Reservation System Implementation Plan

## Overview
This document outlines the comprehensive implementation plan for the multi-account reservation system for forex bookings, addressing the business logic requirements for NGN to USD and USD to NGN transactions.

## Business Logic Summary

### NGN to USD (Buying USD - We sell USD, collect NGN)
1. **Booking Creation**: Reserve USD from USD account
2. **Completion**: Keep USD debited, credit NGN to CBN (cbn_total + markup_amount) and Difference (difference_amount) accounts
3. **Cancellation**: Release USD reservation back to balance

### USD to NGN (Selling USD - We sell NGN, collect USD)  
1. **Booking Creation**: Reserve NGN from accounts in priority order (CBN → Difference → Investment)
2. **Completion**: Keep NGN debited, credit USD to USD account
3. **Cancellation**: Release NGN reservations back to respective account balances

## Current Issues Identified

1. **Completion Logic Wrong**: Currently credits back reserved amounts instead of keeping them debited
2. **Single Account Limitation**: USD to NGN only uses single target account, doesn't distribute across multiple NGN accounts
3. **No Multi-Account Tracking**: No way to track which accounts were used for reservations
4. **Frontend Auto-Selection Missing**: Target account selection not automated based on transaction type

## Implementation Tasks

### 1. Database Schema Implementation
- Create `forex_booking_reservations` table
- Track multi-account reservations per booking
- Link reservations to specific accounts and amounts

### 2. Backend Model Updates
- Add ForexBookingReservation model
- Update ForexBooking relationships
- Add helper methods for reservation management

### 3. ForexBookingService Core Logic
- Implement multi-account reservation distribution
- Fix completion logic to follow business rules
- Add proper cancellation logic

### 4. Frontend Enhancements
- Auto-select target accounts based on transaction type
- Show multi-account reservation breakdown
- Update booking details view

### 5. Validation Logic
- Aggregate balance validation for USD to NGN
- Account priority ordering (CBN → Difference → Investment)
- Insufficient balance error handling

## Database Schema Design

### forex_booking_reservations Table
```sql
CREATE TABLE forex_booking_reservations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    forex_booking_id BIGINT NOT NULL,
    forex_account_id BIGINT NOT NULL,
    reserved_amount DECIMAL(18,8) NOT NULL,
    status ENUM('reserved', 'completed', 'cancelled') DEFAULT 'reserved',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    FOREIGN KEY (forex_booking_id) REFERENCES forex_bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (forex_account_id) REFERENCES forex_accounts(id) ON DELETE CASCADE,
    
    INDEX idx_booking_status (forex_booking_id, status),
    INDEX idx_account_status (forex_account_id, status)
);
```

## Implementation Phases

### Phase 1: Database and Models (Tasks 1-2)
- Create migration for forex_booking_reservations
- Update models with relationships
- Add reservation management methods

### Phase 2: Core Business Logic (Tasks 3, 6-8)  
- Fix ForexBookingService completion logic
- Implement multi-account reservation system
- Add proper validation and cancellation

### Phase 3: Frontend Updates (Tasks 4-5)
- Auto-select target accounts
- Update booking creation form
- Enhance booking details view

### Phase 4: Testing and Documentation (Task 9)
- Create test scenarios
- Document new functionality
- Provide testing guide

## Key Implementation Patterns to Follow

### Existing Patterns Observed
1. **Migration Naming**: `YYYY_MM_DD_HHMMSS_descriptive_name.php`
2. **Model Relationships**: Uses standard Laravel relationships with proper foreign keys
3. **Service Layer**: Business logic in ForexBookingService, controllers stay thin
4. **Frontend**: Bootstrap 5 with Tom Select for dropdowns, consistent form patterns
5. **Validation**: Laravel validation in controllers, business validation in services
6. **Transaction Safety**: DB::transaction() for multi-step operations

### Code Quality Standards
- Follow existing naming conventions
- Use proper type hints and return types
- Add comprehensive comments for business logic
- Maintain consistent error handling patterns
- Use existing helper methods where possible

## Implementation Status: ✅ COMPLETED

### ✅ Completed Tasks

#### 1. Database Schema Implementation
- ✅ Created `forex_booking_reservations` table migration
- ✅ Added proper indexes and foreign key constraints
- ✅ Migration executed successfully

#### 2. Backend Model Updates
- ✅ Created `ForexBookingReservation` model with relationships
- ✅ Updated `ForexBooking` model with reservation helpers
- ✅ Updated `ForexAccount` model with reservation relationship

#### 3. ForexBookingService Core Logic
- ✅ Implemented multi-account reservation distribution logic
- ✅ Fixed completion logic to follow correct business rules
- ✅ Updated validation to handle aggregated NGN balance checking
- ✅ Implemented proper cancellation logic for multi-account reservations

#### 4. Frontend Enhancements
- ✅ Added auto-selection of target accounts based on transaction type
- ✅ Enhanced booking details view to show multi-account reservations
- ✅ Updated controller to load reservation relationships

## Changes Made

### Database Changes
```sql
-- New table: forex_booking_reservations
CREATE TABLE forex_booking_reservations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    forex_booking_id BIGINT NOT NULL,
    forex_account_id BIGINT NOT NULL,
    reserved_amount DECIMAL(18,8) NOT NULL,
    status ENUM('reserved', 'completed', 'cancelled') DEFAULT 'reserved',
    forex_transaction_id BIGINT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Business Logic Changes

#### NGN to USD (Buying USD) - CORRECTED
**Before:** Incorrectly credited USD back on completion
**After:**
1. **Booking Creation**: Reserve USD from USD account ✅
2. **Completion**: Keep USD debited, credit NGN to CBN (cbn_total + markup_amount) and Difference (difference_amount) ✅
3. **Cancellation**: Release USD reservation ✅

#### USD to NGN (Selling USD) - CORRECTED
**Before:** Single account reservation only
**After:**
1. **Booking Creation**: Reserve NGN across multiple accounts (CBN → Difference → Investment) ✅
2. **Completion**: Keep NGN debited, credit USD to USD account ✅
3. **Cancellation**: Release all NGN reservations ✅

### Frontend Changes
- Auto-select USD account for NGN to USD transactions
- Auto-select CBN account for USD to NGN transactions
- Display multi-account reservation breakdown in booking details
- Show reservation status and amounts per account

## Testing Guide

### Prerequisites
1. Ensure you have active forex accounts:
   - USD account with sufficient balance
   - CBN account (NGN) with some balance
   - Difference account (NGN) with some balance
   - Investment account (NGN) with some balance
2. Have an active forex rate configured
3. Clear any existing cache: `php artisan cache:clear`

### Test Scenarios

#### Scenario 1: NGN to USD Transaction (Single Account)
**Objective:** Test that USD is properly reserved and NGN accounts are credited on completion

**Steps:**
1. Go to `/admin/forex/bookings/create`
2. Fill in client details
3. Select "Buying USD (Client pays NGN, receives USD)"
4. **Verify:** Target account auto-selects to USD account
5. Enter amount (e.g., $500)
6. Submit booking
7. **Verify:**
   - Booking created successfully
   - USD account balance decreased by $500
   - USD account pending balance increased by $500
   - One reservation record created in `forex_booking_reservations`

**Completion Test:**
1. Go to booking details page
2. **Verify:** Reservation breakdown shows USD account with $500 reserved
3. Click "Complete" booking
4. **Verify:**
   - USD account: Balance stays reduced (USD given to customer)
   - CBN account: Balance increased by (cbn_total + markup_amount)
   - Difference account: Balance increased by difference_amount
   - Reservation status changed to "completed"

#### Scenario 2: USD to NGN Transaction (Multi-Account)
**Objective:** Test multi-account reservation and proper USD crediting

**Setup:** Ensure CBN account has insufficient balance for the full transaction

**Steps:**
1. Go to `/admin/forex/bookings/create`
2. Fill in client details
3. Select "Selling USD (Client pays USD, receives NGN)"
4. **Verify:** Target account auto-selects to CBN account
5. Enter large NGN amount that exceeds CBN balance
6. Submit booking
7. **Verify:**
   - Booking created successfully
   - Multiple accounts debited in priority order (CBN → Difference → Investment)
   - Multiple reservation records created
   - Total reserved amount equals customer_total

**Completion Test:**
1. Go to booking details page
2. **Verify:** Reservation breakdown shows multiple accounts
3. Click "Complete" booking
4. **Verify:**
   - All NGN accounts: Balances stay reduced (NGN given to customer)
   - USD account: Balance increased by booking amount
   - All reservations status changed to "completed"

#### Scenario 3: Booking Cancellation (Multi-Account)
**Objective:** Test that all reservations are properly released

**Steps:**
1. Create a USD to NGN booking (multi-account scenario)
2. **Verify:** Multiple accounts have reduced balances
3. Go to booking details and click "Cancel"
4. Enter cancellation reason
5. **Verify:**
   - All account balances restored to original amounts
   - All reservations status changed to "cancelled"
   - Cancellation transactions created for each account

#### Scenario 4: Insufficient Balance Validation
**Objective:** Test that bookings are rejected when insufficient balance

**Steps:**
1. Try to create NGN to USD booking for amount > USD account balance
2. **Verify:** Booking creation fails with appropriate error
3. Try to create USD to NGN booking for amount > total NGN across all accounts
4. **Verify:** Booking creation fails with appropriate error

### Database Verification Queries

```sql
-- Check reservation records
SELECT
    fbr.*,
    fb.booking_reference,
    fa.account_name,
    fa.account_type
FROM forex_booking_reservations fbr
JOIN forex_bookings fb ON fbr.forex_booking_id = fb.id
JOIN forex_accounts fa ON fbr.forex_account_id = fa.id
ORDER BY fbr.created_at DESC;

-- Check account balances
SELECT
    account_name,
    account_type,
    currency_code,
    balance,
    pending_balance,
    available_balance
FROM forex_accounts
WHERE is_active = 1;

-- Check recent transactions
SELECT
    ft.*,
    fa.account_name,
    fb.booking_reference
FROM forex_transactions ft
JOIN forex_accounts fa ON ft.forex_account_id = fa.id
LEFT JOIN forex_bookings fb ON ft.forex_booking_id = fb.id
ORDER BY ft.created_at DESC
LIMIT 20;
```

### Expected Results Summary

| Transaction Type | Booking Creation | Completion | Cancellation |
|-----------------|------------------|------------|--------------|
| NGN to USD | Reserve USD from USD account | Keep USD debited, credit NGN to CBN+Difference | Release USD reservation |
| USD to NGN | Reserve NGN from multiple accounts | Keep NGN debited, credit USD to USD account | Release all NGN reservations |

### Troubleshooting

**Issue:** Auto-selection not working
- **Solution:** Clear browser cache, check JavaScript console for errors

**Issue:** Reservation not showing in booking details
- **Solution:** Verify `reservations.account` relationship is loaded in controller

**Issue:** Multi-account distribution not working
- **Solution:** Check account balances, verify priority order logic

**Issue:** Completion not crediting accounts
- **Solution:** Check `processBookingTransactions` is called in `completeBooking` method
