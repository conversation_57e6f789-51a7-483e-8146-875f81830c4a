@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">@lang('Forex Exchange Rates')</h1>
                    <p class="page-header-text">@lang('Manage CBN and parallel market exchange rates') ({{ $totalRates ?? 0 }} total rates)</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-primary" href="{{ route('admin.forex.rates.create') }}">
                        <i class="bi-plus me-1"></i> @lang('Add New Rate')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Current Active Rate -->
        @if($activeRate)
            <div class="alert alert-soft-success mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="bi-check-circle-fill"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('Current Active Rate')</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>@lang('Buy Rates (NGN to USD)'):</strong></p>
                                <p class="mb-0">
                                    <strong>@lang('CBN'):</strong> ₦{{ $activeRate->formatted_cbn_rate }} |
                                    <strong>@lang('Parallel'):</strong> ₦{{ $activeRate->formatted_parallel_rate }} |
                                    <strong>@lang('Markup'):</strong> {{ $activeRate->markup_percentage }}%
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>@lang('Sell Rates (USD to NGN)'):</strong></p>
                                <p class="mb-0">
                                    <strong>@lang('CBN'):</strong> ₦{{ $activeRate->formatted_cbn_sell_rate }} |
                                    <strong>@lang('Parallel'):</strong> ₦{{ $activeRate->formatted_parallel_sell_rate }} |
                                    <strong>@lang('Markup'):</strong> {{ $activeRate->sell_markup_percentage }}%
                                </p>
                            </div>
                        </div>
                        <small class="text-muted">@lang('Last updated'): {{ $activeRate->updated_at->diffForHumans() }}</small>
                    </div>
                </div>
            </div>
        @else
            <div class="alert alert-soft-warning mb-4" role="alert">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="bi-exclamation-triangle-fill"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h4 class="alert-heading">@lang('No Active Rate Set')</h4>
                        <p class="mb-0">@lang('Please set an active exchange rate to enable forex trading operations.')</p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Card -->
        <div class="card">
            <!-- Header -->
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <div class="input-group input-group-merge input-group-flush">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input id="datatableSearch" type="search" class="form-control" placeholder="@lang('Search rates')" aria-label="@lang('Search rates')">
                    </div>
                </div>

                <div class="d-grid d-sm-flex gap-2">
                    <div class="dropdown">
                        <button type="button" class="btn btn-white btn-sm dropdown-toggle w-100" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi-filter me-1"></i> @lang('Filter')
                        </button>
                        <div class="dropdown-menu dropdown-menu-sm-end dropdown-card card-dropdown-filter-centered" aria-labelledby="filterDropdown">
                            <div class="card card-sm">
                                <div class="card-body card-body-centered">
                                    <form>
                                        <div class="row">
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('Status')</span>
                                                <select class="form-select" id="filter_status">
                                                    <option value="">@lang('All Status')</option>
                                                    <option value="pending">@lang('Pending')</option>
                                                    <option value="approved">@lang('Approved')</option>
                                                    <option value="rejected">@lang('Rejected')</option>
                                                </select>
                                            </div>
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('Active Status')</span>
                                                <select class="form-select" id="filter_is_active">
                                                    <option value="">@lang('All')</option>
                                                    <option value="1">@lang('Active')</option>
                                                    <option value="0">@lang('Inactive')</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row gx-2 mt-4">
                                            <div class="col">
                                                <button type="button" id="clear_filter" class="btn btn-white w-100">@lang('Clear Filters')</button>
                                            </div>
                                            <div class="col">
                                                <button type="button" class="btn btn-primary w-100" id="filter_button">
                                                    <i class="bi-search"></i> @lang('Apply')
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Header -->

            <!-- Table -->
            <div class="table-responsive datatable-custom">
                <table id="datatable"
                       class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                       "columnDefs": [{
                          "targets": [0, 5],
                          "orderable": false
                        }],
                       "order": [],
                       "info": {
                         "totalQty": "#datatableWithPaginationInfoTotalQty"
                       },
                       "search": "#datatableSearch",
                       "entries": "#datatableEntries",
                       "pageLength": 15,
                       "isResponsive": false,
                       "isShowPaging": false,
                       "pagination": "datatablePagination"
                     }'>
                    <thead class="thead-light">
                    <tr>
                        <th class="table-column-pe-0">
                            <div class="form-check">
                                <input class="form-check-input check-all tic-check" type="checkbox" name="check-all"
                                       id="datatableCheckAll">
                                <label class="form-check-label" for="datatableCheckAll"></label>
                            </div>
                        </th>
                        <th>@lang('Rate Info')</th>
                        <th>@lang('Markup')</th>
                        <th>@lang('Status')</th>
                        <th>@lang('Created By')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <!-- End Table -->

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing'):</span>
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto" autocomplete="off"
                                        data-hs-tom-select-options='{
                                            "searchInDropdown": false,
                                            "hideSearch": true
                                        }'>
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="20">20</option>
                                </select>
                            </div>
                            <span class="text-secondary me-2">@lang('of')</span>
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Card -->
    </div>

    <!-- Approve Rate Modal -->
    <div class="modal fade" id="approveRateModal" tabindex="-1" aria-labelledby="approveRateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approveRateModalLabel">@lang('Approve Exchange Rate')</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="approveRateForm">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="approvalNotes" class="form-label">@lang('Approval Notes') <span class="text-muted">(@lang('Optional'))</span></label>
                            <textarea class="form-control" id="approvalNotes" name="approval_notes" rows="3" placeholder="@lang('Add any notes about this approval...')"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-white" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn-success">@lang('Approve Rate')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reject Rate Modal -->
    <div class="modal fade" id="rejectRateModal" tabindex="-1" aria-labelledby="rejectRateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectRateModalLabel">@lang('Reject Exchange Rate')</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="rejectRateForm">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="rejectionNotes" class="form-label">@lang('Rejection Reason') <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectionNotes" name="approval_notes" rows="3" placeholder="@lang('Please provide a reason for rejecting this rate...')" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-white" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn-danger">@lang('Reject Rate')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

<x-assets :datatable="true" :counter="true"/>

@push('script')
<script>
    'use strict';

    $(document).on('ready', function () {
        initDataTable("{{ route('admin.forex.rates.search') }}",
            {!! json_encode(['checkbox', 'rate_info', 'markup', 'status', 'created_info', 'action']) !!}
        );

        document.getElementById("filter_button").addEventListener("click", function () {
            applyFilter("{{ route('admin.forex.rates.search') }}", {
                status: $('#filter_status').val(),
                is_active: $('#filter_is_active').val()
            });
        });

        document.getElementById("clear_filter").addEventListener("click", function () {
            $('#filter_status').val('').trigger('change');
            $('#filter_is_active').val('').trigger('change');
            applyFilter("{{ route('admin.forex.rates.search') }}", {});
        });

        // Approve rate
        $(document).on('click', '.approve-rate', function() {
            let rateId = $(this).data('id');
            $('#approveRateForm').data('rate-id', rateId);
            $('#approveRateModal').modal('show');
        });

        $('#approveRateForm').on('submit', function(e) {
            e.preventDefault();
            let rateId = $(this).data('rate-id');
            let url = `{{ route('admin.forex.rates.approve', ':id') }}`.replace(':id', rateId);
            let csrfToken = $('meta[name="csrf-token"]').attr('content');
            let approvalNotes = $('#approvalNotes').val();

            let requestData = {
                _token: csrfToken,
                approval_notes: approvalNotes
            };

            $.ajax({
                url: url,
                type: 'POST',
                data: requestData,
                dataType: 'json'
            })
                .done(function(response) {
                    if (response.success) {
                        $('#approveRateModal').modal('hide');
                        const datatable = HSCore.components.HSDatatables.getItem(0);
                        datatable.ajax.reload();
                        Notiflix.Notify.success(response.message);
                    }
                })
                .fail(function(xhr) {
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        let errors = xhr.responseJSON.errors;
                        Object.keys(errors).forEach(function(key) {
                            Notiflix.Notify.failure(errors[key][0]);
                        });
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        Notiflix.Notify.failure(xhr.responseJSON.message);
                    } else {
                        Notiflix.Notify.failure('An error occurred. Please try again.');
                    }
                });
        });

        // Reject rate
        $(document).on('click', '.reject-rate', function() {
            let rateId = $(this).data('id');
            $('#rejectRateForm').data('rate-id', rateId);
            $('#rejectRateModal').modal('show');
        });

        $('#rejectRateForm').on('submit', function(e) {
            e.preventDefault();
            let rateId = $(this).data('rate-id');
            let url = `{{ route('admin.forex.rates.reject', ':id') }}`.replace(':id', rateId);
            let csrfToken = $('meta[name="csrf-token"]').attr('content');
            let rejectionNotes = $('#rejectionNotes').val();

            let requestData = {
                _token: csrfToken,
                approval_notes: rejectionNotes
            };

            console.log('Reject URL:', url);
            console.log('Request Data:', requestData);

            $.ajax({
                url: url,
                type: 'POST',
                data: requestData,
                dataType: 'json'
            })
                .done(function(response) {
                    if (response.success) {
                        $('#rejectRateModal').modal('hide');
                        const datatable = HSCore.components.HSDatatables.getItem(0);
                        datatable.ajax.reload();
                        Notiflix.Notify.success(response.message);
                    }
                })
                .fail(function(xhr) {
                    console.log('AJAX Error:', xhr);
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        let errors = xhr.responseJSON.errors;
                        Object.keys(errors).forEach(function(key) {
                            Notiflix.Notify.failure(errors[key][0]);
                        });
                    } else if (xhr.responseJSON && xhr.responseJSON.message) {
                        Notiflix.Notify.failure(xhr.responseJSON.message);
                    } else {
                        Notiflix.Notify.failure('An error occurred. Please try again.');
                    }
                });
        });
    });
</script>
@endpush
