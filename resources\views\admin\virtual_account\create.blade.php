@extends('admin.layouts.app')
@section('page_title', __('Create Virtual Account'))

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.dashboard') }}">@lang('Dashboard')</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.virtual.accounts.index') }}">@lang('Virtual Accounts')</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Create Virtual Account')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Create Virtual Account')</h1>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Card -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Virtual Account Information')</h4>
                    </div>

                    <div class="card-body">
                        <form action="{{ route('admin.virtual.accounts.store') }}" method="POST">
                            @csrf

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="user_id" class="form-label">@lang('User') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Select the user for whom to create the virtual account')"></i></label>
                                        <select class="form-select" name="user_id" id="user_id" required>
                                            <option value="">@lang('Select User')</option>
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                                    {{ $user->name }} ({{ $user->email }}) - {{ ucfirst($user->type ?? 'user') }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('user_id')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="currency" class="form-label">@lang('Currency') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Select the currency for the virtual account')"></i></label>
                                        <select class="form-select" name="currency" id="currency" required>
                                            <option value="">@lang('Select Currency')</option>
                                            @foreach($currencies as $currency)
                                                <option value="{{ $currency->code }}" {{ old('currency') == $currency->code ? 'selected' : '' }}>
                                                    {{ $currency->code }} - {{ $currency->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('currency')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="type" class="form-label">@lang('Account Type') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Select the type of virtual account to create')"></i></label>
                                        <select class="form-select" name="type" id="type" required>
                                            <option value="">@lang('Select Account Type')</option>
                                            <option value="customer" {{ old('type') == 'customer' ? 'selected' : '' }}>
                                                @lang('Customer Account')
                                            </option>
                                            {{-- <option value="individual" {{ old('type') == 'individual' ? 'selected' : '' }}>
                                                @lang('Individual Account')
                                            </option> --}}
                                            <option value="business" {{ old('type') == 'business' ? 'selected' : '' }}>
                                                @lang('Business Account')
                                            </option>
                                        </select>
                                        @error('type')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="provider" class="form-label">@lang('Provider') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Select the virtual account provider')"></i></label>
                                        <select class="form-select" name="provider" id="provider">
                                            <option value="numero" {{ old('provider', 'numero') == 'numero' ? 'selected' : '' }}>
                                                Numero
                                            </option>
                                        </select>
                                        @error('provider')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <i class="bi-info-circle"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="alert-heading">@lang('Important Notes')</h5>
                                        <ul class="mb-0">
                                            <li>@lang('The user must have completed and verified their KYC information')</li>
                                            {{-- <li>@lang('Customer accounts are for regular users (default for users)')</li> --}}
                                            <li>@lang('Individual accounts are for personal use, business accounts are for commercial use')</li>
                                            <li>@lang('Each user can have one account per type per currency')</li>
                                            <li>@lang('Virtual account creation may take a few moments to complete')</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <div class="d-flex gap-3">
                                    <a href="{{ route('admin.virtual.accounts.index') }}" class="btn btn-white">@lang('Cancel')</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi-plus-circle me-1"></i>@lang('Create Virtual Account')
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Card -->
    </div>
@endsection

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
@endpush

@push('js-lib')
    <script src="{{ asset('assets/admin/js/tom-select.complete.min.js') }}"></script>
@endpush

@push('script')
    <script>
        'use strict';
        $(document).ready(function () {
            HSCore.components.HSTomSelect.init('.js-select');

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize Tom Select for better dropdowns
            new TomSelect('#user_id', {
                placeholder: '@lang("Select User")',
                allowEmptyOption: true,
                render: {
                    option: function(data, escape) {
                        return '<div>' +
                            '<span class="fw-bold">' + escape(data.text.split(' (')[0]) + '</span><br>' +
                            '<small class="text-muted">' + escape(data.text.split(' (')[1]) + '</small>' +
                        '</div>';
                    }
                }
            });

            new TomSelect('#currency', {
                placeholder: '@lang("Select Currency")',
                allowEmptyOption: true
            });

            new TomSelect('#type', {
                placeholder: '@lang("Select Account Type")',
                allowEmptyOption: true,
                render: {
                    option: function(data, escape) {
                        const icon = data.value === 'business' ? 'bi-building' : 'bi-person';
                        return '<div class="d-flex align-items-center">' +
                            '<i class="' + icon + ' me-2"></i>' +
                            '<span>' + escape(data.text) + '</span>' +
                        '</div>';
                    }
                }
            });
        });
    </script>
@endpush
