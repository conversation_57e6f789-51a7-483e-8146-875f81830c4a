<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;

/**
 * Admin Menu Helper
 * 
 * Provides permission-based menu rendering for the admin dashboard.
 */
class AdminMenuHelper
{
    /**
     * Get the complete admin menu structure with permission checking
     */
    public static function getMenuStructure(): array
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin) {
            return [];
        }

        $menu = [
            [
                'title' => 'Dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'route' => 'admin.dashboard',
                'permission' => 'dashboard.admin',
                'active_patterns' => ['admin/dashboard*'],
            ],
            [
                'title' => 'User & Role Management',
                'icon' => 'fas fa-users-cog',
                'permission' => 'user_roles.read|users.read|advanced_roles.read',
                'active_patterns' => ['admin/users*', 'admin/roles*', 'admin/user-roles*', 'admin/user-role-management*'],
                'children' => [
                    [
                        'title' => 'Dashboard',
                        'icon' => 'fas fa-chart-pie',
                        'route' => 'admin.user-role-management.dashboard',
                        'permission' => 'user_roles.read',
                        'active_patterns' => ['admin/user-role-management'],
                    ],
                    [
                        'title' => 'Manage Users',
                        'icon' => 'fas fa-users',
                        'route' => 'admin.users.index',
                        'permission' => 'users.read',
                        'active_patterns' => ['admin/users*'],
                        'badge' => self::getUsersCount(),
                    ],
                    [
                        'title' => 'Manage Roles',
                        'icon' => 'fas fa-user-shield',
                        'route' => 'admin.roles.index',
                        'permission' => 'advanced_roles.read',
                        'active_patterns' => ['admin/roles*'],
                        'badge' => self::getRolesCount(),
                    ],
                    [
                        'title' => 'Role Assignments',
                        'icon' => 'fas fa-link',
                        'route' => 'admin.user-roles.index',
                        'permission' => 'user_roles.read',
                        'active_patterns' => ['admin/user-roles*'],
                    ],
                    [
                        'title' => 'Permissions',
                        'icon' => 'fas fa-key',
                        'route' => 'admin.permissions.index',
                        'permission' => 'advanced_permissions.read',
                        'active_patterns' => ['admin/permissions*'],
                    ],
                ],
            ],
            [
                'title' => 'Merchants',
                'icon' => 'fas fa-store',
                'permission' => 'merchants.read',
                'active_patterns' => ['admin/merchants*'],
                'children' => [
                    [
                        'title' => 'All Merchants',
                        'icon' => 'fas fa-list',
                        'route' => 'admin.merchants.index',
                        'permission' => 'merchants.read',
                        'active_patterns' => ['admin/merchants'],
                    ],
                    [
                        'title' => 'Add Merchant',
                        'icon' => 'fas fa-plus',
                        'route' => 'admin.merchants.create',
                        'permission' => 'merchants.create',
                        'active_patterns' => ['admin/merchants/create'],
                    ],
                    [
                        'title' => 'Pending Approval',
                        'icon' => 'fas fa-clock',
                        'route' => 'admin.merchants.pending',
                        'permission' => 'merchants.approve',
                        'active_patterns' => ['admin/merchants/pending'],
                        'badge' => self::getPendingMerchantsCount(),
                        'badge_class' => 'bg-warning',
                    ],
                ],
            ],
            [
                'title' => 'Financial Management',
                'icon' => 'fas fa-coins',
                'permission' => 'finance.read|transactions.read|forex.rates.read',
                'active_patterns' => ['admin/transactions*', 'admin/forex*', 'admin/payouts*', 'admin/deposits*'],
                'children' => [
                    [
                        'title' => 'Transactions',
                        'icon' => 'fas fa-exchange-alt',
                        'route' => 'admin.transactions.index',
                        'permission' => 'transactions.read',
                        'active_patterns' => ['admin/transactions*'],
                    ],
                    [
                        'title' => 'Forex Trading',
                        'icon' => 'fas fa-chart-line',
                        'permission' => 'forex.rates.read',
                        'active_patterns' => ['admin/forex*'],
                        'children' => [
                            [
                                'title' => 'Exchange Rates',
                                'route' => 'admin.forex.rates.index',
                                'permission' => 'forex.rates.read',
                            ],
                            [
                                'title' => 'Trading Accounts',
                                'route' => 'admin.forex.accounts.index',
                                'permission' => 'forex.accounts.read',
                            ],
                            [
                                'title' => 'Bookings',
                                'route' => 'admin.forex.bookings.index',
                                'permission' => 'forex.bookings.read',
                            ],
                        ],
                    ],
                    [
                        'title' => 'Payouts',
                        'icon' => 'fas fa-money-bill-wave',
                        'route' => 'admin.payouts.index',
                        'permission' => 'payouts.read',
                        'active_patterns' => ['admin/payouts*'],
                    ],
                    [
                        'title' => 'Deposits',
                        'icon' => 'fas fa-piggy-bank',
                        'route' => 'admin.deposits.index',
                        'permission' => 'deposits.read',
                        'active_patterns' => ['admin/deposits*'],
                    ],
                    [
                        'title' => 'Virtual Accounts',
                        'icon' => 'fas fa-university',
                        'route' => 'admin.virtual-accounts.index',
                        'permission' => 'virtual_accounts.read',
                        'active_patterns' => ['admin/virtual-accounts*'],
                    ],
                ],
            ],
            [
                'title' => 'Compliance & KYC',
                'icon' => 'fas fa-shield-alt',
                'permission' => 'kyc.read|compliance.reports',
                'active_patterns' => ['admin/kyc*', 'admin/compliance*'],
                'children' => [
                    [
                        'title' => 'KYC Submissions',
                        'icon' => 'fas fa-id-card',
                        'route' => 'admin.kyc.index',
                        'permission' => 'kyc.read',
                        'active_patterns' => ['admin/kyc*'],
                        'badge' => self::getPendingKycCount(),
                        'badge_class' => 'bg-info',
                    ],
                    [
                        'title' => 'Compliance Reports',
                        'icon' => 'fas fa-file-alt',
                        'route' => 'admin.compliance.reports',
                        'permission' => 'compliance.reports',
                        'active_patterns' => ['admin/compliance*'],
                    ],
                    [
                        'title' => 'Audit Logs',
                        'icon' => 'fas fa-history',
                        'route' => 'admin.audit.index',
                        'permission' => 'audit.read',
                        'active_patterns' => ['admin/audit*'],
                    ],
                ],
            ],
            [
                'title' => 'Support & Tickets',
                'icon' => 'fas fa-headset',
                'permission' => 'tickets.read|disputes.read',
                'active_patterns' => ['admin/tickets*', 'admin/disputes*'],
                'children' => [
                    [
                        'title' => 'Support Tickets',
                        'icon' => 'fas fa-ticket-alt',
                        'route' => 'admin.tickets.index',
                        'permission' => 'tickets.read',
                        'active_patterns' => ['admin/tickets*'],
                        'badge' => self::getOpenTicketsCount(),
                        'badge_class' => 'bg-danger',
                    ],
                    [
                        'title' => 'Disputes',
                        'icon' => 'fas fa-gavel',
                        'route' => 'admin.disputes.index',
                        'permission' => 'disputes.read',
                        'active_patterns' => ['admin/disputes*'],
                    ],
                ],
            ],
            [
                'title' => 'Reports & Analytics',
                'icon' => 'fas fa-chart-bar',
                'permission' => 'reports.read|analytics.read',
                'active_patterns' => ['admin/reports*', 'admin/analytics*'],
                'children' => [
                    [
                        'title' => 'Financial Reports',
                        'icon' => 'fas fa-chart-pie',
                        'route' => 'admin.reports.financial',
                        'permission' => 'reports.read',
                        'active_patterns' => ['admin/reports/financial*'],
                    ],
                    [
                        'title' => 'User Analytics',
                        'icon' => 'fas fa-users',
                        'route' => 'admin.analytics.users',
                        'permission' => 'analytics.read',
                        'active_patterns' => ['admin/analytics/users*'],
                    ],
                    [
                        'title' => 'Transaction Analytics',
                        'icon' => 'fas fa-chart-line',
                        'route' => 'admin.analytics.transactions',
                        'permission' => 'analytics.read',
                        'active_patterns' => ['admin/analytics/transactions*'],
                    ],
                ],
            ],
            [
                'title' => 'System Settings',
                'icon' => 'fas fa-cogs',
                'permission' => 'system.settings|system.admin',
                'active_patterns' => ['admin/settings*', 'admin/system*'],
                'children' => [
                    [
                        'title' => 'General Settings',
                        'icon' => 'fas fa-cog',
                        'route' => 'admin.settings.general',
                        'permission' => 'system.settings',
                        'active_patterns' => ['admin/settings/general*'],
                    ],
                    [
                        'title' => 'Email Templates',
                        'icon' => 'fas fa-envelope',
                        'route' => 'admin.settings.email-templates',
                        'permission' => 'templates.read',
                        'active_patterns' => ['admin/settings/email-templates*'],
                    ],
                    [
                        'title' => 'System Logs',
                        'icon' => 'fas fa-file-code',
                        'route' => 'admin.system.logs',
                        'permission' => 'system.logs',
                        'active_patterns' => ['admin/system/logs*'],
                    ],
                    [
                        'title' => 'Cache Management',
                        'icon' => 'fas fa-database',
                        'route' => 'admin.system.cache',
                        'permission' => 'cache.view',
                        'active_patterns' => ['admin/system/cache*'],
                    ],
                ],
            ],
        ];

        return self::filterMenuByPermissions($menu, $admin);
    }

    /**
     * Filter menu items based on user permissions
     */
    protected static function filterMenuByPermissions(array $menu, $admin): array
    {
        $filteredMenu = [];

        foreach ($menu as $item) {
            // Check if user has permission for this menu item
            if (isset($item['permission']) && !self::hasMenuPermission($admin, $item['permission'])) {
                continue;
            }

            // Filter children if they exist
            if (isset($item['children'])) {
                $item['children'] = self::filterMenuByPermissions($item['children'], $admin);
                
                // If no children remain after filtering, skip this parent item
                if (empty($item['children'])) {
                    continue;
                }
            }

            $filteredMenu[] = $item;
        }

        return $filteredMenu;
    }

    /**
     * Check if admin has permission for menu item
     */
    protected static function hasMenuPermission($admin, string $permission): bool
    {
        // Super admin has access to everything
        if (method_exists($admin, 'isSuperAdmin') && $admin->isSuperAdmin()) {
            return true;
        }

        // Handle OR permissions (permission1|permission2)
        if (strpos($permission, '|') !== false) {
            $permissions = explode('|', $permission);
            foreach ($permissions as $perm) {
                if ($admin->hasAdvancedPermission(trim($perm))) {
                    return true;
                }
            }
            return false;
        }

        // Handle AND permissions (permission1&permission2)
        if (strpos($permission, '&') !== false) {
            $permissions = explode('&', $permission);
            foreach ($permissions as $perm) {
                if (!$admin->hasAdvancedPermission(trim($perm))) {
                    return false;
                }
            }
            return true;
        }

        // Single permission
        return $admin->hasAdvancedPermission($permission);
    }

    /**
     * Check if current route matches menu item
     */
    public static function isMenuItemActive(array $item): bool
    {
        $currentRoute = request()->route()->getName();
        $currentPath = request()->path();

        // Check route name
        if (isset($item['route']) && $currentRoute === $item['route']) {
            return true;
        }

        // Check active patterns
        if (isset($item['active_patterns'])) {
            foreach ($item['active_patterns'] as $pattern) {
                if (fnmatch($pattern, $currentPath)) {
                    return true;
                }
            }
        }

        // Check children
        if (isset($item['children'])) {
            foreach ($item['children'] as $child) {
                if (self::isMenuItemActive($child)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get users count for badge
     */
    protected static function getUsersCount(): ?int
    {
        try {
            return \App\Models\User::count();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get roles count for badge
     */
    protected static function getRolesCount(): ?int
    {
        try {
            return \App\Models\AdvancedRole::count();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get pending merchants count for badge
     */
    protected static function getPendingMerchantsCount(): ?int
    {
        try {
            return \App\Models\Merchant::where('status', 'pending')->count();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get pending KYC count for badge
     */
    protected static function getPendingKycCount(): ?int
    {
        try {
            return \App\Models\CustomerInformation::where('status', 'pending')->count();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get open tickets count for badge
     */
    protected static function getOpenTicketsCount(): ?int
    {
        try {
            return \App\Models\Ticket::where('status', 'open')->count();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Generate menu HTML
     */
    public static function renderMenu(): string
    {
        $menu = self::getMenuStructure();
        return view('admin.partials.menu', compact('menu'))->render();
    }

    /**
     * Get breadcrumb for current page
     */
    public static function getBreadcrumb(): array
    {
        $menu = self::getMenuStructure();
        $currentRoute = request()->route()->getName();
        
        return self::findBreadcrumbPath($menu, $currentRoute);
    }

    /**
     * Find breadcrumb path recursively
     */
    protected static function findBreadcrumbPath(array $menu, string $currentRoute, array $path = []): array
    {
        foreach ($menu as $item) {
            $currentPath = array_merge($path, [$item['title']]);
            
            if (isset($item['route']) && $item['route'] === $currentRoute) {
                return $currentPath;
            }
            
            if (isset($item['children'])) {
                $result = self::findBreadcrumbPath($item['children'], $currentRoute, $currentPath);
                if (!empty($result)) {
                    return $result;
                }
            }
        }
        
        return [];
    }
}
