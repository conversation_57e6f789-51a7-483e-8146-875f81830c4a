# Webhook API Documentation

This document describes the webhook management API endpoints for the Numero payout integration.

## Authentication

All webhook endpoints require authentication using the `auth:sanctum` middleware. Include your API token in the Authorization header:

```
Authorization: Bearer {your-api-token}
```

## Base URL

All endpoints are prefixed with `/api/`

## Endpoints

### 1. Get Supported Providers

Get a list of supported webhook providers and their capabilities.

**Endpoint:** `GET /webhook/providers`

**Response:**
```json
{
    "status": "success",
    "message": {
        "providers": [
            {
                "code": "numero",
                "name": "Numero",
                "description": "Numero webhook management",
                "supported_events": [
                    "transfer.success",
                    "transfer.failed",
                    "transfer.pending"
                ]
            }
        ],
        "message": "Supported webhook providers retrieved successfully"
    }
}
```

### 2. Subscribe to Webhook

Subscribe to webhook notifications for a specific provider.

**Endpoint:** `POST /webhook/subscribe`

**Request Body:**
```json
{
    "webhook_url": "https://your-domain.com/webhook/numero",
    "events": ["transfer.success", "transfer.failed", "transfer.pending"],
    "provider": "numero"
}
```

**Response:**
```json
{
    "status": "success",
    "message": {
        "status": "success",
        "data": {
            "webhook_id": "webhook_123456",
            "url": "https://your-domain.com/webhook/numero",
            "events": ["transfer.success", "transfer.failed", "transfer.pending"],
            "created_at": "2024-01-01T00:00:00Z"
        },
        "message": "Webhook subscribed successfully"
    }
}
```

### 3. Get Webhook Secret

Retrieve the webhook secret token for signature verification.

**Endpoint:** `GET /webhook/secret`

**Query Parameters:**
- `provider` (required): The webhook provider (e.g., "numero")

**Response:**
```json
{
    "status": "success",
    "message": {
        "status": "success",
        "data": {
            "secret": "your_webhook_secret_token"
        },
        "message": "Webhook secret retrieved successfully"
    }
}
```

### 4. Get All Webhooks

Retrieve a list of all configured webhooks for a provider.

**Endpoint:** `GET /webhook/list`

**Query Parameters:**
- `provider` (required): The webhook provider (e.g., "numero")

**Response:**
```json
{
    "status": "success",
    "message": {
        "status": "success",
        "data": [
            {
                "webhook_id": "webhook_123456",
                "url": "https://your-domain.com/webhook/numero",
                "events": ["transfer.success", "transfer.failed"],
                "status": "active",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "message": "Webhooks retrieved successfully"
    }
}
```

### 5. Update Webhook

Update an existing webhook configuration.

**Endpoint:** `POST /webhook/update`

**Request Body:**
```json
{
    "webhook_id": "webhook_123456",
    "webhook_url": "https://your-new-domain.com/webhook/numero",
    "events": ["transfer.success", "transfer.failed"],
    "provider": "numero"
}
```

**Response:**
```json
{
    "status": "success",
    "message": {
        "status": "success",
        "data": {
            "webhook_id": "webhook_123456",
            "url": "https://your-new-domain.com/webhook/numero",
            "events": ["transfer.success", "transfer.failed"],
            "updated_at": "2024-01-01T00:00:00Z"
        },
        "message": "Webhook updated successfully"
    }
}
```

### 6. Unsubscribe Webhook

Remove a webhook subscription.

**Endpoint:** `POST /webhook/unsubscribe`

**Request Body:**
```json
{
    "webhook_id": "webhook_123456",
    "provider": "numero"
}
```

**Response:**
```json
{
    "status": "success",
    "message": {
        "status": "success",
        "data": {},
        "message": "Webhook unsubscribed successfully"
    }
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
    "status": "failed",
    "message": "Error description"
}
```

Common error scenarios:
- Missing or invalid authentication token (401)
- Invalid request parameters (422)
- Provider not found or not supported (400)
- API credentials missing or invalid (400)

## Webhook Events

### Numero Events

- `transfer.success`: Transfer completed successfully
- `transfer.failed`: Transfer failed
- `transfer.pending`: Transfer is pending processing

## Security

- All webhook URLs must use HTTPS
- Webhook payloads are signed using HMAC-SHA256
- Use the webhook secret to verify payload authenticity
- Store webhook secrets securely and rotate them regularly

## Rate Limiting

Webhook management endpoints are subject to standard API rate limiting. Ensure your application handles rate limit responses appropriately.
