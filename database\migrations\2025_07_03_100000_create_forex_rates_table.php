<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_rates', function (Blueprint $table) {
            $table->id();
            $table->string('rate_type')->comment('CBN, Parallel Market');
            $table->decimal('cbn_rate', 18, 8)->nullable()->default(0.00000000)->comment('CBN buy rate');
            $table->decimal('parallel_rate', 18, 8)->nullable()->default(0.00000000)->comment('Parallel buy rate');
            $table->decimal('markup_percentage', 5, 2)->nullable()->default(0.00)->comment('Markup percentage for buy rates');
            $table->decimal('cbn_sell_rate', 18, 8)->nullable()->default(0.00000000)->comment('CBN sell rate');
            $table->decimal('parallel_sell_rate', 18, 8)->nullable()->default(0.00000000)->comment('Parallel sell rate');
            $table->decimal('sell_markup_percentage', 5, 2)->nullable()->default(0.00)->comment('Markup percentage for sell rates');
            $table->boolean('is_active')->default(false)->comment('Only one rate set can be active at a time');
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('approval_notes')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('admins')->onDelete('set null');
            $table->foreignId('approved_by')->nullable()->constrained('admins')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            $table->index(['is_active', 'status']);
            $table->index('created_by');
            $table->index('approved_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_rates');
    }
};
