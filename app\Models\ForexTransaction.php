<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForexTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'transaction_reference',
        'forex_booking_id',
        'forex_account_id',
        'transaction_type',
        'transaction_subtype',
        'currency',
        'amount',
        'balance_before',
        'balance_after',
        'description',
        'notes',
        'created_by',
        'is_completed',
        'related_transaction_id',
        'metadata'
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'balance_before' => 'decimal:8',
        'balance_after' => 'decimal:8',
        'is_completed' => 'boolean',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function (ForexTransaction $transaction) {
            if (empty($transaction->transaction_reference)) {
                $transaction->transaction_reference = $transaction->generateTransactionReference();
            }
        });
    }

    // Relationships
    public function forexBooking()
    {
        return $this->belongsTo(ForexBooking::class, 'forex_booking_id');
    }

    public function relatedTransaction()
    {
        return $this->belongsTo(ForexTransaction::class, 'related_transaction_id');
    }

    public function completionTransaction()
    {
        return $this->hasOne(ForexTransaction::class, 'related_transaction_id');
    }

    public function forexAccount()
    {
        return $this->belongsTo(ForexAccount::class, 'forex_account_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    // Scopes
    public function scopeCredits($query)
    {
        return $query->where('transaction_type', 'credit');
    }

    public function scopeDebits($query)
    {
        return $query->where('transaction_type', 'debit');
    }

    public function scopeTransfers($query)
    {
        return $query->where('transaction_type', 'transfer');
    }

    public function scopePending($query)
    {
        return $query->where('transaction_type', 'pending');
    }

    public function scopePendingRelease($query)
    {
        return $query->where('transaction_type', 'pending_release');
    }

    public function scopePendingCancelled($query)
    {
        return $query->where('transaction_type', 'pending_cancelled');
    }

    public function scopePendingCleared($query)
    {
        return $query->where('transaction_type', 'pending_cleared');
    }

    public function scopeByAccount($query, $accountId)
    {
        return $query->where('forex_account_id', $accountId);
    }

    public function scopeByBooking($query, $bookingId)
    {
        return $query->where('forex_booking_id', $bookingId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // Accessors
    public function getTypeClassAttribute()
    {
        return [
            'credit' => 'success',
            'debit' => 'danger',
            'transfer' => 'info',
            'pending' => 'warning',
            'pending_release' => 'success',
            'pending_cancelled' => 'info',
            'pending_cleared' => 'primary',
        ][$this->transaction_type] ?? 'secondary';
    }

    public function getFormattedAmountAttribute()
    {
        $sign = match($this->transaction_type) {
            'credit' => '+',
            'debit' => '-',
            'pending' => '~',
            'pending_release' => '↩',
            'pending_cancelled' => '↩',
            'pending_cleared' => '💰',
            'transfer' => '↔',
            default => ''
        };
        return $sign . number_format($this->amount, 2) . ' ' . $this->currency;
    }

    public function getFormattedBalanceBeforeAttribute()
    {
        return number_format($this->balance_before, 2) . ' ' . $this->currency;
    }

    public function getFormattedBalanceAfterAttribute()
    {
        return number_format($this->balance_after, 2) . ' ' . $this->currency;
    }

    // Business Logic Methods
    private function generateTransactionReference()
    {
        return 'FXT' . date('YmdHis') . rand(100, 999);
    }

    public static function getAccountSummary($accountId, $startDate = null, $endDate = null)
    {
        $query = static::byAccount($accountId);

        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }

        return [
            'total_credits' => $query->clone()->credits()->sum('amount'),
            'total_debits' => $query->clone()->debits()->sum('amount'),
            'transaction_count' => $query->count(),
            'net_amount' => $query->clone()->credits()->sum('amount') - $query->clone()->debits()->sum('amount'),
        ];
    }

    public static function getDailySummary($date = null)
    {
        $date = $date ?? today();

        return static::whereDate('created_at', $date)
            ->selectRaw('
                forex_account_id,
                transaction_type,
                currency,
                SUM(amount) as total_amount,
                COUNT(*) as transaction_count
            ')
            ->groupBy(['forex_account_id', 'transaction_type', 'currency'])
            ->with('forexAccount')
            ->get();
    }
}
