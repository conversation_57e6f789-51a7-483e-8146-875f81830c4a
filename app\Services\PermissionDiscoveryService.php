<?php

namespace App\Services;

use App\Models\AdvancedPermission;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionMethod;

/**
 * Permission Discovery Service
 * 
 * Automatically discovers controllers, methods, and resources in the application
 * to generate comprehensive permission lists for the advanced role system.
 */
class PermissionDiscoveryService
{
    /**
     * Standard CRUD actions
     */
    const CRUD_ACTIONS = ['create', 'read', 'update', 'delete'];

    /**
     * Additional common actions
     */
    const COMMON_ACTIONS = [
        'list', 'show', 'store', 'edit', 'destroy', 'index',
        'approve', 'reject', 'activate', 'deactivate', 'suspend',
        'export', 'import', 'download', 'upload', 'search',
        'assign', 'unassign', 'transfer', 'process', 'cancel'
    ];

    /**
     * Controller method patterns to ignore
     */
    const IGNORED_METHODS = [
        '__construct', '__destruct', '__call', '__callStatic',
        'middleware', 'getMiddleware', 'callAction', 'authorize',
        'validate', 'validateWith', 'dispatch', 'dispatchNow'
    ];

    /**
     * Resource categories mapping
     */
    const RESOURCE_CATEGORIES = [
        'users' => 'user_management',
        'admins' => 'admin_management',
        'roles' => 'role_management',
        'permissions' => 'permission_management',
        'transactions' => 'finance',
        'payouts' => 'finance',
        'deposits' => 'finance',
        'forex' => 'finance',
        'currencies' => 'finance',
        'wallets' => 'finance',
        'invoices' => 'finance',
        'vouchers' => 'finance',
        'escrows' => 'finance',
        'transfers' => 'finance',
        'exchanges' => 'finance',
        'kyc' => 'compliance',
        'disputes' => 'support',
        'tickets' => 'support',
        'notifications' => 'communication',
        'templates' => 'communication',
        'reports' => 'reporting',
        'analytics' => 'reporting',
        'logs' => 'system',
        'settings' => 'system',
        'configurations' => 'system',
    ];

    /**
     * Discover all permissions from controllers
     */
    public function discoverAllPermissions(): array
    {
        $permissions = [];
        
        // Discover from controllers
        $controllerPermissions = $this->discoverFromControllers();
        $permissions = array_merge($permissions, $controllerPermissions);
        
        // Discover from routes
        $routePermissions = $this->discoverFromRoutes();
        $permissions = array_merge($permissions, $routePermissions);
        
        // Add standard resource permissions
        $resourcePermissions = $this->generateStandardResourcePermissions();
        $permissions = array_merge($permissions, $resourcePermissions);
        
        // Remove duplicates and sort
        $permissions = $this->deduplicateAndSort($permissions);
        
        return $permissions;
    }

    /**
     * Discover permissions from controller files
     */
    public function discoverFromControllers(): array
    {
        $permissions = [];
        $controllerPaths = [
            app_path('Http/Controllers'),
            app_path('Http/Controllers/Admin'),
            base_path('Modules/*/Http/Controllers'),
        ];

        foreach ($controllerPaths as $path) {
            if (Str::contains($path, '*')) {
                $paths = glob($path);
                foreach ($paths as $modulePath) {
                    $permissions = array_merge($permissions, $this->scanControllerDirectory($modulePath));
                }
            } else {
                $permissions = array_merge($permissions, $this->scanControllerDirectory($path));
            }
        }

        return $permissions;
    }

    /**
     * Scan a controller directory for permissions
     */
    protected function scanControllerDirectory(string $path): array
    {
        if (!File::exists($path)) {
            return [];
        }

        $permissions = [];
        $files = File::allFiles($path);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $controllerPermissions = $this->analyzeControllerFile($file->getPathname());
                $permissions = array_merge($permissions, $controllerPermissions);
            }
        }

        return $permissions;
    }

    /**
     * Analyze a controller file for permissions
     */
    protected function analyzeControllerFile(string $filePath): array
    {
        $permissions = [];
        
        try {
            $className = $this->getClassNameFromFile($filePath);
            if (!$className || !class_exists($className)) {
                return [];
            }

            $reflection = new ReflectionClass($className);
            if (!$reflection->isSubclassOf('Illuminate\Routing\Controller')) {
                return [];
            }

            $resource = $this->extractResourceFromController($className);
            $category = $this->getCategoryForResource($resource);

            $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
            foreach ($methods as $method) {
                if ($this->shouldIgnoreMethod($method->getName())) {
                    continue;
                }

                $action = $this->mapMethodToAction($method->getName());
                $permissionName = "{$resource}.{$action}";
                
                $permissions[] = [
                    'name' => $permissionName,
                    'display_name' => $this->generateDisplayName($resource, $action),
                    'description' => $this->generateDescription($resource, $action, $className, $method->getName()),
                    'resource' => $resource,
                    'action' => $action,
                    'category' => $category,
                    'controller' => $className,
                    'method' => $method->getName(),
                    'is_system' => true,
                ];
            }
        } catch (\Exception $e) {
            // Log error but continue processing
            \Log::warning("Failed to analyze controller file: {$filePath}", ['error' => $e->getMessage()]);
        }

        return $permissions;
    }

    /**
     * Extract class name from PHP file
     */
    protected function getClassNameFromFile(string $filePath): ?string
    {
        $content = File::get($filePath);
        
        // Extract namespace
        preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatches);
        $namespace = $namespaceMatches[1] ?? '';
        
        // Extract class name
        preg_match('/class\s+(\w+)/', $content, $classMatches);
        $className = $classMatches[1] ?? '';
        
        if ($namespace && $className) {
            return $namespace . '\\' . $className;
        }
        
        return null;
    }

    /**
     * Extract resource name from controller class name
     */
    protected function extractResourceFromController(string $className): string
    {
        $baseName = class_basename($className);
        
        // Remove 'Controller' suffix
        $resource = Str::replaceLast('Controller', '', $baseName);
        
        // Convert to snake_case and pluralize
        $resource = Str::snake($resource);
        
        // Handle special cases
        $resource = match ($resource) {
            'admin_user' => 'users',
            'admin_admin' => 'admins',
            'user_kyc' => 'kyc',
            'forex_rate' => 'forex_rates',
            'forex_account' => 'forex_accounts',
            'forex_booking' => 'forex_bookings',
            'forex_transaction' => 'forex_transactions',
            'virtual_account' => 'virtual_accounts',
            'payout_method' => 'payout_methods',
            'notification_template' => 'notification_templates',
            default => Str::plural($resource),
        };
        
        return $resource;
    }

    /**
     * Map controller method to permission action
     */
    protected function mapMethodToAction(string $methodName): string
    {
        return match ($methodName) {
            'index', 'list', 'show', 'search' => 'read',
            'create', 'store' => 'create',
            'edit', 'update' => 'update',
            'destroy', 'delete' => 'delete',
            'approve' => 'approve',
            'reject' => 'reject',
            'activate', 'enable' => 'activate',
            'deactivate', 'disable', 'suspend' => 'deactivate',
            'export' => 'export',
            'import' => 'import',
            'download' => 'download',
            'upload' => 'upload',
            'assign' => 'assign',
            'unassign' => 'unassign',
            'transfer' => 'transfer',
            'process' => 'process',
            'cancel' => 'cancel',
            default => Str::snake($methodName),
        };
    }

    /**
     * Get category for resource
     */
    protected function getCategoryForResource(string $resource): string
    {
        return self::RESOURCE_CATEGORIES[$resource] ?? 'general';
    }

    /**
     * Generate display name for permission
     */
    protected function generateDisplayName(string $resource, string $action): string
    {
        $resourceName = Str::title(str_replace('_', ' ', Str::singular($resource)));
        $actionName = Str::title(str_replace('_', ' ', $action));
        
        return "{$actionName} {$resourceName}";
    }

    /**
     * Generate description for permission
     */
    protected function generateDescription(string $resource, string $action, string $controller, string $method): string
    {
        $resourceName = Str::title(str_replace('_', ' ', $resource));
        $actionDescription = match ($action) {
            'create' => 'create new',
            'read' => 'view and list',
            'update' => 'edit and modify',
            'delete' => 'delete and remove',
            'approve' => 'approve',
            'reject' => 'reject',
            'activate' => 'activate',
            'deactivate' => 'deactivate',
            'export' => 'export data from',
            'import' => 'import data to',
            default => $action,
        };
        
        return "Allow user to {$actionDescription} {$resourceName} (Controller: {$controller}::{$method})";
    }

    /**
     * Check if method should be ignored
     */
    protected function shouldIgnoreMethod(string $methodName): bool
    {
        return in_array($methodName, self::IGNORED_METHODS) ||
               Str::startsWith($methodName, '__') ||
               Str::startsWith($methodName, 'get') && Str::endsWith($methodName, 'Attribute');
    }

    /**
     * Discover permissions from routes
     */
    public function discoverFromRoutes(): array
    {
        $permissions = [];
        $routes = Route::getRoutes();

        foreach ($routes as $route) {
            $action = $route->getAction();
            
            if (isset($action['controller'])) {
                $routePermissions = $this->analyzeRouteAction($route);
                $permissions = array_merge($permissions, $routePermissions);
            }
        }

        return $permissions;
    }

    /**
     * Analyze route action for permissions
     */
    protected function analyzeRouteAction($route): array
    {
        $action = $route->getAction();
        $routeName = $route->getName();
        
        if (!$routeName || !isset($action['controller'])) {
            return [];
        }

        [$controller, $method] = explode('@', $action['controller']);
        $resource = $this->extractResourceFromController($controller);
        $permissionAction = $this->mapMethodToAction($method);
        
        return [[
            'name' => "{$resource}.{$permissionAction}",
            'display_name' => $this->generateDisplayName($resource, $permissionAction),
            'description' => "Route: {$routeName} ({$controller}@{$method})",
            'resource' => $resource,
            'action' => $permissionAction,
            'category' => $this->getCategoryForResource($resource),
            'route_name' => $routeName,
            'is_system' => true,
        ]];
    }

    /**
     * Generate standard resource permissions
     */
    public function generateStandardResourcePermissions(): array
    {
        $permissions = [];
        $resources = array_keys(self::RESOURCE_CATEGORIES);

        foreach ($resources as $resource) {
            foreach (self::CRUD_ACTIONS as $action) {
                $permissions[] = [
                    'name' => "{$resource}.{$action}",
                    'display_name' => $this->generateDisplayName($resource, $action),
                    'description' => "Standard {$action} permission for {$resource}",
                    'resource' => $resource,
                    'action' => $action,
                    'category' => self::RESOURCE_CATEGORIES[$resource],
                    'is_system' => true,
                ];
            }
        }

        return $permissions;
    }

    /**
     * Remove duplicates and sort permissions
     */
    protected function deduplicateAndSort(array $permissions): array
    {
        // Remove duplicates based on permission name
        $unique = [];
        foreach ($permissions as $permission) {
            $unique[$permission['name']] = $permission;
        }

        // Sort by category, resource, then action
        uasort($unique, function ($a, $b) {
            if ($a['category'] !== $b['category']) {
                return strcmp($a['category'], $b['category']);
            }
            if ($a['resource'] !== $b['resource']) {
                return strcmp($a['resource'], $b['resource']);
            }
            return strcmp($a['action'], $b['action']);
        });

        return array_values($unique);
    }

    /**
     * Sync discovered permissions to database
     */
    public function syncPermissionsToDatabase(array $permissions = null): array
    {
        if ($permissions === null) {
            $permissions = $this->discoverAllPermissions();
        }

        $created = 0;
        $updated = 0;
        $errors = [];

        foreach ($permissions as $permissionData) {
            try {
                $permission = AdvancedPermission::updateOrCreate(
                    ['name' => $permissionData['name']],
                    $permissionData
                );

                if ($permission->wasRecentlyCreated) {
                    $created++;
                } else {
                    $updated++;
                }
            } catch (\Exception $e) {
                $errors[] = "Failed to sync permission {$permissionData['name']}: " . $e->getMessage();
            }
        }

        return [
            'total_discovered' => count($permissions),
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors,
        ];
    }

    /**
     * Get permission statistics
     */
    public function getPermissionStatistics(): array
    {
        $discovered = $this->discoverAllPermissions();
        $existing = AdvancedPermission::count();
        
        $byCategory = [];
        $byResource = [];
        $byAction = [];

        foreach ($discovered as $permission) {
            $byCategory[$permission['category']] = ($byCategory[$permission['category']] ?? 0) + 1;
            $byResource[$permission['resource']] = ($byResource[$permission['resource']] ?? 0) + 1;
            $byAction[$permission['action']] = ($byAction[$permission['action']] ?? 0) + 1;
        }

        return [
            'total_discovered' => count($discovered),
            'total_existing' => $existing,
            'by_category' => $byCategory,
            'by_resource' => $byResource,
            'by_action' => $byAction,
        ];
    }
}
