@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">@lang('Forex Bookings')</h1>
                    <p class="page-header-text">@lang('Manage forex trading bookings and transactions')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-primary" href="{{ route('admin.forex.bookings.create') }}">
                        <i class="bi-plus me-1"></i> @lang('Create Booking')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['total_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Bookings')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-primary text-primary">
                                    <i class="bi-journal-text"></i> @lang('Total')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Pending Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['pending_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Pending')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-warning text-warning">
                                    <i class="bi-clock"></i> @lang('Pending')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Completed Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['completed_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Completed')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi-check-circle"></i> @lang('Completed')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Today\'s Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $stats['today_bookings'] }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Today')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-info text-info">
                                    <i class="bi-calendar-day"></i> @lang('Today')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Stats Cards -->

        <!-- Card -->
        <div class="card">
            <!-- Header -->
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <div class="input-group input-group-merge navbar-input-group">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input type="search" id="datatableSearch" class="search form-control"
                               placeholder="@lang('Search bookings')" aria-label="@lang('Search bookings')">
                    </div>
                </div>

                <div class="d-grid d-sm-flex gap-2">
                    <select class="js-select form-select" name="status" data-hs-tom-select-options='{"placeholder": "@lang('All statuses')", "searchInDropdown": false}'>
                        <option value="">@lang('All statuses')</option>
                        <option value="pending">@lang('Pending')</option>
                        <option value="completed">@lang('Completed')</option>
                        <option value="cancelled">@lang('Cancelled')</option>
                    </select>

                    <select class="js-select form-select" name="transaction_type" data-hs-tom-select-options='{"placeholder": "@lang('All types')", "searchInDropdown": false}'>
                        <option value="">@lang('All types')</option>
                        <option value="buying">@lang('Buying USD (Client pays NGN)')</option>
                        <option value="selling">@lang('Selling USD (Client pays USD)')</option>
                    </select>

                    <!-- Unfold -->
                    <div class="hs-unfold">
                        <a class="js-hs-unfold-invoker btn btn-white" href="javascript:;"
                           data-hs-unfold-options='{
                             "target": "#usersExportDropdown",
                             "type": "css-animation"
                           }'>
                            <i class="bi-download me-1"></i> @lang('Export')
                        </a>

                        <div id="usersExportDropdown"
                             class="hs-unfold-content dropdown-unfold dropdown-menu dropdown-menu-sm-end">
                            <span class="dropdown-header">@lang('Options')</span>
                            <a id="export-copy" class="dropdown-item" href="javascript:;">
                                <i class="bi-clipboard me-2"></i>
                                @lang('Copy')
                            </a>
                            <a id="export-print" class="dropdown-item" href="javascript:;">
                                <i class="bi-printer me-2"></i>
                                @lang('Print')
                            </a>
                            <div class="dropdown-divider"></div>
                            <span class="dropdown-header">@lang('Download options')</span>
                            <a id="export-excel" class="dropdown-item" href="javascript:;">
                                <i class="bi-file-earmark-excel me-2"></i>
                                @lang('Excel')
                            </a>
                            <a id="export-csv" class="dropdown-item" href="javascript:;">
                                <i class="bi-file-earmark-text me-2"></i>
                                @lang('CSV')
                            </a>
                            <a id="export-pdf" class="dropdown-item" href="javascript:;">
                                <i class="bi-file-earmark-pdf me-2"></i>
                                @lang('PDF')
                            </a>
                        </div>
                    </div>
                    <!-- End Unfold -->
                </div>
            </div>
            <!-- End Header -->

            <!-- Table -->
            <div class="table-responsive datatable-custom">
                <table id="datatable"
                       class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                         "columnDefs": [{
                            "targets": [0, 7],
                            "orderable": false
                          }],
                         "order": [],
                         "info": {
                           "totalQty": "#datatableWithPaginationInfoTotalQty"
                         },
                         "search": "#datatableSearch",
                         "entries": "#datatableEntries",
                         "pageLength": 15,
                         "isResponsive": false,
                         "isShowPaging": false,
                         "pagination": "datatablePagination"
                       }'>
                    <thead class="thead-light">
                    <tr>
                        <th class="table-column-pe-0">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="datatableCheckAll">
                                <label class="form-check-label" for="datatableCheckAll"></label>
                            </div>
                        </th>
                        <th>@lang('Booking Details')</th>
                        <th>@lang('Client Info')</th>
                        <th>@lang('Transaction')</th>
                        <th>@lang('Amounts')</th>
                        <th>@lang('Status')</th>
                        <th>@lang('Created')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <!-- End Table -->

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing:')</span>
                            <!-- Select -->
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto"
                                        autocomplete="off"
                                        data-hs-tom-select-options='{
                                            "searchInDropdown": false,
                                            "hideSearch": true
                                          }'>
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="20">20</option>
                                </select>
                            </div>
                            <!-- End Select -->
                            <span class="text-secondary me-2">@lang('of')</span>
                            <!-- Dynamic Data -->
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <!-- End Col -->

                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <!-- Pagination -->
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Card -->
    </div>

    <!-- Complete Booking Modal -->
    <div class="modal fade" id="completeModal" tabindex="-1" role="dialog" aria-labelledby="completeModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="completeModalLabel">@lang('Complete Booking')</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="completeForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="statusNotes" class="form-label">@lang('Completion Notes') (@lang('Optional'))</label>
                            <textarea class="form-control" name="status_notes" id="statusNotes" rows="3"
                                      placeholder="@lang('Add any notes about completing this booking...')"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn-success">@lang('Complete Booking')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div class="modal fade" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="cancelModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="cancelModalLabel">@lang('Cancel Booking')</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="cancelForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="cancelNotes" class="form-label">@lang('Cancellation Reason') <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="status_notes" id="cancelNotes" rows="3"
                                      placeholder="@lang('Please provide a reason for cancelling this booking...')" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                        <button type="submit" class="btn btn-danger">@lang('Cancel Booking')</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

<x-assets :datatable="true" :counter="true"/>

@push('script')
    <script>
        'use strict';

        $(document).on('ready', function () {
            initDataTable("{{ route('admin.forex.bookings.search') }}",
                {!! json_encode(['checkbox', 'booking_info', 'client_info', 'transaction_type', 'amounts', 'status', 'created_at', 'action']) !!}
            );

            // Filter functionality
            $('select[name="status"], select[name="transaction_type"]').on('change', function() {
                const datatable = HSCore.components.HSDatatables.getItem(0);
                datatable.ajax.url("{{ route('admin.forex.bookings.search') }}" +
                    "?status=" + $('select[name="status"]').val() +
                    "&transaction_type=" + $('select[name="transaction_type"]').val()).load();
            });

            // Complete booking functionality
            $(document).on('click', '.complete-booking', function() {
                let bookingId = $(this).data('id');
                let completeUrl = "{{ route('admin.forex.bookings.complete', ':id') }}".replace(':id', bookingId);
                $('#completeForm').attr('action', completeUrl);
                $('#completeModal').modal('show');
            });

            // Cancel booking functionality
            $(document).on('click', '.cancel-booking', function() {
                let bookingId = $(this).data('id');
                let cancelUrl = "{{ route('admin.forex.bookings.cancel', ':id') }}".replace(':id', bookingId);
                $('#cancelForm').attr('action', cancelUrl);
                $('#cancelModal').modal('show');
            });

            // Handle form submissions
            $('#completeForm, #cancelForm').on('submit', function(e) {
                e.preventDefault();

                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            // Reload the DataTable to reflect changes
                            const datatable = HSCore.components.HSDatatables.getItem(0);
                            datatable.ajax.reload();

                            // Close the modal
                            $('.modal').modal('hide');

                            // Show success message (optional)
                            // You can implement a toast notification here if needed
                        }
                    },
                    error: function(xhr) {
                        let errors = xhr.responseJSON?.errors || {};
                        Object.keys(errors).forEach(function(key) {
                            alert(errors[key][0]);
                        });
                    }
                });
            });

            // Export functionality - Note: Export buttons functionality would need to be implemented separately
            $('#export-copy').on('click', function() {
                // Copy functionality would need custom implementation
                console.log('Copy export clicked');
            });

            $('#export-excel').on('click', function() {
                // Excel export would need custom implementation
                console.log('Excel export clicked');
            });

            $('#export-csv').on('click', function() {
                // CSV export would need custom implementation
                console.log('CSV export clicked');
            });

            $('#export-pdf').on('click', function() {
                // PDF export would need custom implementation
                console.log('PDF export clicked');
            });

            $('#export-print').on('click', function() {
                // Print functionality would need custom implementation
                console.log('Print export clicked');
            });
        });
    </script>
@endpush
