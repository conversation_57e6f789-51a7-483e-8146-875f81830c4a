<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * Advanced Permission Audit Model
 * 
 * Tracks all permission-related events for security, compliance, and debugging.
 * Records access attempts, role assignments, and permission changes.
 * 
 * @property int $id
 * @property string $event_type Type of event (access_granted, access_denied, etc.)
 * @property string $action Specific action attempted
 * @property int|null $user_id User/Admin ID who performed action
 * @property string|null $user_type User model type
 * @property string|null $user_identifier Username/email for reference
 * @property int|null $role_id Role used for permission check
 * @property int|null $permission_id Permission that was checked
 * @property string|null $resource Resource being accessed
 * @property string|null $resource_id Specific resource ID
 * @property string|null $ip_address IP address of request
 * @property string|null $user_agent User agent string
 * @property string|null $route_name Laravel route name
 * @property string|null $url Full URL accessed
 * @property string|null $method HTTP method
 * @property bool $was_granted Whether access was granted
 * @property string|null $reason Reason for grant/denial
 * @property array|null $context_data Additional context data
 * @property array|null $request_data Relevant request data
 * @property int|null $check_duration_ms Time taken for permission check
 * @property Carbon $occurred_at When the event occurred
 */
class AdvancedPermissionAudit extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'action',
        'user_id',
        'user_type',
        'user_identifier',
        'role_id',
        'permission_id',
        'resource',
        'resource_id',
        'ip_address',
        'user_agent',
        'route_name',
        'url',
        'method',
        'was_granted',
        'reason',
        'context_data',
        'request_data',
        'check_duration_ms',
        'occurred_at',
    ];

    protected $casts = [
        'was_granted' => 'boolean',
        'context_data' => 'array',
        'request_data' => 'array',
        'check_duration_ms' => 'integer',
        'occurred_at' => 'datetime',
    ];

    protected $attributes = [
        'occurred_at' => 'now',
    ];

    // Event type constants
    const EVENT_ACCESS_GRANTED = 'access_granted';
    const EVENT_ACCESS_DENIED = 'access_denied';
    const EVENT_ROLE_ASSIGNED = 'role_assigned';
    const EVENT_ROLE_REVOKED = 'role_revoked';
    const EVENT_PERMISSION_GRANTED = 'permission_granted';
    const EVENT_PERMISSION_REVOKED = 'permission_revoked';
    const EVENT_LOGIN_SUCCESS = 'login_success';
    const EVENT_LOGIN_FAILED = 'login_failed';
    const EVENT_ROLE_CREATED = 'role_created';
    const EVENT_ROLE_UPDATED = 'role_updated';
    const EVENT_ROLE_DELETED = 'role_deleted';
    const EVENT_PERMISSION_CREATED = 'permission_created';
    const EVENT_PERMISSION_UPDATED = 'permission_updated';
    const EVENT_PERMISSION_DELETED = 'permission_deleted';

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->occurred_at) {
                $model->occurred_at = now();
            }
            
            // Auto-populate request data if not set
            if (request() && !$model->ip_address) {
                $model->ip_address = request()->ip();
                $model->user_agent = request()->userAgent();
                $model->url = request()->fullUrl();
                $model->method = request()->method();
                
                if (request()->route()) {
                    $model->route_name = request()->route()->getName();
                }
            }
        });
    }

    /**
     * Get the role
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(AdvancedRole::class, 'role_id');
    }

    /**
     * Get the permission
     */
    public function permission(): BelongsTo
    {
        return $this->belongsTo(AdvancedPermission::class, 'permission_id');
    }

    /**
     * Scope: Filter by event type
     */
    public function scopeEventType(Builder $query, string $eventType): Builder
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope: Filter by user
     */
    public function scopeForUser(Builder $query, $user): Builder
    {
        return $query->where('user_id', $user->id)
            ->where('user_type', get_class($user));
    }

    /**
     * Scope: Filter by action
     */
    public function scopeForAction(Builder $query, string $action): Builder
    {
        return $query->where('action', $action);
    }

    /**
     * Scope: Filter by resource
     */
    public function scopeForResource(Builder $query, string $resource): Builder
    {
        return $query->where('resource', $resource);
    }

    /**
     * Scope: Only granted access
     */
    public function scopeGranted(Builder $query): Builder
    {
        return $query->where('was_granted', true);
    }

    /**
     * Scope: Only denied access
     */
    public function scopeDenied(Builder $query): Builder
    {
        return $query->where('was_granted', false);
    }

    /**
     * Scope: Filter by IP address
     */
    public function scopeFromIp(Builder $query, string $ip): Builder
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * Scope: Recent events
     */
    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('occurred_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope: Date range
     */
    public function scopeDateRange(Builder $query, Carbon $from, Carbon $to): Builder
    {
        return $query->whereBetween('occurred_at', [$from, $to]);
    }

    /**
     * Scope: Ordered by occurrence (newest first)
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderByDesc('occurred_at');
    }

    /**
     * Log permission check event
     */
    public static function logPermissionCheck(
        string $action,
        bool $wasGranted,
        $user = null,
        AdvancedRole $role = null,
        AdvancedPermission $permission = null,
        string $reason = null,
        array $context = [],
        int $durationMs = null
    ): self {
        return static::create([
            'event_type' => $wasGranted ? self::EVENT_ACCESS_GRANTED : self::EVENT_ACCESS_DENIED,
            'action' => $action,
            'user_id' => $user?->id,
            'user_type' => $user ? get_class($user) : null,
            'user_identifier' => $user?->email ?? $user?->username,
            'role_id' => $role?->id,
            'permission_id' => $permission?->id,
            'resource' => $permission?->resource,
            'was_granted' => $wasGranted,
            'reason' => $reason,
            'context_data' => $context,
            'check_duration_ms' => $durationMs,
        ]);
    }

    /**
     * Log role assignment event
     */
    public static function logRoleAssignment(
        $user,
        AdvancedRole $role,
        bool $assigned = true,
        string $reason = null,
        $assignedBy = null
    ): self {
        return static::create([
            'event_type' => $assigned ? self::EVENT_ROLE_ASSIGNED : self::EVENT_ROLE_REVOKED,
            'action' => $assigned ? 'role.assign' : 'role.revoke',
            'user_id' => $assignedBy?->id ?? auth()->id(),
            'user_type' => $assignedBy ? get_class($assignedBy) : (auth()->user() ? get_class(auth()->user()) : null),
            'user_identifier' => $assignedBy?->email ?? auth()->user()?->email,
            'role_id' => $role->id,
            'resource' => 'roles',
            'resource_id' => $role->id,
            'was_granted' => $assigned,
            'reason' => $reason,
            'context_data' => [
                'target_user_id' => $user->id,
                'target_user_type' => get_class($user),
                'target_user_identifier' => $user->email ?? $user->username,
            ],
        ]);
    }

    /**
     * Log login attempt
     */
    public static function logLoginAttempt(
        string $identifier,
        bool $successful,
        string $reason = null,
        $user = null
    ): self {
        return static::create([
            'event_type' => $successful ? self::EVENT_LOGIN_SUCCESS : self::EVENT_LOGIN_FAILED,
            'action' => 'auth.login',
            'user_id' => $user?->id,
            'user_type' => $user ? get_class($user) : null,
            'user_identifier' => $identifier,
            'resource' => 'auth',
            'was_granted' => $successful,
            'reason' => $reason,
        ]);
    }

    /**
     * Get event type badge HTML
     */
    public function getEventTypeBadge(): string
    {
        return match ($this->event_type) {
            self::EVENT_ACCESS_GRANTED => '<span class="badge bg-success">Access Granted</span>',
            self::EVENT_ACCESS_DENIED => '<span class="badge bg-danger">Access Denied</span>',
            self::EVENT_ROLE_ASSIGNED => '<span class="badge bg-info">Role Assigned</span>',
            self::EVENT_ROLE_REVOKED => '<span class="badge bg-warning">Role Revoked</span>',
            self::EVENT_LOGIN_SUCCESS => '<span class="badge bg-success">Login Success</span>',
            self::EVENT_LOGIN_FAILED => '<span class="badge bg-danger">Login Failed</span>',
            default => '<span class="badge bg-secondary">' . ucwords(str_replace('_', ' ', $this->event_type)) . '</span>',
        };
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDuration(): string
    {
        if (!$this->check_duration_ms) {
            return 'N/A';
        }
        
        if ($this->check_duration_ms < 1000) {
            return $this->check_duration_ms . 'ms';
        }
        
        return round($this->check_duration_ms / 1000, 2) . 's';
    }

    /**
     * Get security risk level
     */
    public function getSecurityRiskLevel(): string
    {
        // Failed login attempts
        if ($this->event_type === self::EVENT_LOGIN_FAILED) {
            return 'medium';
        }
        
        // Access denied events
        if ($this->event_type === self::EVENT_ACCESS_DENIED) {
            return 'low';
        }
        
        // Multiple failed attempts from same IP
        if ($this->ip_address) {
            $recentFailures = static::where('ip_address', $this->ip_address)
                ->where('was_granted', false)
                ->where('occurred_at', '>=', now()->subHour())
                ->count();
                
            if ($recentFailures > 5) {
                return 'high';
            }
        }
        
        return 'low';
    }

    /**
     * Get all event types
     */
    public static function getEventTypes(): array
    {
        return [
            self::EVENT_ACCESS_GRANTED => 'Access Granted',
            self::EVENT_ACCESS_DENIED => 'Access Denied',
            self::EVENT_ROLE_ASSIGNED => 'Role Assigned',
            self::EVENT_ROLE_REVOKED => 'Role Revoked',
            self::EVENT_PERMISSION_GRANTED => 'Permission Granted',
            self::EVENT_PERMISSION_REVOKED => 'Permission Revoked',
            self::EVENT_LOGIN_SUCCESS => 'Login Success',
            self::EVENT_LOGIN_FAILED => 'Login Failed',
            self::EVENT_ROLE_CREATED => 'Role Created',
            self::EVENT_ROLE_UPDATED => 'Role Updated',
            self::EVENT_ROLE_DELETED => 'Role Deleted',
            self::EVENT_PERMISSION_CREATED => 'Permission Created',
            self::EVENT_PERMISSION_UPDATED => 'Permission Updated',
            self::EVENT_PERMISSION_DELETED => 'Permission Deleted',
        ];
    }
}
