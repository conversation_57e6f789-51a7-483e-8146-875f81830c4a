<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

class CurrencyFactory extends Factory
{
    protected $model = Currency::class;

    public function definition()
    {
        return [
            'name' => $this->faker->currencyCode(),
            'code' => $this->faker->currencyCode(),
            'symbol' => $this->faker->randomElement(['$', '€', '£', '₦', '¥']),
            'rate' => $this->faker->randomFloat(4, 0.1, 1000),
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
