# Merchant Payout Configuration Feature

## Overview

The Merchant Payout Configuration feature allows administrators to set custom payout limits and charges for individual merchants, overriding the default payout method settings. This provides flexibility to offer different pricing tiers and limits based on merchant agreements or business requirements.

## Key Features

- **Per-merchant, per-currency customization**: Set custom limits and charges for each merchant per payout method per currency
- **Currency-specific configurations**: Different settings for NGN, USD, EUR, etc. within the same payout method
- **Partial inheritance**: Configure only specific fields (e.g., only min_limit), others fall back to defaults
- **Admin-only management**: Only administrators can configure merchant-specific settings
- **User visibility**: Merchants can view their custom settings only if they exist
- **API integration**: All endpoints automatically use effective configurations
- **Backward compatibility**: Existing merchants continue using global defaults

## Database Schema

### Table: `merchant_payout_configurations`

```sql
CREATE TABLE merchant_payout_configurations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    merchant_id BIGINT NOT NULL,
    payout_method_id BIGINT NOT NULL,
    currency VARCHAR(10) NOT NULL,         -- Currency code (NGN, USD, EUR, etc.)
    min_limit DECIMAL(28,8) NULL,          -- Custom minimum limit, NULL uses default
    max_limit DECIMAL(28,8) NULL,          -- Custom maximum limit, NULL uses default
    percentage_charge DECIMAL(5,2) NULL,   -- Custom percentage charge, NULL uses default
    fixed_charge DECIMAL(28,8) NULL,       -- Custom fixed charge, NULL uses default
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE KEY unique_merchant_payout_method_currency (merchant_id, payout_method_id, currency),
    FOREIGN KEY (merchant_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (payout_method_id) REFERENCES payout_methods(id) ON DELETE CASCADE
);
```

## Configuration Logic

### Inheritance Priority
1. **Custom merchant setting** (if not NULL)
2. **Default payout method setting** (fallback)

### Example Scenarios

**Scenario 1: Currency-Specific Partial Configuration**
- Merchant has custom NGN config: `min_limit: 500` and `percentage_charge: 1.5`
- Merchant has custom USD config: `max_limit: 10000` only
- NGN: Custom min/percentage + Default max/fixed
- USD: Custom max + Default min/percentage/fixed

**Scenario 2: No Custom Configuration**
- Merchant has no record in `merchant_payout_configurations` for requested currency
- Result: All default values from payout method for that currency

**Scenario 3: Mixed Currency Configuration**
- Merchant has full custom config for NGN
- Merchant has no custom config for USD
- NGN: All custom values used
- USD: All default values used

**Scenario 4: Multi-Currency Payout Method**
- PayPal supports NGN, USD, EUR
- Merchant can have different configurations for each currency
- Each currency configuration is independent

## API Changes

### Payout Method Listing

**Endpoint**: `GET /api/v1/payout`

**Response Changes**:
```json
{
  "data": {
    "gateways": [
      {
        "id": 29,
        "name": "Numero",
        "payout_currencies": [
          {
            "min_limit": "200.00000000",     // Custom value
            "max_limit": "5000000",          // Default value
            "percentage_charge": "2.00",     // Custom value
            "fixed_charge": "50",            // Default value
            "has_custom_config": true,
            "custom_fields": ["min_limit", "percentage_charge"]
          }
        ],
        "has_custom_config": true
      }
    ]
  }
}
```

### Validation Response

**Endpoint**: `POST /api/v1/check-amount`

**Response Changes**:
```json
{
  "status": true,
  "min_limit": 200.0,           // Custom value
  "max_limit": 5000000.0,       // Default value
  "percentage": 2.0,            // Custom value
  "fixed_charge": 50.0,         // Default value
  "has_custom_config": true,
  "custom_fields": ["min_limit", "percentage_charge"]
}
```

## Admin Interface

### Access Path
1. Navigate to **Admin Panel** → **Merchant Management** → **All Merchants**
2. Find the merchant and click the **Actions** dropdown
3. Select **"Payout Configuration"** from the dropdown menu
4. URL: `/admin/merchants/{merchantId}/payout-configuration`

### Features
- **Configuration Table**: Shows all payout methods with editable fields
- **Default Values Display**: Shows current default values as placeholders
- **Partial Configuration**: Can set only specific fields, others remain default
- **Reset Functionality**: Clear custom values to revert to defaults
- **Validation**: Ensures max_limit > min_limit when both are set
- **Bulk Operations**: Apply same configuration to multiple merchants

### Form Fields
- **Min Limit**: Custom minimum payout amount
- **Max Limit**: Custom maximum payout amount  
- **Percentage Charge**: Custom percentage fee (0-100%)
- **Fixed Charge**: Custom fixed fee amount

## User Interface

### Merchant Dashboard
- **Custom Settings Indicator**: Badge showing "Custom Settings Applied" on payout methods
- **Settings Visibility**: Only shown if merchant has custom configurations
- **Transparent Operation**: Merchants see effective limits/charges without knowing source

### Payout Request Flow
1. Merchant selects payout method
2. System automatically applies effective configuration
3. Validation uses custom limits/charges
4. Fee calculation uses custom rates
5. Process completes with merchant-specific settings

## Code Implementation

### Key Classes

**Model**: `App\Models\MerchantPayoutConfiguration`
- Handles effective configuration retrieval
- Manages partial inheritance logic
- Provides validation rules

**Controller**: `App\Http\Controllers\Admin\MerchantPayoutConfigurationController`
- Admin interface for managing configurations
- CRUD operations with validation
- Bulk update functionality

**Trait**: `App\Traits\PayoutTrait`
- Updated `validationCheck()` method
- Automatic merchant-specific configuration usage
- Backward compatibility maintained

### Key Methods

```php
// Get effective configuration for merchant
MerchantPayoutConfiguration::getEffectiveConfigFor($merchantId, $payoutMethodId, $currency);

// Get effective config from instance
$config->getEffectiveConfig($currency);

// Check if merchant has custom settings
$config->hasAnyCustomSettings();

// Get list of customized fields
$config->getCustomFields();
```

## Testing

### Test Scenarios Verified

1. **Effective Configuration Retrieval**
   - ✅ Custom min_limit: 200 (vs default 100)
   - ✅ Default max_limit: 5000000 (custom is null)
   - ✅ Custom percentage: 2.0% (vs default 0%)
   - ✅ Default fixed_charge: 50 (custom is null)

2. **Validation Integration**
   - ✅ Amount validation uses custom limits
   - ✅ Charge calculation uses custom rates
   - ✅ Proper inheritance of default values

3. **API Integration**
   - ✅ Payout method listing shows effective configs
   - ✅ Validation endpoints use merchant-specific settings
   - ✅ Custom configuration indicators work

## Migration Instructions

### 1. Run Migration
```bash
php artisan migrate --path=database/migrations/2024_12_09_000001_create_merchant_payout_configurations_table.php
```

### 2. Test Configuration (Optional)
```bash
php artisan db:seed --class=MerchantPayoutConfigurationSeeder
```

### 3. Verify Installation
- Check admin interface: `/admin/merchants/{id}/payout-configuration`
- Test API endpoints with custom configurations
- Verify user interface shows custom settings indicator

## Troubleshooting

### Common Issues

**Issue**: Custom configuration not applying
**Solution**: Verify `is_active = 1` and merchant_id/payout_method_id are correct

**Issue**: Validation errors on limits
**Solution**: Ensure max_limit > min_limit when both are set

**Issue**: API not returning custom configs
**Solution**: Check MerchantPayoutConfiguration model relationships and effective config methods

### Debug Commands

```bash
# Check merchant configurations
php artisan tinker
$config = App\Models\MerchantPayoutConfiguration::where('merchant_id', 1)->first();
$config->getEffectiveConfig('NGN');

# Test validation with custom config
$trait = new class { use App\Traits\PayoutTrait; };
$result = $trait->validationCheck(1000, 'NGN', 29, 1);
```

## Security Considerations

- **Admin-only access**: Only administrators can modify configurations
- **Validation**: All inputs validated for proper data types and ranges
- **Foreign key constraints**: Prevent orphaned configurations
- **Audit trail**: Created/updated timestamps for tracking changes

## Performance Impact

- **Minimal overhead**: Single additional query per payout validation
- **Efficient caching**: Consider implementing Redis cache for frequently accessed configs
- **Database indexes**: Proper indexing on merchant_id and payout_method_id
- **Query optimization**: Uses efficient joins and where clauses
