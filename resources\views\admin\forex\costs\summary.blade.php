@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.costs.index') }}">
                                    @lang('Operational Costs')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Cost Summary')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Operational Cost Summary')</h1>
                    <p class="page-header-text">@lang('Analyze operational expenses by category and time period')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.costs.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Costs')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Filter Card -->
        <div class="card mb-3 mb-lg-5">
            <div class="card-header">
                <h4 class="card-header-title">@lang('Filter Options')</h4>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.forex.costs.summary') }}">
                    <div class="row">
                        <div class="col-sm-6 col-lg-4 mb-3">
                            <label for="startDateLabel" class="form-label">@lang('Start Date')</label>
                            <input type="date" class="form-control" name="start_date" id="startDateLabel" 
                                   value="{{ $startDate }}">
                        </div>
                        <div class="col-sm-6 col-lg-4 mb-3">
                            <label for="endDateLabel" class="form-label">@lang('End Date')</label>
                            <input type="date" class="form-control" name="end_date" id="endDateLabel" 
                                   value="{{ $endDate }}">
                        </div>
                        <div class="col-sm-12 col-lg-4 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi-funnel me-1"></i> @lang('Apply Filter')
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- End Filter Card -->

        <!-- Summary Stats -->
        <div class="row">
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Costs (NGN)')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ number_format($totalCosts['NGN'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">NGN</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-warning text-warning">
                                    <i class="bi-currency-exchange"></i> @lang('NGN')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Costs (USD)')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ number_format($totalCosts['USD'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">USD</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi-currency-dollar"></i> @lang('USD')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Entries')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $costsByCategory->sum('cost_count') }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Records')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-info text-info">
                                    <i class="bi-list-ul"></i> @lang('Entries')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Categories')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $costsByCategory->count() }}
                                </span>
                                <span class="text-body fs-5 ms-1">@lang('Active')</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-secondary text-secondary">
                                    <i class="bi-tags"></i> @lang('Categories')
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Summary Stats -->

        <!-- Costs by Category -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-header-title">@lang('Costs by Category')</h4>
                <span class="badge bg-soft-primary text-primary">
                    {{ \Carbon\Carbon::parse($startDate)->format('M d, Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('M d, Y') }}
                </span>
            </div>

            @if($costsByCategory->count() > 0)
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                        <thead class="thead-light">
                        <tr>
                            <th>@lang('Category')</th>
                            <th>@lang('Total Amount')</th>
                            <th>@lang('Number of Entries')</th>
                            <th>@lang('Average Cost')</th>
                            <th>@lang('Percentage')</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $grandTotal = $costsByCategory->sum('total_amount');
                        @endphp
                        @foreach($costsByCategory as $categoryData)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs avatar-soft-primary avatar-circle me-2">
                                            <span class="avatar-initials">
                                                <i class="bi-tag"></i>
                                            </span>
                                        </div>
                                        <span class="h6 mb-0">{{ $categoryData->category ?: __('Uncategorized') }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="h6 mb-0">{{ number_format($categoryData->total_amount, 2) }} NGN</span>
                                </td>
                                <td>
                                    <span class="badge bg-soft-info text-info">{{ $categoryData->cost_count }}</span>
                                </td>
                                <td>
                                    {{ number_format($categoryData->total_amount / $categoryData->cost_count, 2) }} NGN
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @php
                                            $percentage = $grandTotal > 0 ? ($categoryData->total_amount / $grandTotal) * 100 : 0;
                                        @endphp
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ $percentage }}%;" 
                                                 aria-valuenow="{{ $percentage }}" 
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <span class="text-secondary">{{ number_format($percentage, 1) }}%</span>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                        <tfoot class="thead-light">
                        <tr>
                            <th>@lang('Total')</th>
                            <th>{{ number_format($grandTotal, 2) }} NGN</th>
                            <th>{{ $costsByCategory->sum('cost_count') }}</th>
                            <th>{{ $costsByCategory->count() > 0 ? number_format($grandTotal / $costsByCategory->sum('cost_count'), 2) : '0.00' }} NGN</th>
                            <th>100%</th>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            @else
                <div class="card-body">
                    <div class="text-center p-4">
                        <img class="dataTables-image mb-3" src="{{ asset('assets/admin/img/oc-error.svg') }}" 
                             alt="Image Description" data-hs-theme-appearance="default">
                        <img class="dataTables-image mb-3" src="{{ asset('assets/admin/img/oc-error-light.svg') }}" 
                             alt="Image Description" data-hs-theme-appearance="dark">
                        <p class="mb-0">@lang('No operational costs found for the selected period')</p>
                        <a href="{{ route('admin.forex.costs.create') }}" class="btn btn-primary mt-3">
                            <i class="bi-plus-circle me-1"></i> @lang('Record First Cost')
                        </a>
                    </div>
                </div>
            @endif
        </div>
        <!-- End Costs by Category -->
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        
        $(document).ready(function () {
            // Set default date range to current month if not specified
            const startDateInput = document.getElementById('startDateLabel');
            const endDateInput = document.getElementById('endDateLabel');
            
            if (!startDateInput.value) {
                const now = new Date();
                const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
                startDateInput.value = firstDay.toISOString().split('T')[0];
            }
            
            if (!endDateInput.value) {
                const now = new Date();
                const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
                endDateInput.value = lastDay.toISOString().split('T')[0];
            }
        });
    </script>
@endpush
