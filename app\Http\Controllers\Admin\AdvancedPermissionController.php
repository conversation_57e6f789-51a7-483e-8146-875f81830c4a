<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdvancedPermission;
use App\Models\AdvancedRole;
use App\Services\PermissionDiscoveryService;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

/**
 * Advanced Permission Management Controller
 * 
 * Handles CRUD operations for advanced permissions with discovery,
 * bulk operations, and usage tracking.
 */
class AdvancedPermissionController extends Controller
{
    use HasAdvancedPermissions;

    protected PermissionDiscoveryService $discoveryService;

    public function __construct(PermissionDiscoveryService $discoveryService)
    {
        $this->discoveryService = $discoveryService;
        
        // Apply advanced permission middleware
        $this->middleware('advanced.permission:advanced_permissions.read')->only(['index', 'show']);
        $this->middleware('advanced.permission:advanced_permissions.create')->only(['create', 'store']);
        $this->middleware('advanced.permission:advanced_permissions.update')->only(['edit', 'update']);
        $this->middleware('advanced.permission:advanced_permissions.delete')->only(['destroy']);
    }

    /**
     * Display a listing of permissions
     */
    public function index(Request $request): View
    {
        $query = AdvancedPermission::withCount(['roles']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('resource')) {
            $query->where('resource', $request->get('resource'));
        }

        if ($request->filled('action')) {
            $query->where('action', $request->get('action'));
        }

        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'system') {
                $query->where('is_system', true);
            } elseif ($status === 'unused') {
                $query->doesntHave('roles');
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'category');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['name', 'display_name', 'resource', 'action', 'category', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->ordered();
        }

        $permissions = $query->paginate(25)->withQueryString();

        // Get filter options
        $resources = AdvancedPermission::distinct()->pluck('resource')->filter()->sort();
        $actions = AdvancedPermission::distinct()->pluck('action')->filter()->sort();
        $categories = AdvancedPermission::distinct()->pluck('category')->filter()->sort();

        // Get discovery statistics
        $discoveryStats = $this->discoveryService->getPermissionStatistics();

        return view('admin.advanced-permissions.index', compact(
            'permissions',
            'resources',
            'actions',
            'categories',
            'discoveryStats'
        ));
    }

    /**
     * Show the form for creating a new permission
     */
    public function create(): View
    {
        $resources = AdvancedPermission::distinct()->pluck('resource')->filter()->sort();
        $actions = AdvancedPermission::distinct()->pluck('action')->filter()->sort();
        $categories = AdvancedPermission::distinct()->pluck('category')->filter()->sort();

        return view('admin.advanced-permissions.create', compact(
            'resources',
            'actions',
            'categories'
        ));
    }

    /**
     * Store a newly created permission
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:advanced_permissions,name|regex:/^[a-z_]+\.[a-z_]+$/',
            'display_name' => 'required|string|max:150',
            'description' => 'nullable|string|max:1000',
            'resource' => 'required|string|max:50',
            'action' => 'required|string|max:20',
            'category' => 'nullable|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $permission = AdvancedPermission::create($request->all());

            $this->logPermissionEvent(
                'permission_created',
                'advanced_permissions.create',
                "Created permission: {$permission->display_name}",
                ['permission_id' => $permission->id]
            );

            return redirect()->route('admin.advanced-permissions.show', $permission)
                ->with('success', "Permission '{$permission->display_name}' created successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create permission: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified permission
     */
    public function show(AdvancedPermission $permission): View
    {
        $permission->load([
            'roles.userRoles',
            'rolePermissions.role',
            'creator',
            'updater'
        ]);

        // Get usage statistics
        $stats = [
            'total_roles' => $permission->roles()->count(),
            'active_roles' => $permission->roles()->where('is_active', true)->count(),
            'total_users' => $permission->roles()
                ->withCount('userRoles')
                ->get()
                ->sum('user_roles_count'),
            'usage_count' => $permission->getUsageCount(),
        ];

        // Get roles using this permission
        $rolesWithPermission = $permission->roles()
            ->withPivot(['constraints', 'is_granted', 'priority', 'valid_from', 'valid_until'])
            ->orderBy('display_name')
            ->get();

        return view('admin.advanced-permissions.show', compact(
            'permission',
            'stats',
            'rolesWithPermission'
        ));
    }

    /**
     * Show the form for editing the specified permission
     */
    public function edit(AdvancedPermission $permission): View
    {
        if ($permission->is_system) {
            return back()->withErrors(['error' => 'System permissions cannot be edited.']);
        }

        $resources = AdvancedPermission::distinct()->pluck('resource')->filter()->sort();
        $actions = AdvancedPermission::distinct()->pluck('action')->filter()->sort();
        $categories = AdvancedPermission::distinct()->pluck('category')->filter()->sort();

        return view('admin.advanced-permissions.edit', compact(
            'permission',
            'resources',
            'actions',
            'categories'
        ));
    }

    /**
     * Update the specified permission
     */
    public function update(Request $request, AdvancedPermission $permission): RedirectResponse
    {
        if ($permission->is_system) {
            return back()->withErrors(['error' => 'System permissions cannot be modified.']);
        }

        $validator = Validator::make($request->all(), [
            'name' => "required|string|max:100|unique:advanced_permissions,name,{$permission->id}|regex:/^[a-z_]+\.[a-z_]+$/",
            'display_name' => 'required|string|max:150',
            'description' => 'nullable|string|max:1000',
            'resource' => 'required|string|max:50',
            'action' => 'required|string|max:20',
            'category' => 'nullable|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $permission->update($request->all());

            $this->logPermissionEvent(
                'permission_updated',
                'advanced_permissions.update',
                "Updated permission: {$permission->display_name}",
                ['permission_id' => $permission->id]
            );

            return redirect()->route('admin.advanced-permissions.show', $permission)
                ->with('success', "Permission '{$permission->display_name}' updated successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update permission: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified permission
     */
    public function destroy(AdvancedPermission $permission): RedirectResponse
    {
        if ($permission->is_system) {
            return back()->withErrors(['error' => 'System permissions cannot be deleted.']);
        }

        if (!$permission->isDeletable()) {
            return back()->withErrors(['error' => 'Permission is in use and cannot be deleted.']);
        }

        try {
            $permissionName = $permission->display_name;
            $permission->delete();

            $this->logPermissionEvent(
                'permission_deleted',
                'advanced_permissions.delete',
                "Deleted permission: {$permissionName}",
                ['permission_name' => $permissionName]
            );

            return redirect()->route('admin.advanced-permissions.index')
                ->with('success', "Permission '{$permissionName}' deleted successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete permission: ' . $e->getMessage()]);
        }
    }

    /**
     * Discover and sync permissions
     */
    public function discover(Request $request): JsonResponse
    {
        $this->authorizePermission('advanced_permissions.create');

        try {
            $result = $this->discoveryService->syncPermissionsToDatabase();

            $this->logPermissionEvent(
                'permissions_discovered',
                'advanced_permissions.discover',
                "Discovered {$result['total_discovered']} permissions, created {$result['created']}, updated {$result['updated']}",
                $result
            );

            return response()->json([
                'success' => true,
                'message' => "Discovery completed. Created {$result['created']} new permissions, updated {$result['updated']} existing permissions.",
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Permission discovery failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk operations on permissions
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'permission_ids' => 'required|array|min:1',
            'permission_ids.*' => 'exists:advanced_permissions,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $action = $request->get('action');
        $permissionIds = $request->get('permission_ids');

        try {
            $permissions = AdvancedPermission::whereIn('id', $permissionIds)->get();
            $processed = 0;
            $errors = [];

            foreach ($permissions as $permission) {
                try {
                    switch ($action) {
                        case 'activate':
                            $this->authorizePermission('advanced_permissions.update');
                            $permission->update(['is_active' => true]);
                            $processed++;
                            break;

                        case 'deactivate':
                            $this->authorizePermission('advanced_permissions.update');
                            $permission->update(['is_active' => false]);
                            $processed++;
                            break;

                        case 'delete':
                            $this->authorizePermission('advanced_permissions.delete');
                            if ($permission->is_system) {
                                $errors[] = "Cannot delete system permission: {$permission->name}";
                            } elseif (!$permission->isDeletable()) {
                                $errors[] = "Permission in use, cannot delete: {$permission->name}";
                            } else {
                                $permission->delete();
                                $processed++;
                            }
                            break;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Failed to {$action} permission {$permission->name}: " . $e->getMessage();
                }
            }

            $this->logPermissionEvent(
                'permissions_bulk_action',
                "advanced_permissions.{$action}",
                "Bulk {$action} on {$processed} permissions",
                ['action' => $action, 'processed' => $processed, 'errors' => $errors]
            );

            return response()->json([
                'success' => true,
                'message' => "Bulk {$action} completed. Processed {$processed} permissions.",
                'processed' => $processed,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => "Bulk operation failed: " . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get permission suggestions for autocomplete
     */
    public function suggestions(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $category = $request->get('category');

        $permissions = AdvancedPermission::active()
            ->when($query, function ($q) use ($query) {
                $q->where(function ($subQ) use ($query) {
                    $subQ->where('name', 'like', "%{$query}%")
                         ->orWhere('display_name', 'like', "%{$query}%");
                });
            })
            ->when($category, function ($q) use ($category) {
                $q->where('category', $category);
            })
            ->limit(20)
            ->get(['id', 'name', 'display_name', 'category', 'resource', 'action']);

        return response()->json($permissions);
    }

    /**
     * Export permissions as JSON
     */
    public function export(Request $request): JsonResponse
    {
        $this->authorizePermission('advanced_permissions.read');

        $permissions = AdvancedPermission::when($request->filled('category'), function ($query) use ($request) {
                $query->where('category', $request->get('category'));
            })
            ->when($request->filled('resource'), function ($query) use ($request) {
                $query->where('resource', $request->get('resource'));
            })
            ->ordered()
            ->get();

        $export = [
            'exported_at' => now()->toISOString(),
            'exported_by' => auth()->user()->email,
            'total_permissions' => $permissions->count(),
            'permissions' => $permissions->map(function ($permission) {
                return [
                    'name' => $permission->name,
                    'display_name' => $permission->display_name,
                    'description' => $permission->description,
                    'resource' => $permission->resource,
                    'action' => $permission->action,
                    'category' => $permission->category,
                    'is_system' => $permission->is_system,
                    'is_active' => $permission->is_active,
                ];
            }),
        ];

        return response()->json($export);
    }
}
