<?php

// Test script to verify booking cancellation and pending balance release
require_once 'vendor/autoload.php';

use App\Models\ForexBooking;
use App\Models\ForexAccount;
use App\Models\ForexTransaction;
use App\Services\ForexBookingService;

echo "=== BOOKING CANCELLATION TEST ===\n";

// Get USD account
$usdAccount = ForexAccount::where('currency_code', 'USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

echo "USD Account before test:\n";
echo "- Balance: $" . number_format($usdAccount->balance, 2) . "\n";
echo "- Pending: $" . number_format($usdAccount->pending_balance, 2) . "\n";
echo "- Available: $" . number_format($usdAccount->available_balance, 2) . "\n\n";

// Create a test booking
$bookingData = [
    'client_name' => 'Test Client',
    'client_email' => '<EMAIL>',
    'client_phone' => '**********',
    'client_type' => 'individual',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 100,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test account details'
];

$bookingService = new ForexBookingService();

try {
    echo "Creating test booking...\n";
    $booking = $bookingService->createBooking($bookingData, 1); // Admin ID = 1
    echo "✅ Booking created: {$booking->booking_reference}\n";
    
    // Check account after booking creation
    $usdAccount->refresh();
    echo "\nUSD Account after booking creation:\n";
    echo "- Balance: $" . number_format($usdAccount->balance, 2) . "\n";
    echo "- Pending: $" . number_format($usdAccount->pending_balance, 2) . "\n";
    echo "- Available: $" . number_format($usdAccount->available_balance, 2) . "\n";
    
    // Check pending transactions
    $pendingTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_type', 'pending')
        ->get();
    
    echo "\nPending transactions created: " . $pendingTransactions->count() . "\n";
    foreach ($pendingTransactions as $transaction) {
        echo "- Transaction ID: {$transaction->id}, Amount: $" . number_format($transaction->amount, 2) . "\n";
    }
    
    echo "\nCancelling booking...\n";
    $bookingService->cancelBooking($booking, 1, 'Test cancellation');
    echo "✅ Booking cancelled\n";
    
    // Check account after cancellation
    $usdAccount->refresh();
    echo "\nUSD Account after cancellation:\n";
    echo "- Balance: $" . number_format($usdAccount->balance, 2) . "\n";
    echo "- Pending: $" . number_format($usdAccount->pending_balance, 2) . "\n";
    echo "- Available: $" . number_format($usdAccount->available_balance, 2) . "\n";
    
    // Check if pending transactions still exist
    $remainingPendingTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_type', 'pending')
        ->get();
    
    echo "\nRemaining pending transactions: " . $remainingPendingTransactions->count() . "\n";
    
    // Check release transactions
    $releaseTransactions = ForexTransaction::where('forex_booking_id', $booking->id)
        ->where('transaction_type', 'pending_release')
        ->get();
    
    echo "Release transactions created: " . $releaseTransactions->count() . "\n";
    foreach ($releaseTransactions as $transaction) {
        echo "- Transaction ID: {$transaction->id}, Amount: $" . number_format($transaction->amount, 2) . "\n";
    }
    
    echo "\n=== TEST COMPLETE ===\n";
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
