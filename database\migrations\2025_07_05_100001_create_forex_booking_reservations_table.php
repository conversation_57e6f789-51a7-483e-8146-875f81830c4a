<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_booking_reservations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('forex_booking_id')->constrained('forex_bookings')->onDelete('cascade');
            $table->foreignId('forex_account_id')->constrained('forex_accounts')->onDelete('cascade');
            $table->decimal('reserved_amount', 18, 8)->comment('Amount reserved from this account for the booking');
            $table->enum('status', ['reserved', 'completed', 'cancelled'])->default('reserved')
                ->comment('Status of the reservation: reserved=active, completed=booking completed, cancelled=booking cancelled');
            $table->foreignId('forex_transaction_id')->nullable()->constrained('forex_transactions')->onDelete('set null')
                ->comment('Link to the actual debit transaction that created this reservation');
            $table->json('metadata')->nullable()->comment('Additional reservation data like priority order, account type, etc.');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['forex_booking_id', 'status'], 'idx_booking_status');
            $table->index(['forex_account_id', 'status'], 'idx_account_status');
            $table->index(['status', 'created_at'], 'idx_status_date');
            
            // Ensure no duplicate reservations for same booking-account combination
            $table->unique(['forex_booking_id', 'forex_account_id'], 'unique_booking_account');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_booking_reservations');
    }
};
