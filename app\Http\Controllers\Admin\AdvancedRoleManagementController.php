<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Models\AdvancedRolePermission;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

/**
 * Advanced Role Management Controller
 * 
 * Provides comprehensive role management with permission assignment interface.
 */
class AdvancedRoleManagementController extends Controller
{
    use HasAdvancedPermissions;

    /**
     * Display roles with permission information
     */
    public function index(Request $request): View
    {
        $query = AdvancedRole::withCount(['permissions', 'userRoles'])
            ->with('parentRole');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('category', $request->get('category'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($status === 'system') {
                $query->where('is_system', true);
            }
        }

        $sortBy = $request->get('sort_by', 'display_name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        if (in_array($sortBy, ['name', 'display_name', 'category', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('display_name');
        }

        $roles = $query->paginate(25)->withQueryString();

        // Get filter options
        $categories = AdvancedRole::distinct()->pluck('category')->filter()->sort();

        return view('admin.roles.index', compact('roles', 'categories'));
    }

    /**
     * Show form for creating new role
     */
    public function create(): View
    {
        $permissions = AdvancedPermission::active()
            ->orderBy('category')
            ->orderBy('resource')
            ->orderBy('action')
            ->get()
            ->groupBy('category');

        $parentRoles = AdvancedRole::active()
            ->whereNull('parent_role_id')
            ->orderBy('display_name')
            ->get();

        $categories = AdvancedRole::distinct()->pluck('category')->filter()->sort();

        return view('admin.roles.create', compact('permissions', 'parentRoles', 'categories'));
    }

    /**
     * Store new role with permissions
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:advanced_roles,name|regex:/^[a-z_]+$/',
            'display_name' => 'required|string|max:150',
            'description' => 'nullable|string|max:1000',
            'category' => 'nullable|string|max:50',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'parent_role_id' => 'nullable|exists:advanced_roles,id',
            'max_users' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'inherit_permissions' => 'boolean',
            'is_active' => 'boolean',
            'permissions' => 'array',
            'permissions.*' => 'exists:advanced_permissions,id',
            'permission_constraints' => 'array',
            'permission_constraints.*' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Create role
            $role = AdvancedRole::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'category' => $request->category,
                'color' => $request->color,
                'parent_role_id' => $request->parent_role_id,
                'max_users' => $request->max_users,
                'expires_at' => $request->expires_at,
                'inherit_permissions' => $request->boolean('inherit_permissions', true),
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Assign permissions
            if ($request->filled('permissions')) {
                foreach ($request->permissions as $permissionId) {
                    $constraints = $request->permission_constraints[$permissionId] ?? null;
                    $role->grantPermission(
                        AdvancedPermission::find($permissionId),
                        ['constraints' => $constraints]
                    );
                }
            }

            DB::commit();

            $this->logPermissionEvent(
                'role_created',
                'advanced_roles.create',
                "Created role: {$role->display_name}",
                ['role_id' => $role->id]
            );

            return redirect()->route('admin.roles.show', $role)
                ->with('success', "Role '{$role->display_name}' created successfully.");

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to create role: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display role with permissions and users
     */
    public function show(AdvancedRole $role): View
    {
        $role->load([
            'permissions',
            'rolePermissions.permission',
            'userRoles.user',
            'parentRole',
            'childRoles',
        ]);

        $availablePermissions = AdvancedPermission::active()
            ->whereNotIn('id', $role->permissions->pluck('id'))
            ->orderBy('category')
            ->orderBy('resource')
            ->orderBy('action')
            ->get()
            ->groupBy('category');

        $effectivePermissions = $role->getAllPermissions()->groupBy('category');

        $stats = [
            'direct_permissions' => $role->permissions()->count(),
            'inherited_permissions' => $role->getInheritedPermissions()->count(),
            'total_users' => $role->userRoles()->where('is_active', true)->count(),
            'child_roles' => $role->childRoles()->count(),
        ];

        return view('admin.roles.show', compact('role', 'availablePermissions', 'effectivePermissions', 'stats'));
    }

    /**
     * Show form for editing role
     */
    public function edit(AdvancedRole $role): View
    {
        if ($role->is_system) {
            return back()->withErrors(['error' => 'System roles cannot be edited.']);
        }

        $role->load(['permissions', 'rolePermissions']);

        $permissions = AdvancedPermission::active()
            ->orderBy('category')
            ->orderBy('resource')
            ->orderBy('action')
            ->get()
            ->groupBy('category');

        $parentRoles = AdvancedRole::active()
            ->where('id', '!=', $role->id)
            ->whereNull('parent_role_id')
            ->orderBy('display_name')
            ->get();

        $categories = AdvancedRole::distinct()->pluck('category')->filter()->sort();

        // Get current permission constraints
        $currentConstraints = [];
        foreach ($role->rolePermissions as $rolePermission) {
            $currentConstraints[$rolePermission->permission_id] = $rolePermission->constraints;
        }

        return view('admin.roles.edit', compact('role', 'permissions', 'parentRoles', 'categories', 'currentConstraints'));
    }

    /**
     * Update role and permissions
     */
    public function update(Request $request, AdvancedRole $role): RedirectResponse
    {
        if ($role->is_system) {
            return back()->withErrors(['error' => 'System roles cannot be modified.']);
        }

        $validator = Validator::make($request->all(), [
            'name' => "required|string|max:100|unique:advanced_roles,name,{$role->id}|regex:/^[a-z_]+$/",
            'display_name' => 'required|string|max:150',
            'description' => 'nullable|string|max:1000',
            'category' => 'nullable|string|max:50',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'parent_role_id' => 'nullable|exists:advanced_roles,id',
            'max_users' => 'nullable|integer|min:1',
            'expires_at' => 'nullable|date|after:now',
            'inherit_permissions' => 'boolean',
            'is_active' => 'boolean',
            'permissions' => 'array',
            'permissions.*' => 'exists:advanced_permissions,id',
            'permission_constraints' => 'array',
            'permission_constraints.*' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Update role
            $role->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'category' => $request->category,
                'color' => $request->color,
                'parent_role_id' => $request->parent_role_id,
                'max_users' => $request->max_users,
                'expires_at' => $request->expires_at,
                'inherit_permissions' => $request->boolean('inherit_permissions', true),
                'is_active' => $request->boolean('is_active', true),
            ]);

            // Update permissions
            $role->rolePermissions()->delete();
            
            if ($request->filled('permissions')) {
                foreach ($request->permissions as $permissionId) {
                    $constraints = $request->permission_constraints[$permissionId] ?? null;
                    $role->grantPermission(
                        AdvancedPermission::find($permissionId),
                        ['constraints' => $constraints]
                    );
                }
            }

            DB::commit();

            $this->logPermissionEvent(
                'role_updated',
                'advanced_roles.update',
                "Updated role: {$role->display_name}",
                ['role_id' => $role->id]
            );

            return redirect()->route('admin.roles.show', $role)
                ->with('success', "Role '{$role->display_name}' updated successfully.");

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Failed to update role: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Delete role
     */
    public function destroy(AdvancedRole $role): RedirectResponse
    {
        if ($role->is_system) {
            return back()->withErrors(['error' => 'System roles cannot be deleted.']);
        }

        if (!$role->isDeletable()) {
            return back()->withErrors(['error' => 'Role is in use and cannot be deleted.']);
        }

        try {
            $roleName = $role->display_name;
            $role->delete();

            $this->logPermissionEvent(
                'role_deleted',
                'advanced_roles.delete',
                "Deleted role: {$roleName}",
                ['role_name' => $roleName]
            );

            return redirect()->route('admin.roles.index')
                ->with('success', "Role '{$roleName}' deleted successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete role: ' . $e->getMessage()]);
        }
    }

    /**
     * Assign permission to role
     */
    public function assignPermission(Request $request, AdvancedRole $role): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'permission_id' => 'required|exists:advanced_permissions,id',
            'constraints' => 'nullable|array',
            'is_granted' => 'boolean',
            'priority' => 'nullable|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $permission = AdvancedPermission::find($request->permission_id);
            
            // Check if permission is already assigned
            $existingAssignment = $role->rolePermissions()
                ->where('permission_id', $permission->id)
                ->first();

            if ($existingAssignment) {
                return response()->json(['success' => false, 'message' => 'Permission already assigned to this role'], 400);
            }

            $role->grantPermission($permission, [
                'constraints' => $request->constraints,
                'is_granted' => $request->boolean('is_granted', true),
                'priority' => $request->priority ?? 0,
            ]);

            $this->logPermissionEvent(
                'permission_assigned_to_role',
                'advanced_roles.update',
                "Assigned permission '{$permission->display_name}' to role '{$role->display_name}'",
                ['role_id' => $role->id, 'permission_id' => $permission->id]
            );

            return response()->json([
                'success' => true,
                'message' => "Permission '{$permission->display_name}' assigned successfully",
                'permission' => [
                    'id' => $permission->id,
                    'name' => $permission->name,
                    'display_name' => $permission->display_name,
                    'constraints' => $request->constraints,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to assign permission: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Revoke permission from role
     */
    public function revokePermission(AdvancedRole $role, AdvancedPermission $permission): JsonResponse
    {
        try {
            $assignment = $role->rolePermissions()
                ->where('permission_id', $permission->id)
                ->first();

            if (!$assignment) {
                return response()->json(['success' => false, 'message' => 'Permission not assigned to this role'], 400);
            }

            $assignment->delete();

            $this->logPermissionEvent(
                'permission_revoked_from_role',
                'advanced_roles.update',
                "Revoked permission '{$permission->display_name}' from role '{$role->display_name}'",
                ['role_id' => $role->id, 'permission_id' => $permission->id]
            );

            return response()->json([
                'success' => true,
                'message' => "Permission '{$permission->display_name}' revoked successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to revoke permission: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Bulk assign permissions to role
     */
    public function bulkAssignPermissions(Request $request, AdvancedRole $role): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'permission_ids' => 'required|array|min:1',
            'permission_ids.*' => 'exists:advanced_permissions,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $assigned = 0;
            $skipped = 0;

            foreach ($request->permission_ids as $permissionId) {
                $permission = AdvancedPermission::find($permissionId);
                
                // Check if already assigned
                $existingAssignment = $role->rolePermissions()
                    ->where('permission_id', $permission->id)
                    ->exists();

                if (!$existingAssignment) {
                    $role->grantPermission($permission);
                    $assigned++;
                } else {
                    $skipped++;
                }
            }

            $this->logPermissionEvent(
                'bulk_permissions_assigned',
                'advanced_roles.update',
                "Bulk assigned {$assigned} permissions to role '{$role->display_name}'",
                ['role_id' => $role->id, 'assigned' => $assigned, 'skipped' => $skipped]
            );

            return response()->json([
                'success' => true,
                'message' => "Assigned {$assigned} permissions successfully" . ($skipped > 0 ? ", skipped {$skipped} already assigned" : ""),
                'assigned' => $assigned,
                'skipped' => $skipped,
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to assign permissions: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Set parent role
     */
    public function setParent(Request $request, AdvancedRole $role): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'parent_role_id' => 'nullable|exists:advanced_roles,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $role->update(['parent_role_id' => $request->parent_role_id]);

            $parentRole = $request->parent_role_id ? AdvancedRole::find($request->parent_role_id) : null;
            $message = $parentRole 
                ? "Set '{$parentRole->display_name}' as parent role"
                : "Removed parent role";

            $this->logPermissionEvent(
                'role_parent_updated',
                'advanced_roles.update',
                $message . " for role '{$role->display_name}'",
                ['role_id' => $role->id, 'parent_role_id' => $request->parent_role_id]
            );

            return response()->json([
                'success' => true,
                'message' => $message . ' successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update parent role: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Search roles for AJAX requests
     */
    public function searchRoles(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $limit = $request->get('limit', 20);

        $roles = AdvancedRole::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('display_name', 'like', "%{$query}%");
            })
            ->active()
            ->limit($limit)
            ->get(['id', 'name', 'display_name', 'category', 'color']);

        return response()->json($roles);
    }

    /**
     * Get role permissions for AJAX requests
     */
    public function getRolePermissions(AdvancedRole $role): JsonResponse
    {
        $permissions = $role->rolePermissions()
            ->with('permission')
            ->get()
            ->map(function ($rolePermission) {
                return [
                    'id' => $rolePermission->permission->id,
                    'name' => $rolePermission->permission->name,
                    'display_name' => $rolePermission->permission->display_name,
                    'category' => $rolePermission->permission->category,
                    'resource' => $rolePermission->permission->resource,
                    'action' => $rolePermission->permission->action,
                    'constraints' => $rolePermission->constraints,
                    'is_granted' => $rolePermission->is_granted,
                    'priority' => $rolePermission->priority,
                ];
            });

        return response()->json($permissions);
    }
}
