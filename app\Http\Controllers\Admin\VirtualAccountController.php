<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\VirtualAccount;
use App\Models\User;
use App\Models\Currency;
use App\Services\Payout\numero\Card as NumeroCard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class VirtualAccountController extends Controller
{
    /**
     * Display a listing of virtual accounts
     */
    public function index()
    {
        $data['basic'] = basicControl();
        $data['currencies'] = Currency::where('is_active', 1)->get();
        $data['stats'] = [
            'total_accounts' => VirtualAccount::count(),
            'active_accounts' => VirtualAccount::where('is_active', true)->count(),
            'ngn_accounts' => VirtualAccount::where('currency', 'NGN')->count(),
            'usd_accounts' => VirtualAccount::where('currency', 'USD')->count(),
        ];

        return view('admin.virtual_account.index', $data);
    }

    /**
     * Get virtual accounts data for DataTables
     */
    public function search(Request $request)
    {
        $search = $request->search['value'] ?? null;

        $virtualAccounts = VirtualAccount::with(['user', 'currency'])
            ->when(!empty($search), function ($query) use ($search) {
                return $query->where('account_number', 'LIKE', "%{$search}%")
                    ->orWhere('account_name', 'LIKE', "%{$search}%")
                    ->orWhere('bank_name', 'LIKE', "%{$search}%")
                    ->orWhere('provider', 'LIKE', "%{$search}%")
                    ->orWhere('currency', 'LIKE', "%{$search}%")
                    ->orWhere('type', 'LIKE', "%{$search}%")
                    ->orWhereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('firstname', 'LIKE', "%{$search}%")
                            ->orWhere('lastname', 'LIKE', "%{$search}%")
                            ->orWhere('email', 'LIKE', "%{$search}%")
                            ->orWhere('username', 'LIKE', "%{$search}%")
                            ->orWhere('phone', 'LIKE', "%{$search}%");
                    });
            })
            ->when($request->currency, function ($query) use ($request) {
                return $query->where('currency', $request->currency);
            })
            ->when($request->provider, function ($query) use ($request) {
                return $query->where('provider', $request->provider);
            })
            ->when($request->status !== null, function ($query) use ($request) {
                return $query->where('is_active', $request->status);
            })
            ->when($request->type, function ($query) use ($request) {
                return $query->where('type', $request->type);
            })
            ->latest();

        return DataTables::of($virtualAccounts)
            ->addColumn('checkbox', function ($item) {
                return '<input type="checkbox" class="form-check-input row-tic" name="check" value="' . $item->id . '">';
            })
            ->addColumn('user', function ($item) {
                $user = $item->user;
                $userType = ucfirst($user->type ?? 'user');
                return '<div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="avatar avatar-sm avatar-circle">
                                    <img class="avatar-img" src="' . $user->getImageUrlAttribute() . '" alt="' . $user->name . '">
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="text-inherit mb-0">' . $user->name . '</h5>
                                <span class="d-block fs-6 text-body">' . $user->email . '</span>
                                <span class="badge bg-soft-info text-info">' . $userType . '</span>
                            </div>
                        </div>';
            })
            ->addColumn('account_details', function ($item) {
                return '<div>
                            <strong>Account Number:</strong> ' . $item->account_number . '<br>
                            <strong>Account Name:</strong> ' . $item->account_name . '<br>
                            <strong>Bank:</strong> ' . $item->bank_name . '
                        </div>';
            })
            ->addColumn('provider', function ($item) {
                return '<span class="badge bg-soft-primary text-primary">' . ucfirst($item->provider) . '</span>';
            })
            ->addColumn('currency', function ($item) {
                return '<div class="d-flex align-items-center">
                            <span class="badge bg-soft-secondary text-secondary">' . $item->currency . '</span>
                            <span class="ms-2">' . optional($item->currency())->name . '</span>
                        </div>';
            })
            ->addColumn('type', function ($item) {
                $typeIcon = match($item->type) {
                    'business' => 'bi-building',
                    'customer' => 'bi-person-circle',
                    default => 'bi-person'
                };
                $typeClass = match($item->type) {
                    'business' => 'bg-soft-warning text-warning',
                    'customer' => 'bg-soft-primary text-primary',
                    default => 'bg-soft-info text-info'
                };
                return '<div class="d-flex align-items-center">
                            <i class="' . $typeIcon . ' me-2"></i>
                            <span class="badge ' . $typeClass . '">' . ucfirst($item->type) . '</span>
                        </div>';
            })
            ->addColumn('status', function ($item) {
                $status = $item->is_active ? 'Active' : 'Inactive';
                $badgeClass = $item->is_active ? 'bg-soft-success text-success' : 'bg-soft-danger text-danger';
                return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
            })
            ->addColumn('created_at', function ($item) {
                return '<div>
                            <span class="d-block h5 mb-0">' . dateTime($item->created_at, 'd M Y') . '</span>
                            <span class="d-block fs-6 text-body">' . dateTime($item->created_at, 'h:i A') . '</span>
                        </div>';
            })
            ->addColumn('action', function ($item) {
                $editUrl = route('admin.virtual.accounts.edit', $item->id);
                $viewUrl = route('admin.virtual.accounts.show', $item->id);

                return '<div class="btn-group" role="group">
                            <a href="' . $viewUrl . '" class="btn btn-white btn-sm">
                                <i class="bi-eye me-1"></i> View
                            </a>
                            <div class="btn-group">
                                <button type="button" class="btn btn-white btn-icon btn-sm dropdown-toggle dropdown-toggle-empty"
                                        data-bs-toggle="dropdown" aria-expanded="false"></button>
                                <div class="dropdown-menu dropdown-menu-end mt-1">
                                    <a class="dropdown-item" href="' . $editUrl . '">
                                        <i class="bi-pencil-square dropdown-item-icon"></i> Edit
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item text-danger" href="javascript:void(0)"
                                       onclick="toggleStatus(' . $item->id . ', ' . ($item->is_active ? 'false' : 'true') . ')">
                                        <i class="bi-' . ($item->is_active ? 'x-circle' : 'check-circle') . ' dropdown-item-icon"></i>
                                        ' . ($item->is_active ? 'Deactivate' : 'Activate') . '
                                    </a>
                                </div>
                            </div>
                        </div>';
            })
            ->rawColumns(['checkbox', 'user', 'account_details', 'provider', 'currency', 'type', 'status', 'created_at', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new virtual account
     */
    public function create()
    {
        $data['basic'] = basicControl();
        $data['users'] = User::where('status', 1)->get();
        $data['currencies'] = Currency::where('is_active', 1)->get();

        return view('admin.virtual_account.create', $data);
    }

    /**
     * Store a newly created virtual account
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'currency' => 'required|string|size:3|exists:currencies,code',
                'type' => 'required|string|in:customer,individual,business',
                'provider' => 'sometimes|string|in:numero'
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $user = User::findOrFail($request->user_id);
            $currency = $request->currency;
            $type = $request->type;
            $provider = $request->provider ?? 'numero';

            // Check if user already has a virtual account for this provider, currency and type
            if (VirtualAccount::userHasAccount($user->id, $provider, $currency, $type)) {
                return back()->with('error', 'User already has a ' . $type . ' virtual account for this currency and provider')->withInput();
            }

            // Create virtual account using the specified provider
            switch ($provider) {
                case 'numero':
                    $result = NumeroCard::createVirtualAccount($user, $currency, null, $type);
                    break;
                default:
                    return back()->with('error', 'Unsupported provider')->withInput();
            }

            if ($result['status'] !== 'success') {
                return back()->with('error', $result['data'])->withInput();
            }

            // Save virtual account to database
            $providerData = $result['data'];
            $virtualAccount = VirtualAccount::create([
                'user_id' => $user->id,
                'provider' => $provider,
                'currency' => $currency,
                'account_number' => $providerData['account_number'] ?? $providerData['accountNumber'],
                'account_name' => $providerData['account_name'] ?? $providerData['accountName'],
                'bank_name' => $providerData['bank_name'] ?? $providerData['bankName'],
                //'bank_code' => $providerData['bank_code'] ?? $providerData['bankCode'],
                'provider_data' => $providerData,
                'kyc_data_used' => NumeroCard::getKycDataForUser($user),
                'provider_reference' => $providerData['reference'] ?? $providerData['id'] ?? null,
                'is_active' => true,
                'created_at_provider' => $providerData['created_at'] ?? now(),
                'type' => $result['type'],
            ]);

            return redirect()->route('admin.virtual.accounts.index')
                ->with('success', 'Virtual account created successfully for ' . $user->name);

        } catch (\Exception $e) {
            Log::error('Admin virtual account creation failed: ' . $e->getMessage());
            return back()->with('error', 'Virtual account creation failed: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified virtual account
     */
    public function show($id)
    {
        $data['virtualAccount'] = VirtualAccount::with(['user', 'currency'])->findOrFail($id);
        $data['basic'] = basicControl();

        return view('admin.virtual_account.show', $data);
    }

    /**
     * Show the form for editing the specified virtual account
     */
    public function edit($id)
    {
        $data['virtualAccount'] = VirtualAccount::with(['user', 'currency'])->findOrFail($id);
        $data['basic'] = basicControl();

        return view('admin.virtual_account.edit', $data);
    }

    /**
     * Update the specified virtual account
     */
    public function update(Request $request, $id)
    {
        try {
            $virtualAccount = VirtualAccount::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $virtualAccount->update([
                'is_active' => $request->is_active
            ]);

            return redirect()->route('admin.virtual.accounts.index')
                ->with('success', 'Virtual account updated successfully');

        } catch (\Exception $e) {
            Log::error('Admin virtual account update failed: ' . $e->getMessage());
            return back()->with('error', 'Virtual account update failed: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Toggle virtual account status
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $virtualAccount = VirtualAccount::findOrFail($id);
            $virtualAccount->is_active = !$virtualAccount->is_active;
            $virtualAccount->save();

            $status = $virtualAccount->is_active ? 'activated' : 'deactivated';

            return response()->json([
                'status' => 'success',
                'message' => "Virtual account {$status} successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update virtual account status'
            ]);
        }
    }
}
