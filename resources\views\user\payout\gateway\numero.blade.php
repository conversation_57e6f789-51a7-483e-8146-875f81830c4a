@extends(userLayout())
@section('title',__('Payout Preview'))
@section('content')

    <div class="content {{ containerClass() }}">
        <x-page-header menu="Payout"/>

        <div class="row">
            <div class="col-lg-6 mb-3 mb-lg-0">
                <div class="card mb-3 mb-lg-5 h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Payout Confirm')</h4>
                    </div>
                    <form action="{{ userRoute('payout.numero',$payout->trx_id) }}"
                          method="post" enctype="multipart/form-data">
                        @csrf
                        <div class="card-body">

                            <div class="col-md-12 input-box dynamic-bank d-none mt-3">
                                <label class="form-label" for="dynamic-bank">@lang('Select Bank')</label>
                                <select id="dynamic-bank" name="bank"
                                        class="form-control cmn-select2">
                                </select>
                                @error('bank')
                                <span class="text-danger">{{$message}}</span>
                                @enderror
                            </div>

                            <div class="col-md-12 input-box mt-3" id="transfer-name-container">
                                <label class="form-label" for="transfer_name">@lang('Transfer Name')</label>
                                <select id="transfer_name" name="transfer_name"
                                        class="form-control cmn-select2 bank">
                                    <option value="">@lang('Select Transfer Name')</option>
                                    @if(isset($payout->method->banks))
                                        @foreach($payout->method->banks as $bank)
                                            <option value="{{$bank}}"
                                                    @if(old('transfer_name') == $bank) selected @endif>{{$bank}}</option>
                                        @endforeach
                                    @endif
                                </select>
                                @error('transfer_name')
                                <span class="text-danger">{{$message}}</span>
                                @enderror
                            </div>

                            <div class="dynamic-form">
                                <!-- Dynamic form fields will be loaded here -->
                            </div>

                            <!-- Hidden field for bank_code - required for form submission -->
                            <input type="hidden" name="bank_code" id="bank_code" value="{{ old('bank_code') }}">
                            <input type="hidden" name="account_name" id="account_name" value="{{ old('account_name') }}">

                            <div class="col-md-12 input-box mt-3">
                                <label class="form-label" for="security_pin">@lang('Security Pin')</label>
                                <input type="password" class="form-control" name="security_pin" id="security_pin"
                                       placeholder="@lang('Enter Security Pin')" autocomplete="off">
                                @error('security_pin')
                                <span class="text-danger">{{$message}}</span>
                                @enderror
                            </div>

                        </div>
                        <div class="card-footer">
                            <button type="submit" class="btn btn-primary">@lang('Confirm')</button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header card-header-content-between">
                        <h4 class="card-header-title">@lang('Transaction Details')</h4>
                    </div>
                    <div class="card-body">
                        @php $cc = $payout->payout_currency_code @endphp
                        <div class="list-group list-group-flush list-group-no-gutters showCharge">
                            <x-list-item label="Payout Method" value="{{ __($payout->method->name) }}" class="text-info fw-semibold"/>
                            <x-list-item label="Requested Amount" value="{{ getAmount($payout->amount).' '.$cc }}" class="fw-semibold"/>
                            <x-list-item label="Processing Fee" value="{{ getAmount($payout->charge).' '.$cc }}" class="text-danger"/>
                            <x-list-item label="Total Amount to be Debited" value="{{ (getAmount($payout->net_amount)) }} {{ $cc }}" class="text-warning fw-bold border-top border-bottom py-2"/>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Information')</h4>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush list-group-no-gutters">
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <small class="card-subtitle">@lang('Processing Time')</small>
                                        <span class="d-block">@lang('Instant - Real-time processing')</span>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <small class="card-subtitle">@lang('Supported Banks')</small>
                                        <span class="d-block">@lang('All Nigerian Banks')</span>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <small class="card-subtitle">@lang('Currency')</small>
                                        <span class="d-block">@lang('Nigerian Naira (NGN)')</span>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <small class="card-subtitle">@lang('Limits')</small>
                                        <span class="d-block">@lang('₦100 - ₦5,000,000')</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('script')
    <script type="text/javascript">
        'use strict';

        var bankName = null;
        var payAmount = '{{$payout->amount}}'
        var baseCurrency = "{{basicControl()->base_currency}}"
        var transferName = "{{old('transfer_name')}}";
        if (transferName) {
            getBankForm(transferName);
        }

        $(document).ready(function () {
            // Initialize Select2 first
            $('.cmn-select2').select2();

            // Auto-select first transfer name option and hide the field after Select2 initialization
            setTimeout(function() {
                autoSelectFirstTransferName();
            }, 100);

            $(document).on("change", ".bank", function () {
                bankName = $(this).val();
                $('.dynamic-bank').addClass('d-none');
                getBankForm(bankName);
            });

            // Handle dynamic bank selection change to update hidden bank_code field
            $(document).on("change", "#dynamic-bank", function () {
                var selectedBankCode = $(this).val();
                $('#bank_code').val(selectedBankCode);
            });
        });

        function autoSelectFirstTransferName() {
            var transferNameSelect = $('#transfer_name');
            var firstOption = transferNameSelect.find('option[value!=""]').first();

            // Debug mode - uncomment for debugging
            // console.log('Auto-selecting transfer name...');
            // console.log('First option found:', firstOption.length > 0);
            // console.log('Transfer name from old input:', transferName);

            if (firstOption.length > 0 && !transferName) {
                // Auto-select the first available option
                var firstValue = firstOption.val();

                // Debug mode - uncomment for debugging
                // console.log('Auto-selecting value:', firstValue);

                // Set value using Select2 method
                transferNameSelect.val(firstValue).trigger('change');

                // Update the bankName variable
                bankName = firstValue;

                // Hide the transfer name field container
                $('#transfer-name-container').addClass('d-none');

                // Call getBankForm directly to ensure it loads
                // Debug mode - uncomment for debugging
                // console.log('Calling getBankForm with:', firstValue);
                getBankForm(firstValue);
            } else {
                // Debug mode - uncomment for debugging
                // console.log('Auto-selection skipped - no options or transferName exists');
            }
        }

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function getBankForm(bankName) {
            // Debug mode - uncomment for debugging
            // console.log('getBankForm called with bankName:', bankName);

            $.ajax({
                url: "{{userRoute('payout.getBankForm')}}",
                type: "post",
                data: {
                    bankName,
                },
                beforeSend: function() {
                    // Debug mode - uncomment for debugging
                    // console.log('Sending AJAX request to get bank form...');
                },
                success: function (response) {
                    // Debug mode - uncomment for debugging
                    // console.log('getBankForm response:', response);

                    if (response.bank != null) {
                        // Debug mode - uncomment for debugging
                        // console.log('Showing bank list with', response.bank.data.length, 'banks');
                        showBank(response.bank.data)
                    }

                    if (response.input_form != null) {
                        // Debug mode - uncomment for debugging
                        // console.log('Showing input form with fields:', Object.keys(response.input_form));
                        showInputForm(response.input_form)
                    } else {
                        // Debug mode - uncomment for debugging
                        // console.log('No input form data received');
                    }
                },
                error: function(xhr, status, error) {
                    // Always log errors for debugging
                    console.error('getBankForm AJAX error:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                }
            });
        }

        function showBank(bankLists) {
            // Debug mode - uncomment for debugging
            // console.log('showBank called with:', bankLists);
            $('#dynamic-bank').html(``);
            if (bankLists.length > 0) {
                $('.dynamic-bank').removeClass('d-none');
                $('#dynamic-bank').append(`<option value="">@lang('Select Bank')</option>`);
                $.each(bankLists, function (index, value) {
                    $('#dynamic-bank').append(`<option value="${value.code}" data-name="${value.name}">${value.name}</option>`);
                });

                // Debug mode - uncomment for debugging
                // console.log('Bank dropdown populated with', bankLists.length, 'banks');

                // Re-initialize Select2 for the dynamic bank dropdown
                $('#dynamic-bank').select2();
            } else {
                // Debug mode - uncomment for debugging
                // console.log('No banks to display');
            }
        }

        function showInputForm(inputForm) {
            // Debug mode - uncomment for debugging
            // console.log('showInputForm called with:', inputForm);
            $('.dynamic-form').html('');
            if (inputForm != null) {
                var fieldsAdded = 0;
                $.each(inputForm, function (index, value) {
                    if (index != 'account_bank' && index != 'reference' && index != 'bank_code') {
                        let labelText = index.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        let placeholderText = 'Enter ' + index.replace(/_/g, ' ');
                        let html = `
                            <div class="col-md-12 input-box mt-3">
                                <label class="form-label" for="${index}">${labelText}</label>
                                <input type="text" class="form-control" name="${index}" id="${index}"
                                    placeholder="${placeholderText}" value="${value}" autocomplete="off">
                                <span class="text-danger" id="${index}-error"></span>
                            </div>
                        `;
                        $('.dynamic-form').append(html);
                        fieldsAdded++;
                    }
                });
                // Debug mode - uncomment for debugging
                // console.log('Added', fieldsAdded, 'dynamic form fields');
            } else {
                // Debug mode - uncomment for debugging.
                // console.log('No input form data to display');
            }
        }

        // Account validation for Numero
        $(document).on('blur', '#account_number', function() {
            let accountNumber = $(this).val();
            let bankCode = $('#dynamic-bank').val();

            if (accountNumber && bankCode) {
                validateAccount(accountNumber, bankCode);
            }
        });

        function validateAccount(accountNumber, bankCode) {
            $.ajax({
                url: "{{userRoute('payout.validate.account')}}",
                type: "post",
                data: {
                    account_number: accountNumber,
                    bank_code: bankCode,
                    method: 'numero'
                },
                beforeSend: function() {
                    $('#account_number').addClass('loading');
                },
                success: function (response) {
                    $('#account_number').removeClass('loading');
                    if (response.status === 'success') {
                        $('#account_name').val(response.data.accountName);
                        $('#account_number-error').text('').removeClass('text-danger').addClass('text-success').text(response.data.accountName);
                        $('#bank_code').val(response.data.bankCode)
                    } else {
                        $('#account_number-error').text(response.message || 'Account validation failed').removeClass('text-success').addClass('text-danger');
                    }
                },
                error: function() {
                    $('#account_number').removeClass('loading');
                    $('#account_number-error').text('Validation failed. Please try again.').removeClass('text-success').addClass('text-danger');
                }
            });
        }
    </script>
@endpush
