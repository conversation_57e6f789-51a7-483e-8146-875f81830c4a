<?php

/**
 * JSON Debug Test Script
 * 
 * This script tests the debug endpoint with properly formatted JSON
 * to isolate the JSON parsing issue.
 */

class JsonDebugTester
{
    private $baseUrl;

    public function __construct($baseUrl = 'http://currency.test')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Test with properly formatted JSON
     */
    public function testValidJson()
    {
        echo "=== Testing with Valid JSON ===\n";
        
        $url = $this->baseUrl . '/api/debug-request';
        $data = [
            'publicKey' => '12345',
            'secretKey' => '2d05d381bb3cf03fb6c496d5f0ba05a369c956b5',
            'testField' => 'test_value'
        ];

        echo "URL: $url\n";
        echo "Sending valid JSON data:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

        $response = $this->makeJsonRequest($url, $data);
        $this->analyzeResponse($response, 'Valid JSON');
    }

    /**
     * Test with the exact data from user's request (but fixed)
     */
    public function testUserDataFixed()
    {
        echo "=== Testing User Data (Fixed JSON) ===\n";
        
        $url = $this->baseUrl . '/api/debug-request';
        $data = [
            'publicKey' => '12345',
            'secretKey' => '2d05d381bb3cf03fb6c496d5f0ba05a369c956b5'
        ];

        echo "URL: $url\n";
        echo "Sending user data with fixed JSON:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

        $response = $this->makeJsonRequest($url, $data);
        $this->analyzeResponse($response, 'User Data Fixed');
    }

    /**
     * Test with malformed JSON to see error handling
     */
    public function testMalformedJson()
    {
        echo "=== Testing with Malformed JSON ===\n";
        
        $url = $this->baseUrl . '/api/debug-request';
        
        // Send the exact malformed JSON from user's request
        $malformedJson = '{"publicKey": "12345", "secretKey": 2d05d381bb3cf03fb6c496d5f0ba05a369c956b5}';
        
        echo "URL: $url\n";
        echo "Sending malformed JSON (missing quotes on secretKey):\n";
        echo $malformedJson . "\n\n";

        $response = $this->makeRawJsonRequest($url, $malformedJson);
        $this->analyzeResponse($response, 'Malformed JSON');
    }

    /**
     * Test with simple data
     */
    public function testSimpleJson()
    {
        echo "=== Testing with Simple JSON ===\n";
        
        $url = $this->baseUrl . '/api/debug-request';
        $data = [
            'test' => 'value',
            'number' => 123
        ];

        echo "URL: $url\n";
        echo "Sending simple JSON data:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

        $response = $this->makeJsonRequest($url, $data);
        $this->analyzeResponse($response, 'Simple JSON');
    }

    /**
     * Make a JSON request with proper encoding
     */
    private function makeJsonRequest($url, $data)
    {
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        if (curl_errno($curl)) {
            echo "cURL Error: " . curl_error($curl) . "\n";
            curl_close($curl);
            return false;
        }
        
        curl_close($curl);
        
        echo "HTTP Status: $httpCode\n";
        return $response;
    }

    /**
     * Make a request with raw JSON string
     */
    private function makeRawJsonRequest($url, $jsonString)
    {
        $curl = curl_init();
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonString,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        
        if (curl_errno($curl)) {
            echo "cURL Error: " . curl_error($curl) . "\n";
            curl_close($curl);
            return false;
        }
        
        curl_close($curl);
        
        echo "HTTP Status: $httpCode\n";
        return $response;
    }

    /**
     * Analyze the response from debug endpoint
     */
    private function analyzeResponse($response, $testName)
    {
        if (!$response) {
            echo "❌ No response received for $testName\n\n";
            return;
        }

        $result = json_decode($response, true);
        
        if (!$result) {
            echo "❌ Invalid JSON response for $testName:\n";
            echo $response . "\n\n";
            return;
        }

        echo "Response Analysis for $testName:\n";
        echo "- Status: " . ($result['status'] ?? 'unknown') . "\n";
        echo "- request_all: " . json_encode($result['request_all'] ?? []) . "\n";
        echo "- request_json_all: " . json_encode($result['request_json_all'] ?? []) . "\n";
        echo "- is_json: " . ($result['is_json'] ? 'true' : 'false') . "\n";
        echo "- wants_json: " . ($result['wants_json'] ? 'true' : 'false') . "\n";
        echo "- content_type: " . ($result['content_type'] ?? 'not set') . "\n";
        echo "- raw_content_length: " . ($result['raw_content_length'] ?? 0) . "\n";
        echo "- json_error: " . ($result['json_error'] ?? 'none') . "\n";
        echo "- decoded_json: " . json_encode($result['decoded_json'] ?? null) . "\n";

        // Determine if JSON parsing is working
        if (!empty($result['request_all']) || !empty($result['request_json_all'])) {
            echo "✅ JSON parsing is working for $testName!\n";
        } else {
            echo "❌ JSON parsing is NOT working for $testName\n";
            
            // Check if the issue is malformed JSON
            if ($result['json_error'] !== 'No error' && $result['json_error'] !== null) {
                echo "   Issue: JSON syntax error - " . $result['json_error'] . "\n";
            } else {
                echo "   Issue: Laravel is not parsing valid JSON into request data\n";
            }
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }
}

// Run the tests
echo "JSON Debug Test Suite\n";
echo "====================\n\n";

$tester = new JsonDebugTester('http://currency.test'); // Adjust URL as needed

echo "This script will test various JSON scenarios to identify the parsing issue.\n\n";

// Test 1: Simple valid JSON
$tester->testSimpleJson();

// Test 2: Valid JSON with user's data structure
$tester->testUserDataFixed();

// Test 3: Valid JSON with exact user data (fixed)
$tester->testValidJson();

// Test 4: Malformed JSON (like user's original request)
$tester->testMalformedJson();

echo "====================\n";
echo "Summary:\n";
echo "- If 'JSON parsing is working' appears for valid JSON tests, the issue is resolved\n";
echo "- If 'JSON parsing is NOT working' appears for valid JSON, there's still a Laravel config issue\n";
echo "- The malformed JSON test should show a JSON syntax error\n";
echo "\nNext steps:\n";
echo "1. Fix any JSON syntax errors in your requests\n";
echo "2. If valid JSON still doesn't work, check Laravel configuration\n";
echo "3. Test the authenticate endpoint with properly formatted JSON\n";
