<?php

namespace App\Providers;

use App\Http\Middleware\AdvancedPermissionMiddleware;
use App\Services\AdvancedPermissionService;
use App\Services\PermissionDiscoveryService;
use App\Services\PermissionTemplateService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Gate;

/**
 * Advanced Permission Service Provider
 *
 * Registers all advanced permission system services, middleware, and directives.
 */
class AdvancedPermissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register core services
        $this->app->singleton('advanced-permission', function ($app) {
            return new \App\Services\AdvancedPermissionService();
        });

        $this->app->singleton(\App\Services\AdvancedPermissionService::class, function ($app) {
            return $app['advanced-permission'];
        });

        $this->app->singleton(PermissionDiscoveryService::class, function ($app) {
            return new PermissionDiscoveryService();
        });

        $this->app->singleton(PermissionTemplateService::class, function ($app) {
            return new PermissionTemplateService();
        });

        // Register middleware
        $this->app->singleton(AdvancedPermissionMiddleware::class, function ($app) {
            return new AdvancedPermissionMiddleware($app[\App\Services\AdvancedPermissionService::class]);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register middleware alias
        $this->app['router']->aliasMiddleware('advanced.permission', AdvancedPermissionMiddleware::class);

        // Register Blade directives
        $this->registerBladeDirectives();

        // Register Gates
        $this->registerGates();

        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\DiscoverPermissions::class,
                \App\Console\Commands\ManagePermissionTemplates::class,
                \App\Console\Commands\ManageAdvancedPermissions::class,
                \App\Console\Commands\PermissionSystemHealth::class,
            ]);
        }
    }

    /**
     * Register Blade directives for permission checking
     */
    protected function registerBladeDirectives(): void
    {
        // @canAdvanced('permission.name')
        Blade::directive('canAdvanced', function ($expression) {
            return "<?php if(app('advanced-permission')->check(auth()->user(), {$expression})): ?>";
        });

        Blade::directive('endcanAdvanced', function () {
            return '<?php endif; ?>';
        });

        // @cannotAdvanced('permission.name')
        Blade::directive('cannotAdvanced', function ($expression) {
            return "<?php if(!app('advanced-permission')->check(auth()->user(), {$expression})): ?>";
        });

        Blade::directive('endcannotAdvanced', function () {
            return '<?php endif; ?>';
        });

        // @hasRole('role.name')
        Blade::directive('hasRole', function ($expression) {
            return "<?php if(app('advanced-permission')->hasRole(auth()->user(), {$expression})): ?>";
        });

        Blade::directive('endhasRole', function () {
            return '<?php endif; ?>';
        });

        // @hasAnyRole(['role1', 'role2'])
        Blade::directive('hasAnyRole', function ($expression) {
            return "<?php if(app('advanced-permission')->hasAnyRole(auth()->user(), {$expression})): ?>";
        });

        Blade::directive('endhasAnyRole', function () {
            return '<?php endif; ?>';
        });

        // @canAny(['perm1', 'perm2'])
        Blade::directive('canAny', function ($expression) {
            return "<?php if(app('advanced-permission')->checkMultiple(auth()->user(), {$expression}, 'or')): ?>";
        });

        Blade::directive('endcanAny', function () {
            return '<?php endif; ?>';
        });

        // @canAll(['perm1', 'perm2'])
        Blade::directive('canAll', function ($expression) {
            return "<?php if(app('advanced-permission')->checkMultiple(auth()->user(), {$expression}, 'and')): ?>";
        });

        Blade::directive('endcanAll', function () {
            return '<?php endif; ?>';
        });

        // @isSuperAdmin
        Blade::directive('isSuperAdmin', function () {
            return "<?php if(auth()->check() && method_exists(auth()->user(), 'isSuperAdmin') && auth()->user()->isSuperAdmin()): ?>";
        });

        Blade::directive('endisSuperAdmin', function () {
            return '<?php endif; ?>';
        });
    }

    /**
     * Register Laravel Gates for advanced permissions
     */
    protected function registerGates(): void
    {
        Gate::before(function ($user, $ability) {
            // Super admin bypass
            if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
                return true;
            }

            // Check if this is an advanced permission
            if (str_contains($ability, '.')) {
                $permissionService = app('advanced-permission');
                $result = $permissionService->check($user, $ability);
                return $result ? true : null; // Return null to continue with other gates
            }

            return null; // Let other gates handle non-advanced permissions
        });
    }
}
