<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add webhook_url to users table if it doesn't exist
        if (!Schema::hasColumn('users', 'webhook_url')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('webhook_url')->nullable()->comment('Webhook URL for transaction notifications')->after('email_verification');
            });
        }

        // Add webhook_url to merchant_settings table if it doesn't exist
        if (Schema::hasTable('merchant_settings') && !Schema::hasColumn('merchant_settings', 'webhook_url')) {
            Schema::table('merchant_settings', function (Blueprint $table) {
                $table->string('webhook_url')->nullable()->comment('Webhook URL for transaction notifications')->after('withdraw_information');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove webhook_url from users table
        if (Schema::hasColumn('users', 'webhook_url')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('webhook_url');
            });
        }

        // Remove webhook_url from merchant_settings table
        if (Schema::hasTable('merchant_settings') && Schema::hasColumn('merchant_settings', 'webhook_url')) {
            Schema::table('merchant_settings', function (Blueprint $table) {
                $table->dropColumn('webhook_url');
            });
        }
    }
};
