<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_account_transfers', function (Blueprint $table) {
            $table->id();
            $table->string('transfer_reference')->unique();
            $table->foreignId('from_account_id')->constrained('forex_accounts')->onDelete('cascade');
            $table->foreignId('to_account_id')->constrained('forex_accounts')->onDelete('cascade');
            $table->decimal('amount', 18, 8);
            $table->string('currency', 3);
            $table->text('description');
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('admins')->onDelete('cascade');
            $table->enum('status', ['pending', 'completed', 'failed'])->default('completed');
            $table->timestamps();
            
            $table->index(['from_account_id', 'created_at']);
            $table->index(['to_account_id', 'created_at']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_account_transfers');
    }
};
