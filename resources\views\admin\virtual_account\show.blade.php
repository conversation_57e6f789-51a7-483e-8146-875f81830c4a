@extends('admin.layouts.app')
@section('page_title', __('Virtual Account Details'))

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.virtual.accounts.index') }}">
                                    @lang('Virtual Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Account Details')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Virtual Account Details')</h1>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-primary" href="{{ route('admin.virtual.accounts.edit', $virtualAccount->id) }}">
                        <i class="bi-pencil-square me-1"></i> @lang('Edit Account')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row">
            <div class="col-lg-8">
                <!-- Card -->
                <div class="card">
                    <!-- Header -->
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Information')</h4>
                    </div>
                    <!-- End Header -->

                    <!-- Body -->
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Account Number'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-soft-primary text-primary">{{ $virtualAccount->account_number }}</span>
                                    </dd>
                                </dl>
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Account Name'):</dt>
                                    <dd class="col-sm-7">{{ $virtualAccount->account_name }}</dd>
                                </dl>
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Bank Name'):</dt>
                                    <dd class="col-sm-7">{{ $virtualAccount->bank_name }}</dd>
                                </dl>
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Provider'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-soft-info text-info">{{ ucfirst($virtualAccount->provider) }}</span>
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Currency'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-soft-warning text-warning">{{ $virtualAccount->currency }}</span>
                                    </dd>
                                </dl>
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Account Type'):</dt>
                                    <dd class="col-sm-7">
                                        @if($virtualAccount->type == 'customer')
                                            <span class="badge bg-soft-success text-success">@lang('Customer')</span>
                                        @elseif($virtualAccount->type == 'business')
                                            <span class="badge bg-soft-primary text-primary">@lang('Business')</span>
                                        @else
                                            <span class="badge bg-soft-secondary text-secondary">{{ ucfirst($virtualAccount->type) }}</span>
                                        @endif
                                    </dd>
                                </dl>
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Status'):</dt>
                                    <dd class="col-sm-7">
                                        @if($virtualAccount->is_active)
                                            <span class="badge bg-soft-success text-success">
                                                <i class="bi-check-circle me-1"></i> @lang('Active')
                                            </span>
                                        @else
                                            <span class="badge bg-soft-danger text-danger">
                                                <i class="bi-x-circle me-1"></i> @lang('Inactive')
                                            </span>
                                        @endif
                                    </dd>
                                </dl>
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Created'):</dt>
                                    <dd class="col-sm-7">{{ $virtualAccount->created_at->format('M d, Y H:i') }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Card -->
            </div>

            <div class="col-lg-4">
                <!-- Card -->
                <div class="card">
                    <!-- Header -->
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Owner')</h4>
                    </div>
                    <!-- End Header -->

                    <!-- Body -->
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                {!! $virtualAccount->user->profilePicture() !!}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-1">
                                    <a href="{{ route('admin.user.view.profile', $virtualAccount->user->id) }}">
                                        {{ $virtualAccount->user->name }}
                                    </a>
                                </h5>
                                <span class="d-block text-muted">{{ $virtualAccount->user->email }}</span>
                                <span class="d-block text-muted">{{ $virtualAccount->user->username }}</span>
                            </div>
                        </div>

                        <hr>

                        <dl class="row">
                            <dt class="col-sm-5">@lang('User ID'):</dt>
                            <dd class="col-sm-7">#{{ $virtualAccount->user->id }}</dd>
                        </dl>
                        <dl class="row">
                            <dt class="col-sm-5">@lang('Phone'):</dt>
                            <dd class="col-sm-7">{{ $virtualAccount->user->phone ?? 'N/A' }}</dd>
                        </dl>
                        <dl class="row">
                            <dt class="col-sm-5">@lang('Status'):</dt>
                            <dd class="col-sm-7">
                                @if($virtualAccount->user->status)
                                    <span class="badge bg-soft-success text-success">@lang('Active')</span>
                                @else
                                    <span class="badge bg-soft-danger text-danger">@lang('Inactive')</span>
                                @endif
                            </dd>
                        </dl>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Card -->

                <!-- Actions Card -->
                <div class="card">
                    <!-- Header -->
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Actions')</h4>
                    </div>
                    <!-- End Header -->

                    <!-- Body -->
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.virtual.accounts.edit', $virtualAccount->id) }}" class="btn btn-primary">
                                <i class="bi-pencil-square me-1"></i> @lang('Edit Account')
                            </a>

                            <form action="{{ route('admin.virtual.accounts.toggle.status', $virtualAccount->id) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn {{ $virtualAccount->is_active ? 'btn-danger' : 'btn-success' }} w-100"
                                        onclick="return confirm('Are you sure you want to {{ $virtualAccount->is_active ? 'deactivate' : 'activate' }} this account?')">
                                    <i class="bi-{{ $virtualAccount->is_active ? 'x-circle' : 'check-circle' }} me-1"></i>
                                    {{ $virtualAccount->is_active ? __('Deactivate') : __('Activate') }}
                                </button>
                            </form>
                        </div>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Actions Card -->
            </div>
        </div>
    </div>
@endsection
