@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Transfer History')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Transfer History')</h1>
                    <p class="page-header-text">@lang('Complete history of all account transfers')</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Accounts')
                        </a>
                        <a class="btn btn-primary" href="{{ route('admin.forex.accounts.transfer') }}">
                            <i class="bi-plus-circle me-1"></i> @lang('New Transfer')
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Transfer Summary -->
        <div class="row mb-3 mb-lg-5">
            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Transfers')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-dark">
                                    {{ number_format($summary['total_transfers']) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Completed')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-success">
                                    {{ number_format($summary['completed_transfers']) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6 mb-3 mb-lg-0">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Pending')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-warning">
                                    {{ number_format($summary['pending_transfers']) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-sm-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Total Amount')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-6 text-info">
                                    {{ number_format($summary['total_amount'], 2) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Transfer Summary -->



        <!-- Transfer History -->
        <div class="card">
            <div class="card-header card-header-content-md-between">
                <div class="mb-2 mb-md-0">
                    <div class="input-group input-group-merge input-group-flush">
                        <div class="input-group-prepend input-group-text">
                            <i class="bi-search"></i>
                        </div>
                        <input id="datatableSearch" type="search" class="form-control" placeholder="@lang('Search transfers')" aria-label="@lang('Search transfers')">
                    </div>
                </div>

                <div class="d-grid d-sm-flex gap-2">
                    <div class="dropdown">
                        <button type="button" class="btn btn-white btn-sm dropdown-toggle w-100" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi-filter me-1"></i> @lang('Filter')
                        </button>
                        <div class="dropdown-menu dropdown-menu-sm-end dropdown-card card-dropdown-filter-centered" aria-labelledby="filterDropdown">
                            <div class="card card-sm">
                                <div class="card-body card-body-centered">
                                    <form>
                                        <div class="row">
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('Date Range')</span>
                                                <input type="text" class="form-control" id="filter_date_range" autocomplete="off" placeholder="@lang('Select date range')">
                                            </div>
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('From Account')</span>
                                                <select class="form-select" id="filter_from_account">
                                                    <option value="">@lang('All Accounts')</option>
                                                    @foreach($accounts as $account)
                                                        <option value="{{ $account->id }}">{{ $account->account_name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('To Account')</span>
                                                <select class="form-select" id="filter_to_account">
                                                    <option value="">@lang('All Accounts')</option>
                                                    @foreach($accounts as $account)
                                                        <option value="{{ $account->id }}">{{ $account->account_name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-12 mb-4">
                                                <span class="text-cap text-body">@lang('Status')</span>
                                                <select class="form-select" id="filter_status">
                                                    <option value="">@lang('All Status')</option>
                                                    @foreach($statusOptions as $key => $value)
                                                        <option value="{{ $key }}">{{ $value }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row gx-2 mt-4">
                                            <div class="col">
                                                <button type="button" id="clear_filter" class="btn btn-white w-100">@lang('Clear Filters')</button>
                                            </div>
                                            <div class="col">
                                                <button type="button" class="btn btn-primary w-100" id="filter_button">
                                                    <i class="bi-search"></i> @lang('Apply')
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive datatable-custom">
                <table id="datatable"
                       class="js-datatable table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                       data-hs-datatables-options='{
                       "columnDefs": [{
                          "targets": [0, 7],
                          "orderable": false
                        }],
                       "order": [],
                       "info": {
                         "totalQty": "#datatableWithPaginationInfoTotalQty"
                       },
                       "search": "#datatableSearch",
                       "entries": "#datatableEntries",
                       "pageLength": 15,
                       "isResponsive": false,
                       "isShowPaging": false,
                       "pagination": "datatablePagination"
                     }'>
                    <thead class="thead-light">
                    <tr>
                        <th>@lang('Date')</th>
                        <th>@lang('Reference')</th>
                        <th>@lang('From Account')</th>
                        <th>@lang('To Account')</th>
                        <th>@lang('Amount')</th>
                        <th>@lang('Status')</th>
                        <th>@lang('Created By')</th>
                        <th>@lang('Action')</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>

            <!-- Footer -->
            <div class="card-footer">
                <div class="row justify-content-center justify-content-sm-between align-items-sm-center">
                    <div class="col-sm mb-2 mb-sm-0">
                        <div class="d-flex justify-content-center justify-content-sm-start align-items-center">
                            <span class="me-2">@lang('Showing'):</span>
                            <div class="tom-select-custom">
                                <select id="datatableEntries" class="js-select form-select form-select-borderless w-auto"
                                        autocomplete="off"
                                        data-hs-tom-select-options='{
                                            "searchInDropdown": false,
                                            "hideSearch": true
                                          }'>
                                    <option value="10">10</option>
                                    <option value="15" selected>15</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                </select>
                            </div>
                            <span class="text-secondary me-2">@lang('of')</span>
                            <span id="datatableWithPaginationInfoTotalQty"></span>
                        </div>
                    </div>
                    <!-- End Col -->

                    <div class="col-sm-auto">
                        <div class="d-flex justify-content-center justify-content-sm-end">
                            <!-- Pagination -->
                            <nav id="datatablePagination" aria-label="Activity pagination"></nav>
                        </div>
                    </div>
                    <!-- End Col -->
                </div>
                <!-- End Row -->
            </div>
            <!-- End Footer -->
        </div>
        <!-- End Transfer History -->
    </div>
@endsection

<x-assets :datatable="true" :counter="true" :flatpickr="true"/>

@push('script')
    <script>
        $(document).on('ready', function () {
            initDataTable("{{ route('admin.forex.accounts.transfer.search') }}",
                {!! json_encode(['date', 'reference', 'from_account', 'to_account', 'amount', 'status', 'created_by', 'action']) !!}
            );

            document.getElementById("filter_button").addEventListener("click", function () {
                applyFilter("{{ route('admin.forex.accounts.transfer.search') }}", {
                    filterStatus: $('#filter_status').val(),
                    filterFromAccount: $('#filter_from_account').val(),
                    filterToAccount: $('#filter_to_account').val(),
                    filterDate: $('#filter_date_range').val()
                });
            });

            document.getElementById("clear_filter").addEventListener("click", function () {
                $('#filter_status').val('').trigger('change');
                $('#filter_from_account').val('').trigger('change');
                $('#filter_to_account').val('').trigger('change');
                $('#filter_date_range').val('');
                applyFilter("{{ route('admin.forex.accounts.transfer.search') }}", {});
            });

            // Initialize date range picker
            $('#filter_date_range').flatpickr({
                mode: "range",
                dateFormat: "Y-m-d",
                placeholder: "@lang('Select date range')"
            });
        });
    </script>
@endpush
