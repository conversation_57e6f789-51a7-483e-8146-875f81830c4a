@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.show', $account->id) }}">
                                    {{ $account->account_name }}
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.transactions', $account->id) }}">
                                    @lang('Transactions')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $transaction->transaction_reference }}</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Transaction Details')</h1>
                    <p class="page-header-text">{{ $transaction->transaction_reference }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.transactions', $account->id) }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Transactions')
                        </a>
                        @if($transaction->forexBooking)
                            <a class="btn btn-outline-primary" href="{{ route('admin.forex.bookings.show', $transaction->forexBooking->id) }}">
                                <i class="bi-receipt me-1"></i> @lang('View Booking')
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row">
            <div class="col-lg-8 mb-3 mb-lg-5">
                <!-- Transaction Details -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transaction Information')</h4>
                        <span class="badge bg-{{ $transaction->type_class }}">
                            {{ ucfirst(str_replace('_', ' ', $transaction->transaction_type)) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Reference'):</dt>
                                    <dd class="col-sm-7">
                                        <code>{{ $transaction->transaction_reference }}</code>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Type'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-{{ $transaction->type_class }}">
                                            {{ ucfirst(str_replace('_', ' ', $transaction->transaction_type)) }}
                                        </span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Amount'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="fw-bold fs-4">{{ $transaction->formatted_amount }}</span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Currency'):</dt>
                                    <dd class="col-sm-7">{{ $transaction->currency }}</dd>

                                    <dt class="col-sm-5">@lang('Date'):</dt>
                                    <dd class="col-sm-7">{{ $transaction->created_at->format('M d, Y H:i:s') }}</dd>
                                </dl>
                            </div>
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-6">@lang('Balance Before'):</dt>
                                    <dd class="col-sm-6">{{ $transaction->formatted_balance_before }}</dd>

                                    <dt class="col-sm-6">@lang('Balance After'):</dt>
                                    <dd class="col-sm-6">{{ $transaction->formatted_balance_after }}</dd>

                                    <dt class="col-sm-6">@lang('Created By'):</dt>
                                    <dd class="col-sm-6">{{ $transaction->createdBy->name ?? 'System' }}</dd>

                                    <dt class="col-sm-6">@lang('Account'):</dt>
                                    <dd class="col-sm-6">
                                        <a href="{{ route('admin.forex.accounts.show', $account->id) }}">
                                            {{ $account->account_name }}
                                        </a>
                                    </dd>
                                </dl>
                            </div>
                        </div>

                        @if($transaction->description)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Description')</h6>
                                <p class="text-muted">{{ $transaction->description }}</p>
                            </div>
                        @endif

                        @if($transaction->notes)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Notes')</h6>
                                <p class="text-muted">{{ $transaction->notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- End Transaction Details -->

                @if($transaction->forexBooking)
                    <!-- Related Booking -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h4 class="card-header-title">@lang('Related Booking')</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-6">
                                    <dl class="row">
                                        <dt class="col-sm-5">@lang('Reference'):</dt>
                                        <dd class="col-sm-7">
                                            <a href="{{ route('admin.forex.bookings.show', $transaction->forexBooking->id) }}">
                                                {{ $transaction->forexBooking->booking_reference }}
                                            </a>
                                        </dd>

                                        <dt class="col-sm-5">@lang('Client'):</dt>
                                        <dd class="col-sm-7">{{ $transaction->forexBooking->client_name }}</dd>

                                        <dt class="col-sm-5">@lang('Status'):</dt>
                                        <dd class="col-sm-7">
                                            <span class="badge bg-{{ $transaction->forexBooking->status_class }}">
                                                {{ ucfirst($transaction->forexBooking->status) }}
                                            </span>
                                        </dd>
                                    </dl>
                                </div>
                                <div class="col-sm-6">
                                    <dl class="row">
                                        <dt class="col-sm-6">@lang('Amount'):</dt>
                                        <dd class="col-sm-6">{{ $transaction->forexBooking->formatted_amount }}</dd>

                                        <dt class="col-sm-6">@lang('Type'):</dt>
                                        <dd class="col-sm-6">{{ ucfirst($transaction->forexBooking->transaction_type) }}</dd>

                                        <dt class="col-sm-6">@lang('Created'):</dt>
                                        <dd class="col-sm-6">{{ $transaction->forexBooking->created_at->format('M d, Y') }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Related Booking -->
                @endif

                @if($relatedTransactions->count() > 0)
                    <!-- Related Transactions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h4 class="card-header-title">@lang('Related Transactions')</h4>
                            <small class="text-muted">@lang('Linked transactions (e.g., transfer counterparts)')</small>
                        </div>
                        <div class="card-body">
                            @foreach($relatedTransactions as $relatedTransaction)
                                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="{{ route('admin.forex.accounts.transaction.show', [$relatedTransaction->forex_account_id, $relatedTransaction->id]) }}">
                                                {{ $relatedTransaction->transaction_reference }}
                                            </a>
                                        </h6>
                                        <p class="text-muted mb-0">{{ $relatedTransaction->description }}</p>
                                        <small class="text-muted">
                                            {{ $relatedTransaction->forexAccount->account_name }} •
                                            {{ $relatedTransaction->created_at->format('M d, Y H:i') }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ $relatedTransaction->type_class }}">
                                            {{ ucfirst(str_replace('_', ' ', $relatedTransaction->transaction_type)) }}
                                        </span>
                                        <div class="fw-bold">{{ $relatedTransaction->formatted_amount }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- End Related Transactions -->
                @endif
            </div>

            <div class="col-lg-4">
                <!-- Metadata -->
                @if($transaction->metadata)
                    <div class="card mb-3">
                        <div class="card-header">
                            <h4 class="card-header-title">@lang('Additional Information')</h4>
                        </div>
                        <div class="card-body">
                            @foreach($transaction->metadata as $key => $value)
                                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                    <span class="text-muted">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                    <span class="fw-semibold">
                                        @if(is_array($value))
                                            {{ json_encode($value) }}
                                        @else
                                            {{ $value }}
                                        @endif
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Quick Actions')</h4>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.forex.accounts.show', $account->id) }}" class="btn btn-outline-primary">
                                <i class="bi-bank me-1"></i> @lang('View Account')
                            </a>
                            <a href="{{ route('admin.forex.accounts.transactions', $account->id) }}" class="btn btn-outline-secondary">
                                <i class="bi-list-ul me-1"></i> @lang('All Transactions')
                            </a>
                            <a href="{{ route('admin.forex.accounts.fund', $account->id) }}" class="btn btn-outline-success">
                                <i class="bi-plus-circle me-1"></i> @lang('Fund Account')
                            </a>
                            @if($transaction->forexBooking)
                                <a href="{{ route('admin.forex.bookings.show', $transaction->forexBooking->id) }}" class="btn btn-outline-info">
                                    <i class="bi-receipt me-1"></i> @lang('View Booking')
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- End Quick Actions -->

                <!-- Account Summary -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Summary')</h4>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-6">@lang('Current Balance'):</dt>
                            <dd class="col-6">{{ $account->formatted_balance }}</dd>

                            <dt class="col-6">@lang('Available'):</dt>
                            <dd class="col-6">{{ $account->formatted_balance }}</dd>

                            @if($account->pending_balance > 0)
                                <dt class="col-6">@lang('Pending'):</dt>
                                <dd class="col-6 text-warning">{{ number_format($account->pending_balance, 2) }} {{ $account->currency_code }}</dd>
                            @endif
                        </dl>
                    </div>
                </div>
                <!-- End Account Summary -->
            </div>
        </div>
    </div>
@endsection
