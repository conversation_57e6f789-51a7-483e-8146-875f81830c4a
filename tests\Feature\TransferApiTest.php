<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Currency;
use App\Models\PayoutMethod;
use App\Models\Wallet;
use App\Models\Payout;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

class TransferApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $currency;
    protected $payoutMethod;
    protected $wallet;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'type' => 'user',
            'status' => 1,
            'email_verification' => 1,
            'sms_verification' => 1,
            'two_fa_verify' => 1
        ]);

        // Create test currency
        $this->currency = Currency::factory()->create([
            'code' => 'NGN',
            'name' => 'Nigerian Naira',
            'is_active' => 1
        ]);

        // Create Numero payout method
        $this->payoutMethod = PayoutMethod::factory()->create([
            'code' => 'numero',
            'name' => 'Numero',
            'is_active' => 1,
            'parameters' => (object) [
                'api_key' => 'test_api_key',
                'public_key' => 'test_public_key',
                'base_url' => 'https://api-dev.getnumero.co/numeroaccount'
            ],
            'payout_currencies' => [
                [
                    'name' => 'NGN',
                    'currency_symbol' => 'NGN',
                    'min_limit' => 100,
                    'max_limit' => 1000000,
                    'percentage_charge' => 1.5,
                    'fixed_charge' => 50
                ]
            ]
        ]);

        // Create wallet with sufficient balance
        $this->wallet = Wallet::factory()->create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'balance' => 100000
        ]);
    }

    /**
     * Test validate account endpoint with valid data
     */
    public function test_validate_account_success()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/validate-account', [
            'accountNumber' => '**********',
            'bankCode' => '044'
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message'
        ]);
    }

    /**
     * Test validate account endpoint with invalid data
     */
    public function test_validate_account_validation_errors()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/validate-account', [
            'accountNumber' => '123', // Too short
            'bankCode' => '44' // Too short
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'failed'
        ]);
    }

    /**
     * Test single transfer endpoint with valid data
     */
    public function test_single_transfer_success()
    {
        Sanctum::actingAs($this->user);

        $transferData = [
            'amount' => 1000,
            'currency' => 'NGN',
            'narration' => 'Test payment',
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '044',
            'destinationAccountName' => 'John Doe'
        ];

        $response = $this->postJson('/api/single-transfer', $transferData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'transaction_id',
                'amount',
                'charge',
                'net_amount'
            ]
        ]);

        // Verify payout record was created
        $this->assertDatabaseHas('payouts', [
            'user_id' => $this->user->id,
            'amount' => 1000,
            'payout_method_id' => $this->payoutMethod->id
        ]);

        // Verify transaction record was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $this->user->id,
            'trx_type' => '-',
            'remarks' => 'Transfer request - amount debited'
        ]);
    }

    /**
     * Test single transfer with insufficient balance
     */
    public function test_single_transfer_insufficient_balance()
    {
        Sanctum::actingAs($this->user);

        // Update wallet to have insufficient balance
        $this->wallet->update(['balance' => 100]);

        $transferData = [
            'amount' => 10000,
            'currency' => 'NGN',
            'narration' => 'Test payment',
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '044',
            'destinationAccountName' => 'John Doe'
        ];

        $response = $this->postJson('/api/single-transfer', $transferData);

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'failed',
            'message' => 'Insufficient wallet balance'
        ]);
    }

    /**
     * Test bulk transfer endpoint with valid data
     */
    public function test_bulk_transfer_success()
    {
        Sanctum::actingAs($this->user);

        $transferData = [
            'currency' => 'NGN',
            'transfers' => [
                [
                    'amount' => 1000,
                    'narration' => 'Payment 1',
                    'destinationAccountNumber' => '**********',
                    'destinationBankCode' => '044',
                    'destinationAccountName' => 'John Doe'
                ],
                [
                    'amount' => 2000,
                    'narration' => 'Payment 2',
                    'destinationAccountNumber' => '**********',
                    'destinationBankCode' => '058',
                    'destinationAccountName' => 'Jane Smith'
                ]
            ]
        ];

        $response = $this->postJson('/api/bulk-transfer', $transferData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'batch_id',
                'total_transfers',
                'total_amount',
                'total_charge',
                'total_net_amount',
                'transfers'
            ]
        ]);

        // Verify multiple payout records were created
        $this->assertEquals(2, Payout::where('user_id', $this->user->id)->count());

        // Verify all payouts have batch information
        $payouts = Payout::where('user_id', $this->user->id)->get();
        foreach ($payouts as $payout) {
            $this->assertArrayHasKey('batch_id', $payout->information);
            $this->assertArrayHasKey('is_bulk', $payout->information);
            $this->assertTrue($payout->information['is_bulk']);
        }
    }

    /**
     * Test bulk transfer with insufficient balance
     */
    public function test_bulk_transfer_insufficient_balance()
    {
        Sanctum::actingAs($this->user);

        // Update wallet to have insufficient balance
        $this->wallet->update(['balance' => 1000]);

        $transferData = [
            'currency' => 'NGN',
            'transfers' => [
                [
                    'amount' => 10000,
                    'narration' => 'Payment 1',
                    'destinationAccountNumber' => '**********',
                    'destinationBankCode' => '044',
                    'destinationAccountName' => 'John Doe'
                ],
                [
                    'amount' => 20000,
                    'narration' => 'Payment 2',
                    'destinationAccountNumber' => '**********',
                    'destinationBankCode' => '058',
                    'destinationAccountName' => 'Jane Smith'
                ]
            ]
        ];

        $response = $this->postJson('/api/bulk-transfer', $transferData);

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'failed',
            'message' => 'Insufficient wallet balance for bulk transfer'
        ]);
    }

    /**
     * Test authentication requirement for all endpoints
     */
    public function test_endpoints_require_authentication()
    {
        // Test validate account without auth
        $response = $this->postJson('/api/validate-account', [
            'accountNumber' => '**********',
            'bankCode' => '044'
        ]);
        $response->assertStatus(401);

        // Test single transfer without auth
        $response = $this->postJson('/api/single-transfer', [
            'amount' => 1000,
            'currency' => 'NGN',
            'narration' => 'Test',
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '044',
            'destinationAccountName' => 'John Doe'
        ]);
        $response->assertStatus(401);

        // Test bulk transfer without auth
        $response = $this->postJson('/api/bulk-transfer', [
            'currency' => 'NGN',
            'transfers' => []
        ]);
        $response->assertStatus(401);
    }

    /**
     * Test charge calculation for transfers
     */
    public function test_charge_calculation()
    {
        Sanctum::actingAs($this->user);

        $transferData = [
            'amount' => 1000,
            'currency' => 'NGN',
            'narration' => 'Test payment',
            'destinationAccountNumber' => '**********',
            'destinationBankCode' => '044',
            'destinationAccountName' => 'John Doe'
        ];

        $response = $this->postJson('/api/single-transfer', $transferData);

        $response->assertStatus(200);
        
        $responseData = $response->json('data');
        
        // Verify charge calculation (1.5% + 50 fixed)
        $expectedCharge = (1000 * 1.5 / 100) + 50; // 15 + 50 = 65
        $this->assertEquals($expectedCharge, $responseData['charge']);
        $this->assertEquals(1000 + $expectedCharge, $responseData['net_amount']);
    }
}
