<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forex_bookings', function (Blueprint $table) {
            // Add payment method field to track how payment will be made
            if (!Schema::hasColumn('forex_bookings', 'payment_method')) {
                $table->enum('payment_method', ['account_details', 'wallet'])
                      ->default('account_details')
                      ->after('account_details')
                      ->comment('Payment method: account_details for bank details, wallet for user wallet');
            }

            // Add wallet currency ID to track which wallet to fund
            if (!Schema::hasColumn('forex_bookings', 'wallet_currency_id')) {
                $table->unsignedBigInteger('wallet_currency_id')
                      ->nullable()
                      ->after('payment_method')
                      ->comment('Currency ID of the wallet to be funded when payment_method is wallet');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forex_bookings', function (Blueprint $table) {
            $table->dropColumn(['payment_method', 'wallet_currency_id']);
        });
    }
};
