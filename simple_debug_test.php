<?php

require 'vendor/autoload.php';

use App\Traits\Notify;
use App\Models\NotificationTemplate;
use Illuminate\Support\Facades\DB;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== SIMPLE DEBUG TEST ===\n\n";

// Check email notification setting
$basic = basicControl();
echo "Email notification: " . ($basic->email_notification ? 'Enabled' : 'Disabled') . "\n";

// Check template
$template = NotificationTemplate::where('template_key', 'FOREX_BOOKING_CONFIRMATION')->first();
echo "Template found: " . ($template ? 'Yes' : 'No') . "\n";
if ($template) {
    echo "Template mail status: " . ($template->status['mail'] ? 'Enabled' : 'Disabled') . "\n";
}

// Check initial queue count
$initialCount = DB::table('jobs')->count();
echo "Initial queue jobs: {$initialCount}\n\n";

// Test class
class DebugEmailTest {
    use Notify;
}

$emailTest = new DebugEmailTest();

// Create test user
$testUser = new stdClass();
$testUser->email = '<EMAIL>';
$testUser->username = 'Debug User';
$testUser->language_id = 1;
$testUser->notifypermission = new stdClass();
$testUser->notifypermission->template_email_key = [
    'FOREX_BOOKING_CONFIRMATION'
];

echo "User object created:\n";
echo "- Email: {$testUser->email}\n";
echo "- Username: {$testUser->username}\n";
echo "- Language ID: {$testUser->language_id}\n";
echo "- Permissions: " . json_encode($testUser->notifypermission->template_email_key) . "\n\n";

// Test parameters
$params = [
    'client_name' => 'Debug Client',
    'booking_reference' => 'DEBUG123'
];

echo "Calling mail() method...\n";
$result = $emailTest->mail($testUser, 'FOREX_BOOKING_CONFIRMATION', $params);

echo "Mail result: " . ($result ? 'TRUE' : 'FALSE') . "\n";

// Check final queue count
$finalCount = DB::table('jobs')->count();
echo "Final queue jobs: {$finalCount}\n";
echo "Jobs added: " . ($finalCount - $initialCount) . "\n";

echo "\n=== CHECK LOGS FOR DEBUG INFO ===\n";
