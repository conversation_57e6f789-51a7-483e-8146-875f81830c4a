<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\ForexAccount;
use App\Models\ForexRate;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ForexBookingWalletControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $user;
    protected $usdCurrency;
    protected $ngnCurrency;
    protected $forexAccount;
    protected $activeRate;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test currencies
        $this->usdCurrency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
            'is_active' => 1,
            'currency_type' => 0,
            'driver' => 'local',
            'logo' => 'usd.png'
        ]);

        $this->ngnCurrency = Currency::create([
            'name' => 'Nigerian Naira',
            'code' => 'NGN',
            'symbol' => '₦',
            'is_active' => 1,
            'currency_type' => 1,
            'driver' => 'local',
            'logo' => 'ngn.png'
        ]);

        // Create admin user
        $this->admin = User::create([
            'firstname' => 'Admin',
            'lastname' => 'User',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'email_verified_at' => now(),
        ]);

        // Create regular user
        $this->user = User::create([
            'firstname' => 'Test',
            'lastname' => 'User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'email_verified_at' => now(),
        ]);

        // Create forex account
        $this->forexAccount = ForexAccount::create([
            'account_name' => 'Test USD Account',
            'account_type' => 'USD',
            'currency_code' => 'USD',
            'balance' => 10000.00,
            'pending_balance' => 0.00,
            'is_active' => true,
            'description' => 'Test account for USD transactions'
        ]);

        // Create active forex rate
        $this->activeRate = ForexRate::create([
            'cbn_rate' => 800.00,
            'parallel_rate' => 1200.00,
            'markup_percentage' => 5.00,
            'cbn_sell_rate' => 790.00,
            'parallel_sell_rate' => 1190.00,
            'sell_markup_percentage' => 3.00,
            'is_active' => true,
            'created_by' => 1,
        ]);
    }

    /** @test */
    public function admin_can_get_user_wallet_info()
    {
        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        $response = $this->actingAs($this->admin)
            ->getJson(route('admin.forex.bookings.user.wallet.info', [
                'user_id' => $this->user->id,
                'transaction_type' => 'buying'
            ]));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'has_wallet' => true,
                    'target_currency' => 'USD',
                    'payment_methods' => [
                        'account_details' => [
                            'label' => 'Bank Account Details'
                        ],
                        'wallet' => [
                            'label' => 'Pay to USD Wallet'
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function admin_can_create_booking_with_wallet_payment()
    {
        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        $bookingData = [
            'client_type' => 'user',
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_phone' => '+************',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->forexAccount->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id,
            'payment_instructions' => 'Test wallet payment'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.forex.bookings.store'), $bookingData);

        $response->assertRedirect()
            ->assertSessionHas('success');

        $this->assertDatabaseHas('forex_bookings', [
            'user_id' => $this->user->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id,
            'transaction_type' => 'buying',
            'amount' => 100.00
        ]);
    }

    /** @test */
    public function admin_cannot_create_wallet_booking_for_user_without_wallet()
    {
        $bookingData = [
            'client_type' => 'user',
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->forexAccount->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.forex.bookings.store'), $bookingData);

        $response->assertRedirect()
            ->assertSessionHasErrors(['payment_method']);
    }

    /** @test */
    public function admin_can_create_booking_with_account_details()
    {
        $bookingData = [
            'client_type' => 'user',
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->forexAccount->id,
            'payment_method' => 'account_details',
            'account_details' => 'Bank: Test Bank, Account: **********'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.forex.bookings.store'), $bookingData);

        $response->assertRedirect()
            ->assertSessionHas('success');

        $this->assertDatabaseHas('forex_bookings', [
            'user_id' => $this->user->id,
            'payment_method' => 'account_details',
            'wallet_currency_id' => null,
            'account_details' => 'Bank: Test Bank, Account: **********'
        ]);
    }

    /** @test */
    public function external_client_booking_defaults_to_account_details()
    {
        $bookingData = [
            'client_type' => 'external',
            'client_name' => 'External Client',
            'client_email' => '<EMAIL>',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->forexAccount->id,
            'account_details' => 'Bank: External Bank, Account: **********'
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.forex.bookings.store'), $bookingData);

        $response->assertRedirect()
            ->assertSessionHas('success');

        $this->assertDatabaseHas('forex_bookings', [
            'client_type' => 'external',
            'payment_method' => 'account_details',
            'wallet_currency_id' => null
        ]);
    }

    /** @test */
    public function api_user_can_get_wallet_payment_options()
    {
        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson(route('api.forex.wallet.payment.options', [
                'transaction_type' => 'buying'
            ]));

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'has_wallet' => true,
                    'target_currency' => 'USD'
                ]
            ]);
    }

    /** @test */
    public function api_user_can_create_booking_with_wallet_payment()
    {
        // Create USD wallet for user
        Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 100.00
        ]);

        $bookingData = [
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id
        ];

        $response = $this->actingAs($this->user, 'sanctum')
            ->postJson(route('api.forex.booking.create'), $bookingData);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Forex booking created successfully'
            ]);

        $this->assertDatabaseHas('forex_bookings', [
            'user_id' => $this->user->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id
        ]);
    }

    /** @test */
    public function api_requires_authentication_for_wallet_options()
    {
        $response = $this->getJson(route('api.forex.wallet.payment.options', [
            'transaction_type' => 'buying'
        ]));

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Authentication required'
            ]);
    }
}
