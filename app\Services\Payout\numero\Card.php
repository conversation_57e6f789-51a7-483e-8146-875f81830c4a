<?php

namespace App\Services\Payout\numero;

use App\Models\PayoutMethod;
use App\Models\Payout;
use App\Models\Gateway;
use App\Models\Deposit;
use App\Services\WebhookForwardingService;
use App\Traits\PayoutTrait;
use Facades\App\Services\BasicCurl;
use Illuminate\Support\Facades\Log;

class Card
{
    use PayoutTrait;

    /**
     * Get Nigerian banks list
     */
    public static function getBank($currencyCode = 'NGN')
    {
        // Return Nigerian banks - Numero works with Nigerian banks
        $banks = [
            ['code' => '090140', 'name' => 'Sagamu Microfinance Bank'],
            ['code' => '090148', 'name' => 'Bowen Microfinance Bank'],
            ['code' => '110002', 'name' => 'Flutterwave Technology Solutions Limited'],
            ['code' => '090143', 'name' => 'Apeks Microfinance Bank'],
            ['code' => '090141', 'name' => 'Chikum Microfinance Bank'],
            ['code' => '090144', 'name' => 'CIT Microfinance Bank'],
            ['code' => '070014', 'name' => 'First Generation Mortgage Bank'],
            ['code' => '090147', 'name' => 'Hackman Microfinance Bank'],
            ['code' => '070016', 'name' => 'Infinity Trust Mortgage Bank'],
            ['code' => '090149', 'name' => 'IRL Microfinance Bank'],
            ['code' => '090151', 'name' => 'Mutual Trust Microfinance Bank'],
            ['code' => '090170', 'name' => 'Rahama Microfinance Bank'],
            ['code' => '090193', 'name' => 'Unical Microfinance Bank'],
            ['code' => '090152', 'name' => 'Nagarta Microfinance Bank'],
            ['code' => '090268', 'name' => 'ADEYEMI COLLEGE STAFF MICROFINANCE BANK'],
            ['code' => '110001', 'name' => 'PayAttitude Online'],
            ['code' => '100023', 'name' => 'TagPay'],
            ['code' => '070001', 'name' => 'NPF MicroFinance Bank'],
            ['code' => '070009', 'name' => 'Gateway Mortgage Bank'],
            ['code' => '070011', 'name' => 'Refuge Mortgage Bank'],
            ['code' => '090179', 'name' => 'Fast Microfinance Bank '],
            ['code' => '070013', 'name' => 'Platinum Mortgage Bank'],
            ['code' => '090156', 'name' => 'e-BARCS MICROFINANCE BANK '],
            ['code' => '090176', 'name' => 'Bosak Microfinance Bank'],
            ['code' => '090119', 'name' => 'Ohafia Microfinance Bank'],
            ['code' => '090120', 'name' => 'Wetland Microfinance Bank'],
            ['code' => '090174', 'name' => 'Malachy Microfinance Bank'],
            ['code' => '090259', 'name' => 'Alekun Microfinance Bank'],
            ['code' => '090198', 'name' => 'Renmoney Microfinance Bank'],
            ['code' => '000022', 'name' => 'SunTrust Bank'],
            ['code' => '090118', 'name' => 'Ibile Microfinance Bank'],
            ['code' => '090117', 'name' => 'Boctrust Microfinance Bank'],
            ['code' => '090130', 'name' => 'Consumer Microfinance Bank'],
            ['code' => '100032', 'name' => 'NOW NOW DIGITAL SYSTEMS LIMITED'],
            ['code' => '090164', 'name' => 'First Royal Microfinance Bank'],
            ['code' => '090165', 'name' => 'Petra Microfinance Bank'],
            ['code' => '100031', 'name' => 'FCMB Easy Account'],
            ['code' => '090192', 'name' => 'Midland Microfinance Bank'],
            ['code' => '090262', 'name' => 'Stellas Microfinance Bank '],
            ['code' => '090133', 'name' => 'Al-Barakah Microfinance Bank'],
            ['code' => '090135', 'name' => 'Personal Trust Microfinance Bank'],
            ['code' => '090139', 'name' => 'Visa Microfinance Bank'],
            ['code' => '090138', 'name' => 'Royal Exchange Microfinance Bank'],
            ['code' => '090134', 'name' => 'Accion Microfinance Bank'],
            ['code' => '090145', 'name' => 'Fullrange Microfinance Bank'],
            ['code' => '090267', 'name' => 'Kuda Microfinance Bank'],
            ['code' => '090006', 'name' => 'SafeTrust Mortgage Bank'],
            ['code' => '100022', 'name' => 'GoMoney'],
            ['code' => '090114', 'name' => 'EMPIRE MFB'],
            ['code' => '090126', 'name' => 'Fidifund Microfinance Bank'],
            ['code' => '090123', 'name' => 'Verite Microfinance Bank'],
            ['code' => '060002', 'name' => 'FBNQUEST MERCHANT BANK'],
            ['code' => '090108', 'name' => 'New Prudential Bank'],
            ['code' => '090121', 'name' => 'Hasal Microfinance Bank'],
            ['code' => '090112', 'name' => 'Seed Capital Microfinance Bank'],
            ['code' => '090177', 'name' => 'Lapo Microfinance Bank '],
            ['code' => '090191', 'name' => 'KCMB Microfinance Bank'],
            ['code' => '090189', 'name' => 'Esan Microfinance Bank'],
            ['code' => '090188', 'name' => 'Baines Credit Microfinance Bank'],
            ['code' => '090265', 'name' => 'Lovonus Microfinance Bank'],
            ['code' => '090269', 'name' => 'Greenville Microfinance Bank'],
            ['code' => '000020', 'name' => 'Heritage'],
            ['code' => '000021', 'name' => 'Standard Chartered Bank'],
            ['code' => '070002', 'name' => 'Fortis Microfinance Bank'],
            ['code' => '070006', 'name' => 'Covenant Microfinance Bank'],
            ['code' => '090001', 'name' => 'ASO Savings & Loans'],
            ['code' => '090003', 'name' => 'Jubilee Life Mortgage Bank'],
            ['code' => '100003', 'name' => 'ReadyCash (Parkway)'],
            ['code' => '100004', 'name' => 'OPAY'],
            ['code' => '100005', 'name' => 'Cellulant'],
            ['code' => '100006', 'name' => 'eTranzact'],
            ['code' => '100007', 'name' => 'Stanbic IBTC @ease wallet'],
            ['code' => '100008', 'name' => 'Ecobank Xpress Account'],
            ['code' => '100009', 'name' => 'GTMobile'],
            ['code' => '100010', 'name' => 'TeasyMobile'],
            ['code' => '100011', 'name' => 'Mkudi'],
            ['code' => '100013', 'name' => 'Access Money'],
            ['code' => '100017', 'name' => 'Hedonmark'],
            ['code' => '100020', 'name' => 'MoneyBox'],
            ['code' => '100021', 'name' => 'Eartholeum'],
            ['code' => '000007', 'name' => 'Fidelity Bank'],
            ['code' => '000008', 'name' => 'Polaris Bank'],
            ['code' => '000009', 'name' => 'CitiBank'],
            ['code' => '000010', 'name' => 'Ecobank Plc'],
            ['code' => '000011', 'name' => 'Unity Bank'],
            ['code' => '000015', 'name' => 'Zenith Bank'],
            ['code' => '000016', 'name' => 'First Bank of Nigeria'],
            ['code' => '000017', 'name' => 'Wema Bank'],
            ['code' => '000018', 'name' => 'Union Bank'],
            ['code' => '000019', 'name' => 'Enterprise Bank'],
            ['code' => '090004', 'name' => 'Parralex'],
            ['code' => '090005', 'name' => 'Trustbond'],
            ['code' => '100001', 'name' => 'FET'],
            ['code' => '100002', 'name' => 'Pagatech'],
            ['code' => '100012', 'name' => 'VTNetworks'],
            ['code' => '100014', 'name' => 'Firstmonie Wallet'],
            ['code' => '400001', 'name' => 'FSDH'],
            ['code' => '999999', 'name' => 'NIP Virtual Bank'],
            ['code' => '060001', 'name' => 'Coronation Merchant Bank'],
            ['code' => '000014', 'name' => 'Access Bank'],
            ['code' => '000001', 'name' => 'Sterling Bank'],
            ['code' => '000002', 'name' => 'Keystone Bank'],
            ['code' => '000003', 'name' => 'FCMB'],
            ['code' => '000004', 'name' => 'United Bank for Africa'],
            ['code' => '000006', 'name' => 'JAIZ Bank'],
            ['code' => '000012', 'name' => 'Stanbic IBTC Bank'],
            ['code' => '000013', 'name' => 'GTBank Plc'],
            ['code' => '100015', 'name' => 'ChamsMobile'],
            ['code' => '100016', 'name' => 'FortisMobile'],
            ['code' => '100018', 'name' => 'ZenithMobile'],
            ['code' => '100019', 'name' => 'Fidelity Mobile'],
            ['code' => '070008', 'name' => 'Page MFBank'],
            ['code' => '000024', 'name' => 'Rand Merchant Bank'],
            ['code' => '090160', 'name' => 'ADDOSSER MICROFINANCE BANK'],
            ['code' => '000023', 'name' => 'Providus Bank'],
            ['code' => '999033', 'name' => 'NIP NEWBANK TSQ'],
            ['code' => '090180', 'name' => 'Amju Unique Microfinance Bank '],
            ['code' => '090159', 'name' => 'CREDIT AFRIQUE MICROFINANCE BANK'],
            ['code' => '090153', 'name' => 'FFS Microfinance'],
            ['code' => '090172', 'name' => 'Astrapolaris Microfinance Bank '],
            ['code' => '100028', 'name' => 'AG MORTGAGE BANK PLC'],
            ['code' => '070017', 'name' => 'Haggai Mortgage Bank Limited'],
            ['code' => '090261', 'name' => 'Quickfund Microfinance Bank'],
            ['code' => '100024', 'name' => 'Imperial Homes Mortgage Bank'],
            ['code' => '100027', 'name' => 'miMONEY'],
            ['code' => '090175', 'name' => 'Rubies MFB'],
            ['code' => '090195', 'name' => 'GROOMING MICROFINANCE BANK'],
            ['code' => '090273', 'name' => 'EMERALDS MICROFINANCE BANK '],
            ['code' => '090276', 'name' => 'TRUSTFUND MICROFINANCE BANK '],
            ['code' => '100029', 'name' => 'Innovectives Kesh'],
            ['code' => '100025', 'name' => 'Zinternet – KongaPay'],
            ['code' => '090115', 'name' => 'TCF MICROFINANCE BANK'],
            ['code' => '070012', 'name' => 'Lagos Building Investment Company'],
            ['code' => '090097', 'name' => 'Ekondo MFB'],
            ['code' => '090111', 'name' => 'FinaTrust Microfinance Bank'],
            ['code' => '090116', 'name' => 'AMML MFB'],
            ['code' => '090122', 'name' => 'Gowans Microfinance Bank'],
            ['code' => '090124', 'name' => 'Xslnce Microfinance Bank'],
            ['code' => '090125', 'name' => 'Regent Microfinance Bank'],
            ['code' => '090127', 'name' => 'BC Kash Microfinance Bank'],
            ['code' => '090128', 'name' => 'Ndiorah Microfinance Bank'],
            ['code' => '090129', 'name' => 'Money Trust Microfinance Bank'],
            ['code' => '090131', 'name' => 'Allworkers Microfinance Bank'],
            ['code' => '090132', 'name' => 'Richway Microfinance Bank'],
            ['code' => '090137', 'name' => 'PecanTrust Microfinance Bank'],
            ['code' => '090142', 'name' => 'Yes Microfinance Bank'],
            ['code' => '090154', 'name' => 'CEMCS Microfinance Bank'],
            ['code' => '090155', 'name' => 'La Fayette Microfinance Bank'],
            ['code' => '070015', 'name' => 'Brent Mortgage Bank'],
            ['code' => '090205', 'name' => 'New Dawn Microfinance Bank'],
            ['code' => '090258', 'name' => 'Imo State Microfinance Bank'],
            ['code' => '090190', 'name' => 'Mutual Benefits Microfinance Bank'],
            ['code' => '090162', 'name' => 'Stanford Microfinance Bank'],
            ['code' => '090167', 'name' => 'Daylight Microfinance Bank'],
            ['code' => '090178', 'name' => 'GREENBANK MICROFINANCE BANK'],
            ['code' => '070007', 'name' => 'LIVINGTRUST MORTGAGE BANK'],
            ['code' => '060003', 'name' => 'Nova Merchant Bank'],
            ['code' => '090173', 'name' => 'Reliance Microfinance Bank'],
            ['code' => '090264', 'name' => 'Auchi Microfinance Bank'],
            ['code' => '090270', 'name' => 'AB MICROFINANCE BANK '],
            ['code' => '090263', 'name' => 'NIGERIAN NAVY MICROFINANCE BANK '],
            ['code' => '090107', 'name' => 'FBN MORTGAGES'],
            ['code' => '070010', 'name' => 'Abbey Mortgage Bank'],
            ['code' => '090110', 'name' => 'VFD MFB'],
            ['code' => '100026', 'name' => 'One Finance Bank'],
            ['code' => '090169', 'name' => 'Alpha Kapital Microfinance Bank'],
            ['code' => '090251', 'name' => 'UNIVERSITY OF NIGERIA'],
            ['code' => '090136', 'name' => 'Baobab Microfinance Bank'],
            ['code' => '090146', 'name' => 'Trident Microfinance Bank'],
            ['code' => '090194', 'name' => 'NIRSAL MICROFINANCE BANK'],
            ['code' => '090197', 'name' => 'ABU MICROFINANCE BANK'],
            ['code' => '100052', 'name' => 'BETA/ACCESS YELLO'],
            ['code' => '000027', 'name' => 'Globus Bank'],
            ['code' => '000025', 'name' => 'Titan Trust Bank'],
            ['code' => '090286', 'name' => 'SAFE HAVEN MICROFINANCE BANK'],
            ['code' => '090272', 'name' => 'Olabisi Onabanjo University Microfinance Bank'],
            ['code' => '000026', 'name' => 'Taj Bank'],
            ['code' => '090295', 'name' => 'Omiye Microfinance Bank'],
            ['code' => '090297', 'name' => 'Alert Microfinance Bank'],
            ['code' => '090289', 'name' => 'Pillar Microfinance Bank'],
            ['code' => '090271', 'name' => 'Lavender Microfinance Bank'],
            ['code' => '110006', 'name' => 'Paystack Payment Limited'],
            ['code' => '090310', 'name' => 'EdFin Microfinance Bank'],
            ['code' => '090304', 'name' => 'EVANGEL MICROFINANCE BANK'],
            ['code' => '090327', 'name' => 'TRUST MICROFINANCE BANK'],
            ['code' => '090328', 'name' => 'EYOWO MICROFINANCE BANK'],
            ['code' => '090332', 'name' => 'EVERGREEN MICROFINANCE BANK'],
            ['code' => '070019', 'name' => 'Mayfresh Mortgage Bank Ltd'],
            ['code' => '090279', 'name' => 'IKIRE MICROFINANCE BANK'],
            ['code' => '090329', 'name' => 'NEPTUNE MICROFINANCE BANK'],
            ['code' => '090374', 'name' => 'COASTLINE MICROFINANCE BANK'],
            ['code' => '090303', 'name' => 'PURPLEMONEY MFB'],
            ['code' => '090275', 'name' => 'MERIDIAN MFB'],
            ['code' => '120002', 'name' => 'Hope Payment Service Bank'],
            ['code' => '090393', 'name' => 'Bridgeway Microfinance Bank'],
            ['code' => '090401', 'name' => 'Shepherd Trust Microfinance Bank'],
            ['code' => '090400', 'name' => 'FINCA MICROFINANCE BANK'],
            ['code' => '090376', 'name' => 'Apple Microfinance Bank'],
            ['code' => '090366', 'name' => 'FIRMUS Microfinance Bank'],
            ['code' => '090373', 'name' => 'Think Finance MFB'],
            ['code' => '090308', 'name' => 'Brightway Microfinance Bank'],
            ['code' => '090333', 'name' => 'OCHE Microfinance Bank'],
            ['code' => '120001', 'name' => '9 PAYMENT SERVICE BANK'],
            ['code' => '090281', 'name' => 'MINT-FINEX MFB'],
            ['code' => '090380', 'name' => 'CONPRO MICROFINANCE BANK'],
            ['code' => '090405', 'name' => 'ROLEZ MICROFINANCE BANK'],
            ['code' => '090113', 'name' => 'Microvis MFB'],
            ['code' => '090391', 'name' => 'DAVODANI MICROFINANCE BANK'],
            ['code' => '090389', 'name' => 'EK-Reliable Microfinance Bank'],
            ['code' => '090166', 'name' => 'Eso-E Microfinance Bank'],
            ['code' => '090399', 'name' => 'NWANNEGADI MICROFINANCE BANK'],
            ['code' => '090371', 'name' => 'AGOSASA MICROFINANCE BANK'],
            ['code' => '100035', 'name' => 'M36'],
            ['code' => '090412', 'name' => 'PREEMINENT MICROFINANCE BANK'],
            ['code' => '090424', 'name' => 'ABUCOOP MICROFINANCE BANK'],
            ['code' => '090325', 'name' => 'SPARKLE'],
            ['code' => '090326', 'name' => 'BALOGUN GAMBARI MFB'],
            ['code' => '090299', 'name' => 'KONTAGORA MICROFINANCE BANK'],
            ['code' => '090318', 'name' => 'FEDERAL UNIVERSITY DUTSE MICROFINANCE BANK'],
            ['code' => '090321', 'name' => 'MAYFAIR MICROFINANCE BANK'],
            ['code' => '090322', 'name' => 'REPHIDIM MICROFINANCE BANK'],
            ['code' => '090324', 'name' => 'IKENNE MICROFINANCE BANK'],
            ['code' => '090331', 'name' => 'UNAAB MICROFINANCE BANK'],
            ['code' => '090336', 'name' => 'BIPC MICROFINANCE BANK'],
            ['code' => '090360', 'name' => 'CASHCONNECT MFB'],
            ['code' => '090362', 'name' => 'MOLUSI MICROFINANCE BANK'],
            ['code' => '090364', 'name' => 'NUTURE MICROFINANCE BANK'],
            ['code' => '090372', 'name' => 'LEGEND MICROFINANCE BANK'],
            ['code' => '110005', 'name' => '3LINE CARD MANAGEMENT LIMITED'],
            ['code' => '090365', 'name' => 'CORESTEP MICROFINANCE BANK'],
            ['code' => '090370', 'name' => 'ILISAN MICROFINANCE BANK'],
            ['code' => '070021', 'name' => 'Coop Mortgage Bank'],
            ['code' => '090416', 'name' => 'Chibueze MFB'],
        ];

        return [
            'status' => 'success',
            'data' => $banks
        ];
    }

    /**
     * Generate signature for Numero API requests
     */
    private static function generateSignature($requestBody, $publicKey)
    {
        try {
            $jsonString = json_encode($requestBody);
            $signature = base64_encode(hash_hmac('sha256', $jsonString, $publicKey, true));
            return $signature;
        } catch (\Exception $e) {
            Log::error('Numero signature generation failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate account number with bank
     */
    public static function validateAccount($accountNumber, $bankCode, $method)
    {
        try {
            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            $requestBody = [
                'accountNumber' => $accountNumber,
                'bankCode' => $bankCode
            ];


            $signature = self::generateSignature(($requestBody), $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];



            $url = $baseUrl . '/api/v1/business/validate';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);


            Log::info('Call to Validate Account', [
                'requestBody' => $requestBody,
                'signature' => $signature,
                'headers' => $headers,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data']
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Account validation failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero account validation failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Account validation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process payout via Numero API
     */
    public static function payouts($payout)
    {
        try {
            $method = PayoutMethod::where('code', 'numero')->first();
            if (!$method) {
                return [
                    'status' => 'error',
                    'data' => 'Numero payout method not found'
                ];
            }

            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey || !$publicKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            // $info = json_decode($payout->information, true);
            // if (!$info) {
            //     return [
            //         'status' => 'error',
            //         'data' => 'Invalid payout information'
            //     ];
            // }

            // Prepare request body for single transfer
            $requestBody = [
                'narration' => $payout->information->narration->field_value ?? 'Payout from ' . env('APP_NAME', 'Cignum One'),
                'amount' => (int)$payout->amount,
                'destinationAccountNumber' => isset($payout->information->destinationAccountNumber) ? $payout->information->destinationAccountNumber : $payout->information->account_number->field_value,
                'destinationBankCode' => isset($payout->information->destinationBankCode) ? $payout->information->destinationBankCode : $payout->information->bank_code->field_value,
                'destinationAccountName' => isset($payout->information->destinationAccountName) ? $payout->information->destinationAccountName : $payout->information->account_name->field_value,
                'phoneNumber' => $payout->information->phone_number->field_value ?? ''
            ];

            $signature = self::generateSignature($requestBody, $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];

            $url = $baseUrl . '/api/v1/business/single';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);

            Log::info('Payouts Single', [
                'requestBody' => $requestBody,
                'signature' => $signature,
                'headers' => $headers,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data']['transferReference'])) {
                return [
                    'status' => 'success',
                    'response_id' => $result['data']['transferReference'][0] ?? $result['reference'],
                    'message' => $result['message'] ?? 'Transfer initiated successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Transfer failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero payout failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Payout failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check transfer status
     */
    public static function checkTransferStatus($reference, $method)
    {
        try {
            $apiKey = optional($method->parameters)->api_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey
            ];

            $url = $baseUrl . '/api/v1/business/status?reference=' . urlencode($reference);
            $response = BasicCurl::curlGetRequestWithHeaders($url, $headers);
            $result = json_decode($response, true);

            Log::info('Numero status check response', [
                'reference' => $reference,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data']
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Status check failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero status check failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Status check failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process bulk transfers
     */
    public static function bulkTransfer($transfers, $phoneNumber, $method)
    {
        try {
            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            $requestBody = [
                'transferRequest' => $transfers,
                'phoneNumber' => $phoneNumber
            ];

            $signature = self::generateSignature($requestBody, $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];

            $url = $baseUrl . '/api/v1/business/bulk';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);

            Log::info('Payout Bulk', [
                'requestBody' => $requestBody,
                'signature' => $signature,
                'headers' => $headers,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data']['transferReference'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data'],
                    'message' => $result['message'] ?? 'Bulk transfer initiated successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Bulk transfer failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero bulk transfer failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Bulk transfer failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Subscribe to webhook notifications
     */
    public static function subscribeWebhook($webhookUrl, $events = [], $method = null)
    {
        try {
            if (!$method) {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return [
                        'status' => 'error',
                        'data' => 'Numero payout method not found'
                    ];
                }
            }

            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey || !$publicKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            // Default events if none provided
            if (empty($events)) {
                $events = ['transfer.success', 'transfer.failed', 'transfer.pending'];
            }

            $requestBody = [
                'url' => $webhookUrl,
                'events' => $events,
                'description' => 'Webhook subscription for ' . env('APP_NAME', 'Cignum One')
            ];

            $signature = self::generateSignature($requestBody, $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];

            $url = $baseUrl . '/api/v1/webhook/subscribe';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);

            Log::info('Numero webhook subscription request', [
                'requestBody' => $requestBody,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data'],
                    'message' => $result['message'] ?? 'Webhook subscribed successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Webhook subscription failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero webhook subscription failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Webhook subscription failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get webhook secret token
     */
    public static function getWebhookSecret($method = null)
    {
        try {
            if (!$method) {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return [
                        'status' => 'error',
                        'data' => 'Numero payout method not found'
                    ];
                }
            }

            $apiKey = optional($method->parameters)->api_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey
            ];

            $url = $baseUrl . '/api/v1/webhook/secret';
            $response = BasicCurl::curlGetRequestWithHeaders($url, $headers);
            $result = json_decode($response, true);

            Log::info('Numero webhook secret request', [
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data'],
                    'message' => $result['message'] ?? 'Webhook secret retrieved successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Failed to get webhook secret'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero webhook secret retrieval failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Webhook secret retrieval failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get all webhooks
     */
    public static function getAllWebhooks($method = null)
    {
        try {
            if (!$method) {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return [
                        'status' => 'error',
                        'data' => 'Numero payout method not found'
                    ];
                }
            }

            $apiKey = optional($method->parameters)->api_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey
            ];

            $url = $baseUrl . '/api/v1/webhook/list';
            $response = BasicCurl::curlGetRequestWithHeaders($url, $headers);
            $result = json_decode($response, true);

            Log::info('Numero webhooks list request', [
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data'],
                    'message' => $result['message'] ?? 'Webhooks retrieved successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Failed to get webhooks'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero webhooks list retrieval failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Webhooks list retrieval failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Unsubscribe from webhook notifications
     */
    public static function unsubscribeWebhook($webhookId, $method = null)
    {
        try {
            if (!$method) {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return [
                        'status' => 'error',
                        'data' => 'Numero payout method not found'
                    ];
                }
            }

            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey || !$publicKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            $requestBody = [
                'webhookId' => $webhookId
            ];

            $signature = self::generateSignature($requestBody, $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];

            $url = $baseUrl . '/api/v1/webhook/unsubscribe';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);

            Log::info('Numero webhook unsubscribe request', [
                'requestBody' => $requestBody,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true) {
                return [
                    'status' => 'success',
                    'data' => $result['data'] ?? [],
                    'message' => $result['message'] ?? 'Webhook unsubscribed successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Webhook unsubscribe failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero webhook unsubscribe failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Webhook unsubscribe failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update webhook configuration
     */
    public static function updateWebhook($webhookId, $webhookUrl, $events = [], $method = null)
    {
        try {
            if (!$method) {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return [
                        'status' => 'error',
                        'data' => 'Numero payout method not found'
                    ];
                }
            }

            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey || !$publicKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            $requestBody = [
                'webhookId' => $webhookId,
                'url' => $webhookUrl,
                'events' => $events,
                'description' => 'Updated webhook for ' . env('APP_NAME', 'Cignum One')
            ];

            $signature = self::generateSignature($requestBody, $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];

            $url = $baseUrl . '/api/v1/webhook/update';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);

            Log::info('Numero webhook update request', [
                'requestBody' => $requestBody,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status'])) {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message']
                ];
            }

            if (isset($result) && $result['status'] === true && isset($result['data'])) {
                return [
                    'status' => 'success',
                    'data' => $result['data'],
                    'message' => $result['message'] ?? 'Webhook updated successfully'
                ];
            } else {
                return [
                    'status' => 'error',
                    'data' => $result['error']['message'] ?? 'Webhook update failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Numero webhook update failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Webhook update failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Handle webhook notifications
     */
    public static function webhook($apiResponse)
    {
        try {
            if (!$apiResponse) {
                return false;
            }

            // Convert to object if it's an array
            if (is_array($apiResponse)) {
                $apiResponse = (object) $apiResponse;
            }

            //

            // Check if this is a valid Numero webhook
            if (!isset($apiResponse->Event) || !isset($apiResponse->Data)) {
                Log::warning('Invalid Numero webhook payload received');
                return false;
            }

            $event = $apiResponse->Event;
            $data = $apiResponse->Data;

            Log::info('Numero webhook received', [
                'event' => $event,
                'data' => $data
            ]);

            // Handle different webhook events
            switch ($event) {
                case 'TRANSFER_NOTIFICATION':
                    return self::handlePayoutWebhook($data);

                case 'FUNDING_NOTIFICATION':
                    return self::handleDepositWebhook($data);

                default:
                    Log::warning('Numero webhook: Unknown event type received: ' . $event);
                    return false;
            }

        } catch (\Exception $e) {
            Log::error('Numero webhook processing failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle payout webhook (existing functionality)
     */
    private static function handlePayoutWebhook($data)
    {
        try {
            if (!isset($data['Reference'])) {
                Log::warning('Invalid payout webhook data received');
                return false;
            }

            $reference = $data['Reference'];
            $payout = Payout::where('response_id', $reference)->first();

            if (!$payout) {
                Log::warning('Numero webhook: Payout not found for reference: ' . $reference);
                return false;
            }

            // Handle different transaction statuses
            $status = $data['Status'] ?? '';

            switch (strtolower($status)) {
                case 'successful':
                case 'completed':
                    if ($payout->status != 2) {
                        $payout->status = 2; // Mark as completed
                        $payout->save();

                        // No need to create transaction record here as it was already created when payout was requested
                        Log::info('Numero webhook: Payout completed for reference: ' . $reference);

                        // Forward webhook to user/merchant
                        Log::info('Forward Webhook', [
                            'payout' => $payout,
                            'data' => $data
                        ]);
                        WebhookForwardingService::forwardTransferNotification($payout, $data);
                    }
                    break;

                case 'failed':
                case 'declined':
                    $payout->status = 6; // Failed
                    $payout->last_error = 'Transfer failed';
                    $payout->save();

                    // Refund user wallet including charge
                    updateWallet($payout->user_id, $payout->currency_id, $payout->net_amount, 1);

                    // Create transaction record for the refund
                    (new self)->createPayoutTransaction($payout, '+', 'Payout failed - refund');
                    Log::info('Numero webhook: Payout failed for reference: ' . $reference);

                    // Forward webhook to user/merchant
                    WebhookForwardingService::forwardTransferNotification($payout, $data);
                    break;

                case 'pending':
                case 'processing':
                    $payout->status = 1; // Pending
                    $payout->save();
                    Log::info('Numero webhook: Payout pending for reference: ' . $reference);
                    break;

                default:
                    Log::warning('Numero webhook: Unknown status received: ' . $status . ' for reference: ' . $reference);
                    break;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Numero payout webhook processing failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle deposit webhook (new functionality for virtual accounts)
     */
    public static function handleDepositWebhook($data)
    {
        try {
            // {
            //     "TrxAmount": 500,
            //     "TrxFee": 100,
            //     "SettledAmount": 400,
            //     "Merchant": "Cignum Solutions",
            //     "BusinessCode": "********",
            //     "Reference": "F490052409875",
            //     "SessionId": null,
            //     "VendorReference": "rqYBv9mO675uLmLMVXXQ5",
            //     "Type": "FundWallet",
            //     "Status": "Successful",
            //     "Channel": "Bank",
            //     "PostingType": "Cr",
            //     "Description": "Inflow from OLUWASEGUN MOSES AJAYI",
            //     "Service": "FUNDING",
            //     "RequestState": "Completed",
            //     "RequestStateDetails": "Completed",
            //     "BeneficiaryName": "OLUWASEGUN MOSES AJAYI",
            //     "BeneficiaryNumber": "**********",
            //     "BeneficiaryBank": "ACCESS BANK",
            //     "DateCreated": "2025-07-12T21:40:05.5674501+00:00",
            //     "DateModified": "2025-07-12T21:40:06.0213955+00:00"
            // }
            //Here's  the deposit webhook. Provider also charges fee in TrxFee. So I want the transaction record done this way, a credit transaction is created, amount is trxAmount, a debit transaction TrxFee and only SettledAmount is added to user wallet balance. These transactions must be duly recorded.
            // Handle both possible webhook data formats
            $transactionId = $data['VendorReference'] ?? $data['Reference'] ?? null;
            $initialAmount = $data['TrxAmount'] ?? null;
            $fee = $data['TrxFee'] ?? 0;
            $amount = $data['SettledAmount'] ?? null;
            $currency = 'NGN';
            $accountNumber = $data['ToAccountNumber'] ?? $data['toAccountNumber'] ?? null;

            if (!$transactionId || !$amount || !$accountNumber) {
                Log::warning('Invalid deposit webhook data received', [
                    'transaction_id' => $transactionId,
                    'amount' => $amount,
                    'account_number' => $accountNumber,
                    'data' => $data
                ]);
                return false;
            }

            // Find the virtual account
            $virtualAccount = \App\Models\VirtualAccount::findByAccountNumber($accountNumber, 'numero');
            if (!$virtualAccount) {
                Log::warning('Numero deposit webhook: Virtual account not found for account number: ' . $accountNumber);
                return false;
            }

            $user = $virtualAccount->user;
            if (!$user) {
                Log::warning('Numero deposit webhook: User not found for virtual account: ' . $accountNumber);
                return false;
            }

            // Get currency ID
            $currencyModel = \App\Models\Currency::where('code', $currency)->first();
            if (!$currencyModel) {
                Log::warning('Numero deposit webhook: Currency not found: ' . $currency);
                return false;
            }

            // Check if transaction already processed
            $existingTransaction = \App\Models\Transaction::where('trx_id', $transactionId)->first();
            if ($existingTransaction) {
                Log::info('Numero deposit webhook: Transaction already processed: ' . $transactionId);
                return true;
            }

            // Get or create wallet
            $wallet = \App\Models\Wallet::firstOrCreate([
                'user_id' => $user->id,
                'currency_id' => $currencyModel->id,
            ], [
                'balance' => 0
            ]);

            // Update wallet balance
            $wallet->balance += $amount;
            $wallet->save();

            // Find or create a virtual account gateway for analytics tracking
            $gateway = Gateway::firstOrCreate(
                ['code' => 'virtual_account'],
                [
                    'name' => 'Virtual Account',
                    'sort_by' => 999,
                    'image' => 'gateway/virtual_account.png',
                    'driver' => 'local',
                    'status' => 1,
                    'parameters' => json_encode([]),
                    'currencies' => json_encode(['0' => ['NGN' => 'NGN', 'USD' => 'USD']]),
                    'extra_parameters' => null,
                    'supported_currency' => json_encode(['NGN', 'USD']),
                    'receivable_currencies' => json_encode([
                        ['name' => 'NGN', 'currency_symbol' => 'NGN', 'conversion_rate' => '1', 'min_limit' => '1', 'max_limit' => '1000000', 'percentage_charge' => '0', 'fixed_charge' => '0'],
                        ['name' => 'USD', 'currency_symbol' => 'USD', 'conversion_rate' => '0.0013', 'min_limit' => '1', 'max_limit' => '10000', 'percentage_charge' => '0', 'fixed_charge' => '0']
                    ]),
                    'description' => 'Virtual Account Deposits',
                    'currency_type' => 1,
                    'is_sandbox' => 0,
                    'environment' => 'live',
                    'is_manual' => 0,
                    'note' => 'Automatic virtual account deposits'
                ]
            );

            // Create deposit record for analytics tracking
            $deposit = Deposit::create([
                'user_id' => $user->id,
                'payment_method_id' => $gateway->id,
                'payment_method_currency' => $currency,
                'amount' => $initialAmount,
                'percentage_charge' => 0,
                'fixed_charge' => 0,
                'payable_amount' => $initialAmount,
                'base_currency_charge' => 0,
                'payable_amount_in_base_currency' => $initialAmount,
                'status' => 1, // Mark as successful immediately
                'trx_id' => $transactionId,
                'currency_id' => $currencyModel->id,
            ]);

            // Create transaction record linked to deposit
            $transaction = new \App\Models\Transaction();
            $transaction->user_id = $user->id;
            $transaction->currency_id = $currencyModel->id;
            $transaction->amount = $initialAmount;
            $transaction->charge = $fee;
            $transaction->trx_type = '+';
            $transaction->remarks = 'Virtual Account Deposit via '. $accountNumber;
            $transaction->trx_id = $transactionId;
            $deposit->transactional()->save($transaction);

            //If fee was charged, create transaction for fee
            // if ($fee > 0) {
            //     $feeTransaction = new \App\Models\Transaction();
            //     $feeTransaction->user_id = $user->id;
            //     $feeTransaction->currency_id = $currencyModel->id;
            //     $feeTransaction->amount = $fee;
            //     $feeTransaction->charge = 0;
            //     $feeTransaction->trx_type = '-';
            //     $feeTransaction->remarks = 'Virtual Account Deposit Fee via '. $accountNumber;
            //     $feeTransaction->trx_id = $transactionId;
            //     $deposit->transactional()->save($feeTransaction);
            // }

            Log::info('Numero deposit webhook: Successfully processed deposit', [
                'user_id' => $user->id,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'currency' => $currency,
                'account_number' => $accountNumber
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Numero deposit webhook processing failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create virtual account using Numero API
     */
    public static function createVirtualAccount($user, $currency = 'NGN', $method = null, $type = null)
    {
        try {
            if (!$method) {
                $method = PayoutMethod::where('code', 'numero')->first();
                if (!$method) {
                    return [
                        'status' => 'error',
                        'data' => 'Numero payout method not found'
                    ];
                }
            }

            $apiKey = optional($method->parameters)->api_key;
            $publicKey = optional($method->parameters)->public_key;
            $baseUrl = optional($method->parameters)->base_url ?? 'https://api-dev.getnumero.co/numeroaccount';

            if (!$apiKey || !$publicKey) {
                return [
                    'status' => 'error',
                    'data' => 'Missing API credentials'
                ];
            }

            // Get KYC data for the user
            $kycData = self::getKycDataForUser($user);
            if (!$kycData) {
                return [
                    'status' => 'error',
                    'data' => 'KYC data not found. Please complete your KYC verification first.'
                ];
            }

            $mechantKycData = null;

            if(isset($type) && $type == 'business' && $user->type == 'merchant'){
                $mechantKycData = self::getKycDataForMerchant($user);
                if (!$mechantKycData) {
                    return [
                        'status' => 'error',
                        'data' => 'KYC data not found. Please complete your Business Information KYC verification first.'
                    ];
                }
            }

            //Form request body per request type. If type is null, create user account, else create business
            if(isset($type) && $type == 'business'){
                $requestBody = [
                    'businessName' => $mechantKycData['BusinessName'],
                    'rcNumber' => $mechantKycData['RegistrationNumber'],
                    'tin' => $mechantKycData['TaxIdentificationNumber'],
                    'address' => $mechantKycData['AddressInFull'],
                    'firstName' => $kycData['FirstName'],
                    'lastName' => $kycData['LastName'],
                    'email' => $kycData['Email'],
                    //'mobileNumber' => $kycData['MobileNumber'],
                    'mobileNumber' => self::formatPhoneNumber($kycData['MobileNumber']),
                ];
            }else{
                $requestBody = [
                    'firstName' => $kycData['FirstName'],
                    'lastName' => $kycData['LastName'],
                    'email' => $kycData['Email'],
                    //'mobileNumber' => $kycData['MobileNumber'],
                    'mobileNumber' => self::formatPhoneNumber($kycData['MobileNumber']),
                    'bvn' => $kycData['BankVerificationNumber'],
                ];
            }

            // $requestBody = [
            //     'firstName' => $kycData['FirstName'],
            //     'lastName' => $kycData['LastName'],
            //     'email' => $kycData['Email'],
            //     'mobileNumber' => $kycData['MobileNumber'],
            //     'bvn' => $kycData['BankVerificationNumber'],
            //     // 'currency' => $currency,
            //     // 'accountType' => 'individual'
            // ];

            $signature = self::generateSignature($requestBody, $publicKey);
            if (!$signature) {
                return [
                    'status' => 'error',
                    'data' => 'Failed to generate signature'
                ];
            }

            $headers = [
                'Content-Type: application/json',
                'x-api-key: ' . $apiKey,
                'x-signature-key: ' . $signature
            ];

            $url = (isset($type) && $type == 'business') ? $baseUrl . '/api/v1/business/virtualaccount/business' : $baseUrl . '/api/v1/business/virtualaccount/customer';
            $response = BasicCurl::curlPostRequestWithHeaders($url, $headers, $requestBody);
            $result = json_decode($response, true);

            Log::info('Numero virtual account creation request', [
                'user_id' => $user->id,
                'requestBody' => $requestBody,
                'response' => $response,
                'result' => $result
            ]);

            if (!isset($result) || !isset($result['status']) || $result['message'] !== 'Successful') {
                return [
                    'status' => 'error',
                    'data' => $result['message'] ?? 'Virtual account creation failed'
                ];
            }

            return [
                'status' => 'success',
                'data' => $result['data'] ?? $result,
                'type' => (isset($type) && $type == 'business') ? 'business' : 'customer'
            ];

        } catch (\Exception $e) {
            Log::error('Numero virtual account creation failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'data' => 'Virtual account creation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get KYC data for user from customer-information KYC record
     */
    public static function getKycDataForUser($user)
    {
        try {
            // Get the customer-information KYC record
            $kyc = \App\Models\Kyc::where('slug', 'customer-information')->where('status', 1)->first();
            if (!$kyc) {
                return null;
            }

            // Get user's KYC submission for this KYC type
            $userKyc = \App\Models\UserKyc::where('user_id', $user->id)
                ->where('kyc_id', $kyc->id)
                ->where('status', 1) // Only verified KYC
                ->first();

            if (!$userKyc || !$userKyc->kyc_info) {
                return null;
            }

            $kycInfo = $userKyc->kyc_info;
            $extractedData = [];

            // Extract required fields
            $requiredFields = ['Email', 'FirstName', 'LastName', 'MobileNumber', 'BankVerificationNumber'];

            foreach ($requiredFields as $field) {
                if (isset($kycInfo->{$field}->field_value)) {
                    $extractedData[$field] = $kycInfo->{$field}->field_value;
                } else {
                    // Field missing
                    return null;
                }
            }

            return $extractedData;

        } catch (\Exception $e) {
            Log::error('Failed to get KYC data for user: ' . $e->getMessage());
            return null;
        }
    }

    public static function getKycDataForMerchant($user)
    {
        try {
            if($user->type != 'merchant'){
                return null;
            }
            // Get the customer-information KYC record
            $kyc = \App\Models\Kyc::where('slug', 'business-information')->where('status', 1)->first();
            if (!$kyc) {
                return null;
            }

            // Get user's KYC submission for this KYC type
            $userKyc = \App\Models\UserKyc::where('user_id', $user->id)
                ->where('kyc_id', $kyc->id)
                ->where('status', 1) // Only verified KYC
                ->first();

            if (!$userKyc || !$userKyc->kyc_info) {
                return null;
            }

            $kycInfo = $userKyc->kyc_info;
            $extractedData = [];

            // Extract required fields
            $requiredFields = ['BusinessName', 'RegistrationNumber', 'TaxIdentificationNumber', 'AddressInFull'];

            foreach ($requiredFields as $field) {
                if (isset($kycInfo->{$field}->field_value)) {
                    $extractedData[$field] = $kycInfo->{$field}->field_value;
                } else {
                    // Field missing
                    return null;
                }
            }

            return $extractedData;

        } catch (\Exception $e) {
            Log::error('Failed to get KYC data for merchant: ' . $e->getMessage());
            return null;
        }
    }

    //Write a function to format phone number to nigeria country code. Regardless of what is passed, it should be formatted to nigeria country code. If it starts with 0, replace 0 with +234, if it starts with 234, replace with +234
    public static function formatPhoneNumber($phoneNumber)
    {
        if (strpos($phoneNumber, '0') === 0) {
            $phoneNumber = '+234' . substr($phoneNumber, 1);
        } elseif (strpos($phoneNumber, '234') === 0) {
            $phoneNumber = '+234' . substr($phoneNumber, 3);
        } elseif (strpos($phoneNumber, '+234') !== 0) {
            $phoneNumber = '+234' . $phoneNumber;
        }

        return $phoneNumber;
    }

    //Write a function to format number to local code. If it starts with 0 its all good, but if it starts with +234, convert it the +234 to 0, if it starts with 234, convert that to 0 also
    public static function formatPhoneNumberToLocal($phoneNumber)
    {
        if (strpos($phoneNumber, '+234') === 0) {
            $phoneNumber = '0' . substr($phoneNumber, 4);
        } elseif (strpos($phoneNumber, '234') === 0) {
            $phoneNumber = '0' . substr($phoneNumber, 3);
        }

        return $phoneNumber;
    }
}
