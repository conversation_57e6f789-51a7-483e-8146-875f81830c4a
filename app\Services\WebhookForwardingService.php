<?php

namespace App\Services;

use App\Models\User;
use App\Models\Payout;
use Modules\Merchant\Models\MerchantSetting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WebhookForwardingService
{
    /**
     * Forward webhook notification to user/merchant configured webhook URL
     */
    public static function forwardWebhook($payout, $eventType, $data = [])
    {
        try {
            if (!$payout || !$payout->user_id) {
                Log::warning('WebhookForwardingService: Invalid payout or user_id');
                return false;
            }

            $user = User::find($payout->user_id);
            if (!$user) {
                Log::warning('WebhookForwardingService: User not found', ['user_id' => $payout->user_id]);
                return false;
            }

            $webhookUrl = null;

            // Check if user is a merchant and has merchant settings
            if ($user->type === 'merchant') {
                $merchantSetting = MerchantSetting::where('merchant_id', $user->id)->first();
                $webhookUrl = $merchantSetting?->webhook_url;
            } else {
                // Regular user
                $webhookUrl = $user->webhook_url;
            }

            if (!$webhookUrl) {
                Log::info('WebhookForwardingService: No webhook URL configured', [
                    'user_id' => $user->id,
                    'user_type' => $user->type
                ]);
                return false;
            }

            // Prepare webhook payload
            $payload = self::prepareWebhookPayload($payout, $eventType, $data);

            // Get user's secret key for signature generation
            $secretKey = WebhookSecurityService::getApiKeyForUser($user);
            if (!$secretKey) {
                Log::warning('WebhookForwardingService: No secret key found for user', [
                    'user_id' => $user->id
                ]);
                return false;
            }

            \Log::info('ForwardWebhook 2', [
                'payload' => $payload,
                'webhookUrl' => $webhookUrl,
                'secretKey' => $secretKey,
                'userId' => $user->id
            ]);

            // Send webhook with signature
            return self::sendWebhook($webhookUrl, $payload, $user->id, $secretKey);

        } catch (\Exception $e) {
            Log::error('WebhookForwardingService: Error forwarding webhook', [
                'error' => $e->getMessage(),
                'payout_id' => $payout?->id
            ]);
            return false;
        }
    }

    /**
     * Prepare standardized webhook payload
     */
    private static function prepareWebhookPayload($payout, $eventType, $additionalData = [])
    {
        $payload = [
            'event' => $eventType,
            'timestamp' => now()->toISOString(),
            'data' => [
                'transaction_id' => $payout->trx_id,
                'reference' => $payout->response_id,
                'amount' => (float) $payout->amount,
                'currency' => $payout->payout_currency_code,
                'status' => match ($payout->status) {
                    0 => 'pending',
                    1 => 'processing',
                    2 => 'success',
                    3 => 'cancelled',
                    6 => 'failed',
                    default => 'unknown'
                },
                //'method' => $payout->method?->name,
                'created_at' => $payout->created_at?->toISOString(),
                'updated_at' => $payout->updated_at?->toISOString(),
                'user_id' => $payout->user_id,
                'charge' => (float) $payout->charge,
                'net_amount' => (float) $payout->net_amount,
            ]
        ];

        // Add any additional data
        if (!empty($additionalData)) {
            //$payload['data'] = array_merge($payload['data'], $additionalData);
        }

        return $payload;
    }

    /**
     * Send webhook to configured URL with HMAC-SHA256 signature
     */
    private static function sendWebhook($webhookUrl, $payload, $userId, $secretKey)
    {
        try {
            // Generate signature for the payload
            $signature = WebhookSecurityService::generateSignature($payload, $secretKey);

            if (!$signature) {
                Log::error('WebhookForwardingService: Failed to generate webhook signature', [
                    'user_id' => $userId,
                    'webhook_url' => $webhookUrl
                ]);
                return false;
            }

            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'User-Agent' => env('APP_NAME', 'Cignum One') . ' Webhook',
                    'X-Webhook-Source' => env('APP_NAME', 'Cignum One'),
                    'X-Webhook-Signature' => $signature,
                ])
                ->post($webhookUrl, $payload);

            \Log::info('WebhookForwardingService: Webhook sent', [
                'user_id' => $userId,
                'webhook_url' => $webhookUrl,
                'status_code' => $response->status(),
                //'success' => $success,
                'payload' => $payload,
                //'transaction_id' => $payload['data']['transaction_id']
            ]);

            return $response->status() >= 200 && $response->status() < 300;

        } catch (\Exception $e) {
            Log::error('WebhookForwardingService: Failed to send webhook', [
                'user_id' => $userId,
                'webhook_url' => $webhookUrl,
                'error' => $e->getMessage(),
                'event' => $payload['event'],
                'transaction_id' => $payload['data']['transaction_id']
            ]);
            return false;
        }
    }

    /**
     * Forward transfer notification webhook
     */
    public static function forwardTransferNotification($payout, $numeroData = [])
    {
        return self::forwardWebhook($payout, 'TRANSFER_NOTIFICATION', [
            'provider' => 'numero',
            'provider_data' => $numeroData
        ]);
    }

    /**
     * Forward deposit notification webhook
     */
    public static function forwardDepositNotification($payout, $numeroData = [])
    {
        return self::forwardWebhook($payout, 'FUNDING_NOTIFICATION', [
            'provider' => 'numero',
            'provider_data' => $numeroData
        ]);
    }

    /**
     * Test webhook URL by sending a test notification
     */
    public static function testWebhook($webhookUrl, $userId)
    {
        try {
            // Get user for secret key
            $user = User::find($userId);
            if (!$user) {
                Log::error('WebhookForwardingService: User not found for test webhook', [
                    'user_id' => $userId
                ]);
                return false;
            }

            $secretKey = WebhookSecurityService::getApiKeyForUser($user);
            if (!$secretKey) {
                Log::error('WebhookForwardingService: No secret key found for test webhook', [
                    'user_id' => $userId
                ]);
                return false;
            }

            $testPayload = [
                'event' => 'TEST_NOTIFICATION',
                'timestamp' => now()->toISOString(),
                'data' => [
                    'message' => 'This is a test webhook notification',
                    'user_id' => $userId,
                    'test' => true
                ]
            ];

            return self::sendWebhook($webhookUrl, $testPayload, $userId, $secretKey);

        } catch (\Exception $e) {
            Log::error('WebhookForwardingService: Test webhook failed', [
                'user_id' => $userId,
                'webhook_url' => $webhookUrl,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
