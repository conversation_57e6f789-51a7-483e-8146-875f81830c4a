<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Language;

class ForexNotificationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $basic = basicControl();
        $emailFrom = $basic->sender_email ?? '<EMAIL>';
        $languageId = Language::first()->id ?? 1;
        $langCode = Language::first()->short_name ?? 'en';
        $now = now();

        $templates = [
            [
                'template_key' => 'FOREX_BOOKING_CONFIRMATION',
                'name' => 'Forex Booking Confirmation',
                'subject' => 'Forex Booking Confirmation - [[booking_reference]]',
                'email' => 'Dear [[client_name]],

Your forex booking has been created successfully.

Booking Details:
- Reference: [[booking_reference]]
- Transaction Type: [[transaction_type]]
- Amount: [[amount]] [[currency]]
- Customer Rate: ₦[[customer_rate]]/$1
- Total Amount: ₦[[customer_total]]
- Status: [[status]]

[[payment_instructions]]

Next Steps:
- Please make the payment as per the instructions above
- Keep this booking reference for your records: [[booking_reference]]
- You will receive a confirmation email once the payment is processed
- For any queries, please contact our support team

Thank you for choosing us for your forex needs.',
                'sms' => 'Forex booking [[booking_reference]] created. Amount: [[amount]] [[currency]]. Rate: ₦[[customer_rate]]/$1. Total: ₦[[customer_total]].',
                'in_app' => 'Your forex booking [[booking_reference]] has been created successfully. Amount: [[amount]] [[currency]].',
                'push' => 'Forex booking [[booking_reference]] created successfully.',
                'short_keys' => json_encode([
                    'client_name' => 'Client Name',
                    'booking_reference' => 'Booking Reference',
                    'transaction_type' => 'Transaction Type (buying/selling)',
                    'amount' => 'Transaction Amount',
                    'currency' => 'Currency Code',
                    'customer_rate' => 'Customer Rate',
                    'customer_total' => 'Total Amount in NGN',
                    'status' => 'Booking Status',
                    'payment_instructions' => 'Payment Instructions',
                ]),
            ],
            [
                'template_key' => 'FOREX_BOOKING_COMPLETION',
                'name' => 'Forex Booking Completion',
                'subject' => 'Booking Completed - [[booking_reference]]',
                'email' => 'Dear [[client_name]],

Your forex booking has been successfully completed.

Booking Details:
- Reference: [[booking_reference]]
- Transaction Type: [[transaction_type]]
- Amount: [[amount]] [[currency]]
- Customer Rate: ₦[[customer_rate]]/$1
- Total Amount: ₦[[customer_total]]
- Status: [[status]]
- Completed Date: [[completion_date]]

[[completion_message]]

What happens next:
- Your transaction has been successfully processed
- All account balances have been updated accordingly
- You can view your transaction history in your account
- Keep this email as confirmation of your completed transaction

Thank you for choosing us for your forex trading needs. We appreciate your business!',
                'sms' => 'Forex booking [[booking_reference]] completed successfully. Amount: [[amount]] [[currency]].',
                'in_app' => 'Your forex booking [[booking_reference]] has been completed successfully.',
                'push' => 'Forex booking [[booking_reference]] completed.',
                'short_keys' => json_encode([
                    'client_name' => 'Client Name',
                    'booking_reference' => 'Booking Reference',
                    'transaction_type' => 'Transaction Type (buying/selling)',
                    'amount' => 'Transaction Amount',
                    'currency' => 'Currency Code',
                    'customer_rate' => 'Customer Rate',
                    'customer_total' => 'Total Amount in NGN',
                    'status' => 'Booking Status',
                    'completion_date' => 'Completion Date',
                    'completion_message' => 'Additional Completion Message',
                ]),
            ],
            [
                'template_key' => 'FOREX_PAYMENT_REMINDER',
                'name' => 'Forex Payment Reminder',
                'subject' => 'Payment Reminder - [[booking_reference]]',
                'email' => 'Dear [[client_name]],

This is a reminder about your pending forex booking.

Booking Details:
- Reference: [[booking_reference]]
- Transaction Type: [[transaction_type]]
- Amount: [[amount]] [[currency]]
- Customer Rate: ₦[[customer_rate]]/$1
- Total Amount: ₦[[customer_total]]
- Status: [[status]]
- Created Date: [[created_date]]

[[reminder_message]]

Please complete your payment to avoid cancellation. If you have already made the payment, please contact our support team.

Thank you for your attention to this matter.',
                'sms' => 'Payment reminder for forex booking [[booking_reference]]. Amount: [[amount]] [[currency]]. Please complete payment.',
                'in_app' => 'Payment reminder: Your forex booking [[booking_reference]] is still pending payment.',
                'push' => 'Payment reminder for booking [[booking_reference]].',
                'short_keys' => json_encode([
                    'client_name' => 'Client Name',
                    'booking_reference' => 'Booking Reference',
                    'transaction_type' => 'Transaction Type (buying/selling)',
                    'amount' => 'Transaction Amount',
                    'currency' => 'Currency Code',
                    'customer_rate' => 'Customer Rate',
                    'customer_total' => 'Total Amount in NGN',
                    'status' => 'Booking Status',
                    'created_date' => 'Booking Creation Date',
                    'reminder_message' => 'Custom Reminder Message',
                ]),
            ],
        ];

        foreach ($templates as $template) {
            DB::table('notification_templates')->updateOrInsert(
                ['template_key' => $template['template_key'], 'language_id' => $languageId],
                array_merge([
                    'lang_code' => $langCode,
                    'email_from' => $emailFrom,
                    'notify_for' => 0, // 0 for user notifications
                    'status' => json_encode(['mail' => 1, 'sms' => 1, 'in_app' => 1, 'push' => 1]),
                    'created_at' => $now,
                    'updated_at' => $now,
                ], $template)
            );
        }
    }
}
