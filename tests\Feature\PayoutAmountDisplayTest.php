<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Payout;
use App\Models\Transaction;
use App\Models\Currency;
use App\Models\PayoutMethod;
use App\Traits\PayoutTrait;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PayoutAmountDisplayTest extends TestCase
{
    use RefreshDatabase, WithFaker, PayoutTrait;

    protected $user;
    protected $currency;
    protected $payoutMethod;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'status' => 1,
            'email_verification' => 1
        ]);

        // Create test currency
        $this->currency = Currency::factory()->create([
            'code' => 'USD',
            'symbol' => '$',
            'exchange_rate' => 1.0
        ]);

        // Create test payout method
        $this->payoutMethod = PayoutMethod::factory()->create([
            'is_active' => 1,
            'is_automatic' => 1
        ]);
    }

    /** @test */
    public function test_payout_transaction_records_correct_amount()
    {
        // Create a test payout
        $payout = Payout::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'amount' => 1500.00,           // Requested amount
            'charge' => 500.00,            // Processing fee
            'net_amount' => 2000.00,       // Total debited (amount + charge)
            'payout_currency_code' => 'USD',
            'trx_id' => 'TXN' . time(),
            'status' => 1
        ]);

        // Create transaction using the trait method
        $this->createPayoutTransaction($payout, '-', 'Payout request - amount debited');

        // Verify transaction was created with correct amount
        $transaction = Transaction::where('transactional_id', $payout->id)
            ->where('transactional_type', get_class($payout))
            ->first();

        $this->assertNotNull($transaction);
        
        // CRITICAL: Transaction should record net_amount (total debited), not just amount
        $this->assertEquals(2000.00, $transaction->amount, 
            'Transaction should record net_amount (total debited) not just requested amount');
        
        $this->assertEquals(500.00, $transaction->charge, 
            'Transaction should record the processing fee');
        
        $this->assertEquals('-', $transaction->trx_type, 
            'Transaction should be a debit');
    }

    /** @test */
    public function test_payout_cancellation_refunds_correct_amount()
    {
        // Create a test payout
        $payout = Payout::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'amount' => 1500.00,
            'charge' => 500.00,
            'net_amount' => 2000.00,
            'payout_currency_code' => 'USD',
            'trx_id' => 'TXN' . time(),
            'status' => 1
        ]);

        // Cancel the payout
        $this->cancelPayout($payout->id, 'Test cancellation');

        // Verify refund transaction was created with correct amount
        $refundTransaction = Transaction::where('transactional_id', $payout->id)
            ->where('transactional_type', get_class($payout))
            ->where('trx_type', '+')
            ->first();

        $this->assertNotNull($refundTransaction);
        
        // CRITICAL: Refund should be for the full net_amount (total that was debited)
        $this->assertEquals(2000.00, $refundTransaction->amount, 
            'Refund should be for the full net_amount that was originally debited');
    }

    /** @test */
    public function test_api_payout_list_returns_correct_amounts()
    {
        $this->actingAs($this->user, 'sanctum');

        // Create a test payout
        $payout = Payout::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->currency->id,
            'payout_method_id' => $this->payoutMethod->id,
            'amount' => 1500.00,
            'charge' => 500.00,
            'net_amount' => 2000.00,
            'payout_currency_code' => 'USD',
            'trx_id' => 'TXN' . time(),
            'status' => 1
        ]);

        // Call API endpoint
        $response = $this->getJson('/api/payout-list');

        $response->assertStatus(200);
        
        $payoutData = $response->json('data.payouts.data')[0];
        
        // Verify API returns clear amount breakdown
        $this->assertEquals('1500.00', $payoutData['requested_amount']);
        $this->assertEquals('500.00', $payoutData['processing_fee']);
        $this->assertEquals('2000.00', $payoutData['total_debited']);
    }
}
