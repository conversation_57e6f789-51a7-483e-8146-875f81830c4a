<?php

namespace App\Models;

use App\Traits\Notify;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Admin extends Authenticatable
{
    use HasFactory, Notifiable, HasApiTokens, Notify, HasAdvancedPermissions;

    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'image',
        'image_driver',
        'phone',
        'address',
        'admin_access',
        'role_id',
        'use_advanced_roles',
        'advanced_role_cache',
        'role_cache_updated_at',
        'permission_overrides',
        'can_override_permissions',
        'is_super_admin',
        'last_login',
        'status',
        'remember_token',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'verification_token'
    ];

    protected $casts = [
        'use_advanced_roles' => 'boolean',
        'advanced_role_cache' => 'array',
        'role_cache_updated_at' => 'datetime',
        'permission_overrides' => 'array',
        'can_override_permissions' => 'boolean',
        'is_super_admin' => 'boolean',
        'status' => 'integer',
    ];

    public function fireBaseToken()
    {
        return $this->morphMany(FireBaseToken::class, 'tokenable');
    }

    public function inAppNotification()
    {
        return $this->morphOne(InAppNotification::class, 'inAppNotificationable', 'in_app_notificationable_type', 'in_app_notificationable_id');
    }

    public function sendPasswordResetNotification($token)
    {
        $this->mail($this, 'PASSWORD_RESET', $params = [
            'message' => '<a href="' . url('admin/password/reset', $token) . '?email=' . $this->email . '" target="_blank">Click To Reset Password</a>'
        ]);
    }

    public function profilePicture()
    {
        return getFile($this->image_driver, $this->image);
    }

    public function avatarImage(): string
    {
        $status = $this->LastSeenActivity ? 'success' : 'warning';
        if ($this->image) {
            $url = getFile($this->image_driver, $this->image);
            return <<<HTML
            <div class="avatar avatar-sm avatar-circle">
                <img class="avatar-img" src="{$url}" alt="...">
                <span class="avatar-status avatar-sm-status avatar-status-{$status}"></span>
            </div>
        HTML;
        }

        $initial = mb_check_encoding($this->name, 'UTF-8') ? mb_substr(trim($this->name), 0, 1) : '';
        return <<<HTML
        <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
            <span class="avatar-initials">{$initial}</span>
            <span class="avatar-status avatar-sm-status avatar-status-{$status}"></span>
        </div>
    HTML;
    }

    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }

    public function activeRole()
    {
        return $this->belongsTo(Role::class, 'role_id', 'id')->where('status', 1);
    }

    // ===== ADVANCED ROLE SYSTEM RELATIONSHIPS =====

    /**
     * Get advanced role assignments for this admin
     */
    public function advancedUserRoles()
    {
        return $this->morphMany(AdvancedUserRole::class, 'user');
    }

    /**
     * Get active advanced roles for this admin
     */
    public function advancedRoles()
    {
        return $this->morphToMany(AdvancedRole::class, 'user', 'advanced_user_roles')
            ->withPivot([
                'is_active',
                'priority',
                'assigned_at',
                'expires_at',
                'context',
                'context_data'
            ])
            ->wherePivot('is_active', true)
            ->withTimestamps();
    }

    /**
     * Check if admin is super admin (bypasses all permission checks)
     */
    public function isSuperAdmin(): bool
    {
        return $this->is_super_admin ?? false;
    }

    /**
     * Check if admin has advanced role system enabled
     */
    public function usesAdvancedRoles(): bool
    {
        return $this->use_advanced_roles ?? false;
    }

    /**
     * Enable advanced role system for this admin
     */
    public function enableAdvancedRoles(): void
    {
        $this->update(['use_advanced_roles' => true]);
    }

    /**
     * Get all permissions for this admin (from advanced roles)
     */
    public function getAdvancedPermissions(): \Illuminate\Support\Collection
    {
        // Super admin has all permissions
        if ($this->isSuperAdmin()) {
            return AdvancedPermission::active()->get();
        }

        if (!$this->usesAdvancedRoles()) {
            return collect();
        }

        // Check cache first
        if ($this->advanced_role_cache && $this->role_cache_updated_at &&
            $this->role_cache_updated_at > now()->subMinutes(60)) {
            return collect($this->advanced_role_cache);
        }

        $permissions = collect();

        // Get permissions from all active roles
        $activeRoles = AdvancedUserRole::forUser($this)
            ->currentlyValid()
            ->with('role.permissions')
            ->byPriority()
            ->get();

        foreach ($activeRoles as $userRole) {
            if ($userRole->role) {
                $rolePermissions = $userRole->role->getAllPermissions();
                $permissions = $permissions->merge($rolePermissions);
            }
        }

        // Add permission overrides
        if ($this->can_override_permissions && $this->permission_overrides) {
            foreach ($this->permission_overrides as $override) {
                if ($override['is_granted']) {
                    $permission = AdvancedPermission::findByName($override['permission']);
                    if ($permission) {
                        $permissions->push($permission);
                    }
                } else {
                    // Remove denied permissions
                    $permissions = $permissions->reject(function ($permission) use ($override) {
                        return $permission->name === $override['permission'];
                    });
                }
            }
        }

        // Cache the result
        $permissionArray = $permissions->unique('id')->values()->toArray();
        $this->update([
            'advanced_role_cache' => $permissionArray,
            'role_cache_updated_at' => now(),
        ]);

        return $permissions->unique('id');
    }

    /**
     * Check if admin has specific advanced permission
     */
    public function hasAdvancedPermission(string $permissionName): bool
    {
        // Super admin has all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        if (!$this->usesAdvancedRoles()) {
            return false;
        }

        return $this->getAdvancedPermissions()
            ->contains('name', $permissionName);
    }

    /**
     * Clear permission cache
     */
    public function clearPermissionCache(): void
    {
        $this->update([
            'advanced_role_cache' => null,
            'role_cache_updated_at' => null,
        ]);
    }

    /**
     * Assign advanced role to admin
     */
    public function assignAdvancedRole(AdvancedRole $role, array $options = []): AdvancedUserRole
    {
        $assignment = AdvancedUserRole::assignRole($this, $role, $options);
        $this->clearPermissionCache();
        return $assignment;
    }

    /**
     * Remove advanced role from admin
     */
    public function removeAdvancedRole(AdvancedRole $role, string $reason = null): void
    {
        $assignment = $this->advancedUserRoles()
            ->where('role_id', $role->id)
            ->where('is_active', true)
            ->first();

        if ($assignment) {
            $assignment->revoke($reason);
            $this->clearPermissionCache();
        }
    }

    /**
     * Check if admin has specific advanced role
     */
    public function hasAdvancedRole(string $roleName): bool
    {
        return AdvancedUserRole::userHasRole($this, $roleName);
    }

    /**
     * Make admin super admin
     */
    public function makeSuperAdmin(): void
    {
        $this->update(['is_super_admin' => true]);
        $this->clearPermissionCache();
    }

    /**
     * Remove super admin status
     */
    public function removeSuperAdmin(): void
    {
        $this->update(['is_super_admin' => false]);
        $this->clearPermissionCache();
    }
}
