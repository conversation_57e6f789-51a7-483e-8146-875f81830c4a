<?php

require 'vendor/autoload.php';

use App\Traits\Notify;
use App\Models\NotificationTemplate;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== SIMPLE EMAIL TEST ===\n\n";

// Check email notification setting
$basic = basicControl();
echo "Email notification setting: " . ($basic->email_notification ? 'Enabled' : 'Disabled') . "\n";

// Check templates
$template = NotificationTemplate::where('template_key', 'FOREX_BOOKING_CONFIRMATION')->first();
if ($template) {
    echo "✓ Template found: {$template->name}\n";
    echo "  Status: " . json_encode($template->status) . "\n";
} else {
    echo "❌ Template not found\n";
    exit;
}

// Test class with Notify trait
class TestEmailClass {
    use Notify;
}

$emailTest = new TestEmailClass();

// Create test user object
$testUser = new stdClass();
$testUser->email = '<EMAIL>';
$testUser->username = 'Test User';
$testUser->language_id = 1;
$testUser->notifypermission = new stdClass();
$testUser->notifypermission->template_email_key = [
    'FOREX_BOOKING_CONFIRMATION',
    'FOREX_BOOKING_COMPLETION', 
    'FOREX_PAYMENT_REMINDER'
];

// Test parameters
$params = [
    'client_name' => 'Test Client',
    'booking_reference' => 'TEST123',
    'transaction_type' => 'Buying',
    'amount' => '100.00',
    'currency' => 'USD',
    'customer_rate' => '1500.00',
    'customer_total' => '150000.00',
    'status' => 'Pending',
    'payment_instructions' => 'Test payment instructions'
];

echo "\nTesting email send...\n";
try {
    $result = $emailTest->mail($testUser, 'FOREX_BOOKING_CONFIRMATION', $params);
    echo "Email result: " . ($result === false ? 'Failed/Returned False' : 'Success/Queued') . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
