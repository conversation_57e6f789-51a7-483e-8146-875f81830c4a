@extends('admin.layouts.app')
@section('page_title', __('Merchant Payout Configuration'))

@section('content')
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-sm mb-2 mb-sm-0">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-no-gutter">
                        <li class="breadcrumb-item">
                            <a class="breadcrumb-link" href="{{ route('admin.dashboard') }}">@lang('Dashboard')</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a class="breadcrumb-link" href="{{ route('admin.merchants') }}">@lang('Merchants')</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a class="breadcrumb-link" href="{{ route('admin.user.view.profile', $merchant->id) }}">{{ $merchant->name }}</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">@lang('Payout Configuration')</li>
                    </ol>
                </nav>

                <h1 class="page-header-title">
                    @lang('Payout Configuration for') {{ $merchant->name }}
                    <span class="badge bg-soft-secondary text-secondary ms-2">{{ ucfirst($merchant->type) }}</span>
                </h1>
            </div>
        </div>
    </div>
    <!-- End Page Header -->

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Info Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col">
                    <h4 class="card-title">@lang('Custom Payout Configuration')</h4>
                    <p class="card-text">
                        @lang('Configure custom payout limits and charges for this merchant. Leave fields empty to use default values from the payout method.')
                    </p>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2">
                        <span class="badge bg-soft-info text-info">
                            <i class="bi-info-circle me-1"></i>@lang('Per Currency Configuration')
                        </span>
                        <span class="badge bg-soft-warning text-warning">
                            <i class="bi-exclamation-triangle me-1"></i>@lang('Admin Only')
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Form -->
    <div class="card">
        <div class="card-header card-header-content-md-between">
            <div class="mb-2 mb-md-0">
                <h4 class="card-header-title">@lang('Payout Method Configurations')</h4>
                <p class="card-header-text mb-0">@lang('Configure limits and charges per currency for each payout method')</p>
            </div>
        </div>

        <form method="POST" action="{{ route('admin.merchant.payout.config.update', $merchant->id) }}">
            @csrf
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                        <thead class="thead-light">
                            <tr>
                                <th>@lang('Payout Method')</th>
                                <th>@lang('Min Limit')</th>
                                <th>@lang('Max Limit')</th>
                                <th>@lang('Percentage Charge (%)')</th>
                                <th>@lang('Fixed Charge')</th>
                                <th>@lang('Default Values')</th>
                                <th>@lang('Actions')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $globalIndex = 0; @endphp
                            @foreach($payoutMethods as $method)
                                @php
                                    $methodConfigs = $existingConfigs->get($method->id, collect());
                                    $payoutCurrencies = collect($method->payout_currencies);
                                @endphp

                                @foreach($payoutCurrencies as $currencyIndex => $defaultConfig)
                                    @php
                                        $currency = $defaultConfig['name'] ?? $defaultConfig['currency_symbol'] ?? 'NGN';
                                        $existingConfig = $methodConfigs->get($currency);
                                        $configIndex = $globalIndex++;
                                    @endphp
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($method->logo && $currencyIndex === 0)
                                                    <img src="{{ getFile($method->driver, $method->logo) }}" 
                                                         alt="{{ $method->name }}" class="avatar avatar-xs me-2">
                                                @else
                                                    <div class="avatar avatar-xs me-2"></div>
                                                @endif
                                                <div>
                                                    <span class="d-block h5 text-inherit mb-0">
                                                        {{ $method->name }}
                                                        @if($currencyIndex > 0)
                                                            <small class="text-muted">({{ $currency }})</small>
                                                        @endif
                                                    </span>
                                                    @if($currencyIndex === 0)
                                                        <span class="d-block fs-6 text-body">{{ $method->description }}</span>
                                                    @endif
                                                    <span class="badge bg-soft-secondary text-secondary">{{ $currency }}</span>
                                                </div>
                                            </div>
                                        </td>
                                        
                                        <td>
                                            <input type="hidden" name="configurations[{{ $configIndex }}][payout_method_id]" value="{{ $method->id }}">
                                            <input type="hidden" name="configurations[{{ $configIndex }}][currency]" value="{{ $currency }}">
                                            <input type="number" 
                                                   name="configurations[{{ $configIndex }}][min_limit]" 
                                                   class="form-control form-control-sm" 
                                                   placeholder="{{ $defaultConfig['min_limit'] ?? 0 }}"
                                                   value="{{ $existingConfig->min_limit ?? '' }}"
                                                   step="0.01" min="0">
                                        </td>
                                        
                                        <td>
                                            <input type="number" 
                                                   name="configurations[{{ $configIndex }}][max_limit]" 
                                                   class="form-control form-control-sm" 
                                                   placeholder="{{ $defaultConfig['max_limit'] ?? 0 }}"
                                                   value="{{ $existingConfig->max_limit ?? '' }}"
                                                   step="0.01" min="0">
                                        </td>
                                        
                                        <td>
                                            <input type="number" 
                                                   name="configurations[{{ $configIndex }}][percentage_charge]" 
                                                   class="form-control form-control-sm" 
                                                   placeholder="{{ $defaultConfig['percentage_charge'] ?? 0 }}"
                                                   value="{{ $existingConfig->percentage_charge ?? '' }}"
                                                   step="0.01" min="0" max="100">
                                        </td>
                                        
                                        <td>
                                            <input type="number" 
                                                   name="configurations[{{ $configIndex }}][fixed_charge]" 
                                                   class="form-control form-control-sm" 
                                                   placeholder="{{ $defaultConfig['fixed_charge'] ?? 0 }}"
                                                   value="{{ $existingConfig->fixed_charge ?? '' }}"
                                                   step="0.01" min="0">
                                        </td>
                                        
                                        <td>
                                            <small class="text-muted">
                                                <strong>@lang('Min'):</strong> {{ $defaultConfig['min_limit'] ?? 0 }}<br>
                                                <strong>@lang('Max'):</strong> {{ $defaultConfig['max_limit'] ?? 0 }}<br>
                                                <strong>@lang('Percentage'):</strong> {{ $defaultConfig['percentage_charge'] ?? 0 }}%<br>
                                                <strong>@lang('Fixed'):</strong> {{ $defaultConfig['fixed_charge'] ?? 0 }}
                                            </small>
                                        </td>
                                        
                                        <td>
                                            @if($existingConfig)
                                                <button type="button" class="btn btn-sm btn-outline-secondary clear-config"
                                                        data-config-index="{{ $configIndex }}">
                                                    <i class="bi-arrow-clockwise me-1"></i> @lang('Reset')
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-config"
                                                        data-merchant-id="{{ $merchant->id }}"
                                                        data-method-id="{{ $method->id }}"
                                                        data-currency="{{ $currency }}"
                                                        data-method-name="{{ $method->name }}">
                                                    <i class="bi-trash me-1"></i> @lang('Delete')
                                                </button>
                                            @else
                                                <span class="text-muted">@lang('Using defaults')</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card-footer">
                <div class="d-flex justify-content-end gap-3">
                    <a href="{{ route('admin.merchants') }}" class="btn btn-white">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Merchants')
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi-check2 me-1"></i> @lang('Save Configuration')
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Delete Configuration Modal -->
    <div class="modal fade" id="deleteConfigModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="deleteConfigModalLabel">@lang('Confirm Delete')</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>@lang('Are you sure you want to delete the custom configuration for') <strong id="deleteMethodName"></strong> (<strong id="deleteCurrency"></strong>)?</p>
                    <p class="text-muted">@lang('This will revert to using the default payout method settings.')</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">@lang('Cancel')</button>
                    <form id="deleteConfigForm" method="POST">
                        @csrf
                        @method('DELETE')
                        <input type="hidden" name="currency" id="deleteConfigCurrency">
                        <button type="submit" class="btn btn-danger">@lang('Delete Configuration')</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('script')
<script>
    // Clear configuration for a specific method/currency
    document.querySelectorAll('.clear-config').forEach(button => {
        button.addEventListener('click', function() {
            const configIndex = this.dataset.configIndex;
            const row = this.closest('tr');
            const inputs = row.querySelectorAll('input[type="number"]');

            inputs.forEach(input => {
                input.value = '';
            });
        });
    });

    // Delete configuration functionality
    document.querySelectorAll('.delete-config').forEach(button => {
        button.addEventListener('click', function() {
            const merchantId = this.dataset.merchantId;
            const methodId = this.dataset.methodId;
            const currency = this.dataset.currency;
            const methodName = this.dataset.methodName;

            // Set modal content
            document.getElementById('deleteMethodName').textContent = methodName;
            document.getElementById('deleteCurrency').textContent = currency;
            document.getElementById('deleteConfigCurrency').value = currency;

            // Set form action
            const deleteUrl = "{{ route('admin.merchant.payout.config.delete', ['merchantId' => ':merchantId', 'payoutMethodId' => ':methodId']) }}"
                .replace(':merchantId', merchantId)
                .replace(':methodId', methodId);
            document.getElementById('deleteConfigForm').action = deleteUrl;

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('deleteConfigModal'));
            modal.show();
        });
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const inputs = this.querySelectorAll('input[type="number"]');
        let hasError = false;
        
        inputs.forEach(input => {
            if (input.value && parseFloat(input.value) < 0) {
                hasError = true;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        if (hasError) {
            e.preventDefault();
            alert('@lang('Please ensure all values are positive numbers.')');
        }
    });
</script>
@endpush
@endsection
