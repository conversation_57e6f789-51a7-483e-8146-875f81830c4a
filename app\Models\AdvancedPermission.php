<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

/**
 * Advanced Permission Model
 * 
 * Represents granular permissions in the advanced role system.
 * Each permission defines a specific action on a resource (e.g., users.create, transactions.read).
 * 
 * @property int $id
 * @property string $name Unique permission name (e.g., users.create)
 * @property string $display_name Human-readable permission name
 * @property string|null $description Detailed description
 * @property string $resource Resource/module name
 * @property string $action Action type (create, read, update, delete, custom)
 * @property string|null $category Permission category for grouping
 * @property array|null $metadata Additional permission metadata
 * @property int $sort_order Display order for UI
 * @property bool $is_system System permission that cannot be deleted
 * @property bool $is_active Whether permission is active
 * @property int|null $created_by <PERSON><PERSON> who created this permission
 * @property int|null $updated_by <PERSON><PERSON> who last updated this permission
 */
class AdvancedPermission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'display_name',
        'description',
        'resource',
        'action',
        'category',
        'metadata',
        'sort_order',
        'is_system',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_system' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    protected $attributes = [
        'is_system' => false,
        'is_active' => true,
        'sort_order' => 0,
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically set created_by and updated_by
        static::creating(function ($model) {
            if (auth()->check() && auth()->user() instanceof Admin) {
                $model->created_by = auth()->id();
            }
        });

        static::updating(function ($model) {
            if (auth()->check() && auth()->user() instanceof Admin) {
                $model->updated_by = auth()->id();
            }
        });

        // Prevent deletion of system permissions
        static::deleting(function ($model) {
            if ($model->is_system) {
                throw new \Exception('System permissions cannot be deleted.');
            }
        });
    }

    /**
     * Get the admin who created this permission
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * Get the admin who last updated this permission
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'updated_by');
    }

    /**
     * Get all roles that have this permission
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(AdvancedRole::class, 'advanced_role_permissions')
            ->withPivot([
                'constraints',
                'is_granted',
                'priority',
                'valid_from',
                'valid_until',
                'schedule',
                'granted_by',
                'granted_at',
                'grant_reason'
            ])
            ->withTimestamps();
    }

    /**
     * Get role permissions pivot records
     */
    public function rolePermissions(): HasMany
    {
        return $this->hasMany(AdvancedRolePermission::class, 'permission_id');
    }

    /**
     * Scope: Filter by resource
     */
    public function scopeForResource(Builder $query, string $resource): Builder
    {
        return $query->where('resource', $resource);
    }

    /**
     * Scope: Filter by action
     */
    public function scopeForAction(Builder $query, string $action): Builder
    {
        return $query->where('action', $action);
    }

    /**
     * Scope: Filter by category
     */
    public function scopeInCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope: Only active permissions
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Only system permissions
     */
    public function scopeSystem(Builder $query): Builder
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope: Non-system permissions
     */
    public function scopeNonSystem(Builder $query): Builder
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope: Ordered for display
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('category')
            ->orderBy('resource')
            ->orderBy('sort_order')
            ->orderBy('action');
    }

    /**
     * Get permission by name
     */
    public static function findByName(string $name): ?self
    {
        return static::where('name', $name)->first();
    }

    /**
     * Get all permissions for a resource
     */
    public static function forResource(string $resource): Collection
    {
        return static::where('resource', $resource)
            ->active()
            ->ordered()
            ->get();
    }

    /**
     * Get CRUD permissions for a resource
     */
    public static function getCrudPermissions(string $resource): Collection
    {
        return static::where('resource', $resource)
            ->whereIn('action', ['create', 'read', 'update', 'delete'])
            ->active()
            ->ordered()
            ->get();
    }

    /**
     * Create standard CRUD permissions for a resource
     */
    public static function createCrudPermissions(
        string $resource,
        string $category = null,
        array $customActions = []
    ): Collection {
        $actions = array_merge(['create', 'read', 'update', 'delete'], $customActions);
        $permissions = collect();

        foreach ($actions as $action) {
            $permission = static::create([
                'name' => "{$resource}.{$action}",
                'display_name' => ucfirst($action) . ' ' . ucfirst($resource),
                'description' => "Allow {$action} operations on {$resource}",
                'resource' => $resource,
                'action' => $action,
                'category' => $category,
                'is_system' => true, // Mark as system permission
            ]);

            $permissions->push($permission);
        }

        return $permissions;
    }

    /**
     * Check if permission name is valid format
     */
    public static function isValidPermissionName(string $name): bool
    {
        return preg_match('/^[a-z_]+\.[a-z_]+$/', $name) === 1;
    }

    /**
     * Parse permission name into resource and action
     */
    public static function parsePermissionName(string $name): array
    {
        $parts = explode('.', $name, 2);
        return [
            'resource' => $parts[0] ?? '',
            'action' => $parts[1] ?? '',
        ];
    }

    /**
     * Get formatted display name with category
     */
    public function getFormattedDisplayNameAttribute(): string
    {
        $category = $this->category ? "[{$this->category}] " : '';
        return $category . $this->display_name;
    }

    /**
     * Check if permission is deletable
     */
    public function isDeletable(): bool
    {
        return !$this->is_system && $this->rolePermissions()->count() === 0;
    }

    /**
     * Get permission usage count
     */
    public function getUsageCount(): int
    {
        return $this->rolePermissions()->count();
    }
}
