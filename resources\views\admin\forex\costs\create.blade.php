@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.costs.index') }}">
                                    @lang('Operational Costs')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Record New Cost')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Record New Operational Cost')</h1>
                    <p class="page-header-text">@lang('Add operational expenses on-the-fly with categories and attachments')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.costs.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Costs')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row justify-content-lg-center">
            <div class="col-lg-8">
                <!-- Card -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Cost Information')</h4>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <form action="{{ route('admin.forex.costs.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf

                            <!-- Cost Name -->
                            <div class="row mb-4">
                                <label for="costNameLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Cost Name') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Brief description of the operational cost')"></i>
                                </label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control @error('cost_name') is-invalid @enderror" 
                                           name="cost_name" id="costNameLabel" 
                                           placeholder="@lang('e.g., Office Rent, Electricity Bill, Staff Salary')" 
                                           value="{{ old('cost_name') }}" required>
                                    @error('cost_name')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Cost Name -->

                            <!-- Description -->
                            <div class="row mb-4">
                                <label for="descriptionLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Description')
                                </label>
                                <div class="col-sm-9">
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              name="description" id="descriptionLabel" rows="3"
                                              placeholder="@lang('Detailed description of the cost (optional)')">{{ old('description') }}</textarea>
                                    @error('description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Description -->

                            <!-- Amount and Currency -->
                            <div class="row mb-4">
                                <label for="amountLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Amount') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                               name="amount" id="amountLabel" step="0.01" min="0.01"
                                               placeholder="@lang('0.00')" value="{{ old('amount') }}" required>
                                        <select class="form-select @error('currency') is-invalid @enderror" name="currency" required>
                                            <option value="">@lang('Currency')</option>
                                            <option value="NGN" {{ old('currency') == 'NGN' ? 'selected' : '' }}>NGN</option>
                                            <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>USD</option>
                                        </select>
                                    </div>
                                    @error('amount')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    @error('currency')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Amount and Currency -->

                            <!-- Category -->
                            <div class="row mb-4">
                                <label for="categoryLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Category') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Select existing category or create new one')"></i>
                                </label>
                                <div class="col-sm-9">
                                    <div class="tom-select-custom">
                                        <select class="js-select form-select @error('category') is-invalid @enderror" 
                                                name="category" id="categoryLabel"
                                                data-hs-tom-select-options='{
                                                    "create": true,
                                                    "placeholder": "@lang('Select or create category')"
                                                }'>
                                            <option value="">@lang('Select or create category')</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category }}" {{ old('category') == $category ? 'selected' : '' }}>
                                                    {{ $category }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('category')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Common categories: Office Rent, Utilities, Staff Salaries, Marketing, Equipment, Maintenance')
                                    </small>
                                </div>
                            </div>
                            <!-- End Category -->

                            <!-- Cost Date -->
                            <div class="row mb-4">
                                <label for="costDateLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Cost Date') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input type="date" class="form-control @error('cost_date') is-invalid @enderror" 
                                           name="cost_date" id="costDateLabel" 
                                           value="{{ old('cost_date', date('Y-m-d')) }}" 
                                           max="{{ date('Y-m-d') }}" required>
                                    @error('cost_date')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Cost Date -->

                            <!-- Notes -->
                            <div class="row mb-4">
                                <label for="notesLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Notes')
                                </label>
                                <div class="col-sm-9">
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              name="notes" id="notesLabel" rows="2"
                                              placeholder="@lang('Additional notes or comments (optional)')">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Notes -->

                            <!-- File Attachments -->
                            <div class="row mb-4">
                                <label for="attachmentsLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Attachments') <i class="bi-question-circle text-body ms-1" data-bs-toggle="tooltip" data-bs-placement="top" title="@lang('Upload receipts, invoices, or supporting documents')"></i>
                                </label>
                                <div class="col-sm-9">
                                    <div id="attachFileFrontendUploader" class="js-dropzone dz-dropzone dz-dropzone-card">
                                        <div class="dz-message">
                                            <img class="avatar avatar-xl avatar-4x3 mb-3" 
                                                 src="{{ asset('assets/admin/img/oc-upload.svg') }}" 
                                                 alt="Image Description" data-hs-theme-appearance="default">
                                            <img class="avatar avatar-xl avatar-4x3 mb-3" 
                                                 src="{{ asset('assets/admin/img/oc-upload-light.svg') }}" 
                                                 alt="Image Description" data-hs-theme-appearance="dark">
                                            <h5>@lang('Drag and drop your files here')</h5>
                                            <p class="mb-2">@lang('or')</p>
                                            <span class="btn btn-white btn-sm">@lang('Browse files')</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Traditional file input as fallback -->
                                    <input type="file" name="attachments[]" multiple 
                                           class="form-control mt-2 @error('attachments.*') is-invalid @enderror"
                                           accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                                    
                                    @error('attachments.*')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Supported formats: JPG, PNG, PDF, DOC, DOCX. Max size: 2MB per file.')
                                    </small>
                                </div>
                            </div>
                            <!-- End File Attachments -->

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end">
                                <div class="d-flex gap-3">
                                    <a class="btn btn-white" href="{{ route('admin.forex.costs.index') }}">
                                        @lang('Cancel')
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi-check-circle me-1"></i> @lang('Record Cost')
                                    </button>
                                </div>
                            </div>
                            <!-- End Submit Buttons -->
                        </form>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Card -->
            </div>
        </div>
    </div>
@endsection

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/admin/css/dropzone.min.css') }}">
@endpush

@push('js-lib')
    <script src="{{ asset('assets/admin/js/tom-select.complete.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/dropzone.min.js') }}"></script>
@endpush

@push('script')
    <script>
        'use strict';
        
        $(document).ready(function () {
            // Initialize Tom Select for category dropdown with create option
            HSCore.components.HSTomSelect.init('.js-select');
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize Dropzone (if available)
            if (typeof Dropzone !== 'undefined') {
                Dropzone.autoDiscover = false;
                
                var myDropzone = new Dropzone("#attachFileFrontendUploader", {
                    url: "#", // We'll handle this via traditional form submission
                    autoProcessQueue: false,
                    addRemoveLinks: true,
                    maxFilesize: 2, // MB
                    acceptedFiles: ".jpg,.jpeg,.png,.pdf,.doc,.docx",
                    dictDefaultMessage: '@lang("Drag and drop files here or click to browse")',
                    dictRemoveFile: '@lang("Remove")',
                    dictFileTooBig: '@lang("File is too big. Max size: 2MB")',
                    dictInvalidFileType: '@lang("Invalid file type")',
                });
            }
        });
    </script>
@endpush
