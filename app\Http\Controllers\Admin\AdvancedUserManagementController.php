<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Admin;
use App\Models\AdvancedRole;
use App\Models\AdvancedUserRole;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

/**
 * Enhanced User Management Controller with Advanced Role Integration
 * 
 * Provides comprehensive user management with seamless advanced role assignment.
 */
class AdvancedUserManagementController extends Controller
{
    use HasAdvancedPermissions;

    /**
     * User & Role Management Dashboard
     */
    public function dashboard(): View
    {
        $stats = [
            'total_users' => User::count(),
            'users_with_advanced_roles' => User::where('use_advanced_roles', true)->count(),
            'total_admins' => Admin::count(),
            'admins_with_advanced_roles' => Admin::where('use_advanced_roles', true)->count(),
            'total_roles' => AdvancedRole::count(),
            'active_assignments' => AdvancedUserRole::where('is_active', true)->count(),
            'recent_assignments' => AdvancedUserRole::with(['user', 'role'])
                ->latest()
                ->limit(10)
                ->get(),
        ];

        $roleDistribution = AdvancedRole::withCount('userRoles')
            ->orderByDesc('user_roles_count')
            ->limit(10)
            ->get();

        return view('admin.user-role-management.dashboard', compact('stats', 'roleDistribution'));
    }

    /**
     * Display users with advanced role information
     */
    public function index(Request $request): View
    {
        $query = User::with(['advancedUserRoles.role']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role_filter')) {
            $roleId = $request->get('role_filter');
            $query->whereHas('advancedUserRoles', function ($q) use ($roleId) {
                $q->where('role_id', $roleId)->where('is_active', true);
            });
        }

        if ($request->filled('advanced_roles_status')) {
            $status = $request->get('advanced_roles_status');
            if ($status === 'enabled') {
                $query->where('use_advanced_roles', true);
            } elseif ($status === 'disabled') {
                $query->where('use_advanced_roles', false);
            }
        }

        $users = $query->paginate(25)->withQueryString();

        // Get filter options
        $roles = AdvancedRole::active()->orderBy('display_name')->get();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show form for creating new user with role assignment
     */
    public function create(): View
    {
        $roles = AdvancedRole::active()->orderBy('display_name')->get();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store new user with optional role assignment
     */
    public function store(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Password::defaults()],
            'use_advanced_roles' => 'boolean',
            'roles' => 'array',
            'roles.*' => 'exists:advanced_roles,id',
            'role_expires_at' => 'nullable|array',
            'role_expires_at.*' => 'nullable|date|after:now',
            'role_context' => 'nullable|array',
            'role_context.*' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Create user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'use_advanced_roles' => $request->boolean('use_advanced_roles', false),
                'email_verified_at' => now(),
            ]);

            // Assign roles if advanced roles are enabled
            if ($user->use_advanced_roles && $request->filled('roles')) {
                foreach ($request->roles as $index => $roleId) {
                    $role = AdvancedRole::find($roleId);
                    if ($role) {
                        $user->assignAdvancedRole($role, [
                            'expires_at' => $request->role_expires_at[$index] ?? null,
                            'context' => $request->role_context[$index] ?? null,
                            'assignment_reason' => 'Assigned during user creation',
                        ]);
                    }
                }
            }

            $this->logPermissionEvent(
                'user_created',
                'users.create',
                "Created user: {$user->email}",
                ['user_id' => $user->id]
            );

            return redirect()->route('admin.users.show', $user)
                ->with('success', "User '{$user->name}' created successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create user: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display user with role information
     */
    public function show(User $user): View
    {
        $user->load([
            'advancedUserRoles.role',
            'advancedUserRoles' => function ($query) {
                $query->orderByDesc('created_at');
            }
        ]);

        $availableRoles = AdvancedRole::active()
            ->whereNotIn('id', $user->advancedUserRoles()->where('is_active', true)->pluck('role_id'))
            ->orderBy('display_name')
            ->get();

        $effectivePermissions = $user->usesAdvancedRoles() 
            ? $user->getAdvancedPermissions()->groupBy('category')
            : collect();

        return view('admin.users.show', compact('user', 'availableRoles', 'effectivePermissions'));
    }

    /**
     * Show form for editing user
     */
    public function edit(User $user): View
    {
        $user->load('advancedUserRoles.role');
        $roles = AdvancedRole::active()->orderBy('display_name')->get();
        
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update user information and roles
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => ['nullable', 'confirmed', Password::defaults()],
            'use_advanced_roles' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'use_advanced_roles' => $request->boolean('use_advanced_roles'),
            ];

            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            // If advanced roles were disabled, deactivate all role assignments
            if (!$user->use_advanced_roles) {
                $user->advancedUserRoles()->where('is_active', true)->update([
                    'is_active' => false,
                    'revoked_at' => now(),
                    'revoked_by' => auth()->id(),
                    'revocation_reason' => 'Advanced roles disabled for user',
                ]);
            }

            $this->logPermissionEvent(
                'user_updated',
                'users.update',
                "Updated user: {$user->email}",
                ['user_id' => $user->id]
            );

            return redirect()->route('admin.users.show', $user)
                ->with('success', "User '{$user->name}' updated successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update user: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Delete user
     */
    public function destroy(User $user): RedirectResponse
    {
        try {
            $userName = $user->name;
            
            // Revoke all active role assignments
            $user->advancedUserRoles()->where('is_active', true)->update([
                'is_active' => false,
                'revoked_at' => now(),
                'revoked_by' => auth()->id(),
                'revocation_reason' => 'User deleted',
            ]);

            $user->delete();

            $this->logPermissionEvent(
                'user_deleted',
                'users.delete',
                "Deleted user: {$userName}",
                ['user_name' => $userName]
            );

            return redirect()->route('admin.users.index')
                ->with('success', "User '{$userName}' deleted successfully.");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete user: ' . $e->getMessage()]);
        }
    }

    /**
     * Assign role to user
     */
    public function assignRole(Request $request, User $user): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'role_id' => 'required|exists:advanced_roles,id',
            'expires_at' => 'nullable|date|after:now',
            'context' => 'nullable|string|max:100',
            'priority' => 'nullable|integer|min:0|max:100',
            'assignment_reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        if (!$user->use_advanced_roles) {
            return response()->json(['success' => false, 'message' => 'Advanced roles not enabled for this user'], 400);
        }

        try {
            $role = AdvancedRole::find($request->role_id);
            
            // Check if user already has this role
            $existingAssignment = $user->advancedUserRoles()
                ->where('role_id', $role->id)
                ->where('context', $request->context)
                ->where('is_active', true)
                ->first();

            if ($existingAssignment) {
                return response()->json(['success' => false, 'message' => 'User already has this role in the specified context'], 400);
            }

            $user->assignAdvancedRole($role, [
                'expires_at' => $request->expires_at,
                'context' => $request->context,
                'priority' => $request->priority ?? 0,
                'assignment_reason' => $request->assignment_reason,
            ]);

            $this->logPermissionEvent(
                'role_assigned',
                'user_roles.create',
                "Assigned role '{$role->display_name}' to user '{$user->email}'",
                ['user_id' => $user->id, 'role_id' => $role->id]
            );

            return response()->json([
                'success' => true,
                'message' => "Role '{$role->display_name}' assigned successfully",
                'role' => [
                    'id' => $role->id,
                    'name' => $role->display_name,
                    'expires_at' => $request->expires_at,
                    'context' => $request->context,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to assign role: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Revoke role from user
     */
    public function revokeRole(User $user, AdvancedRole $role): JsonResponse
    {
        try {
            $assignment = $user->advancedUserRoles()
                ->where('role_id', $role->id)
                ->where('is_active', true)
                ->first();

            if (!$assignment) {
                return response()->json(['success' => false, 'message' => 'User does not have this role'], 400);
            }

            $assignment->revoke('Revoked by admin');

            $this->logPermissionEvent(
                'role_revoked',
                'user_roles.delete',
                "Revoked role '{$role->display_name}' from user '{$user->email}'",
                ['user_id' => $user->id, 'role_id' => $role->id]
            );

            return response()->json([
                'success' => true,
                'message' => "Role '{$role->display_name}' revoked successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to revoke role: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Toggle advanced roles for user
     */
    public function toggleAdvancedRoles(User $user): JsonResponse
    {
        try {
            $newStatus = !$user->use_advanced_roles;
            $user->update(['use_advanced_roles' => $newStatus]);

            // If disabling, revoke all active assignments
            if (!$newStatus) {
                $user->advancedUserRoles()->where('is_active', true)->update([
                    'is_active' => false,
                    'revoked_at' => now(),
                    'revoked_by' => auth()->id(),
                    'revocation_reason' => 'Advanced roles disabled',
                ]);
            }

            $this->logPermissionEvent(
                'advanced_roles_toggled',
                'users.update',
                "Advanced roles " . ($newStatus ? 'enabled' : 'disabled') . " for user '{$user->email}'",
                ['user_id' => $user->id, 'new_status' => $newStatus]
            );

            return response()->json([
                'success' => true,
                'message' => 'Advanced roles ' . ($newStatus ? 'enabled' : 'disabled') . ' successfully',
                'new_status' => $newStatus
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to toggle advanced roles: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Search users for AJAX requests
     */
    public function searchUsers(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $limit = $request->get('limit', 20);

        $users = User::where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            })
            ->limit($limit)
            ->get(['id', 'name', 'email', 'use_advanced_roles']);

        return response()->json($users);
    }

    /**
     * Get user's roles for AJAX requests
     */
    public function getUserRoles(User $user): JsonResponse
    {
        $roles = $user->advancedUserRoles()
            ->with('role')
            ->where('is_active', true)
            ->get()
            ->map(function ($assignment) {
                return [
                    'id' => $assignment->id,
                    'role' => [
                        'id' => $assignment->role->id,
                        'name' => $assignment->role->name,
                        'display_name' => $assignment->role->display_name,
                        'color' => $assignment->role->color,
                    ],
                    'expires_at' => $assignment->expires_at?->toISOString(),
                    'context' => $assignment->context,
                    'priority' => $assignment->priority,
                    'status' => $assignment->getStatus(),
                ];
            });

        return response()->json($roles);
    }
}
