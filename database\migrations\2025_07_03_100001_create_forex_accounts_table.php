<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('account_name')->unique();
            $table->enum('account_type', ['USD', 'CBN', 'Difference', 'Investment']);
            $table->string('currency_code', 3)->comment('USD or NGN');
            $table->decimal('balance', 18, 8)->default(0.********);
            $table->decimal('pending_balance', 18, 8)->default(0.********)->comment('Balance from pending transactions');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['account_type', 'is_active']);
            $table->index('currency_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_accounts');
    }
};
