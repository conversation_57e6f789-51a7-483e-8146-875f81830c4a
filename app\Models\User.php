<?php

namespace App\Models;

use App\Traits\Notify;
use App\Traits\HasAdvancedPermissions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;


class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes, Notify, HasAdvancedPermissions;

    protected $guarded = ['id'];

    protected $hidden = [
        'password',
        'remember_token',
        'secret_key',
    ];

    protected $appends = ['last-seen-activity',];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'use_advanced_roles' => 'boolean',
        'advanced_role_cache' => 'array',
        'role_cache_updated_at' => 'datetime',
        'permission_overrides' => 'array',
        'can_override_permissions' => 'boolean',
    ];

    protected $dates = ['deleted_at'];

    protected static function boot()
    {
        parent::boot();
        static::saved(function () {
            Cache::forget('userRecord');
        });
    }


    public function getImageUrlAttribute()
    {
        return getFile($this->image_driver, $this->attributes['image']);
    }

    public function getFullNameAttribute()
    {
        return $this->firstname . ' ' . $this->lastname;
    }

    public function funds()
    {
        return $this->hasMany(Fund::class)->latest()->where('status', '!=', 0);
    }

    public function transaction()
    {
        return $this->hasOne(Transaction::class)->latest();
    }

    public function payout()
    {
        return $this->hasMany(Payout::class, 'user_id');
    }

    public function getLastSeenActivityAttribute(): bool
    {
        if (Cache::has('user-is-online-' . $this->id)) {
            return true;
        } else {
            return false;
        }
    }

    public function notifypermission()
    {
        return $this->morphOne(NotificationPermission::class, 'notifyable');
    }

    public function inAppNotification()
    {
        return $this->morphOne(InAppNotification::class, 'inAppNotificationable', 'in_app_notificationable_type', 'in_app_notificationable_id');
    }

    public function fireBaseToken()
    {
        return $this->morphMany(FireBaseToken::class, 'tokenable');
    }


    public function profilePicture($type = false): string
    {
        $type = config('demo.IS_DEMO') ;

        $status = $this->LastSeenActivity ? 'success' : 'warning';
        if (!$type && $this->image) {
            $url = getFile($this->image_driver, $this->image);
            return <<<HTML
            <div class="avatar avatar-sm avatar-circle">
                <img class="avatar-img" src="{$url}" alt="...">
                <span class="avatar-status avatar-sm-status avatar-status-{$status}"></span>
            </div>
        HTML;
        }

        $initial = mb_check_encoding($this->firstname, 'UTF-8') ? mb_substr(trim($this->firstname), 0, 1) : '';
        return <<<HTML
        <div class="avatar avatar-sm avatar-soft-primary avatar-circle">
            <span class="avatar-initials">{$initial}</span>
            <span class="avatar-status avatar-sm-status avatar-status-{$status}"></span>
        </div>
    HTML;
    }


    public function sendPasswordResetNotification($token)
    {
        $this->mail($this, 'PASSWORD_RESET', $params = [
            'message' => '<a href="' . url('password/reset', $token) . '?email=' . $this->email . '" target="_blank">Click To Reset Password</a>'
        ]);
    }

    public function storeCurrency()
    {
        return $this->belongsTo(Currency::class, 'store_currency_id', 'id');
    }

    public function qrCurrency()
    {
        return $this->belongsTo(Currency::class, 'qr_currency_id', 'id');
    }


    public function getNameAttribute()
    {
        return $this->firstname.' '.$this->lastname;
    }

    public function getImage()
    {
        return getFile($this->image_driver, $this->image);
    }

    public function scopeFilterByState($query, $state)
    {
        return match ($state) {
            'active-users' => $query->whereStatus(1),
            'blocked-users' => $query->whereStatus(0),
            'email-unverified' => $query->where('email_verification', 0),
            'sms-unverified' => $query->where('sms_verification', 0),
            default => $query,
        };
    }

    public function qrLink(): string
    {
        return $this->qr_link ? route('public.qr.Payment', $this->qr_link) : 'javascript:void(0)';
    }

    public function getMobileAttribute()
    {
        return $this->phone_code .$this->phone;
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // ===== ADVANCED ROLE SYSTEM RELATIONSHIPS =====

    /**
     * Get advanced role assignments for this user
     */
    public function advancedUserRoles()
    {
        return $this->morphMany(AdvancedUserRole::class, 'user');
    }

    /**
     * Get active advanced roles for this user
     */
    public function advancedRoles()
    {
        return $this->morphToMany(AdvancedRole::class, 'user', 'advanced_user_roles')
            ->withPivot([
                'is_active',
                'priority',
                'assigned_at',
                'expires_at',
                'context',
                'context_data'
            ])
            ->wherePivot('is_active', true)
            ->withTimestamps();
    }

    /**
     * Check if user has advanced role system enabled
     */
    public function usesAdvancedRoles(): bool
    {
        return $this->use_advanced_roles ?? false;
    }

    /**
     * Enable advanced role system for this user
     */
    public function enableAdvancedRoles(): void
    {
        $this->update(['use_advanced_roles' => true]);
    }

    /**
     * Get all permissions for this user (from advanced roles)
     */
    public function getAdvancedPermissions(): \Illuminate\Support\Collection
    {
        if (!$this->usesAdvancedRoles()) {
            return collect();
        }

        // Check cache first
        if ($this->advanced_role_cache && $this->role_cache_updated_at &&
            $this->role_cache_updated_at > now()->subMinutes(60)) {
            return collect($this->advanced_role_cache);
        }

        $permissions = collect();

        // Get permissions from all active roles
        $activeRoles = AdvancedUserRole::forUser($this)
            ->currentlyValid()
            ->with('role.permissions')
            ->byPriority()
            ->get();

        foreach ($activeRoles as $userRole) {
            if ($userRole->role) {
                $rolePermissions = $userRole->role->getAllPermissions();
                $permissions = $permissions->merge($rolePermissions);
            }
        }

        // Add permission overrides
        if ($this->can_override_permissions && $this->permission_overrides) {
            foreach ($this->permission_overrides as $override) {
                if ($override['is_granted']) {
                    $permission = AdvancedPermission::findByName($override['permission']);
                    if ($permission) {
                        $permissions->push($permission);
                    }
                } else {
                    // Remove denied permissions
                    $permissions = $permissions->reject(function ($permission) use ($override) {
                        return $permission->name === $override['permission'];
                    });
                }
            }
        }

        // Cache the result
        $permissionArray = $permissions->unique('id')->values()->toArray();
        $this->update([
            'advanced_role_cache' => $permissionArray,
            'role_cache_updated_at' => now(),
        ]);

        return $permissions->unique('id');
    }

    /**
     * Check if user has specific advanced permission
     */
    public function hasAdvancedPermission(string $permissionName): bool
    {
        if (!$this->usesAdvancedRoles()) {
            return false;
        }

        return $this->getAdvancedPermissions()
            ->contains('name', $permissionName);
    }

    /**
     * Clear permission cache
     */
    public function clearPermissionCache(): void
    {
        $this->update([
            'advanced_role_cache' => null,
            'role_cache_updated_at' => null,
        ]);
    }

    /**
     * Assign advanced role to user
     */
    public function assignAdvancedRole(AdvancedRole $role, array $options = []): AdvancedUserRole
    {
        $assignment = AdvancedUserRole::assignRole($this, $role, $options);
        $this->clearPermissionCache();
        return $assignment;
    }

    /**
     * Remove advanced role from user
     */
    public function removeAdvancedRole(AdvancedRole $role, string $reason = null): void
    {
        $assignment = $this->advancedUserRoles()
            ->where('role_id', $role->id)
            ->where('is_active', true)
            ->first();

        if ($assignment) {
            $assignment->revoke($reason);
            $this->clearPermissionCache();
        }
    }

    /**
     * Check if user has specific advanced role
     */
    public function hasAdvancedRole(string $roleName): bool
    {
        return AdvancedUserRole::userHasRole($this, $roleName);
    }
}
