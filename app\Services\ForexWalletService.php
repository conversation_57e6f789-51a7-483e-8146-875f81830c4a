<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\Deposit;
use App\Models\ForexBooking;
use App\Models\Gateway;
use App\Models\Transaction;
use App\Models\Wallet;
use Illuminate\Support\Facades\DB;

class ForexWalletService
{

    /**
     * Get currency ID by currency code
     */
    public function getCurrencyIdByCode(string $currencyCode): ?int
    {
        return Currency::where('code', $currencyCode)->value('id');
    }

    /**
     * Check if user has a wallet for specific currency
     */
    public function userHasWallet(int $userId, string $currencyCode): bool
    {
        $currencyId = $this->getCurrencyIdByCode($currencyCode);
        if (!$currencyId) {
            return false;
        }

        return Wallet::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->exists();
    }

    /**
     * Get user's wallet for specific currency
     */
    public function getUserWallet(int $userId, string $currencyCode): ?Wallet
    {
        $currencyId = $this->getCurrencyIdByCode($currencyCode);
        if (!$currencyId) {
            return null;
        }

        return Wallet::where('user_id', $userId)
            ->where('currency_id', $currencyId)
            ->with('currency')
            ->first();
    }

    /**
     * Get wallet availability for forex transaction
     * Returns array with wallet availability for both NGN and USD
     */
    public function getWalletAvailability(int $userId): array
    {
        return [
            'ngn_wallet' => [
                'exists' => $this->userHasWallet($userId, 'NGN'),
                'wallet' => $this->getUserWallet($userId, 'NGN'),
                'currency_id' => $this->getCurrencyIdByCode('NGN')
            ],
            'usd_wallet' => [
                'exists' => $this->userHasWallet($userId, 'USD'),
                'wallet' => $this->getUserWallet($userId, 'USD'),
                'currency_id' => $this->getCurrencyIdByCode('USD')
            ]
        ];
    }

    /**
     * Determine which wallet should be funded based on transaction type
     */
    public function getTargetWalletCurrency(string $transactionType): string
    {
        // For buying (NGN to USD): User receives USD, so fund USD wallet
        // For selling (USD to NGN): User receives NGN, so fund NGN wallet
        return $transactionType === 'buying' ? 'USD' : 'NGN';
    }

    /**
     * Fund user wallet with forex booking completion
     */
    public function fundWalletFromBooking(ForexBooking $booking, int $adminId): ?Transaction
    {
        if (!$booking->isWalletPayment() || !$booking->user_id) {
            return null;
        }

        return DB::transaction(function () use ($booking, $adminId) {
            $targetCurrency = $this->getTargetWalletCurrency($booking->transaction_type);
            $currencyId = $this->getCurrencyIdByCode($targetCurrency);

            if (!$currencyId) {
                throw new \Exception("Currency {$targetCurrency} not found");
            }

            // Calculate the amount to fund based on transaction type
            $fundAmount = $this->calculateWalletFundAmount($booking);

            // Update wallet balance
            updateWallet($booking->user_id, $currencyId, $fundAmount, 1);

            // Get or create forex wallet funding gateway for analytics tracking
            $gateway = $this->getOrCreateForexWalletGateway();

            // Create deposit record for analytics tracking
            $deposit = Deposit::create([
                'user_id' => $booking->user_id,
                'depositable_type' => ForexBooking::class,
                'depositable_id' => $booking->id,
                'payment_method_id' => $gateway->id,
                'payment_method_currency' => $targetCurrency,
                'amount' => $fundAmount,
                'percentage_charge' => 0,
                'fixed_charge' => 0,
                'payable_amount' => $fundAmount,
                'base_currency_charge' => 0,
                'payable_amount_in_base_currency' => $fundAmount,
                'status' => 1, // Mark as successful immediately
                'currency_id' => $currencyId,
                'email' => $booking->client_email,
            ]);

            // Create transaction record
            $transaction = Transaction::create([
                'user_id' => $booking->user_id,
                'currency_id' => $currencyId,
                'amount' => $fundAmount,
                'charge' => 0,
                'trx_type' => '+',
                'remarks' => "Forex wallet funding - {$booking->booking_reference}",
                'trx_id' => $deposit->trx_id, // Use the deposit's transaction ID for consistency
                'transactional_type' => ForexBooking::class,
                'transactional_id' => $booking->id,
            ]);

            // Link the transaction to the deposit
            $deposit->transactional()->save($transaction);

            return $transaction;
        });
    }

    /**
     * Calculate the amount to fund to user's wallet
     */
    private function calculateWalletFundAmount(ForexBooking $booking): float
    {
        if ($booking->transaction_type === 'buying') {
            // NGN to USD: User receives USD amount
            return (float) $booking->amount;
        } else {
            // USD to NGN: User receives NGN amount (customer_total)
            return (float) $booking->customer_total;
        }
    }

    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId(): string
    {
        return 'FXW' . date('Ymd') . rand(100000, 999999);
    }

    /**
     * Get or create forex wallet funding gateway for analytics tracking
     */
    private function getOrCreateForexWalletGateway(): Gateway
    {
        return Gateway::firstOrCreate(
            ['code' => 'forex_trading_funding'],
            [
                'name' => 'Forex Trading Funding',
                'sort_by' => 998,
                'image' => 'gateway/forex_wallet.png',
                'driver' => 'local',
                'status' => 1,
                'parameters' => json_encode([]),
                'currencies' => json_encode(['0' => ['NGN' => 'NGN', 'USD' => 'USD']]),
                'extra_parameters' => null,
                'supported_currency' => json_encode(['NGN', 'USD']),
                'receivable_currencies' => json_encode([
                    ['name' => 'NGN', 'currency_symbol' => 'NGN', 'conversion_rate' => '1', 'min_limit' => '1', 'max_limit' => '1000000', 'percentage_charge' => '0', 'fixed_charge' => '0'],
                    ['name' => 'USD', 'currency_symbol' => 'USD', 'conversion_rate' => '1', 'min_limit' => '1', 'max_limit' => '1000000', 'percentage_charge' => '0', 'fixed_charge' => '0']
                ]),
                'note' => 'Internal gateway for forex wallet funding transactions',
                'description' => 'Forex wallet funding from completed bookings',
                'environment' => 'live',
                'currency_type' => 1,
                'is_sandbox' => 0,
                'is_manual' => 0,
            ]
        );
    }

    /**
     * Get payment method options for user based on wallet availability
     */
    public function getPaymentMethodOptions(int $userId, string $transactionType): array
    {
        $targetCurrency = $this->getTargetWalletCurrency($transactionType);
        $hasWallet = $this->userHasWallet($userId, $targetCurrency);
        $wallet = $this->getUserWallet($userId, $targetCurrency);

        $options = [
            'has_wallet' => $hasWallet,
            'target_currency' => $targetCurrency,
            'wallet' => $wallet,
            'currency_id' => $this->getCurrencyIdByCode($targetCurrency),
            'payment_methods' => []
        ];

        // Always include account details option
        $options['payment_methods']['account_details'] = [
            'label' => 'Bank Account Details',
            'description' => 'Provide bank account details for payment'
        ];

        // Add wallet option if user has the required wallet
        if ($hasWallet && $wallet) {
            $options['payment_methods']['wallet'] = [
                'label' => "Pay to {$targetCurrency} Wallet",
                'description' => "Fund your {$targetCurrency} wallet (Balance: " . number_format($wallet->balance, 2) . " {$targetCurrency})",
                'wallet_balance' => $wallet->balance
            ];
        }

        return $options;
    }

    /**
     * Validate wallet payment method selection
     */
    public function validateWalletPayment(int $userId, string $paymentMethod, ?int $walletCurrencyId): bool
    {
        if ($paymentMethod !== 'wallet') {
            return true; // No validation needed for account details
        }

        if (!$walletCurrencyId) {
            return false;
        }

        // Check if user has wallet for the specified currency
        $currency = Currency::find($walletCurrencyId);
        if (!$currency) {
            return false;
        }

        return $this->userHasWallet($userId, $currency->code);
    }
}
