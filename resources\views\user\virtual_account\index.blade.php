@extends('user.layouts.app')
@section('page_title', __('My Virtual Accounts'))

@section('content')
    <div class="container-fluid">
        <div class="main-content">
            <div class="row">
                <div class="col">
                    <div class="header-part">
                        <div class="overview-content">
                            <div class="overview-block">
                                <div class="overview-details">
                                    <h2>@lang('My Virtual Accounts')</h2>
                                    <p>@lang('Manage your virtual accounts for receiving deposits directly to your wallet')</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title">@lang('Virtual Accounts')</h5>
                            <div class="dropdown">
                                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi-plus-circle me-1"></i>@lang('Create Account')
                                </button>
                                <ul class="dropdown-menu">
                                    @foreach(['NGN', 'USD'] as $currency)
                                        <li><h6 class="dropdown-header">{{ $currency }} Accounts</h6></li>
                                        <li>
                                            <a class="dropdown-item virtual-account-btn" href="javascript:void(0)" 
                                               data-currency="{{ $currency }}" 
                                               data-currency-name="{{ $currency }}"
                                               data-type="individual">
                                                <i class="bi-person me-2"></i>@lang('Individual Account')
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item virtual-account-btn" href="javascript:void(0)" 
                                               data-currency="{{ $currency }}" 
                                               data-currency-name="{{ $currency }}"
                                               data-type="business">
                                                <i class="bi-building me-2"></i>@lang('Business Account')
                                            </a>
                                        </li>
                                        @if(!$loop->last)<li><hr class="dropdown-divider"></li>@endif
                                    @endforeach
                                </ul>
                            </div>
                        </div>

                        <div class="card-body">
                            @if($virtualAccounts->count() > 0)
                                <div class="row">
                                    @foreach($virtualAccounts as $account)
                                        <div class="col-md-6 col-lg-4 mb-4">
                                            <div class="card border">
                                                <div class="card-body">
                                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar avatar-sm avatar-circle bg-soft-primary text-primary me-3">
                                                                <i class="bi-{{ $account->type === 'business' ? 'building' : 'person' }}"></i>
                                                            </div>
                                                            <div>
                                                                <h6 class="mb-0">{{ $account->currency }} {{ ucfirst($account->type) }}</h6>
                                                                <small class="text-muted">{{ ucfirst($account->provider) }}</small>
                                                            </div>
                                                        </div>
                                                        <span class="badge bg-{{ $account->is_active ? 'success' : 'danger' }}">
                                                            {{ $account->is_active ? __('Active') : __('Inactive') }}
                                                        </span>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label small text-muted">@lang('Account Number')</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" value="{{ $account->account_number }}" readonly>
                                                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('{{ $account->account_number }}')">
                                                                <i class="bi-clipboard"></i>
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label small text-muted">@lang('Account Name')</label>
                                                        <p class="form-control-plaintext">{{ $account->account_name }}</p>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label small text-muted">@lang('Bank Name')</label>
                                                        <p class="form-control-plaintext">{{ $account->bank_name }}</p>
                                                    </div>

                                                    @if($account->bank_code)
                                                        <div class="mb-3">
                                                            <label class="form-label small text-muted">@lang('Bank Code')</label>
                                                            <p class="form-control-plaintext">{{ $account->bank_code }}</p>
                                                        </div>
                                                    @endif

                                                    <div class="text-muted small">
                                                        @lang('Created'): {{ $account->created_at->format('M d, Y') }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <div class="mb-4">
                                        <i class="bi-bank display-1 text-muted"></i>
                                    </div>
                                    <h5>@lang('No Virtual Accounts Yet')</h5>
                                    <p class="text-muted">@lang('Create your first virtual account to start receiving deposits directly to your wallet.')</p>
                                    <button class="btn btn-primary" data-bs-toggle="dropdown">
                                        <i class="bi-plus-circle me-1"></i>@lang('Create Your First Account')
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        $(document).ready(function() {
            // Handle virtual account creation button click
            $('.virtual-account-btn').on('click', function() {
                const currency = $(this).data('currency');
                const currencyName = $(this).data('currency-name');
                const type = $(this).data('type');
                
                // Check eligibility first
                checkVirtualAccountEligibility(currency, currencyName, type);
            });
        });

        function checkVirtualAccountEligibility(currency, currencyName, type) {
            $.ajax({
                url: '{{ route("user.virtual.accounts.check.eligibility") }}',
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json'
                },
                data: { 
                    currency: currency,
                    type: type
                },
                success: function(response) {
                    if (response.status === 'success') {
                        if (response.data.can_create) {
                            showCreateVirtualAccountModal(currency, currencyName, type);
                        } else {
                            showVirtualAccountExistsMessage(currency, currencyName, type);
                        }
                    } else {
                        showErrorMessage(response.message || 'Failed to check eligibility');
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Failed to check eligibility';
                    showErrorMessage(errorMsg);
                }
            });
        }

        function showCreateVirtualAccountModal(currency, currencyName, type) {
            const typeLabel = type.charAt(0).toUpperCase() + type.slice(1);
            const typeIcon = type === 'business' ? 'bi-building' : 'bi-person';
            
            const modalHtml = `
                <div class="modal fade" id="virtualAccountModal" tabindex="-1" aria-labelledby="virtualAccountModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="virtualAccountModalLabel">Create ${typeLabel} Virtual Account</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <i class="${typeIcon} display-4 text-primary"></i>
                                    <h6 class="mt-3">Create ${typeLabel} ${currencyName} Virtual Account</h6>
                                    <p class="text-muted">This will create a ${type} virtual account for receiving ${currency} deposits directly to your wallet.</p>
                                </div>
                                <div class="alert alert-info">
                                    <i class="bi-info-circle me-2"></i>
                                    <strong>Note:</strong> Your KYC information will be used to create this account. Make sure your KYC is verified.
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" onclick="createVirtualAccount('${currency}', '${type}')">
                                    <span class="spinner-border spinner-border-sm d-none me-2" role="status"></span>
                                    Create Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Remove existing modal if any
            $('#virtualAccountModal').remove();
            
            // Add modal to body and show
            $('body').append(modalHtml);
            $('#virtualAccountModal').modal('show');
        }

        function createVirtualAccount(currency, type) {
            const $button = $('#virtualAccountModal .btn-primary');
            const $spinner = $button.find('.spinner-border');
            
            // Show loading state
            $button.prop('disabled', true);
            $spinner.removeClass('d-none');
            
            $.ajax({
                url: '{{ route("user.virtual.accounts.create") }}',
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json'
                },
                data: { 
                    currency: currency,
                    type: type
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $('#virtualAccountModal').modal('hide');
                        showVirtualAccountCreatedModal(response.data);
                        // Reload page to show new account
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showErrorMessage(response.message || 'Failed to create virtual account');
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Failed to create virtual account';
                    showErrorMessage(errorMsg);
                },
                complete: function() {
                    // Hide loading state
                    $button.prop('disabled', false);
                    $spinner.addClass('d-none');
                }
            });
        }

        function showVirtualAccountCreatedModal(accountData) {
            const modalHtml = `
                <div class="modal fade" id="virtualAccountSuccessModal" tabindex="-1" aria-labelledby="virtualAccountSuccessModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title" id="virtualAccountSuccessModalLabel">
                                    <i class="bi-check-circle me-2"></i>Virtual Account Created Successfully
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Account Number:</strong>
                                        <p class="text-primary fs-5">${accountData.account_number}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Account Name:</strong>
                                        <p>${accountData.account_name}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Bank Name:</strong>
                                        <p>${accountData.bank_name}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Currency:</strong>
                                        <p>${accountData.currency.code} - ${accountData.currency.name}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Account Type:</strong>
                                        <p>${accountData.type}</p>
                                    </div>
                                </div>
                                <div class="alert alert-success mt-3">
                                    <i class="bi-info-circle me-2"></i>
                                    You can now receive deposits directly to this virtual account. Funds will be automatically credited to your wallet.
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Got it!</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Remove existing modal if any
            $('#virtualAccountSuccessModal').remove();
            
            // Add modal to body and show
            $('body').append(modalHtml);
            $('#virtualAccountSuccessModal').modal('show');
        }

        function showVirtualAccountExistsMessage(currency, currencyName, type) {
            const typeLabel = type.charAt(0).toUpperCase() + type.slice(1);
            showErrorMessage(`You already have a ${typeLabel.toLowerCase()} virtual account for ${currencyName} (${currency})`);
        }

        function showErrorMessage(message) {
            // You can customize this to use your preferred notification system
            alert(message);
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // You can show a toast notification here
                alert('Account number copied to clipboard!');
            });
        }
    </script>
@endpush
