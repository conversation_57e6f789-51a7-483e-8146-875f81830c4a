{{-- Permission-Based Admin Navigation Menu --}}
<div id="sidebar-menu">
    <ul class="metismenu list-unstyled" id="side-menu">
        @foreach($menu as $item)
            @php
                $isActive = \App\Helpers\AdminMenuHelper::isMenuItemActive($item);
                $hasChildren = isset($item['children']) && count($item['children']) > 0;
            @endphp
            
            <li class="{{ $isActive ? 'mm-active' : '' }}">
                @if($hasChildren)
                    {{-- Parent menu item with children --}}
                    <a href="javascript: void(0);" class="has-arrow {{ $isActive ? 'mm-active' : '' }}">
                        @if(isset($item['icon']))
                            <i class="{{ $item['icon'] }}"></i>
                        @endif
                        <span data-key="t-{{ Str::slug($item['title']) }}">{{ $item['title'] }}</span>
                        @if(isset($item['badge']) && $item['badge'] > 0)
                            <span class="badge rounded-pill {{ $item['badge_class'] ?? 'bg-primary' }} float-end">
                                {{ $item['badge'] }}
                            </span>
                        @endif
                    </a>
                    
                    <ul class="sub-menu {{ $isActive ? 'mm-show' : '' }}" aria-expanded="{{ $isActive ? 'true' : 'false' }}">
                        @foreach($item['children'] as $child)
                            @php
                                $childActive = \App\Helpers\AdminMenuHelper::isMenuItemActive($child);
                                $hasGrandChildren = isset($child['children']) && count($child['children']) > 0;
                            @endphp
                            
                            <li class="{{ $childActive ? 'mm-active' : '' }}">
                                @if($hasGrandChildren)
                                    {{-- Sub-menu with children --}}
                                    <a href="javascript: void(0);" class="has-arrow {{ $childActive ? 'mm-active' : '' }}">
                                        @if(isset($child['icon']))
                                            <i class="{{ $child['icon'] }} me-2"></i>
                                        @endif
                                        <span data-key="t-{{ Str::slug($child['title']) }}">{{ $child['title'] }}</span>
                                        @if(isset($child['badge']) && $child['badge'] > 0)
                                            <span class="badge rounded-pill {{ $child['badge_class'] ?? 'bg-primary' }} float-end">
                                                {{ $child['badge'] }}
                                            </span>
                                        @endif
                                    </a>
                                    
                                    <ul class="sub-menu {{ $childActive ? 'mm-show' : '' }}" aria-expanded="{{ $childActive ? 'true' : 'false' }}">
                                        @foreach($child['children'] as $grandChild)
                                            @php
                                                $grandChildActive = \App\Helpers\AdminMenuHelper::isMenuItemActive($grandChild);
                                            @endphp
                                            
                                            <li class="{{ $grandChildActive ? 'mm-active' : '' }}">
                                                <a href="{{ isset($grandChild['route']) ? route($grandChild['route']) : '#' }}" 
                                                   class="{{ $grandChildActive ? 'active' : '' }}">
                                                    @if(isset($grandChild['icon']))
                                                        <i class="{{ $grandChild['icon'] }} me-2"></i>
                                                    @endif
                                                    <span data-key="t-{{ Str::slug($grandChild['title']) }}">{{ $grandChild['title'] }}</span>
                                                    @if(isset($grandChild['badge']) && $grandChild['badge'] > 0)
                                                        <span class="badge rounded-pill {{ $grandChild['badge_class'] ?? 'bg-primary' }} float-end">
                                                            {{ $grandChild['badge'] }}
                                                        </span>
                                                    @endif
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                @else
                                    {{-- Regular sub-menu item --}}
                                    <a href="{{ isset($child['route']) ? route($child['route']) : '#' }}" 
                                       class="{{ $childActive ? 'active' : '' }}">
                                        @if(isset($child['icon']))
                                            <i class="{{ $child['icon'] }} me-2"></i>
                                        @endif
                                        <span data-key="t-{{ Str::slug($child['title']) }}">{{ $child['title'] }}</span>
                                        @if(isset($child['badge']) && $child['badge'] > 0)
                                            <span class="badge rounded-pill {{ $child['badge_class'] ?? 'bg-primary' }} float-end">
                                                {{ $child['badge'] }}
                                            </span>
                                        @endif
                                    </a>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                @else
                    {{-- Single menu item without children --}}
                    <a href="{{ isset($item['route']) ? route($item['route']) : '#' }}" 
                       class="{{ $isActive ? 'active' : '' }}">
                        @if(isset($item['icon']))
                            <i class="{{ $item['icon'] }}"></i>
                        @endif
                        <span data-key="t-{{ Str::slug($item['title']) }}">{{ $item['title'] }}</span>
                        @if(isset($item['badge']) && $item['badge'] > 0)
                            <span class="badge rounded-pill {{ $item['badge_class'] ?? 'bg-primary' }} float-end">
                                {{ $item['badge'] }}
                            </span>
                        @endif
                    </a>
                @endif
            </li>
        @endforeach

        {{-- System Administration Section (Super Admin Only) --}}
        @isSuperAdmin
            <li class="menu-title" data-key="t-system">System Administration</li>
            
            <li>
                <a href="javascript: void(0);" class="has-arrow">
                    <i class="fas fa-tools"></i>
                    <span data-key="t-system-tools">System Tools</span>
                </a>
                <ul class="sub-menu" aria-expanded="false">
                    <li>
                        <a href="{{ route('admin.permissions.discover') }}" onclick="return confirm('Discover new permissions?')">
                            <i class="fas fa-search me-2"></i>
                            <span data-key="t-discover-permissions">Discover Permissions</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="clearCache(); return false;">
                            <i class="fas fa-broom me-2"></i>
                            <span data-key="t-clear-cache">Clear Cache</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="runHealthCheck(); return false;">
                            <i class="fas fa-heartbeat me-2"></i>
                            <span data-key="t-health-check">System Health</span>
                        </a>
                    </li>
                </ul>
            </li>

            <li>
                <a href="javascript: void(0);" class="has-arrow">
                    <i class="fas fa-database"></i>
                    <span data-key="t-maintenance">Maintenance</span>
                </a>
                <ul class="sub-menu" aria-expanded="false">
                    <li>
                        <a href="#" onclick="toggleMaintenanceMode(); return false;">
                            <i class="fas fa-wrench me-2"></i>
                            <span data-key="t-maintenance-mode">Maintenance Mode</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" onclick="optimizeDatabase(); return false;">
                            <i class="fas fa-database me-2"></i>
                            <span data-key="t-optimize-db">Optimize Database</span>
                        </a>
                    </li>
                </ul>
            </li>
        @endisSuperAdmin

        {{-- Quick Actions Section --}}
        <li class="menu-title" data-key="t-quick-actions">Quick Actions</li>
        
        @canAdvanced('users.create')
            <li>
                <a href="{{ route('admin.users.create') }}">
                    <i class="fas fa-user-plus"></i>
                    <span data-key="t-create-user">Create User</span>
                </a>
            </li>
        @endcanAdvanced

        @canAdvanced('advanced_roles.create')
            <li>
                <a href="{{ route('admin.roles.create') }}">
                    <i class="fas fa-plus-circle"></i>
                    <span data-key="t-create-role">Create Role</span>
                </a>
            </li>
        @endcanAdvanced

        @canAdvanced('merchants.create')
            <li>
                <a href="{{ route('admin.merchants.create') }}">
                    <i class="fas fa-store"></i>
                    <span data-key="t-add-merchant">Add Merchant</span>
                </a>
            </li>
        @endcanAdvanced

        {{-- Help & Support Section --}}
        <li class="menu-title" data-key="t-help">Help & Support</li>
        
        <li>
            <a href="#" onclick="showKeyboardShortcuts(); return false;">
                <i class="fas fa-keyboard"></i>
                <span data-key="t-shortcuts">Keyboard Shortcuts</span>
            </a>
        </li>
        
        <li>
            <a href="#" onclick="showSystemInfo(); return false;">
                <i class="fas fa-info-circle"></i>
                <span data-key="t-system-info">System Information</span>
            </a>
        </li>
    </ul>
</div>

{{-- Menu Enhancement Scripts --}}
<script>
// System administration functions (Super Admin only)
@isSuperAdmin
function clearCache() {
    if (confirm('Clear all application cache?')) {
        fetch('/admin/system/clear-cache', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success('Cache cleared successfully');
            } else {
                toastr.error('Failed to clear cache');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('An error occurred');
        });
    }
}

function runHealthCheck() {
    toastr.info('Running system health check...');
    
    fetch('/admin/system/health-check', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(`Health check completed. Status: ${data.status}`);
        } else {
            toastr.warning(`Health check completed with issues: ${data.message}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('Health check failed');
    });
}

function toggleMaintenanceMode() {
    if (confirm('Toggle maintenance mode? This will affect all users.')) {
        fetch('/admin/system/toggle-maintenance', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(`Maintenance mode ${data.enabled ? 'enabled' : 'disabled'}`);
            } else {
                toastr.error('Failed to toggle maintenance mode');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('An error occurred');
        });
    }
}

function optimizeDatabase() {
    if (confirm('Optimize database? This may take a few minutes.')) {
        toastr.info('Optimizing database...');
        
        fetch('/admin/system/optimize-database', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success('Database optimized successfully');
            } else {
                toastr.error('Database optimization failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('An error occurred');
        });
    }
}
@endisSuperAdmin

// Help functions
function showKeyboardShortcuts() {
    const shortcuts = `
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Shortcut</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td><kbd>Ctrl + /</kbd></td><td>Show this help</td></tr>
                    <tr><td><kbd>Ctrl + K</kbd></td><td>Quick search</td></tr>
                    <tr><td><kbd>Ctrl + N</kbd></td><td>Create new (context dependent)</td></tr>
                    <tr><td><kbd>Ctrl + S</kbd></td><td>Save current form</td></tr>
                    <tr><td><kbd>Esc</kbd></td><td>Close modal/cancel action</td></tr>
                    <tr><td><kbd>Alt + H</kbd></td><td>Go to dashboard</td></tr>
                    <tr><td><kbd>Alt + U</kbd></td><td>Go to users</td></tr>
                    <tr><td><kbd>Alt + R</kbd></td><td>Go to roles</td></tr>
                </tbody>
            </table>
        </div>
    `;
    
    showModal('Keyboard Shortcuts', shortcuts);
}

function showSystemInfo() {
    fetch('/admin/system/info', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const info = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Application</h6>
                        <p><strong>Version:</strong> ${data.app_version || 'N/A'}</p>
                        <p><strong>Environment:</strong> ${data.environment || 'N/A'}</p>
                        <p><strong>Debug Mode:</strong> ${data.debug ? 'Enabled' : 'Disabled'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Server</h6>
                        <p><strong>PHP Version:</strong> ${data.php_version || 'N/A'}</p>
                        <p><strong>Laravel Version:</strong> ${data.laravel_version || 'N/A'}</p>
                        <p><strong>Database:</strong> ${data.database_type || 'N/A'}</p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Advanced Permission System</h6>
                        <p><strong>Total Permissions:</strong> ${data.permissions_count || 0}</p>
                        <p><strong>Total Roles:</strong> ${data.roles_count || 0}</p>
                        <p><strong>Active Assignments:</strong> ${data.assignments_count || 0}</p>
                    </div>
                </div>
            `;
            showModal('System Information', info);
        } else {
            toastr.error('Failed to load system information');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('An error occurred');
    });
}

function showModal(title, content) {
    const modalHtml = `
        <div class="modal fade" id="infoModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('infoModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add new modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    new bootstrap.Modal(document.getElementById('infoModal')).show();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + / - Show shortcuts
    if (e.ctrlKey && e.key === '/') {
        e.preventDefault();
        showKeyboardShortcuts();
    }
    
    // Alt + H - Dashboard
    if (e.altKey && e.key === 'h') {
        e.preventDefault();
        window.location.href = '{{ route("admin.dashboard") }}';
    }
    
    // Alt + U - Users
    if (e.altKey && e.key === 'u') {
        e.preventDefault();
        @canAdvanced('users.read')
            window.location.href = '{{ route("admin.users.index") }}';
        @endcanAdvanced
    }
    
    // Alt + R - Roles
    if (e.altKey && e.key === 'r') {
        e.preventDefault();
        @canAdvanced('advanced_roles.read')
            window.location.href = '{{ route("admin.roles.index") }}';
        @endcanAdvanced
    }
});
</script>

<style>
.menu-title {
    padding: 12px 20px 8px;
    font-size: 11px;
    font-weight: 600;
    color: #74788d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 20px;
}

.menu-title:first-child {
    margin-top: 0;
}

#side-menu .badge {
    font-size: 10px;
    font-weight: 500;
}

kbd {
    padding: 2px 4px;
    font-size: 87.5%;
    color: #fff;
    background-color: #212529;
    border-radius: 3px;
}
</style>
