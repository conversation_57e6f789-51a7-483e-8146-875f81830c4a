<?php

namespace App\Console\Commands;

use App\Models\AdvancedRole;
use App\Models\AdvancedPermission;
use App\Models\AdvancedUserRole;
use App\Models\AdvancedPermissionAudit;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Permission System Health Monitor
 * 
 * Monitors the health and performance of the advanced permission system.
 */
class PermissionSystemHealth extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'permissions:health 
                            {--detailed : Show detailed health information}
                            {--alerts : Show only alerts and warnings}
                            {--export= : Export health report to file}
                            {--days=7 : Number of days to analyze for trends}';

    /**
     * The console command description.
     */
    protected $description = 'Monitor the health and performance of the advanced permission system';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🏥 Advanced Permission System Health Check');
        $this->newLine();

        $healthData = $this->gatherHealthData();
        $alerts = $this->analyzeHealth($healthData);

        if ($this->option('alerts')) {
            return $this->showAlertsOnly($alerts);
        }

        $this->showOverallHealth($healthData, $alerts);

        if ($this->option('detailed')) {
            $this->showDetailedHealth($healthData);
        }

        if ($this->option('export')) {
            $this->exportHealthReport($healthData, $alerts);
        }

        return empty($alerts['critical']) ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Gather health data
     */
    protected function gatherHealthData(): array
    {
        $days = (int) $this->option('days');
        $since = now()->subDays($days);

        return [
            'system' => [
                'total_permissions' => AdvancedPermission::count(),
                'active_permissions' => AdvancedPermission::where('is_active', true)->count(),
                'system_permissions' => AdvancedPermission::where('is_system', true)->count(),
                'unused_permissions' => AdvancedPermission::doesntHave('roles')->count(),
                'total_roles' => AdvancedRole::count(),
                'active_roles' => AdvancedRole::where('is_active', true)->count(),
                'system_roles' => AdvancedRole::where('is_system', true)->count(),
                'total_assignments' => AdvancedUserRole::count(),
                'active_assignments' => AdvancedUserRole::where('is_active', true)->count(),
                'expired_assignments' => AdvancedUserRole::where('expires_at', '<', now())->where('is_active', true)->count(),
            ],
            'performance' => [
                'avg_check_duration' => AdvancedPermissionAudit::where('created_at', '>=', $since)
                    ->whereNotNull('check_duration_ms')
                    ->avg('check_duration_ms'),
                'slow_checks' => AdvancedPermissionAudit::where('created_at', '>=', $since)
                    ->where('check_duration_ms', '>', 1000)
                    ->count(),
                'total_checks' => AdvancedPermissionAudit::where('created_at', '>=', $since)->count(),
                'denied_checks' => AdvancedPermissionAudit::where('created_at', '>=', $since)
                    ->where('was_granted', false)
                    ->count(),
            ],
            'security' => [
                'failed_logins' => AdvancedPermissionAudit::where('created_at', '>=', $since)
                    ->where('event_type', 'login_failed')
                    ->count(),
                'suspicious_ips' => $this->getSuspiciousIPs($since),
                'privilege_escalations' => $this->getPrivilegeEscalations($since),
                'role_changes' => AdvancedPermissionAudit::where('created_at', '>=', $since)
                    ->whereIn('event_type', ['role_assigned', 'role_revoked'])
                    ->count(),
            ],
            'integrity' => [
                'orphaned_role_permissions' => $this->getOrphanedRolePermissions(),
                'orphaned_user_roles' => AdvancedUserRole::whereDoesntHave('role')->count(),
                'circular_hierarchies' => $this->getCircularHierarchies(),
                'invalid_constraints' => $this->getInvalidConstraints(),
            ],
            'usage' => [
                'most_used_permissions' => $this->getMostUsedPermissions(),
                'least_used_roles' => $this->getLeastUsedRoles(),
                'user_distribution' => $this->getUserDistribution(),
                'permission_categories' => $this->getPermissionCategories(),
            ],
        ];
    }

    /**
     * Analyze health data for alerts
     */
    protected function analyzeHealth(array $healthData): array
    {
        $alerts = [
            'critical' => [],
            'warning' => [],
            'info' => [],
        ];

        // Critical alerts
        if ($healthData['integrity']['orphaned_role_permissions'] > 0) {
            $alerts['critical'][] = "Found {$healthData['integrity']['orphaned_role_permissions']} orphaned role-permission relationships";
        }

        if ($healthData['integrity']['circular_hierarchies'] > 0) {
            $alerts['critical'][] = "Found {$healthData['integrity']['circular_hierarchies']} circular role hierarchies";
        }

        if ($healthData['system']['expired_assignments'] > 10) {
            $alerts['critical'][] = "Found {$healthData['system']['expired_assignments']} expired but active role assignments";
        }

        // Warning alerts
        if ($healthData['performance']['avg_check_duration'] > 500) {
            $alerts['warning'][] = "Average permission check duration is high: " . round($healthData['performance']['avg_check_duration'], 2) . "ms";
        }

        if ($healthData['performance']['slow_checks'] > 100) {
            $alerts['warning'][] = "Found {$healthData['performance']['slow_checks']} slow permission checks (>1000ms)";
        }

        if ($healthData['system']['unused_permissions'] > 50) {
            $alerts['warning'][] = "Found {$healthData['system']['unused_permissions']} unused permissions";
        }

        if ($healthData['security']['failed_logins'] > 100) {
            $alerts['warning'][] = "High number of failed logins: {$healthData['security']['failed_logins']}";
        }

        // Info alerts
        if ($healthData['integrity']['orphaned_user_roles'] > 0) {
            $alerts['info'][] = "Found {$healthData['integrity']['orphaned_user_roles']} orphaned user role assignments";
        }

        if ($healthData['security']['role_changes'] > 50) {
            $alerts['info'][] = "High role change activity: {$healthData['security']['role_changes']} changes";
        }

        return $alerts;
    }

    /**
     * Show overall health
     */
    protected function showOverallHealth(array $healthData, array $alerts): void
    {
        $criticalCount = count($alerts['critical']);
        $warningCount = count($alerts['warning']);

        if ($criticalCount > 0) {
            $this->error("🚨 CRITICAL: System has {$criticalCount} critical issues");
        } elseif ($warningCount > 0) {
            $this->warn("⚠️  WARNING: System has {$warningCount} warnings");
        } else {
            $this->info("✅ HEALTHY: System is operating normally");
        }

        $this->newLine();

        // System overview
        $this->info('📊 System Overview:');
        $this->table(['Metric', 'Value'], [
            ['Total Permissions', $healthData['system']['total_permissions']],
            ['Active Permissions', $healthData['system']['active_permissions']],
            ['Total Roles', $healthData['system']['total_roles']],
            ['Active Roles', $healthData['system']['active_roles']],
            ['Active Assignments', $healthData['system']['active_assignments']],
        ]);

        // Performance metrics
        $this->newLine();
        $this->info('⚡ Performance Metrics:');
        $avgDuration = round($healthData['performance']['avg_check_duration'] ?? 0, 2);
        $this->line("  • Average check duration: {$avgDuration}ms");
        $this->line("  • Total checks: {$healthData['performance']['total_checks']}");
        $this->line("  • Denied checks: {$healthData['performance']['denied_checks']}");
        $this->line("  • Slow checks: {$healthData['performance']['slow_checks']}");

        // Show alerts
        if (!empty($alerts['critical'])) {
            $this->newLine();
            $this->error('🚨 Critical Issues:');
            foreach ($alerts['critical'] as $alert) {
                $this->line("  • {$alert}");
            }
        }

        if (!empty($alerts['warning'])) {
            $this->newLine();
            $this->warn('⚠️  Warnings:');
            foreach ($alerts['warning'] as $alert) {
                $this->line("  • {$alert}");
            }
        }
    }

    /**
     * Show detailed health information
     */
    protected function showDetailedHealth(array $healthData): void
    {
        $this->newLine();
        $this->info('🔍 Detailed Health Information:');

        // Security metrics
        $this->newLine();
        $this->info('🔒 Security Metrics:');
        $this->line("  • Failed logins: {$healthData['security']['failed_logins']}");
        $this->line("  • Suspicious IPs: {$healthData['security']['suspicious_ips']}");
        $this->line("  • Role changes: {$healthData['security']['role_changes']}");

        // Integrity checks
        $this->newLine();
        $this->info('🔧 Integrity Checks:');
        $this->line("  • Orphaned role permissions: {$healthData['integrity']['orphaned_role_permissions']}");
        $this->line("  • Orphaned user roles: {$healthData['integrity']['orphaned_user_roles']}");
        $this->line("  • Circular hierarchies: {$healthData['integrity']['circular_hierarchies']}");

        // Usage statistics
        $this->newLine();
        $this->info('📈 Usage Statistics:');
        foreach ($healthData['usage']['permission_categories'] as $category => $count) {
            $this->line("  • {$category}: {$count} permissions");
        }

        // Most used permissions
        $this->newLine();
        $this->info('🔥 Most Used Permissions:');
        foreach ($healthData['usage']['most_used_permissions'] as $permission) {
            $this->line("  • {$permission['name']}: {$permission['usage_count']} roles");
        }
    }

    /**
     * Show alerts only
     */
    protected function showAlertsOnly(array $alerts): int
    {
        $hasAlerts = false;

        if (!empty($alerts['critical'])) {
            $this->error('🚨 CRITICAL ISSUES:');
            foreach ($alerts['critical'] as $alert) {
                $this->line("  • {$alert}");
            }
            $hasAlerts = true;
        }

        if (!empty($alerts['warning'])) {
            if ($hasAlerts) $this->newLine();
            $this->warn('⚠️  WARNINGS:');
            foreach ($alerts['warning'] as $alert) {
                $this->line("  • {$alert}");
            }
            $hasAlerts = true;
        }

        if (!$hasAlerts) {
            $this->info('✅ No alerts - system is healthy');
        }

        return $hasAlerts ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Export health report
     */
    protected function exportHealthReport(array $healthData, array $alerts): void
    {
        $filename = $this->option('export');
        $report = [
            'timestamp' => now()->toISOString(),
            'health_data' => $healthData,
            'alerts' => $alerts,
            'summary' => [
                'status' => empty($alerts['critical']) ? 'healthy' : 'critical',
                'critical_issues' => count($alerts['critical']),
                'warnings' => count($alerts['warning']),
            ],
        ];

        file_put_contents($filename, json_encode($report, JSON_PRETTY_PRINT));
        $this->info("📄 Health report exported to: {$filename}");
    }

    /**
     * Get suspicious IPs
     */
    protected function getSuspiciousIPs(Carbon $since): int
    {
        return AdvancedPermissionAudit::where('created_at', '>=', $since)
            ->where('was_granted', false)
            ->select('ip_address')
            ->groupBy('ip_address')
            ->havingRaw('COUNT(*) > 10')
            ->count();
    }

    /**
     * Get privilege escalations
     */
    protected function getPrivilegeEscalations(Carbon $since): int
    {
        return AdvancedPermissionAudit::where('created_at', '>=', $since)
            ->where('event_type', 'role_assigned')
            ->whereHas('role', function ($query) {
                $query->where('category', 'admin');
            })
            ->count();
    }

    /**
     * Get orphaned role permissions
     */
    protected function getOrphanedRolePermissions(): int
    {
        return DB::table('advanced_role_permissions')
            ->leftJoin('advanced_roles', 'advanced_role_permissions.role_id', '=', 'advanced_roles.id')
            ->leftJoin('advanced_permissions', 'advanced_role_permissions.permission_id', '=', 'advanced_permissions.id')
            ->where(function ($query) {
                $query->whereNull('advanced_roles.id')
                      ->orWhereNull('advanced_permissions.id');
            })
            ->count();
    }

    /**
     * Get circular hierarchies
     */
    protected function getCircularHierarchies(): int
    {
        $roles = AdvancedRole::whereNotNull('parent_role_id')->get();
        $circular = 0;

        foreach ($roles as $role) {
            if ($this->hasCircularHierarchy($role)) {
                $circular++;
            }
        }

        return $circular;
    }

    /**
     * Check for circular hierarchy
     */
    protected function hasCircularHierarchy(AdvancedRole $role): bool
    {
        $visited = [];
        $current = $role;

        while ($current && $current->parent_role_id) {
            if (in_array($current->id, $visited)) {
                return true;
            }
            $visited[] = $current->id;
            $current = $current->parentRole;
        }

        return false;
    }

    /**
     * Get invalid constraints
     */
    protected function getInvalidConstraints(): int
    {
        return DB::table('advanced_role_permissions')
            ->whereNotNull('constraints')
            ->where('constraints', '!=', '{}')
            ->count(); // Simplified - would need actual validation logic
    }

    /**
     * Get most used permissions
     */
    protected function getMostUsedPermissions(): array
    {
        return AdvancedPermission::withCount('roles')
            ->orderByDesc('roles_count')
            ->limit(5)
            ->get(['name', 'roles_count as usage_count'])
            ->toArray();
    }

    /**
     * Get least used roles
     */
    protected function getLeastUsedRoles(): array
    {
        return AdvancedRole::withCount('userRoles')
            ->orderBy('user_roles_count')
            ->limit(5)
            ->get(['name', 'display_name', 'user_roles_count'])
            ->toArray();
    }

    /**
     * Get user distribution
     */
    protected function getUserDistribution(): array
    {
        return AdvancedUserRole::select('user_type', DB::raw('count(distinct user_id) as count'))
            ->where('is_active', true)
            ->groupBy('user_type')
            ->pluck('count', 'user_type')
            ->toArray();
    }

    /**
     * Get permission categories
     */
    protected function getPermissionCategories(): array
    {
        return AdvancedPermission::select('category', DB::raw('count(*) as count'))
            ->groupBy('category')
            ->pluck('count', 'category')
            ->toArray();
    }
}
