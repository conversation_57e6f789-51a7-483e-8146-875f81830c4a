<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operational_costs', function (Blueprint $table) {
            $table->id();
            $table->string('cost_name');
            $table->text('description')->nullable();
            $table->decimal('amount', 18, 8);
            $table->string('currency', 3)->default('NGN');
            $table->string('category')->nullable()->comment('e.g., Office Rent, Utilities, Staff, etc.');
            $table->date('cost_date');
            $table->foreignId('recorded_by')->constrained('admins')->onDelete('cascade');
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable()->comment('File attachments for receipts/invoices');
            $table->timestamps();
            
            $table->index(['cost_date', 'category']);
            $table->index('recorded_by');
            $table->index('category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operational_costs');
    }
};
