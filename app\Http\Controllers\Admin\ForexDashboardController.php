<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Models\ForexTransaction;
use App\Models\ForexRate;
use App\Models\OperationalCost;
use App\Services\ForexDashboardService;
use Illuminate\Http\Request;

class ForexDashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(ForexDashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    public function index()
    {
        $data['pageTitle'] = 'Forex Trading Dashboard';
        $data['dashboardData'] = $this->dashboardService->getDashboardData();
        return view('admin.forex.dashboard.index', $data);
    }

    public function getBalances()
    {
        $balances = $this->dashboardService->getAccountBalances();
        return response()->json($balances);
    }

    public function getBookingStats(Request $request)
    {
        $period = $request->get('period', 'today'); // today, week, month
        $stats = $this->dashboardService->getBookingStats($period);
        return response()->json($stats);
    }

    public function getTransactionChart(Request $request)
    {
        $period = $request->get('period', 'week'); // week, month, year
        $chartData = $this->dashboardService->getTransactionChartData($period);
        return response()->json($chartData);
    }

    public function getRevenueChart(Request $request)
    {
        $period = $request->get('period', 'month'); // month, quarter, year
        $chartData = $this->dashboardService->getRevenueChartData($period);
        return response()->json($chartData);
    }

    public function getRecentActivity()
    {
        $activity = $this->dashboardService->getRecentActivity();
        return response()->json($activity);
    }

    public function getTopClients(Request $request)
    {
        $period = $request->get('period', 'month');
        $limit = $request->get('limit', 10);
        $clients = $this->dashboardService->getTopClients($period, $limit);
        return response()->json($clients);
    }

    public function getExchangeRateHistory(Request $request)
    {
        $days = $request->get('days', 30);
        $history = $this->dashboardService->getExchangeRateHistory($days);
        return response()->json($history);
    }

    public function getOperationalCostSummary(Request $request)
    {
        $period = $request->get('period', 'month');
        $summary = $this->dashboardService->getOperationalCostSummary($period);
        return response()->json($summary);
    }

    public function refreshData()
    {
        try {
            $this->dashboardService->refreshCachedData();
            return response()->json([
                'success' => true,
                'message' => 'Dashboard data refreshed successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh data: ' . $e->getMessage()
            ], 500);
        }
    }
}
