<?php

require 'vendor/autoload.php';

use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Models\ForexTransaction;
use App\Services\ForexBookingService;
use App\Services\ForexWalletService;

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== TESTING DOUBLE PROCESSING FIX ===\n\n";

// Get services
$walletService = new ForexWalletService();
$bookingService = new ForexBookingService($walletService);

// Get USD account for testing
$usdAccount = ForexAccount::byType('USD')->first();
if (!$usdAccount) {
    echo "❌ No USD account found\n";
    exit;
}

echo "Initial USD Account Balance: $" . number_format($usdAccount->balance, 2) . "\n\n";

// Test data for buying USD (NGN to USD)
$bookingData = [
    'client_name' => 'Test Client - Double Processing Fix',
    'client_email' => '<EMAIL>',
    'client_phone' => '**********',
    'client_type' => 'external',
    'transaction_type' => 'buying',
    'currency' => 'USD',
    'amount' => 100,
    'target_account_id' => $usdAccount->id,
    'account_details' => 'Test account details',
    'payment_instructions' => 'Test payment instructions'
];

try {
    echo "=== TEST 1: BOOKING CREATION ===\n";

    // Record initial balance
    $initialBalance = $usdAccount->balance;
    echo "Balance before booking: $" . number_format($initialBalance, 2) . "\n";

    // Create booking
    $booking = $bookingService->createBooking($bookingData, 1);
    echo "✅ Booking created: {$booking->booking_reference}\n";

    // Check balance after booking creation
    $usdAccount->refresh();
    $afterBookingBalance = $usdAccount->balance;
    $bookingDeduction = $initialBalance - $afterBookingBalance;

    echo "Balance after booking: $" . number_format($afterBookingBalance, 2) . "\n";
    echo "Amount deducted: $" . number_format($bookingDeduction, 2) . "\n";
    echo "Expected deduction: $" . number_format($booking->amount, 2) . "\n";

    if (abs($bookingDeduction - $booking->amount) < 0.01) {
        echo "✅ Correct deduction amount\n";
    } else {
        echo "❌ Incorrect deduction amount - possible double debit!\n";
    }

    // Check transactions created
    $bookingTransactions = ForexTransaction::where('forex_booking_id', $booking->id)->get();
    echo "Transactions created: " . $bookingTransactions->count() . "\n";
    foreach ($bookingTransactions as $transaction) {
        echo "- {$transaction->transaction_type}: $" . number_format($transaction->amount, 2) .
             " ({$transaction->transaction_subtype}) - {$transaction->description}\n";
    }

    echo "\n=== TEST 2: BOOKING COMPLETION ===\n";

    // Complete the booking
    $bookingService->completeBooking($booking, 1, 'Test completion');
    echo "✅ Booking completed\n";

    // Check balance after completion
    $usdAccount->refresh();
    $afterCompletionBalance = $usdAccount->balance;
    $completionChange = $afterCompletionBalance - $afterBookingBalance;

    echo "Balance after completion: $" . number_format($afterCompletionBalance, 2) . "\n";
    echo "Balance change during completion: $" . number_format($completionChange, 2) . "\n";

    if (abs($completionChange) < 0.01) {
        echo "✅ No additional balance change during completion (correct)\n";
    } else {
        echo "❌ Balance changed during completion - possible double processing!\n";
    }

    // Check all transactions
    $allTransactions = ForexTransaction::where('forex_booking_id', $booking->id)->get();
    echo "Total transactions after completion: " . $allTransactions->count() . "\n";
    foreach ($allTransactions as $transaction) {
        echo "- {$transaction->transaction_type}: $" . number_format($transaction->amount, 2) .
             " ({$transaction->transaction_subtype}) - {$transaction->description}\n";
    }

    echo "\n=== TEST 3: BOOKING CANCELLATION ===\n";

    // Create another booking to test cancellation
    $bookingData['client_name'] = 'Test Client - Cancellation';
    $bookingData['amount'] = 50;

    $balanceBeforeSecondBooking = $usdAccount->balance;
    $booking2 = $bookingService->createBooking($bookingData, 1);
    echo "✅ Second booking created: {$booking2->booking_reference}\n";

    $usdAccount->refresh();
    $balanceAfterSecondBooking = $usdAccount->balance;
    $secondBookingDeduction = $balanceBeforeSecondBooking - $balanceAfterSecondBooking;

    echo "Balance before second booking: $" . number_format($balanceBeforeSecondBooking, 2) . "\n";
    echo "Balance after second booking: $" . number_format($balanceAfterSecondBooking, 2) . "\n";
    echo "Second booking deduction: $" . number_format($secondBookingDeduction, 2) . "\n";

    // Cancel the booking
    $bookingService->cancelBooking($booking2, 1, 'Test cancellation');
    echo "✅ Booking cancelled\n";

    // Check balance after cancellation
    $usdAccount->refresh();
    $balanceAfterCancellation = $usdAccount->balance;
    $cancellationRefund = $balanceAfterCancellation - $balanceAfterSecondBooking;

    echo "Balance after cancellation: $" . number_format($balanceAfterCancellation, 2) . "\n";
    echo "Refund amount: $" . number_format($cancellationRefund, 2) . "\n";
    echo "Expected refund: $" . number_format($booking2->amount, 2) . "\n";

    if (abs($cancellationRefund - $booking2->amount) < 0.01) {
        echo "✅ Correct refund amount\n";
    } else {
        echo "❌ Incorrect refund amount - possible double credit!\n";
    }

    // Check if we're back to the original balance (after first booking)
    $finalBalance = $balanceAfterCancellation;
    $totalChange = $finalBalance - $initialBalance;
    $expectedChange = -$booking->amount; // Only first booking should remain

    echo "\nFinal balance: $" . number_format($finalBalance, 2) . "\n";
    echo "Total change from start: $" . number_format($totalChange, 2) . "\n";
    echo "Expected total change: $" . number_format($expectedChange, 2) . "\n";

    if (abs($totalChange - $expectedChange) < 0.01) {
        echo "✅ Final balance is correct - no double processing detected!\n";
    } else {
        echo "❌ Final balance is incorrect - double processing may still exist!\n";
    }

    // Check cancellation transactions
    $cancellationTransactions = ForexTransaction::where('forex_booking_id', $booking2->id)->get();
    echo "\nCancellation transactions: " . $cancellationTransactions->count() . "\n";
    foreach ($cancellationTransactions as $transaction) {
        echo "- {$transaction->transaction_type}: $" . number_format($transaction->amount, 2) .
             " ({$transaction->transaction_subtype}) - {$transaction->description}\n";
    }

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
