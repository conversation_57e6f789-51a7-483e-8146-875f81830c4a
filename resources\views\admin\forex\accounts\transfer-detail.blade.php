@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.transfer.history') }}">
                                    @lang('Transfer History')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $transfer->transfer_reference }}</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Transfer Details')</h1>
                    <p class="page-header-text">{{ $transfer->transfer_reference }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="d-flex gap-2">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.transfer.history') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Transfers')
                        </a>
                        <a class="btn btn-outline-primary" href="{{ route('admin.forex.accounts.transfer') }}">
                            <i class="bi-plus-circle me-1"></i> @lang('New Transfer')
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row">
            <div class="col-lg-8 mb-3 mb-lg-5">
                <!-- Transfer Details -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transfer Information')</h4>
                        <span class="badge bg-{{ $transfer->status_class }}">
                            {{ ucfirst($transfer->status) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-5">@lang('Reference'):</dt>
                                    <dd class="col-sm-7">
                                        <code>{{ $transfer->transfer_reference }}</code>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Status'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="badge bg-{{ $transfer->status_class }}">
                                            {{ ucfirst($transfer->status) }}
                                        </span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Amount'):</dt>
                                    <dd class="col-sm-7">
                                        <span class="fw-bold fs-4">{{ $transfer->formatted_amount }}</span>
                                    </dd>

                                    <dt class="col-sm-5">@lang('Currency'):</dt>
                                    <dd class="col-sm-7">{{ $transfer->currency }}</dd>

                                    <dt class="col-sm-5">@lang('Date'):</dt>
                                    <dd class="col-sm-7">{{ $transfer->created_at->format('M d, Y H:i:s') }}</dd>
                                </dl>
                            </div>
                            <div class="col-sm-6">
                                <dl class="row">
                                    <dt class="col-sm-6">@lang('From Account'):</dt>
                                    <dd class="col-sm-6">
                                        <a href="{{ route('admin.forex.accounts.show', $transfer->fromAccount->id) }}">
                                            {{ $transfer->fromAccount->account_name }}
                                        </a>
                                    </dd>

                                    <dt class="col-sm-6">@lang('To Account'):</dt>
                                    <dd class="col-sm-6">
                                        <a href="{{ route('admin.forex.accounts.show', $transfer->toAccount->id) }}">
                                            {{ $transfer->toAccount->account_name }}
                                        </a>
                                    </dd>

                                    <dt class="col-sm-6">@lang('Created By'):</dt>
                                    <dd class="col-sm-6">{{ $transfer->createdBy->name ?? 'System' }}</dd>
                                </dl>
                            </div>
                        </div>

                        @if($transfer->description)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Description')</h6>
                                <p class="text-muted">{{ $transfer->description }}</p>
                            </div>
                        @endif

                        @if($transfer->notes)
                            <div class="mt-3 pt-3 border-top">
                                <h6>@lang('Notes')</h6>
                                <p class="text-muted">{{ $transfer->notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- End Transfer Details -->

                @if($relatedTransactions->count() > 0)
                    <!-- Related Transactions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h4 class="card-header-title">@lang('Related Transactions')</h4>
                            <small class="text-muted">@lang('Transactions created by this transfer')</small>
                        </div>
                        <div class="card-body">
                            @foreach($relatedTransactions as $transaction)
                                <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2">
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="{{ route('admin.forex.accounts.transaction.show', [$transaction->forex_account_id, $transaction->id]) }}">
                                                {{ $transaction->transaction_reference }}
                                            </a>
                                        </h6>
                                        <p class="text-muted mb-0">{{ $transaction->description }}</p>
                                        <small class="text-muted">
                                            {{ $transaction->forexAccount->account_name }} •
                                            {{ $transaction->created_at->format('M d, Y H:i') }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ $transaction->type_class }}">
                                            {{ ucfirst(str_replace('_', ' ', $transaction->transaction_type)) }}
                                        </span>
                                        <div class="fw-bold">{{ $transaction->formatted_amount }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- End Related Transactions -->
                @endif
            </div>

            <div class="col-lg-4">
                <!-- Account Balances -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Balances')</h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6>@lang('From Account')</h6>
                            <div class="d-flex justify-content-between">
                                <span>{{ $transfer->fromAccount->account_name }}</span>
                                <span class="fw-bold">{{ $transfer->fromAccount->formatted_balance }}</span>
                            </div>
                            <div class="d-flex justify-content-between text-muted">
                                <small>@lang('Available')</small>
                                <small>{{ $transfer->fromAccount->formatted_balance }}</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>@lang('To Account')</h6>
                            <div class="d-flex justify-content-between">
                                <span>{{ $transfer->toAccount->account_name }}</span>
                                <span class="fw-bold">{{ $transfer->toAccount->formatted_balance }}</span>
                            </div>
                            <div class="d-flex justify-content-between text-muted">
                                <small>@lang('Available')</small>
                                <small>{{ $transfer->toAccount->formatted_balance }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Account Balances -->

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Quick Actions')</h4>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.forex.accounts.show', $transfer->fromAccount->id) }}" class="btn btn-outline-primary">
                                <i class="bi-bank me-1"></i> @lang('View From Account')
                            </a>
                            <a href="{{ route('admin.forex.accounts.show', $transfer->toAccount->id) }}" class="btn btn-outline-secondary">
                                <i class="bi-bank me-1"></i> @lang('View To Account')
                            </a>
                            <a href="{{ route('admin.forex.accounts.transfer.history') }}" class="btn btn-outline-info">
                                <i class="bi-list-ul me-1"></i> @lang('All Transfers')
                            </a>
                            <a href="{{ route('admin.forex.accounts.transfer') }}" class="btn btn-outline-success">
                                <i class="bi-plus-circle me-1"></i> @lang('New Transfer')
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Quick Actions -->

                <!-- Transfer Timeline -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transfer Timeline')</h4>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">@lang('Transfer Created')</h6>
                                    <p class="timeline-text">{{ $transfer->created_at->format('M d, Y H:i:s') }}</p>
                                    <small class="text-muted">@lang('By') {{ $transfer->createdBy->name ?? 'System' }}</small>
                                </div>
                            </div>

                            @if($transfer->status === 'completed')
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">@lang('Transfer Completed')</h6>
                                        <p class="timeline-text">{{ $transfer->updated_at->format('M d, Y H:i:s') }}</p>
                                        <small class="text-muted">@lang('Funds successfully transferred')</small>
                                    </div>
                                </div>
                            @elseif($transfer->status === 'failed')
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-danger"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">@lang('Transfer Failed')</h6>
                                        <p class="timeline-text">{{ $transfer->updated_at->format('M d, Y H:i:s') }}</p>
                                        <small class="text-muted">@lang('Transfer could not be completed')</small>
                                    </div>
                                </div>
                            @else
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">@lang('Transfer Pending')</h6>
                                        <p class="timeline-text">@lang('Awaiting processing')</p>
                                        <small class="text-muted">@lang('Transfer is being processed')</small>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- End Transfer Timeline -->
            </div>
        </div>
    </div>
@endsection

@push('css')
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        .timeline-content {
            padding-left: 15px;
        }

        .timeline-title {
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 600;
        }

        .timeline-text {
            margin-bottom: 5px;
            font-size: 13px;
            color: #6c757d;
        }
    </style>
@endpush
