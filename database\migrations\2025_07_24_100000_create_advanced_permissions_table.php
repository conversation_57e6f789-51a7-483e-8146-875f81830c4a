<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the advanced_permissions table for storing granular permissions.
     * Each permission represents a specific action on a resource (e.g., users.create, transactions.read).
     */
    public function up(): void
    {
        Schema::create('advanced_permissions', function (Blueprint $table) {
            $table->id();
            
            // Permission identification
            $table->string('name', 100)->unique()->comment('Unique permission name (e.g., users.create)');
            $table->string('display_name', 150)->comment('Human-readable permission name');
            $table->text('description')->nullable()->comment('Detailed description of what this permission allows');
            
            // Permission categorization
            $table->string('resource', 50)->index()->comment('Resource/module this permission belongs to (e.g., users, transactions)');
            $table->string('action', 20)->index()->comment('Action type: create, read, update, delete, or custom');
            $table->string('category', 50)->nullable()->index()->comment('Permission category for grouping (e.g., admin, user, finance)');
            
            // Permission metadata
            $table->json('metadata')->nullable()->comment('Additional permission metadata (constraints, conditions, etc.)');
            $table->integer('sort_order')->default(0)->comment('Display order for UI');
            
            // Status and control
            $table->boolean('is_system')->default(false)->comment('System permission that cannot be deleted');
            $table->boolean('is_active')->default(true)->comment('Whether permission is active and can be assigned');
            
            // Audit fields
            $table->unsignedBigInteger('created_by')->nullable()->comment('Admin who created this permission');
            $table->unsignedBigInteger('updated_by')->nullable()->comment('Admin who last updated this permission');
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['resource', 'action']);
            $table->index(['category', 'is_active']);
            $table->index(['is_system', 'is_active']);
            
            // Foreign key constraints
            $table->foreign('created_by')->references('id')->on('admins')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('admins')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advanced_permissions');
    }
};
