@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-shield me-2"></i>
                        Advanced Roles Management
                    </h4>
                    @canAdvanced('advanced_roles.create')
                        <div class="btn-group">
                            <a href="{{ route('admin.advanced-roles.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Create Role
                            </a>
                            <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                                <span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.advanced-roles.create', ['template' => 'admin']) }}">
                                    <i class="fas fa-crown me-1"></i> From Admin Template
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.advanced-roles.create', ['template' => 'finance_manager']) }}">
                                    <i class="fas fa-chart-line me-1"></i> From Finance Template
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.advanced-roles.create', ['template' => 'user_manager']) }}">
                                    <i class="fas fa-users me-1"></i> From User Manager Template
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ route('admin.advanced-roles.create') }}">
                                    <i class="fas fa-cog me-1"></i> Custom Role
                                </a></li>
                            </ul>
                        </div>
                    @endcanAdvanced
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">Search</label>
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Search roles..." value="{{ request('search') }}">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Category</label>
                                    <select name="category" class="form-select">
                                        <option value="">All Categories</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                                                {{ ucwords(str_replace('_', ' ', $category)) }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="">All Status</option>
                                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                        <option value="system" {{ request('status') == 'system' ? 'selected' : '' }}>System</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Parent Role</label>
                                    <select name="parent_role" class="form-select">
                                        <option value="">All Roles</option>
                                        @foreach($parentRoles as $id => $name)
                                            <option value="{{ $id }}" {{ request('parent_role') == $id ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-search me-1"></i> Filter
                                        </button>
                                        <a href="{{ route('admin.advanced-roles.index') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Roles Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc']) }}" 
                                           class="text-white text-decoration-none">
                                            Role Name
                                            @if(request('sort_by') == 'name')
                                                <i class="fas fa-sort-{{ request('sort_order') == 'asc' ? 'up' : 'down' }} ms-1"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th>Category</th>
                                    <th>Hierarchy</th>
                                    <th>Permissions</th>
                                    <th>Users</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($roles as $role)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($role->color)
                                                    <div class="role-color-indicator me-2" 
                                                         style="width: 12px; height: 12px; background-color: {{ $role->color }}; border-radius: 50%;"></div>
                                                @endif
                                                <div>
                                                    <strong>{{ $role->display_name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $role->name }}</small>
                                                    @if($role->is_system)
                                                        <span class="badge bg-info ms-1">System</span>
                                                    @endif
                                                    @if($role->is_default)
                                                        <span class="badge bg-success ms-1">Default</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($role->category)
                                                <span class="badge bg-secondary">
                                                    {{ ucwords(str_replace('_', ' ', $role->category)) }}
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="hierarchy-display">
                                                @for($i = 0; $i < $role->level; $i++)
                                                    <span class="text-muted">└─</span>
                                                @endfor
                                                @if($role->parentRole)
                                                    <small class="text-muted">
                                                        Child of: {{ $role->parentRole->display_name }}
                                                    </small>
                                                @else
                                                    <small class="text-muted">Top Level</small>
                                                @endif
                                                @if($role->child_roles_count > 0)
                                                    <br>
                                                    <small class="text-info">
                                                        {{ $role->child_roles_count }} child role(s)
                                                    </small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">
                                                {{ $role->permissions_count }} permissions
                                            </span>
                                            @if($role->inherit_permissions && $role->parentRole)
                                                <br>
                                                <small class="text-success">
                                                    <i class="fas fa-arrow-down me-1"></i>
                                                    Inherits from parent
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                {{ $role->user_roles_count }} users
                                            </span>
                                            @if($role->max_users)
                                                <br>
                                                <small class="text-muted">
                                                    Max: {{ $role->max_users }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            {!! $role->getStatusBadge() !!}
                                            @if($role->expires_at)
                                                <br>
                                                <small class="text-warning">
                                                    Expires: {{ $role->expires_at->format('M j, Y') }}
                                                </small>
                                            @endif
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ $role->created_at->format('M j, Y') }}
                                                @if($role->creator)
                                                    <br>by {{ $role->creator->name }}
                                                @endif
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                @canAdvanced('advanced_roles.read')
                                                    <a href="{{ route('admin.advanced-roles.show', $role) }}" 
                                                       class="btn btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcanAdvanced
                                                @canAdvanced('advanced_roles.update')
                                                    <a href="{{ route('admin.advanced-roles.edit', $role) }}" 
                                                       class="btn btn-outline-primary" title="Edit Role">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endcanAdvanced
                                                @canAdvanced('advanced_roles.delete')
                                                    @if(!$role->is_system && $role->user_roles_count == 0)
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="confirmDelete('{{ $role->id }}', '{{ $role->display_name }}')" 
                                                                title="Delete Role">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif
                                                @endcanAdvanced
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-user-shield fa-3x mb-3"></i>
                                                <p>No roles found matching your criteria.</p>
                                                @canAdvanced('advanced_roles.create')
                                                    <a href="{{ route('admin.advanced-roles.create') }}" class="btn btn-primary">
                                                        Create Your First Role
                                                    </a>
                                                @endcanAdvanced
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($roles->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $roles->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the role <strong id="roleNameToDelete"></strong>?</p>
                    <p class="text-danger">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Role</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
function confirmDelete(roleId, roleName) {
    document.getElementById('roleNameToDelete').textContent = roleName;
    document.getElementById('deleteForm').action = `/admin/advanced-roles/${roleId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush

@push('style')
<style>
.role-color-indicator {
    border: 1px solid #dee2e6;
}

.hierarchy-display {
    font-family: monospace;
}

.table th a {
    color: inherit;
}

.table th a:hover {
    color: #fff !important;
}
</style>
@endpush
