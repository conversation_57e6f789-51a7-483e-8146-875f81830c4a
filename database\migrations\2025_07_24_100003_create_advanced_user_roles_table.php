<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Creates the advanced_user_roles table for user-role assignments.
     * Supports multiple roles per user and polymorphic relationships.
     */
    public function up(): void
    {
        Schema::create('advanced_user_roles', function (Blueprint $table) {
            $table->id();
            
            // Polymorphic user relationship (supports both users and admins)
            $table->unsignedBigInteger('user_id')->comment('User/Admin ID');
            $table->string('user_type', 50)->comment('User model type (App\\Models\\User or App\\Models\\Admin)');
            $table->unsignedBigInteger('role_id')->comment('Reference to advanced_roles table');
            
            // Assignment metadata
            $table->boolean('is_active')->default(true)->comment('Whether role assignment is active');
            $table->integer('priority')->default(0)->comment('Role priority for conflict resolution');
            
            // Temporal role assignment
            $table->timestamp('assigned_at')->nullable()->comment('When role was assigned');
            $table->timestamp('expires_at')->nullable()->comment('When role assignment expires');
            $table->json('schedule')->nullable()->comment('Time-based schedule for role (days, hours)');
            
            // Assignment context
            $table->string('context', 100)->nullable()->comment('Assignment context (project, department, etc.)');
            $table->json('context_data')->nullable()->comment('Additional context data');
            
            // Audit fields
            $table->unsignedBigInteger('assigned_by')->nullable()->comment('Admin who assigned this role');
            $table->text('assignment_reason')->nullable()->comment('Reason for role assignment');
            $table->unsignedBigInteger('revoked_by')->nullable()->comment('Admin who revoked this role');
            $table->timestamp('revoked_at')->nullable()->comment('When role was revoked');
            $table->text('revocation_reason')->nullable()->comment('Reason for role revocation');
            $table->timestamps();
            
            // Unique constraint to prevent duplicate user-role assignments
            $table->unique(['user_id', 'user_type', 'role_id', 'context'], 'unique_user_role_context');
            
            // Indexes for performance
            $table->index(['user_id', 'user_type', 'is_active']);
            $table->index(['role_id', 'is_active']);
            $table->index(['assigned_at', 'expires_at']);
            $table->index(['context', 'is_active']);
            $table->index(['assigned_by', 'assigned_at']);
            
            // Foreign key constraints
            $table->foreign('role_id')->references('id')->on('advanced_roles')->onDelete('cascade');
            $table->foreign('assigned_by')->references('id')->on('admins')->onDelete('set null');
            $table->foreign('revoked_by')->references('id')->on('admins')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('advanced_user_roles');
    }
};
