<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_bookings', function (Blueprint $table) {
            $table->id();
            $table->string('booking_reference')->unique();
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->string('client_name');
            $table->string('client_email');
            $table->string('client_phone')->nullable();
            $table->enum('client_type', ['user', 'merchant', 'external'])->default('external');
            $table->enum('transaction_type', ['credit', 'debit'])->comment('Credit = funding, Debit = exchange');
            $table->string('currency', 3)->comment('USD or NGN');
            $table->decimal('amount', 18, 8);
            $table->decimal('cbn_rate', 18, 8);
            $table->decimal('parallel_rate', 18, 8);
            $table->decimal('cbn_total', 18, 8)->comment('Amount * CBN Rate');
            $table->decimal('parallel_total', 18, 8)->comment('Amount * Parallel Rate');
            $table->decimal('difference_amount', 18, 8)->comment('Parallel Total - CBN Total');
            $table->foreignId('target_account_id')->constrained('forex_accounts')->onDelete('cascade');
            $table->text('account_details')->nullable()->comment('Payment account details');
            $table->enum('status', ['pending', 'completed', 'cancelled'])->default('pending');
            $table->text('status_notes')->nullable();
            $table->foreignId('initiated_by')->constrained('admins')->onDelete('cascade');
            $table->foreignId('completed_by')->nullable()->constrained('admins')->onDelete('set null');
            $table->timestamp('completed_at')->nullable();
            $table->text('payment_instructions')->nullable();
            $table->boolean('email_sent')->default(false);
            $table->timestamps();
            
            $table->index(['status', 'created_at']);
            $table->index(['client_type', 'status']);
            $table->index('user_id');
            $table->index('target_account_id');
            $table->index('initiated_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_bookings');
    }
};
