<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class ForexRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'rate_type',
        'cbn_rate',
        'parallel_rate',
        'markup_percentage',
        'cbn_sell_rate',
        'parallel_sell_rate',
        'sell_markup_percentage',
        'is_active',
        'status',
        'approval_notes',
        'created_by',
        'approved_by',
        'approved_at'
    ];

    protected $casts = [
        'cbn_rate' => 'decimal:8',
        'parallel_rate' => 'decimal:8',
        'markup_percentage' => 'decimal:2',
        'cbn_sell_rate' => 'decimal:8',
        'parallel_sell_rate' => 'decimal:8',
        'sell_markup_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'approved_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('active_forex_rate');
            Cache::forget('forex_dashboard_data');
        });

        static::updating(function (ForexRate $rate) {
            // Ensure only one rate can be active at a time
            if ($rate->is_active && $rate->isDirty('is_active')) {
                static::where('id', '!=', $rate->id)->update(['is_active' => false]);
            }
        });
    }

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(Admin::class, 'approved_by');
    }

    public function bookings()
    {
        return $this->hasMany(ForexBooking::class, 'forex_rate_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'approved');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Accessors & Mutators
    public function getStatusClassAttribute()
    {
        return [
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
        ][$this->status] ?? 'secondary';
    }

    public function getFormattedCbnRateAttribute()
    {
        return number_format($this->cbn_rate, 2);
    }

    public function getFormattedParallelRateAttribute()
    {
        return number_format($this->parallel_rate, 2);
    }

    public function getFormattedCbnSellRateAttribute()
    {
        return number_format($this->cbn_sell_rate, 2);
    }

    public function getFormattedParallelSellRateAttribute()
    {
        return number_format($this->parallel_sell_rate, 2);
    }

    // Business Logic Methods
    public static function getActiveRate()
    {
        return Cache::remember('active_forex_rate', 3600, function () {
            return static::active()->first();
        });
    }

    public function calculateCbnTotal($amount)
    {
        return $amount * $this->cbn_rate;
    }

    public function calculateParallelTotal($amount)
    {
        return $amount * $this->parallel_rate;
    }

    public function calculateDifference($amount)
    {
        return $this->calculateParallelTotal($amount) - $this->calculateCbnTotal($amount);
    }

    public function calculateCbnSellTotal($amount)
    {
        return $amount * $this->cbn_sell_rate;
    }

    public function calculateParallelSellTotal($amount)
    {
        return $amount * $this->parallel_sell_rate;
    }

    public function calculateSellDifference($amount)
    {
        return $this->calculateParallelSellTotal($amount) - $this->calculateCbnSellTotal($amount);
    }

    public function approve($adminId, $notes = null)
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $adminId,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }

    public function reject($adminId, $notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'approved_by' => $adminId,
            'approved_at' => now(),
            'approval_notes' => $notes,
        ]);
    }
}
