<?php

namespace App\Models;

use App\Traits\RandomCode;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DisputeDetails extends Model
{
    use HasFactory, RandomCode;

    protected $casts = [
        'files' => 'object'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'admin_id', 'id');
    }
}
