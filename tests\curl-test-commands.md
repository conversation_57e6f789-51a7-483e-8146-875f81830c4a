# cURL Test Commands for J<PERSON><PERSON> Parsing

## Test 1: Debug Endpoint with <PERSON><PERSON>

```bash
curl --location 'http://currency.test/api/debug-request' \
--header 'Content-Type: application/json' \
--data '{
    "publicKey": "12345",
    "secretKey": "2d05d381bb3cf03fb6c496d5f0ba05a369c956b5",
    "testField": "test_value"
}'
```

**Expected Result:** `request_all` and `request_json_all` should contain the sent data.

## Test 2: Debug Endpoint with Simple JSON

```bash
curl --location 'http://currency.test/api/debug-request' \
--header 'Content-Type: application/json' \
--data '{
    "test": "value",
    "number": 123
}'
```

**Expected Result:** Should show the test data in the response.

## Test 3: Authenticate Endpoint with Valid JSON

```bash
curl --location 'http://currency.test/api/authenticate' \
--header 'Content-Type: application/json' \
--data '{
    "publicKey": "12345",
    "secretKey": "2d05d381bb3cf03fb6c496d5f0ba05a369c956b5"
}'
```

**Expected Result:** Should get authentication error (not validation error about missing fields).

## Test 4: Your Original Request (Fixed JSON)

The issue with your original request was missing quotes around the secretKey value:

**❌ Original (Malformed JSON):**
```json
{
    "publicKey": "12345",
    "secretKey": 2d05d381bb3cf03fb6c496d5f0ba05a369c956b5
}
```

**✅ Fixed (Valid JSON):**
```json
{
    "publicKey": "12345",
    "secretKey": "2d05d381bb3cf03fb6c496d5f0ba05a369c956b5"
}
```

**Fixed cURL Command:**
```bash
curl --location 'http://currency.test/api/debug-request' \
--header 'Content-Type: application/json' \
--data '{
    "publicKey": "12345",
    "secretKey": "2d05d381bb3cf03fb6c496d5f0ba05a369c956b5"
}'
```

## Test 5: Malformed JSON (Should Fail)

```bash
curl --location 'http://currency.test/api/debug-request' \
--header 'Content-Type: application/json' \
--data '{
    "publicKey": "12345",
    "secretKey": 2d05d381bb3cf03fb6c496d5f0ba05a369c956b5
}'
```

**Expected Result:** Should show JSON syntax error.

## Analyzing Results

### ✅ Success Indicators:
- `request_all` contains your sent data
- `request_json_all` contains your sent data  
- `json_error` shows "No error"
- `decoded_json` contains your data

### ❌ Failure Indicators:
- `request_all` is empty `[]`
- `request_json_all` is empty `[]`
- `json_error` shows syntax error for malformed JSON
- `decoded_json` is `null` for valid JSON

### 🔧 Troubleshooting:

1. **If valid JSON still returns empty arrays:**
   - Laravel configuration issue
   - Middleware interference
   - Missing required middleware

2. **If you get JSON syntax errors:**
   - Fix the JSON format (add missing quotes)
   - Validate JSON with online tools

3. **If authentication endpoint works after debug shows success:**
   - JSON parsing is fixed
   - Remove debug endpoint for production

## Quick Test Script

Run this to test all scenarios:
```bash
php tests/test-json-debug.php
```
