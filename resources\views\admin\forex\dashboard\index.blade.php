@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">@lang('Forex Trading Dashboard')</h1>
                    <p class="page-header-text">@lang('Monitor your forex trading operations and analytics')</p>
                </div>
                <div class="col-sm-auto">
                    <button type="button" class="btn btn-primary" id="refreshDashboard">
                        <i class="bi-arrow-clockwise me-1"></i> @lang('Refresh Data')
                    </button>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Stats Cards -->
        <div class="row">
            <!-- Account Balances -->
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('USD Account')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark"
                                      data-hs-counter-options='{"delimiter": ","}'>
                                    {{ number_format($dashboardData['account_balances']['USD']['balance'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">USD</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi-graph-up"></i> @lang('Available')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            @lang('Pending'): {{ number_format($dashboardData['account_balances']['USD']['pending_balance'] ?? 0, 2) }} USD
                        </span>
                    </div>
                </div>
            </div>

            <!-- NGN Total -->
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('NGN Total')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark"
                                      data-hs-counter-options='{"delimiter": ","}'>
                                    {{ number_format($dashboardData['account_balances']['totals']['ngn_total'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">NGN</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-info text-info">
                                    <i class="bi-bank"></i> @lang('Combined')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            @lang('Pending'): {{ number_format($dashboardData['account_balances']['totals']['ngn_pending'] ?? 0, 2) }} NGN
                        </span>
                    </div>
                </div>
            </div>

            <!-- Today's Bookings -->
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Today\'s Bookings')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark">
                                    {{ $dashboardData['booking_stats']['total_bookings'] ?? 0 }}
                                </span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-primary text-primary">
                                    <i class="bi-calendar-day"></i> @lang('Today')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            @lang('Completed'): {{ $dashboardData['booking_stats']['completed_bookings'] ?? 0 }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Weekly Revenue -->
            <div class="col-sm-6 col-lg-3 mb-3 mb-lg-5">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2">@lang('Weekly Revenue')</h6>
                        <div class="row align-items-center gx-2">
                            <div class="col">
                                <span class="js-counter display-4 text-dark"
                                      data-hs-counter-options='{"delimiter": ","}'>
                                    {{ number_format($dashboardData['weekly_stats']['total_revenue'] ?? 0, 2) }}
                                </span>
                                <span class="text-body fs-5 ms-1">NGN</span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-soft-success text-success">
                                    <i class="bi-graph-up"></i> @lang('Revenue')
                                </span>
                            </div>
                        </div>
                        <span class="d-block fs-6">
                            @lang('Costs'): {{ number_format($dashboardData['operational_costs']['total_costs'] ?? 0, 2) }} NGN
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Stats Cards -->

        <!-- Account Balances Detail -->
        <div class="row">
            <div class="col-lg-8 mb-3 mb-lg-5">
                <!-- Card -->
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Account Balances')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach(['USD', 'CBN', 'Difference', 'Investment'] as $accountType)
                                @if(isset($dashboardData['account_balances'][$accountType]))
                                    @php $account = $dashboardData['account_balances'][$accountType] @endphp
                                    <div class="col-sm-6 col-lg-3 mb-4">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <div class="avatar avatar-sm avatar-circle">
                                                    <span class="avatar-initials bg-soft-{{ $accountType === 'USD' ? 'primary' : ($accountType === 'CBN' ? 'success' : ($accountType === 'Difference' ? 'warning' : 'info')) }} text-{{ $accountType === 'USD' ? 'primary' : ($accountType === 'CBN' ? 'success' : ($accountType === 'Difference' ? 'warning' : 'info')) }}">
                                                        {{ substr($accountType, 0, 2) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h5 class="text-inherit mb-0">{{ $account['formatted_balance'] }}</h5>
                                                <span class="d-block fs-6">{{ $account['name'] }}</span>
                                                @if($account['pending_balance'] > 0)
                                                    <small class="text-muted">
                                                        @lang('Pending'): {{ number_format($account['pending_balance'], 2) }} {{ $account['currency'] }}
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
                <!-- End Card -->
            </div>

            <div class="col-lg-4 mb-3 mb-lg-5">
                <!-- Card -->
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Current Exchange Rate')</h4>
                    </div>
                    <div class="card-body">
                        @if($dashboardData['active_rate'])
                            <div class="text-center">
                                <div class="mb-3">
                                    <span class="badge bg-success">@lang('Active Rate')</span>
                                </div>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <h6 class="text-cap">@lang('Buy Rates (NGN to USD)')</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">@lang('CBN')</small>
                                                <div class="h5">₦{{ number_format($dashboardData['active_rate']->cbn_rate, 2) }}</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">@lang('Parallel')</small>
                                                <div class="h5">₦{{ number_format($dashboardData['active_rate']->parallel_rate, 2) }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <h6 class="text-cap">@lang('Sell Rates (USD to NGN)')</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">@lang('CBN')</small>
                                                <div class="h5">₦{{ number_format($dashboardData['active_rate']->cbn_sell_rate, 2) }}</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">@lang('Parallel')</small>
                                                <div class="h5">₦{{ number_format($dashboardData['active_rate']->parallel_sell_rate, 2) }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        @lang('Updated'): {{ $dashboardData['active_rate']->updated_at->diffForHumans() }}
                                    </small>
                                </div>
                            </div>
                        @else
                            <div class="text-center">
                                <div class="mb-3">
                                    <i class="bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                                </div>
                                <h6>@lang('No Active Rate')</h6>
                                <p class="text-muted">@lang('Please set an active exchange rate to start trading.')</p>
                                <a href="{{ route('admin.forex.rates.create') }}" class="btn btn-primary btn-sm">
                                    @lang('Set Rate')
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                <!-- End Card -->
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-lg-8 mb-3 mb-lg-5">
                <!-- Card -->
                <div class="card h-100">
                    <div class="card-header">
                        <div class="row justify-content-between align-items-center flex-grow-1">
                            <div class="col-md">
                                <h4 class="card-header-title">@lang('Transaction Overview')</h4>
                            </div>
                            <div class="col-md-auto">
                                <div class="tom-select-custom tom-select-custom-sm-end">
                                    <select class="js-select form-select form-select-sm" id="transactionChartPeriod"
                                            data-hs-tom-select-options='{"searchInDropdown": false, "hideSearch": true}'>
                                        <option value="week">@lang('This week')</option>
                                        <option value="month">@lang('This month')</option>
                                        <option value="year">@lang('This year')</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="transactionChart" style="height: 300px;"></canvas>
                    </div>
                </div>
                <!-- End Card -->
            </div>

            <div class="col-lg-4 mb-3 mb-lg-5">
                <!-- Card -->
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Recent Activity')</h4>
                    </div>
                    <div class="card-body">
                        <div class="step step-icon-sm step-inline step-item-between-checkpoints">
                            @forelse($dashboardData['recent_activity']['recent_bookings']->take(5) as $booking)
                                <div class="step-item">
                                    <div class="step-content-wrapper">
                                        <span class="step-icon step-icon-pseudo step-icon-soft-{{ $booking['status'] === 'completed' ? 'success' : ($booking['status'] === 'pending' ? 'warning' : 'danger') }}"></span>
                                        <div class="step-content">
                                            <h6 class="mb-0">
                                                <a href="{{ $booking['url'] }}">{{ $booking['reference'] }}</a>
                                            </h6>
                                            <p class="fs-6 mb-0">{{ $booking['client_name'] }} - {{ $booking['amount'] }}</p>
                                            <span class="text-muted fs-6">{{ $booking['created_at'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center">
                                    <p class="text-muted">@lang('No recent activity')</p>
                                </div>
                            @endforelse
                        </div>
                    </div>
                </div>
                <!-- End Card -->
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Quick Actions')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-primary w-100" href="{{ route('admin.forex.bookings.create') }}">
                                    <i class="bi-plus-circle me-1"></i>
                                    @lang('New Booking')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-success w-100" href="{{ route('admin.forex.rates.create') }}">
                                    <i class="bi-currency-exchange me-1"></i>
                                    @lang('Set Exchange Rate')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-info w-100" href="{{ route('admin.forex.accounts.transfer') }}">
                                    <i class="bi-arrow-left-right me-1"></i>
                                    @lang('Transfer Funds')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-warning w-100" href="{{ route('admin.forex.reports.cbn') }}">
                                    <i class="bi-file-earmark-text me-1"></i>
                                    @lang('Generate Report')
                                </a>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-danger w-100" href="{{ route('admin.forex.costs.create') }}">
                                    <i class="bi-receipt me-1"></i>
                                    @lang('Record Cost')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-secondary w-100" href="{{ route('admin.forex.costs.index') }}">
                                    <i class="bi-list-ul me-1"></i>
                                    @lang('View All Costs')
                                </a>
                            </div>
                            <div class="col-sm-6 col-lg-3 mb-3">
                                <a class="btn btn-outline-dark w-100" href="{{ route('admin.forex.costs.summary') }}">
                                    <i class="bi-graph-up me-1"></i>
                                    @lang('Cost Summary')
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('css-lib')
    <link rel="stylesheet" href="{{ asset('assets/admin/css/tom-select.bootstrap5.css') }}">
@endpush

@push('js-lib')
    <script src="{{ asset('assets/admin/js/tom-select.complete.min.js') }}"></script>
    <script src="{{ asset('assets/admin/js/chart.min.js') }}"></script>
@endpush

@push('script')
<script>
    'use strict';

    $(document).ready(function() {
        // Initialize Tom Select
        HSCore.components.HSTomSelect.init('.js-select');

        // Initialize Chart
        initTransactionChart();

        // Refresh dashboard data
        $('#refreshDashboard').on('click', function() {
            refreshDashboardData();
        });

        // Chart period change
        $('#transactionChartPeriod').on('change', function() {
            updateTransactionChart($(this).val());
        });
    });

    function initTransactionChart() {
        const ctx = document.getElementById('transactionChart').getContext('2d');

        // Get chart data via AJAX
        $.get('{{ route("admin.forex.dashboard.transaction.chart") }}', {period: 'week'})
            .done(function(data) {
                new Chart(ctx, {
                    type: 'bar',
                    data: data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            });
    }

    function updateTransactionChart(period) {
        // Update chart with new period data
        $.get('{{ route("admin.forex.dashboard.transaction.chart") }}', {period: period})
            .done(function(data) {
                // Update existing chart or recreate
                location.reload(); // Simple approach for now
            });
    }

    function refreshDashboardData() {
        const btn = $('#refreshDashboard');
        btn.prop('disabled', true).html('<i class="bi-arrow-clockwise me-1"></i> @lang("Refreshing...")');

        $.post('{{ route("admin.forex.dashboard.refresh") }}', {
            _token: $('meta[name="csrf-token"]').attr('content')
        })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Failed to refresh data');
                }
            })
            .fail(function() {
                alert('Error refreshing data');
            })
            .always(function() {
                btn.prop('disabled', false).html('<i class="bi-arrow-clockwise me-1"></i> @lang("Refresh Data")');
            });
    }
</script>
@endpush
