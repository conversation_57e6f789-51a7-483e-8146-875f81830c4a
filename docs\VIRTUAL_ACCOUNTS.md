# Virtual Accounts Feature Documentation

## Overview

The Virtual Accounts feature allows users and merchants to create virtual bank accounts through the Numero integration. These virtual accounts enable direct deposits to user/merchant wallets without manual intervention.

## Features

- **Multi-Provider Support**: Extensible architecture supporting multiple virtual account providers (starting with Numero)
- **Multi-Currency Support**: Support for NGN and USD virtual accounts
- **Account Types**: Support for both Individual and Business virtual accounts
- **Automatic Wallet Integration**: Deposits automatically credit user/merchant wallets
- **KYC Integration**: Uses existing customer information for account creation
- **Admin Management**: Full admin interface for managing virtual accounts
- **Webhook Processing**: Automatic processing of deposit and payout notifications
- **Flexible Account Management**: Users and merchants can have both individual and business accounts per currency

## Database Schema

### Virtual Accounts Table

```sql
CREATE TABLE virtual_accounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    provider VARCHAR(255) DEFAULT 'numero',
    currency VARCHAR(3) NOT NULL,
    type VARCHAR(255) DEFAULT 'individual',
    account_number VARCHAR(255) UNIQUE NOT NULL,
    account_name VA<PERSON>HAR(255),
    bank_name VARCHAR(255),
    bank_code VA<PERSON>HAR(255),
    provider_data JSON,
    kyc_data_used JSON,
    provider_reference VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at_provider TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_provider_currency_type (user_id, provider, currency, type),
    INDEX idx_provider_account (provider, account_number),
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_user_type (user_id, type)
);
```

## API Endpoints

### User Endpoints

- `GET /user/virtual-accounts/check-eligibility` - Check if user can create virtual account (defaults to customer type, NGN currency)
- `POST /user/virtual-accounts/create` - Create new virtual account (defaults to customer type, NGN currency)
- `GET /user/virtual-accounts` - List user's virtual accounts
- `GET /user/virtual-accounts/{id}` - Get specific virtual account details

### Merchant Endpoints

- `GET /merchant/virtual-accounts/check-eligibility?type=individual` - Check if merchant can create virtual account (requires type, defaults to NGN currency)
- `POST /merchant/virtual-accounts/create` - Create new virtual account for merchant (requires type, defaults to NGN currency)
- `GET /merchant/virtual-accounts` - List merchant's virtual accounts
- `GET /merchant/virtual-accounts/{id}` - Get specific virtual account details

### Admin Endpoints

- `GET /admin/virtual-accounts` - Admin virtual accounts management page
- `GET /admin/virtual-accounts/search` - DataTables search endpoint (supports type filtering)
- `GET /admin/virtual-accounts/create` - Create virtual account form
- `POST /admin/virtual-accounts/store` - Store new virtual account (requires user_id, currency, and type - supports customer/individual/business)
- `GET /admin/virtual-accounts/{id}` - View virtual account details
- `GET /admin/virtual-accounts/{id}/edit` - Edit virtual account form
- `PUT /admin/virtual-accounts/{id}` - Update virtual account
- `POST /admin/virtual-accounts/{id}/toggle-status` - Toggle account status

## Webhook Processing

### Webhook Events

The system processes two types of webhook events:

1. **FUNDING_NOTIFICATION** - Deposit webhooks
2. **TRANSFER_NOTIFICATION** - Payout webhooks (existing functionality)

### Webhook Payload Examples

#### Deposit Webhook
```json
{
    "event": "FUNDING_NOTIFICATION",
    "data": {
        "transaction_id": "T489766259758",
        "amount": 5000.00,
        "currency": "NGN",
        "account_number": "**********",
        "status": "successful"
    }
}
```

#### Payout Webhook
```json
{
    "event": "TRANSFER_NOTIFICATION",
    "data": {
        "transaction_id": "T489766259758",
        "amount": 5000.00,
        "currency": "NGN",
        "status": "successful"
    }
}
```

### Deposit Processing Flow

1. Webhook receives FUNDING_NOTIFICATION
2. System identifies virtual account by account number
3. Finds associated user/merchant
4. Checks for existing wallet in specified currency
5. Creates wallet if it doesn't exist
6. Credits wallet with deposit amount
7. Creates transaction record

## KYC Integration

### Required KYC Fields

The system uses the "Customer Information" KYC record with the following fields:

- **Email** - User's email address
- **FirstName** - User's first name
- **LastName** - User's last name
- **MobileNumber** - User's mobile number
- **BankVerificationNumber** - User's BVN

### KYC Data Structure

```json
{
    "Email": {
        "field_name": "Email",
        "field_label": "Email Address",
        "type": "email",
        "validation": "required"
    },
    "FirstName": {
        "field_name": "FirstName",
        "field_label": "First Name",
        "type": "text",
        "validation": "required"
    },
    "LastName": {
        "field_name": "LastName",
        "field_label": "Last Name",
        "type": "text",
        "validation": "required"
    },
    "MobileNumber": {
        "field_name": "MobileNumber",
        "field_label": "Mobile Number",
        "type": "text",
        "validation": "required"
    },
    "BankVerificationNumber": {
        "field_name": "BankVerificationNumber",
        "field_label": "Bank Verification Number (BVN)",
        "type": "text",
        "validation": "required"
    }
}
```

## Frontend Integration

### User Dashboard

Virtual account creation is simplified for users:

1. User clicks wallet dropdown
2. Selects "Create Virtual Account" (no type/currency selection needed)
3. System automatically uses "customer" type and "NGN" currency
4. Shows creation modal if eligible
5. Creates account and displays details

### Merchant Dashboard

Merchant virtual account creation allows type selection:

1. Merchant clicks wallet dropdown
2. Selects account type: "Create Individual Account" or "Create Business Account"
3. System uses selected type with default "NGN" currency
4. Shows creation modal if eligible
5. Creates account and displays details

### Admin Interface

Comprehensive admin interface with:

- Virtual accounts listing with DataTables
- Search and filtering capabilities (including account type filter)
- Account creation for any user/merchant with type selection
- Account status management
- Detailed account views with type information

### Account Type Management

- **Customer Accounts**: Default for regular users, automatically created with NGN currency
- **Individual Accounts**: For personal use, available to merchants
- **Business Accounts**: For commercial use, available to merchants
- **Simplified User Experience**: Users get customer accounts automatically, merchants can choose type
- **Currency Defaults**: All accounts default to NGN currency (admin can override)
- **Type-Specific UI**: Different icons and labels distinguish between account types

## Installation & Setup

### 1. Run Database Migration

```bash
php artisan migrate
```

### 2. Seed KYC Record

```bash
php artisan db:seed --class=CustomerInformationKycSeeder
```

### 3. Configure Numero Integration

Ensure Numero payout method is configured with:
- API Key
- Public Key
- Base URL

### 4. Test Webhook Endpoint

The webhook endpoint is already configured to handle both deposit and payout events.

## Testing

### Running Tests

```bash
php artisan test tests/Feature/VirtualAccountTest.php
```

### Test Coverage

The test suite covers:

- Virtual account eligibility checking
- Duplicate account prevention
- Model relationships and scopes
- KYC data extraction
- Admin and merchant access
- Account lookup functionality

### Manual Testing Steps

1. **User Virtual Account Creation**:
   - Login as user with verified KYC
   - Navigate to dashboard
   - Click wallet dropdown
   - Select "Create Virtual Account"
   - Verify account creation with "customer" type and "NGN" currency
   - Test that duplicate creation is prevented
   - Verify simple, streamlined experience

2. **Merchant Virtual Account Creation**:
   - Login as merchant with verified KYC
   - Navigate to dashboard
   - Click wallet dropdown
   - Select "Create Individual Account" or "Create Business Account"
   - Verify account creation with selected type and "NGN" currency
   - Test creating both types
   - Verify eligibility check prevents duplicates per type

3. **Admin Management**:
   - Login as admin
   - Navigate to Virtual Accounts section
   - Test filtering by account type (customer/individual/business)
   - Test search functionality
   - Create customer, individual, and business accounts for users/merchants
   - Test status toggle
   - Verify type column displays correctly with appropriate icons

4. **Account Type Validation**:
   - Verify users get customer accounts automatically
   - Verify merchants can choose individual or business types
   - Test that duplicate account creation is prevented per type
   - Verify correct icons and labels for each account type (customer: person-circle, individual: person, business: building)
   - Test account type filtering in admin interface

5. **Webhook Testing**:
   - Send test FUNDING_NOTIFICATION webhook
   - Verify wallet credit and transaction creation
   - Send test TRANSFER_NOTIFICATION webhook
   - Verify payout processing

## Security Considerations

- Virtual accounts are tied to specific users/merchants
- KYC verification required for account creation
- Webhook signature verification (inherited from existing Numero integration)
- Admin-only access to create accounts for other users
- Account status can be toggled for security

## Troubleshooting

### Common Issues

1. **KYC Data Not Found**:
   - Ensure "Customer Information" KYC record exists
   - Verify user has completed and verified KYC
   - Check all required fields are present

2. **Duplicate Account Error**:
   - Check if user already has account for currency/provider/type combination
   - Use eligibility check before creation
   - Remember users can have both individual and business accounts per currency

3. **Account Type Issues**:
   - Verify type parameter is included in API requests
   - Check that type validation allows only 'individual' and 'business'
   - Ensure frontend passes correct type parameter

4. **Webhook Not Processing**:
   - Verify webhook endpoint is accessible
   - Check webhook signature validation
   - Review webhook logs

### Logs

Monitor these log channels:
- Virtual account creation: `Log::info('Numero virtual account creation request')`
- Webhook processing: `Log::info('Numero webhook received')`
- Deposit processing: `Log::info('Numero deposit webhook: Successfully processed deposit')`

## Future Enhancements

- Support for additional virtual account providers
- Bulk virtual account creation
- Virtual account analytics and reporting
- Enhanced webhook retry mechanisms
- Virtual account limits and restrictions
- Account type-specific features and restrictions
- Advanced account type management (e.g., corporate accounts, joint accounts)
- Account type-based transaction limits and rules

## Account Type Implementation Details

### Database Changes

The `type` column was added to the `virtual_accounts` table with the following characteristics:
- **Type**: VARCHAR(255)
- **Default**: 'individual'
- **Values**: 'individual', 'business'
- **Indexes**: Added composite index on (user_id, type) and updated main index to include type

### API Changes

Virtual account endpoints have been simplified:
- **User Endpoints**: No parameters required - defaults to 'customer' type and 'NGN' currency
- **Merchant Endpoints**: Requires `type` parameter ('individual' or 'business'), defaults to 'NGN' currency
- **Admin Endpoints**: Requires both `type` and `currency` parameters for full control
- **Validation**: Ensures type is one of 'customer', 'individual', or 'business'
- **Uniqueness**: Prevents duplicate accounts per user/provider/currency/type combination

### Frontend Changes

- **User Interface**:
  - Users: Single "Create Virtual Account" button (simplified)
  - Merchants: Separate buttons for individual and business account creation
- **Icons**:
  - Customer accounts: person-circle icon
  - Individual accounts: person icon
  - Business accounts: building icon
- **Labels**: Clear distinction between account types in all interfaces
- **Modals**: Simplified modals for users, type-specific modals for merchants
- **Filtering**: Admin interface includes account type filtering with all three types

### Business Logic

- **Simplified User Experience**: Users automatically get customer accounts with NGN currency
- **Merchant Flexibility**: Merchants can choose between individual and business account types
- **Currency Defaults**: All accounts default to NGN currency unless overridden by admin
- **Type Validation**: Strict validation ensures only valid account types ('customer', 'individual', 'business')
- **Eligibility**: Automatic eligibility for users, type-specific eligibility for merchants
- **Display**: Account type is prominently displayed with appropriate icons and colors
