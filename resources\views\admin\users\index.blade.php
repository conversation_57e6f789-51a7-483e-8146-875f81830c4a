@extends('admin.layouts.app')

@section('panel')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0 font-size-18">User Management</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.user-role-management.dashboard') }}">User & Role Management</a></li>
                        <li class="breadcrumb-item active">Users</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>All Users
                    </h4>
                    @canAdvanced('users.create')
                        <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Create User
                        </a>
                    @endcanAdvanced
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Search Users</label>
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Search by name or email..." value="{{ request('search') }}">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Filter by Role</label>
                                    <select name="role_filter" class="form-select">
                                        <option value="">All Roles</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}" {{ request('role_filter') == $role->id ? 'selected' : '' }}>
                                                {{ $role->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Advanced Roles Status</label>
                                    <select name="advanced_roles_status" class="form-select">
                                        <option value="">All Users</option>
                                        <option value="enabled" {{ request('advanced_roles_status') == 'enabled' ? 'selected' : '' }}>
                                            Advanced Roles Enabled
                                        </option>
                                        <option value="disabled" {{ request('advanced_roles_status') == 'disabled' ? 'selected' : '' }}>
                                            Advanced Roles Disabled
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-search me-1"></i> Filter
                                        </button>
                                        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i> Clear
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>User</th>
                                    <th>Advanced Roles</th>
                                    <th>Current Roles</th>
                                    <th>Status</th>
                                    <th>Joined</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($users as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title rounded-circle bg-primary">
                                                        {{ substr($user->name, 0, 1) }}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ $user->name }}</h6>
                                                    <small class="text-muted">{{ $user->email }}</small>
                                                    @if($user->email_verified_at)
                                                        <span class="badge bg-success ms-1">Verified</span>
                                                    @else
                                                        <span class="badge bg-warning ms-1">Unverified</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($user->use_advanced_roles)
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check me-1"></i>Enabled
                                                    </span>
                                                    @canAdvanced('users.update')
                                                        <button type="button" class="btn btn-sm btn-outline-warning ms-2" 
                                                                onclick="toggleAdvancedRoles({{ $user->id }}, false)"
                                                                title="Disable Advanced Roles">
                                                            <i class="fas fa-toggle-off"></i>
                                                        </button>
                                                    @endcanAdvanced
                                                @else
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-times me-1"></i>Disabled
                                                    </span>
                                                    @canAdvanced('users.update')
                                                        <button type="button" class="btn btn-sm btn-outline-success ms-2" 
                                                                onclick="toggleAdvancedRoles({{ $user->id }}, true)"
                                                                title="Enable Advanced Roles">
                                                            <i class="fas fa-toggle-on"></i>
                                                        </button>
                                                    @endcanAdvanced
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($user->use_advanced_roles && $user->advancedUserRoles->count() > 0)
                                                <div class="role-badges">
                                                    @foreach($user->advancedUserRoles->where('is_active', true)->take(3) as $assignment)
                                                        <span class="badge me-1 mb-1" 
                                                              style="background-color: {{ $assignment->role->color ?? '#6c757d' }};">
                                                            {{ $assignment->role->display_name }}
                                                            @if($assignment->expires_at && $assignment->expires_at->isPast())
                                                                <i class="fas fa-exclamation-triangle ms-1" title="Expired"></i>
                                                            @endif
                                                        </span>
                                                    @endforeach
                                                    @if($user->advancedUserRoles->where('is_active', true)->count() > 3)
                                                        <span class="badge bg-info">
                                                            +{{ $user->advancedUserRoles->where('is_active', true)->count() - 3 }} more
                                                        </span>
                                                    @endif
                                                </div>
                                            @else
                                                <span class="text-muted">No roles assigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($user->deleted_at)
                                                <span class="badge bg-danger">Deleted</span>
                                            @elseif($user->status === 'active')
                                                <span class="badge bg-success">Active</span>
                                            @elseif($user->status === 'suspended')
                                                <span class="badge bg-warning">Suspended</span>
                                            @else
                                                <span class="badge bg-secondary">{{ ucfirst($user->status ?? 'Unknown') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $user->created_at->format('M j, Y') }}</span>
                                            <br><small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                @canAdvanced('users.read')
                                                    <a href="{{ route('admin.users.show', $user) }}" 
                                                       class="btn btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                @endcanAdvanced
                                                @canAdvanced('users.update')
                                                    <a href="{{ route('admin.users.edit', $user) }}" 
                                                       class="btn btn-outline-primary" title="Edit User">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endcanAdvanced
                                                @if($user->use_advanced_roles)
                                                    @canAdvanced('user_roles.create')
                                                        <button type="button" class="btn btn-outline-success" 
                                                                onclick="showAssignRoleModal({{ $user->id }})" 
                                                                title="Assign Role">
                                                            <i class="fas fa-user-plus"></i>
                                                        </button>
                                                    @endcanAdvanced
                                                @endif
                                                @canAdvanced('users.delete')
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="confirmDelete({{ $user->id }}, '{{ $user->name }}')" 
                                                            title="Delete User">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                @endcanAdvanced
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <p>No users found matching your criteria.</p>
                                                @canAdvanced('users.create')
                                                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary">
                                                        Create Your First User
                                                    </a>
                                                @endcanAdvanced
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($users->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $users->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Assign Role Modal -->
    <div class="modal fade" id="assignRoleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Assign Role to User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="assignRoleForm">
                    <div class="modal-body">
                        <input type="hidden" id="assign_user_id" name="user_id">
                        
                        <div class="mb-3">
                            <label class="form-label">Select Role</label>
                            <select name="role_id" class="form-select" required>
                                <option value="">Choose a role...</option>
                                @foreach($roles as $role)
                                    <option value="{{ $role->id }}">{{ $role->display_name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Expiration Date (Optional)</label>
                            <input type="datetime-local" name="expires_at" class="form-control">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Context (Optional)</label>
                            <input type="text" name="context" class="form-control" 
                                   placeholder="e.g., project_alpha, department_finance">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Assignment Reason</label>
                            <textarea name="assignment_reason" class="form-control" rows="3" 
                                      placeholder="Reason for assigning this role..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Assign Role</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the user <strong id="userNameToDelete"></strong>?</p>
                    <p class="text-danger">This action cannot be undone and will revoke all role assignments.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete User</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
<script>
function showAssignRoleModal(userId) {
    document.getElementById('assign_user_id').value = userId;
    new bootstrap.Modal(document.getElementById('assignRoleModal')).show();
}

function confirmDelete(userId, userName) {
    document.getElementById('userNameToDelete').textContent = userName;
    document.getElementById('deleteForm').action = `/admin/users/${userId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function toggleAdvancedRoles(userId, enable) {
    const action = enable ? 'enable' : 'disable';
    if (confirm(`Are you sure you want to ${action} advanced roles for this user?`)) {
        fetch(`/admin/users/${userId}/toggle-advanced-roles`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                toastr.error(data.message || 'Operation failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('An error occurred');
        });
    }
}

// Handle assign role form submission
document.getElementById('assignRoleForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const userId = formData.get('user_id');
    
    fetch(`/admin/users/${userId}/assign-role`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            bootstrap.Modal.getInstance(document.getElementById('assignRoleModal')).hide();
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            toastr.error(data.message || 'Role assignment failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('An error occurred during role assignment');
    });
});
</script>
@endpush

@push('style')
<style>
.avatar-sm {
    height: 2.5rem;
    width: 2.5rem;
}

.avatar-title {
    align-items: center;
    background-color: #556ee6;
    color: #fff;
    display: flex;
    font-weight: 500;
    height: 100%;
    justify-content: center;
    width: 100%;
}

.role-badges .badge {
    font-size: 0.75rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
@endpush
