<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('forex_bookings', function (Blueprint $table) {
            // Add markup percentage used for the booking
            $table->decimal('markup_percentage', 5, 2)->after('parallel_rate')->comment('Markup percentage applied to parallel rate');
            
            // Add customer rate (parallel rate + markup)
            $table->decimal('customer_rate', 18, 8)->after('markup_percentage')->comment('Final rate charged to customer (parallel + markup)');
            
            // Add customer total (amount * customer rate)
            $table->decimal('customer_total', 18, 8)->after('customer_rate')->comment('Total amount customer pays (amount * customer_rate)');
            
            // Add markup amount (goes to CBN account)
            $table->decimal('markup_amount', 18, 8)->after('customer_total')->comment('Markup amount (customer_total - parallel_total)');
            
            // Update difference amount comment for clarity
            $table->decimal('difference_amount', 18, 8)->comment('Profit amount (parallel_total - cbn_total)')->change();
            
            // Update transaction type comment for clarity
            $table->enum('transaction_type', ['credit', 'debit'])->comment('Credit = client funding account, Debit = client exchanging currency')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('forex_bookings', function (Blueprint $table) {
            $table->dropColumn([
                'markup_percentage',
                'customer_rate', 
                'customer_total',
                'markup_amount'
            ]);
            
            // Revert comments
            $table->decimal('difference_amount', 18, 8)->comment('Parallel Total - CBN Total')->change();
            $table->enum('transaction_type', ['credit', 'debit'])->comment('Credit = funding, Debit = exchange')->change();
        });
    }
};
