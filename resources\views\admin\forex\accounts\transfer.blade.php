@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.accounts.index') }}">
                                    @lang('Forex Accounts')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Account Transfer')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">@lang('Account Transfer')</h1>
                    <p class="page-header-text">@lang('Transfer funds between forex trading accounts')</p>
                </div>
                <div class="col-sm-auto">
                    <a class="btn btn-outline-secondary" href="{{ route('admin.forex.accounts.index') }}">
                        <i class="bi-arrow-left me-1"></i> @lang('Back to Accounts')
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row justify-content-lg-center">
            <div class="col-lg-8">
                <!-- Transfer Form Card -->
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Transfer Details')</h4>
                        <span class="badge bg-soft-warning text-warning">
                            <i class="bi-exclamation-triangle me-1"></i> @lang('Transfers are immediate and cannot be undone')
                        </span>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <form action="{{ route('admin.forex.accounts.transfer.process') }}" method="POST">
                            @csrf

                            <!-- From Account -->
                            <div class="row mb-4">
                                <label for="fromAccountLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('From Account') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <select class="form-select @error('from_account_id') is-invalid @enderror"
                                            name="from_account_id" id="fromAccountLabel" required>
                                        <option value="">@lang('Select source account')</option>
                                        @foreach($accounts as $account)
                                            <option value="{{ $account->id }}"
                                                    data-balance="{{ $account->balance }}"
                                                    data-currency="{{ $account->currency_code }}"
                                                    {{ old('from_account_id') == $account->id ? 'selected' : '' }}>
                                                {{ $account->account_name }} ({{ number_format($account->balance, 2) }} {{ $account->currency_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('from_account_id')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Account to transfer funds from')
                                    </small>
                                </div>
                            </div>
                            <!-- End From Account -->

                            <!-- To Account -->
                            <div class="row mb-4">
                                <label for="toAccountLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('To Account') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <select class="form-select @error('to_account_id') is-invalid @enderror"
                                            name="to_account_id" id="toAccountLabel" required>
                                        <option value="">@lang('Select destination account')</option>
                                        @foreach($accounts as $account)
                                            <option value="{{ $account->id }}"
                                                    data-balance="{{ $account->balance }}"
                                                    data-currency="{{ $account->currency_code }}"
                                                    {{ old('to_account_id') == $account->id ? 'selected' : '' }}>
                                                {{ $account->account_name }} ({{ number_format($account->balance, 2) }} {{ $account->currency_code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('to_account_id')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Account to transfer funds to')
                                    </small>
                                </div>
                            </div>
                            <!-- End To Account -->

                            <!-- Amount -->
                            <div class="row mb-4">
                                <label for="amountLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Amount') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <div class="input-group">
                                        <span class="input-group-text" id="currencySymbol">$</span>
                                        <input type="number" class="form-control @error('amount') is-invalid @enderror"
                                               name="amount" id="amountLabel" step="0.01" min="0.01"
                                               placeholder="@lang('0.00')" value="{{ old('amount') }}" required>
                                        <span class="input-group-text" id="currencyCode">USD</span>
                                    </div>
                                    @error('amount')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        @lang('Amount to transfer (must not exceed available balance)')
                                    </small>
                                    <div id="balanceWarning" class="text-danger small mt-1" style="display: none;">
                                        @lang('Amount exceeds available balance')
                                    </div>
                                </div>
                            </div>
                            <!-- End Amount -->

                            <!-- Description -->
                            <div class="row mb-4">
                                <label for="descriptionLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Description') <span class="text-danger">*</span>
                                </label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control @error('description') is-invalid @enderror"
                                           name="description" id="descriptionLabel"
                                           placeholder="@lang('e.g., Account rebalancing, Profit distribution')"
                                           value="{{ old('description') }}" required>
                                    @error('description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Description -->

                            <!-- Notes -->
                            <div class="row mb-4">
                                <label for="notesLabel" class="col-sm-3 col-form-label form-label">
                                    @lang('Notes')
                                </label>
                                <div class="col-sm-9">
                                    <textarea class="form-control @error('notes') is-invalid @enderror"
                                              name="notes" id="notesLabel" rows="3"
                                              placeholder="@lang('Additional notes or comments (optional)')">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                            <!-- End Notes -->

                            <!-- Transfer Preview -->
                            <div class="row mb-4" id="transferPreview" style="display: none;">
                                <label class="col-sm-3 col-form-label form-label">
                                    @lang('Transfer Preview')
                                </label>
                                <div class="col-sm-9">
                                    <div class="alert alert-soft-info" role="alert">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="bi-arrow-left-right"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="row">
                                                    <div class="col-sm-6">
                                                        <span class="d-block fw-semibold">@lang('From Account')</span>
                                                        <span id="fromAccountName">-</span><br>
                                                        <small class="text-muted">
                                                            @lang('New Balance'): <span id="fromNewBalance">-</span>
                                                        </small>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <span class="d-block fw-semibold">@lang('To Account')</span>
                                                        <span id="toAccountName">-</span><br>
                                                        <small class="text-muted">
                                                            @lang('New Balance'): <span id="toNewBalance">-</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- End Transfer Preview -->

                            <!-- Submit Buttons -->
                            <div class="d-flex justify-content-end">
                                <div class="d-flex gap-3">
                                    <a class="btn btn-white" href="{{ route('admin.forex.accounts.index') }}">
                                        @lang('Cancel')
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="transferBtn" disabled>
                                        <i class="bi-arrow-left-right me-1"></i> @lang('Transfer Funds')
                                    </button>
                                </div>
                            </div>
                            <!-- End Submit Buttons -->
                        </form>
                    </div>
                    <!-- End Body -->
                </div>
                <!-- End Transfer Form Card -->
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        'use strict';

        $(document).ready(function () {
            let fromAccountData = null;
            let toAccountData = null;

            // Update currency display and validation
            function updateCurrencyDisplay() {
                const fromAccount = $('#fromAccountLabel option:selected');
                if (fromAccount.val()) {
                    const currency = fromAccount.data('currency');
                    const symbol = currency === 'USD' ? '$' : '₦';
                    $('#currencySymbol').text(symbol);
                    $('#currencyCode').text(currency);

                    fromAccountData = {
                        id: fromAccount.val(),
                        name: fromAccount.text(),
                        balance: parseFloat(fromAccount.data('balance')),
                        currency: currency
                    };
                } else {
                    fromAccountData = null;
                }
                updateTransferPreview();
            }

            // Update transfer preview
            function updateTransferPreview() {
                const toAccount = $('#toAccountLabel option:selected');
                const amount = parseFloat($('#amountLabel').val()) || 0;

                if (toAccount.val()) {
                    toAccountData = {
                        id: toAccount.val(),
                        name: toAccount.text(),
                        balance: parseFloat(toAccount.data('balance')),
                        currency: toAccount.data('currency')
                    };
                } else {
                    toAccountData = null;
                }

                if (fromAccountData && toAccountData && amount > 0) {
                    // Check if currencies match
                    if (fromAccountData.currency !== toAccountData.currency) {
                        alert('@lang("Cannot transfer between accounts with different currencies")');
                        $('#amountLabel').val('');
                        return;
                    }

                    // Check if accounts are different
                    if (fromAccountData.id === toAccountData.id) {
                        alert('@lang("Cannot transfer to the same account")');
                        $('#toAccountLabel').val('');
                        return;
                    }

                    // Update preview
                    $('#fromAccountName').text(fromAccountData.name);
                    $('#toAccountName').text(toAccountData.name);
                    $('#fromNewBalance').text((fromAccountData.balance - amount).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) + ' ' + fromAccountData.currency);
                    $('#toNewBalance').text((toAccountData.balance + amount).toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) + ' ' + toAccountData.currency);

                    $('#transferPreview').show();

                    // Validate balance
                    if (amount > fromAccountData.balance) {
                        $('#balanceWarning').show();
                        $('#transferBtn').prop('disabled', true);
                    } else {
                        $('#balanceWarning').hide();
                        $('#transferBtn').prop('disabled', false);
                    }
                } else {
                    $('#transferPreview').hide();
                    $('#transferBtn').prop('disabled', true);
                }
            }

            // Bind events
            $('#fromAccountLabel').on('change', updateCurrencyDisplay);
            $('#toAccountLabel').on('change', updateTransferPreview);
            $('#amountLabel').on('input', updateTransferPreview);

            // Initial setup
            updateCurrencyDisplay();
        });
    </script>
@endpush
