<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Mail\SendMail;
use App\Models\Currency;
use App\Models\Fund;
use App\Models\Language;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserProfile;
use App\Models\NotificationPermission;
use App\Models\NotificationTemplate;
use App\Models\Wallet;
use App\Traits\ApiValidation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
	use ApiValidation;

	public function registerUserForm()
	{
		try {
			$info = json_decode(json_encode(getIpInfo()), true);
			$country_code = null;
			if (!empty($info['code'])) {
				$data['country_code'] = @$info['code'][0];
			}
			$data['countries'] = config('country');
			return response()->json($this->withSuccess($data));
		} catch (\Exception $e) {
			return response()->json($this->withErrors($e));
		}
	}

	public function registerUser(Request $request)
	{
		$basic = basicControl();
		try {
            $basic = basicControl();
            $data = $request->all();
            $registerRules = [
                'firstname' => 'required|string',
                'lastname' => 'required|string',
                'username' => 'required|string|alpha_dash|min:5|unique:users,username',
                'email' => 'required|string|email|unique:users,email',
                'password' => $basic->strong_password == 0 ?
                    ['required', 'confirmed', 'min:6'] :
                    ['required', 'confirmed',  Password::min(6)->mixedCase()
                        ->letters()
                        ->numbers()
                        ->symbols()
                        ->uncompromised()],
                'phone' => ['required', 'numeric', 'unique:users,phone'],
                'phone_code' => ['required', 'numeric'],
                'country' => ['required'],
                'country_code' => ['required']

            ];

            $message = [
                'password.letters' => 'password must be contain letters',
                'password.mixed' => 'password must be contain 1 uppercase and lowercase character',
                'password.symbols' => 'password must be contain symbols',
            ];
            $validation = Validator::make($request->all(), $registerRules,$message);
            if ($validation->fails()) {
                return response()->json($this->withErrors(collect($validation->errors())->collapse()));
            }

			if ($request->sponsor != null) {
				$sponsorId = User::where('username', $request->sponsor)->first()->id;
			} else {
				$sponsorId = null;
			}
			$user = User::create([
                'firstname' => $data['firstname'],
                'lastname' => $data['lastname'],
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'phone' => $data['phone'],
                'phone_code' => $data['phone_code'],
                'country' => $data['country'],
                'country_code' => $data['country_code'],
				'referral_id' => $sponsorId,
				'language_id' => Language::select('id')->where('default_status', true)->first()->name ?? null,
                'email_verification' => ($basic->email_verification) ? 0 : 1,
                'sms_verification' => ($basic->sms_verification) ? 0 : 1,
				'qr_link' => strRandom(20),
			]);


            $user->two_fa_verify = ($user->two_fa == 1) ? 0 : 1;
			$user->save();

			$currencies = Currency::All();
			foreach ($currencies as $currency) {
				Wallet::firstOrCreate(['user_id' => $user->id, 'currency_id' => $currency->id]);
			}

			// Create default notification permissions for new user
			$this->createDefaultNotificationPermissions($user);

			if ($user && basicControl()->joining_bonus > 0 && basicControl()->signup_bonus_status == 1) {
				$fund = new Fund();
				$fund->user_id = $user->id;
				$fund->currency_id = basicControl()->base_currency;
				$fund->percentage = 0;
				$fund->charge_percentage = 0;
				$fund->charge_fixed = 0;
				$fund->charge = 0;
				$fund->amount = basicControl()->joining_bonus;
				$fund->email = $user->email;
				$fund->status = 1;
				$fund->note = 'Joining bonus';
				$fund->utr = (string)Str::uuid();
				$fund->save();

				updateWallet($fund->user_id, $fund->currency_id, $fund->amount, 1);
				$transaction = new Transaction();
				$transaction->amount = $fund->amount;
				$transaction->charge = $fund->charge;
				$transaction->currency_id = $fund->currency_id;
				$fund->transactional()->save($transaction);
			}

			$user->save();


			return response()->json([
				'status' => 'success',
				'message' => 'User Created Successfully',
				'token' => $user->createToken("API TOKEN")->plainTextToken
			]);


		} catch (\Exception $th) {
			return response()->json($this->withErrors($th->getMessage()));
		}
	}

	public function loginUser(Request $request)
	{
		$validateUser = Validator::make($request->all(),
			[
				'username' => 'required|string',
				'password' => 'required|string'
			]);

		if ($validateUser->fails()) {
			return response()->json($this->withErrors(collect($validateUser->errors())->collapse()[0]));
		}
		try {
			if (!Auth::attempt($request->only(['username', 'password']))) {
				return response()->json($this->withErrors('Username & Password does not match with our record.'));
			}

			$user = User::where('username', $request->username)->first();
			if (!$user) {
				return response()->json($this->withErrors('record not found'));
			}

            //User type check
            if (!in_array($user->type, getAllowedUserTypes())) {
                return response()->json($this->withErrors("Login restricted for $user->type."));
            }

			$user->two_fa_verify = ($user->two_fa == 1) ? 0 : 1;
			$user->save();

			return response()->json([
				'status' => 'success',
				'message' => 'User Logged In Successfully',
				'token' => $user->createToken("API TOKEN")->plainTextToken
			]);

		} catch (\Throwable $th) {
			return response()->json($this->withErrors($th->getMessage()));
		}
	}

	public function authenticateUser(Request $request)
	{

		$validateUser = Validator::make($request->all(),
			[
				'publicKey' => 'required|string',
				'secretKey' => 'required|string'
			]);

		if ($validateUser->fails()) {
			return response()->json($this->withErrors(collect($validateUser->errors())->collapse()[0]));
		}

		try {
			// Find user by public_key and secret_key
			$user = User::where('public_key', $request->publicKey)
						->where('secret_key', $request->secretKey)
						->first();

			if (!$user) {
				return response()->json($this->withErrors('Invalid API credentials. Public Key & Secret Key do not match with our records.'));
			}

			// User type check
			if (!in_array($user->type, getAllowedUserTypes())) {
				return response()->json($this->withErrors("Authentication restricted for $user->type."));
			}

			// Check if user account is active
			if ($user->status != 1) {
				return response()->json($this->withErrors('Your account has been suspended. Please contact support.'));
			}

			// Set two_fa_verify based on two_fa setting (same logic as loginUser)
			$user->two_fa_verify = ($user->two_fa == 1) ? 0 : 1;
			$user->save();

			return response()->json([
				'status' => 'success',
				'message' => 'User Authenticated Successfully',
				'token' => $user->createToken("API TOKEN")->plainTextToken,
				// 'user' => [
				// 	'id' => $user->id,
				// 	'username' => $user->username,
				// 	'email' => $user->email,
				// 	'type' => $user->type,
				// 	'mode' => $user->mode == 0 ? 'Test' : 'Live'
				// ]
			]);

		} catch (\Throwable $th) {
			return response()->json($this->withErrors($th->getMessage()));
		}
	}

	public function getEmailForRecoverPass(Request $request)
	{
		$validateUser = Validator::make($request->all(),
			[
				'email' => 'required|email',
			]);

		if ($validateUser->fails()) {
			return response()->json($this->withErrors(collect($validateUser->errors())->collapse()[0]));
		}

		try {
			$user = User::where('email', $request->email)->first();
			if (!$user) {
				return response()->json($this->withErrors('Email does not exit on record'));
			}

			$code = rand(10000, 99999);
			$data['email'] = $request->email;
			$data['message'] = 'OTP has been send';
			$user->verify_code = $code;
			$user->save();

			$basic = basicControl();
			$message = 'Your Password Recovery Code is ' . $code;
			$email_from = $basic->sender_email;
			@Mail::to($request->email)->send(new SendMail($email_from, "Recovery Code", $message));

			return response()->json($this->withSuccess($data));
		} catch (\Exception $e) {
			return response()->json($this->withErrors($e->getMessage()));
		}
	}

	public function getCodeForRecoverPass(Request $request)
	{
		$validateUser = Validator::make($request->all(),
			[
				'code' => 'required',
				'email' => 'required|email',
			]);

		if ($validateUser->fails()) {
			return response()->json($this->withErrors(collect($validateUser->errors())->collapse()[0]));
		}

		try {
			$user = User::where('email', $request->email)->first();
			if (!$user) {
				return response()->json($this->withErrors('Email does not exit on record'));
			}

			if ($user->verify_code == $request->code && $user->updated_at > Carbon::now()->subMinutes(5)) {
				$user->verify_code = null;
				$user->save();
				return response()->json($this->withSuccess('Code Matching'));
			}

			return response()->json($this->withErrors('Invalid Code'));
		} catch (\Exception $e) {
			return response()->json($this->withErrors($e->getMessage()));
		}
	}

	public function updatePass(Request $request)
	{
		if (config('basic.strong_password') == 0) {
			$rules['password'] = ['required', 'min:6', 'confirmed'];
		} else {
			$rules['password'] = ["required", 'confirmed',
				Password::min(6)->mixedCase()
					->letters()
					->numbers()
					->symbols()
					->uncompromised()];
		}
		$rules['email'] = ['required', 'email'];

		$validateUser = Validator::make($request->all(), $rules);

		if ($validateUser->fails()) {
			return response()->json($this->withErrors(collect($validateUser->errors())->collapse()[0]));
		}

		$user = User::where('email', $request->email)->first();
		if (!$user) {
			return response()->json($this->withErrors('Email does not exist on record'));
		}
		$user->password = Hash::make($request->password);
		$user->save();
		return response()->json($this->withSuccess('Password Updated'));
	}

    public function logout()
    {
        auth()->user()->tokens()->delete();
        return response()->json($this->withSuccess('User is logged out successfully'));
    }

    /**
     * Create default notification permissions for new user
     */
    protected function createDefaultNotificationPermissions($user)
    {
        try {
            // Default user type to 'user' if not set
            $userType = $user->type ?? 'user';

            // Define which template types are allowed for each user role
            $allowedTypes = match ($userType) {
                'user' => [1, 2, 5, 6], // All, User, User+Agent, User+Merchant
                'agent' => [1, 3, 5, 7], // All, Agent, User+Agent, Agent+Merchant
                'merchant' => [1, 4, 6, 7], // All, Merchant, User+Merchant, Agent+Merchant
                default => [1, 2, 5, 6], // Default to user permissions
            };

            // Get all notification templates for this user type that have email enabled
            $emailTemplates = NotificationTemplate::where('notify_for', 0)
                ->whereIn('type', $allowedTypes)
                ->where('email', 1) // Only templates with email enabled
                ->pluck('template_key')
                ->unique()
                ->values()
                ->toArray();

            // Create notification permission record with all available email templates enabled
            NotificationPermission::create([
                'notifyable_id' => $user->id,
                'notifyable_type' => User::class,
                'template_email_key' => $emailTemplates,
                'template_sms_key' => [], // SMS disabled by default
                'template_push_key' => [], // Push disabled by default
                'template_in_app_key' => [], // In-app disabled by default
            ]);

        } catch (\Exception $e) {
            // Log error but don't fail registration
            \Log::error('Failed to create default notification permissions for user ' . $user->id . ': ' . $e->getMessage());
        }
    }

}
