<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('forex_email_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('forex_booking_id')->nullable()->constrained('forex_bookings')->onDelete('cascade');
            $table->string('email_to');
            $table->string('email_subject');
            $table->text('email_body');
            $table->enum('email_type', ['booking_confirmation', 'payment_reminder', 'completion_notification']);
            $table->enum('status', ['pending', 'sent', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamps();
            
            $table->index(['forex_booking_id', 'email_type']);
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('forex_email_logs');
    }
};
