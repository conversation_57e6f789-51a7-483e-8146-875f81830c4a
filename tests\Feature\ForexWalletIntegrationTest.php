<?php

namespace Tests\Feature;

use App\Models\Currency;
use App\Models\ForexAccount;
use App\Models\ForexBooking;
use App\Models\ForexRate;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Services\ForexBookingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ForexWalletIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $user;
    protected $usdCurrency;
    protected $ngnCurrency;
    protected $usdAccount;
    protected $cbnAccount;
    protected $activeRate;
    protected $bookingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test currencies
        $this->usdCurrency = Currency::create([
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
            'is_active' => 1,
            'currency_type' => 0,
            'driver' => 'local',
            'logo' => 'usd.png'
        ]);

        $this->ngnCurrency = Currency::create([
            'name' => 'Nigerian Naira',
            'code' => 'NGN',
            'symbol' => '₦',
            'is_active' => 1,
            'currency_type' => 1,
            'driver' => 'local',
            'logo' => 'ngn.png'
        ]);

        // Create admin user
        $this->admin = User::create([
            'firstname' => 'Admin',
            'lastname' => 'User',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'email_verified_at' => now(),
        ]);

        // Create regular user
        $this->user = User::create([
            'firstname' => 'Test',
            'lastname' => 'User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'status' => 1,
            'email_verified_at' => now(),
        ]);

        // Create forex accounts
        $this->usdAccount = ForexAccount::create([
            'account_name' => 'USD Account',
            'account_type' => 'USD',
            'currency_code' => 'USD',
            'balance' => 10000.00,
            'pending_balance' => 0.00,
            'is_active' => true,
            'description' => 'USD account for transactions'
        ]);

        $this->cbnAccount = ForexAccount::create([
            'account_name' => 'CBN Account',
            'account_type' => 'CBN',
            'currency_code' => 'NGN',
            'balance' => 5000000.00,
            'pending_balance' => 0.00,
            'is_active' => true,
            'description' => 'CBN account for NGN transactions'
        ]);

        // Create active forex rate
        $this->activeRate = ForexRate::create([
            'cbn_rate' => 800.00,
            'parallel_rate' => 1200.00,
            'markup_percentage' => 5.00,
            'cbn_sell_rate' => 790.00,
            'parallel_sell_rate' => 1190.00,
            'sell_markup_percentage' => 3.00,
            'is_active' => true,
            'created_by' => 1,
        ]);

        $this->bookingService = app(ForexBookingService::class);
    }

    /** @test */
    public function complete_workflow_buying_usd_with_wallet_payment()
    {
        // Create USD wallet for user
        $usdWallet = Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 50.00
        ]);

        $initialWalletBalance = $usdWallet->balance;

        // Step 1: Create booking with wallet payment
        $bookingData = [
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_type' => 'user',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->usdAccount->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id,
        ];

        $booking = $this->bookingService->createBooking($bookingData, $this->admin->id);

        // Verify booking was created correctly
        $this->assertEquals('wallet', $booking->payment_method);
        $this->assertEquals($this->usdCurrency->id, $booking->wallet_currency_id);
        $this->assertEquals('pending', $booking->status);
        $this->assertTrue($booking->isWalletPayment());

        // Step 2: Complete the booking
        $this->bookingService->completeBooking($booking, $this->admin->id, 'Wallet payment completed');

        // Step 3: Verify booking completion
        $booking->refresh();
        $this->assertEquals('completed', $booking->status);
        $this->assertNotNull($booking->completed_at);
        $this->assertEquals($this->admin->id, $booking->completed_by);

        // Step 4: Verify wallet was funded
        $usdWallet->refresh();
        $expectedBalance = $initialWalletBalance + 100.00; // User receives USD amount
        $this->assertEquals($expectedBalance, $usdWallet->balance);

        // Step 5: Verify transaction record was created
        $transaction = Transaction::where('user_id', $this->user->id)
            ->where('currency_id', $this->usdCurrency->id)
            ->where('transactional_type', ForexBooking::class)
            ->where('transactional_id', $booking->id)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(100.00, $transaction->amount);
        $this->assertEquals('+', $transaction->trx_type);
        $this->assertStringContains($booking->booking_reference, $transaction->remarks);
    }

    /** @test */
    public function complete_workflow_selling_usd_with_wallet_payment()
    {
        // Create NGN wallet for user
        $ngnWallet = Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->ngnCurrency->id,
            'balance' => 50000.00
        ]);

        $initialWalletBalance = $ngnWallet->balance;

        // Step 1: Create booking with wallet payment
        $bookingData = [
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_type' => 'user',
            'transaction_type' => 'selling',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->cbnAccount->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->ngnCurrency->id,
        ];

        $booking = $this->bookingService->createBooking($bookingData, $this->admin->id);

        // Verify booking calculations
        $this->assertEquals('wallet', $booking->payment_method);
        $this->assertEquals($this->ngnCurrency->id, $booking->wallet_currency_id);
        
        // For selling: customer_rate = parallel_sell_rate + markup
        $expectedCustomerRate = 1190.00 + (1190.00 * 3.00 / 100); // 1225.70
        $expectedCustomerTotal = 100.00 * $expectedCustomerRate; // 122570.00
        
        $this->assertEquals($expectedCustomerRate, $booking->customer_rate);
        $this->assertEquals($expectedCustomerTotal, $booking->customer_total);

        // Step 2: Complete the booking
        $this->bookingService->completeBooking($booking, $this->admin->id, 'Wallet payment completed');

        // Step 3: Verify wallet was funded with NGN amount
        $ngnWallet->refresh();
        $expectedBalance = $initialWalletBalance + $expectedCustomerTotal;
        $this->assertEquals($expectedBalance, $ngnWallet->balance);

        // Step 4: Verify transaction record
        $transaction = Transaction::where('user_id', $this->user->id)
            ->where('currency_id', $this->ngnCurrency->id)
            ->where('transactional_type', ForexBooking::class)
            ->where('transactional_id', $booking->id)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals($expectedCustomerTotal, $transaction->amount);
        $this->assertEquals('+', $transaction->trx_type);
    }

    /** @test */
    public function booking_with_account_details_does_not_fund_wallet()
    {
        // Create USD wallet for user
        $usdWallet = Wallet::create([
            'user_id' => $this->user->id,
            'currency_id' => $this->usdCurrency->id,
            'balance' => 50.00
        ]);

        $initialWalletBalance = $usdWallet->balance;

        // Create booking with account details payment
        $bookingData = [
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_type' => 'user',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->usdAccount->id,
            'payment_method' => 'account_details',
            'account_details' => 'Bank: Test Bank, Account: **********',
        ];

        $booking = $this->bookingService->createBooking($bookingData, $this->admin->id);

        // Complete the booking
        $this->bookingService->completeBooking($booking, $this->admin->id, 'Account details payment completed');

        // Verify wallet balance unchanged
        $usdWallet->refresh();
        $this->assertEquals($initialWalletBalance, $usdWallet->balance);

        // Verify no wallet transaction was created
        $transaction = Transaction::where('user_id', $this->user->id)
            ->where('currency_id', $this->usdCurrency->id)
            ->where('transactional_type', ForexBooking::class)
            ->where('transactional_id', $booking->id)
            ->first();

        $this->assertNull($transaction);
    }

    /** @test */
    public function external_client_booking_does_not_fund_wallet()
    {
        // Create booking for external client
        $bookingData = [
            'client_name' => 'External Client',
            'client_email' => '<EMAIL>',
            'client_type' => 'external',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->usdAccount->id,
            'payment_method' => 'account_details',
            'account_details' => 'Bank: External Bank, Account: **********',
        ];

        $booking = $this->bookingService->createBooking($bookingData, $this->admin->id);

        // Complete the booking
        $this->bookingService->completeBooking($booking, $this->admin->id, 'External client payment completed');

        // Verify no wallet transactions were created
        $transactionCount = Transaction::where('transactional_type', ForexBooking::class)
            ->where('transactional_id', $booking->id)
            ->count();

        $this->assertEquals(0, $transactionCount);
    }

    /** @test */
    public function booking_completion_handles_missing_wallet_gracefully()
    {
        // Create booking with wallet payment but user doesn't have wallet
        $bookingData = [
            'user_id' => $this->user->id,
            'client_name' => $this->user->firstname . ' ' . $this->user->lastname,
            'client_email' => $this->user->email,
            'client_type' => 'user',
            'transaction_type' => 'buying',
            'currency' => 'USD',
            'amount' => 100.00,
            'target_account_id' => $this->usdAccount->id,
            'payment_method' => 'wallet',
            'wallet_currency_id' => $this->usdCurrency->id,
        ];

        $booking = $this->bookingService->createBooking($bookingData, $this->admin->id);

        // Complete the booking - should not fail even without wallet
        $this->bookingService->completeBooking($booking, $this->admin->id, 'Completed without wallet');

        // Verify booking was completed
        $booking->refresh();
        $this->assertEquals('completed', $booking->status);

        // Verify wallet was created and funded
        $wallet = Wallet::where('user_id', $this->user->id)
            ->where('currency_id', $this->usdCurrency->id)
            ->first();

        $this->assertNotNull($wallet);
        $this->assertEquals(100.00, $wallet->balance);
    }
}
