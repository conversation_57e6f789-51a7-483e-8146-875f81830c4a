<?php

namespace Database\Seeders;

use App\Models\VirtualAccount;
use App\Models\User;
use Illuminate\Database\Seeder;

class VirtualAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some users first if they don't exist
        if (User::count() < 10) {
            User::factory(10)->create();
        }

        $users = User::take(10)->get();

        // Create a mix of virtual accounts
        foreach ($users as $index => $user) {
            // Create NGN customer account for first 5 users
            if ($index < 5) {
                VirtualAccount::factory()
                    ->ngn()
                    ->customer()
                    ->active()
                    ->create(['user_id' => $user->id]);
            }

            // Create USD individual account for next 3 users
            if ($index >= 3 && $index < 6) {
                VirtualAccount::factory()
                    ->usd()
                    ->individual()
                    ->active()
                    ->create(['user_id' => $user->id]);
            }

            // Create business accounts for last 2 users
            if ($index >= 8) {
                VirtualAccount::factory()
                    ->ngn()
                    ->business()
                    ->active()
                    ->create(['user_id' => $user->id]);
            }

            // Create some inactive accounts
            if ($index % 3 === 0) {
                VirtualAccount::factory()
                    ->ngn()
                    ->customer()
                    ->inactive()
                    ->create(['user_id' => $user->id]);
            }
        }

        // Create additional random accounts
        VirtualAccount::factory(15)->create();

        $this->command->info('Virtual accounts seeded successfully!');
        $this->command->info('Total accounts: ' . VirtualAccount::count());
        $this->command->info('Active accounts: ' . VirtualAccount::where('is_active', true)->count());
        $this->command->info('NGN accounts: ' . VirtualAccount::where('currency', 'NGN')->count());
        $this->command->info('USD accounts: ' . VirtualAccount::where('currency', 'USD')->count());
    }
}
