<?php

namespace App\Services;

use App\Models\ForexBooking;
use App\Models\ForexTransaction;
use App\Models\ForexAccount;
use App\Models\OperationalCost;
use App\Models\ForexReportTemplate;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ForexReportService
{
    /**
     * Generate daily report for CBN compliance
     */
    public function generateDailyReport(string $date): array
    {
        $startDate = $date . ' 00:00:00';
        $endDate = $date . ' 23:59:59';

        $bookings = ForexBooking::whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'targetAccount', 'initiatedBy'])
            ->get();

        $transactions = ForexTransaction::whereBetween('created_at', [$startDate, $endDate])
            ->with(['forexAccount', 'forexBooking', 'createdBy'])
            ->get();

        $summary = [
            'total_bookings' => $bookings->count(),
            'completed_bookings' => $bookings->where('status', 'completed')->count(),
            'pending_bookings' => $bookings->where('status', 'pending')->count(),
            'cancelled_bookings' => $bookings->where('status', 'cancelled')->count(),
            'total_usd_amount' => $bookings->where('currency', 'USD')->sum('amount'),
            'total_ngn_amount' => $bookings->where('currency', 'NGN')->sum('amount'),
            'total_cbn_equivalent' => $bookings->sum('cbn_total'),
            'total_parallel_equivalent' => $bookings->sum('parallel_total'),
            'total_difference' => $bookings->sum('difference_amount'),
            'total_transactions' => $transactions->count(),
            'credit_transactions' => $transactions->where('transaction_type', 'credit')->count(),
            'debit_transactions' => $transactions->where('transaction_type', 'debit')->count(),
        ];

        return [
            'date' => $date,
            'summary' => $summary,
            'bookings' => $bookings,
            'transactions' => $transactions,
        ];
    }

    /**
     * Generate weekly report
     */
    public function generateWeeklyReport(string $startDate, string $endDate): array
    {
        $bookings = ForexBooking::whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->with(['user', 'targetAccount', 'initiatedBy'])
            ->get();

        $transactions = ForexTransaction::whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->with(['forexAccount', 'forexBooking', 'createdBy'])
            ->get();

        $dailyBreakdown = [];
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $dayBookings = $bookings->whereBetween('created_at', [$currentDate . ' 00:00:00', $currentDate . ' 23:59:59']);
            $dailyBreakdown[$currentDate] = [
                'bookings_count' => $dayBookings->count(),
                'completed_count' => $dayBookings->where('status', 'completed')->count(),
                'total_usd' => $dayBookings->where('currency', 'USD')->sum('amount'),
                'total_ngn' => $dayBookings->where('currency', 'NGN')->sum('amount'),
            ];
            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }

        $summary = [
            'total_bookings' => $bookings->count(),
            'completed_bookings' => $bookings->where('status', 'completed')->count(),
            'total_usd_amount' => $bookings->where('currency', 'USD')->sum('amount'),
            'total_ngn_amount' => $bookings->where('currency', 'NGN')->sum('amount'),
            'total_difference' => $bookings->sum('difference_amount'),
            'average_daily_bookings' => round($bookings->count() / 7, 2),
        ];

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'summary' => $summary,
            'daily_breakdown' => $dailyBreakdown,
            'bookings' => $bookings,
            'transactions' => $transactions,
        ];
    }

    /**
     * Generate monthly report
     */
    public function generateMonthlyReport(int $month, int $year): array
    {
        $startDate = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
        $endDate = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));

        $bookings = ForexBooking::whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->with(['user', 'targetAccount', 'initiatedBy'])
            ->get();

        $transactions = ForexTransaction::whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->with(['forexAccount', 'forexBooking', 'createdBy'])
            ->get();

        $operationalCosts = OperationalCost::whereBetween('cost_date', [$startDate, $endDate])->get();

        $summary = [
            'total_bookings' => $bookings->count(),
            'completed_bookings' => $bookings->where('status', 'completed')->count(),
            'total_usd_amount' => $bookings->where('currency', 'USD')->sum('amount'),
            'total_ngn_amount' => $bookings->where('currency', 'NGN')->sum('amount'),
            'total_difference' => $bookings->sum('difference_amount'),
            'total_operational_costs' => $operationalCosts->sum('amount'),
            'net_revenue' => $bookings->sum('difference_amount') - $operationalCosts->sum('amount'),
        ];

        return [
            'month' => $month,
            'year' => $year,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'summary' => $summary,
            'bookings' => $bookings,
            'transactions' => $transactions,
            'operational_costs' => $operationalCosts,
        ];
    }

    /**
     * Generate annual report
     */
    public function generateAnnualReport(int $year): array
    {
        $startDate = $year . '-01-01';
        $endDate = $year . '-12-31';

        $bookings = ForexBooking::whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->with(['user', 'targetAccount', 'initiatedBy'])
            ->get();

        $operationalCosts = OperationalCost::whereBetween('cost_date', [$startDate, $endDate])->get();

        $monthlyBreakdown = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthStart = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
            $monthEnd = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));

            $monthBookings = $bookings->whereBetween('created_at', [$monthStart . ' 00:00:00', $monthEnd . ' 23:59:59']);
            $monthCosts = $operationalCosts->whereBetween('cost_date', [$monthStart, $monthEnd]);

            $monthlyBreakdown[date('F', mktime(0, 0, 0, $month, 1))] = [
                'bookings_count' => $monthBookings->count(),
                'total_revenue' => $monthBookings->sum('difference_amount'),
                'total_costs' => $monthCosts->sum('amount'),
                'net_profit' => $monthBookings->sum('difference_amount') - $monthCosts->sum('amount'),
            ];
        }

        $summary = [
            'total_bookings' => $bookings->count(),
            'completed_bookings' => $bookings->where('status', 'completed')->count(),
            'total_revenue' => $bookings->sum('difference_amount'),
            'total_operational_costs' => $operationalCosts->sum('amount'),
            'net_profit' => $bookings->sum('difference_amount') - $operationalCosts->sum('amount'),
            'average_monthly_bookings' => round($bookings->count() / 12, 2),
        ];

        return [
            'year' => $year,
            'summary' => $summary,
            'monthly_breakdown' => $monthlyBreakdown,
            'bookings' => $bookings,
            'operational_costs' => $operationalCosts,
        ];
    }

    /**
     * Generate revenue report
     */
    public function generateRevenueReport(string $startDate, string $endDate): array
    {
        $bookings = ForexBooking::completed()
            ->whereBetween('completed_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->get();

        $operationalCosts = OperationalCost::whereBetween('cost_date', [$startDate, $endDate])->get();

        $revenueByType = [
            'usd_to_ngn' => $bookings->where('currency', 'USD')->sum('difference_amount'),
            'ngn_to_usd' => $bookings->where('currency', 'NGN')->sum('difference_amount'),
        ];

        $costsByCategory = $operationalCosts->groupBy('category')->map(function ($costs) {
            return $costs->sum('amount');
        });

        $summary = [
            'total_revenue' => $bookings->sum('difference_amount'),
            'total_costs' => $operationalCosts->sum('amount'),
            'gross_profit' => $bookings->sum('difference_amount') - $operationalCosts->sum('amount'),
            'profit_margin' => $bookings->sum('difference_amount') > 0 ?
                round((($bookings->sum('difference_amount') - $operationalCosts->sum('amount')) / $bookings->sum('difference_amount')) * 100, 2) : 0,
        ];

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'summary' => $summary,
            'revenue_by_type' => $revenueByType,
            'costs_by_category' => $costsByCategory,
            'bookings' => $bookings,
            'operational_costs' => $operationalCosts,
        ];
    }

    /**
     * Generate operational cost report
     */
    public function generateOperationalCostReport(string $startDate, string $endDate, ?string $category = null): array
    {
        $query = OperationalCost::whereBetween('cost_date', [$startDate, $endDate]);

        if ($category) {
            $query->where('category', $category);
        }

        $costs = $query->with('recordedBy')->get();

        $summary = [
            'total_costs' => $costs->sum('amount'),
            'cost_count' => $costs->count(),
            'average_cost' => $costs->count() > 0 ? round($costs->sum('amount') / $costs->count(), 2) : 0,
        ];

        $costsByCategory = $costs->groupBy('category')->map(function ($categoryCosts) {
            return [
                'total' => $categoryCosts->sum('amount'),
                'count' => $categoryCosts->count(),
                'average' => round($categoryCosts->sum('amount') / $categoryCosts->count(), 2),
            ];
        });

        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'category' => $category,
            'summary' => $summary,
            'costs_by_category' => $costsByCategory,
            'costs' => $costs,
        ];
    }

    /**
     * Export daily report to Excel
     */
    public function exportDailyReportToExcel(array $reportData, string $date)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', 'Daily Forex Report - ' . date('M d, Y', strtotime($date)));
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Summary section
        $row = 3;
        $sheet->setCellValue('A' . $row, 'Summary');
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $row++;

        foreach ($reportData['summary'] as $key => $value) {
            $sheet->setCellValue('A' . $row, ucwords(str_replace('_', ' ', $key)));
            $sheet->setCellValue('B' . $row, is_numeric($value) ? number_format($value, 2) : $value);
            $row++;
        }

        // Bookings section
        $row += 2;
        $sheet->setCellValue('A' . $row, 'Bookings');
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $row++;

        // Headers
        $headers = ['Reference', 'Client', 'Type', 'Currency', 'Amount', 'Status', 'Created At'];
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . $row, $header);
            $sheet->getStyle($col . $row)->getFont()->setBold(true);
            $col++;
        }
        $row++;

        // Data
        foreach ($reportData['bookings'] as $booking) {
            $sheet->setCellValue('A' . $row, $booking->booking_reference);
            $sheet->setCellValue('B' . $row, $booking->client_name);
            $sheet->setCellValue('C' . $row, ucfirst($booking->transaction_type));
            $sheet->setCellValue('D' . $row, $booking->currency);
            $sheet->setCellValue('E' . $row, number_format($booking->amount, 2));
            $sheet->setCellValue('F' . $row, ucfirst($booking->status));
            $sheet->setCellValue('G' . $row, $booking->created_at->format('Y-m-d H:i:s'));
            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'daily_forex_report_' . $date . '.xlsx';

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Export weekly report to Excel
     */
    public function exportWeeklyReportToExcel(array $reportData, string $startDate, string $endDate)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Weekly Forex Report - ' . date('M d', strtotime($startDate)) . ' to ' . date('M d, Y', strtotime($endDate)));
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Summary and daily breakdown logic here...

        $writer = new Xlsx($spreadsheet);
        $filename = 'weekly_forex_report_' . $startDate . '_to_' . $endDate . '.xlsx';

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Export monthly report to Excel
     */
    public function exportMonthlyReportToExcel(array $reportData, int $month, int $year)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Monthly Forex Report - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year)));
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);

        $writer = new Xlsx($spreadsheet);
        $filename = 'monthly_forex_report_' . $year . '_' . str_pad($month, 2, '0', STR_PAD_LEFT) . '.xlsx';

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Export annual report to Excel
     */
    public function exportAnnualReportToExcel(array $reportData, int $year)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Annual Forex Report - ' . $year);
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);

        $writer = new Xlsx($spreadsheet);
        $filename = 'annual_forex_report_' . $year . '.xlsx';

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Export revenue report to Excel
     */
    public function exportRevenueReportToExcel(array $reportData, string $startDate, string $endDate)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Revenue Report - ' . date('M d', strtotime($startDate)) . ' to ' . date('M d, Y', strtotime($endDate)));
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);

        $writer = new Xlsx($spreadsheet);
        $filename = 'revenue_report_' . $startDate . '_to_' . $endDate . '.xlsx';

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    /**
     * Export operational cost report to Excel
     */
    public function exportOperationalCostReportToExcel(array $reportData, string $startDate, string $endDate)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Operational Cost Report - ' . date('M d', strtotime($startDate)) . ' to ' . date('M d, Y', strtotime($endDate)));
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);

        $writer = new Xlsx($spreadsheet);
        $filename = 'operational_cost_report_' . $startDate . '_to_' . $endDate . '.xlsx';

        return response()->streamDownload(function() use ($writer) {
            $writer->save('php://output');
        }, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }

    // PDF export methods would be similar but using a PDF library like TCPDF or DOMPDF
    public function exportDailyReportToPdf(array $reportData, string $date)
    {
        // PDF export implementation
        throw new \Exception('PDF export not implemented yet');
    }

    public function exportWeeklyReportToPdf(array $reportData, string $startDate, string $endDate)
    {
        throw new \Exception('PDF export not implemented yet');
    }

    public function exportMonthlyReportToPdf(array $reportData, int $month, int $year)
    {
        throw new \Exception('PDF export not implemented yet');
    }

    public function exportAnnualReportToPdf(array $reportData, int $year)
    {
        throw new \Exception('PDF export not implemented yet');
    }

    public function exportRevenueReportToPdf(array $reportData, string $startDate, string $endDate)
    {
        throw new \Exception('PDF export not implemented yet');
    }

    public function exportOperationalCostReportToPdf(array $reportData, string $startDate, string $endDate)
    {
        throw new \Exception('PDF export not implemented yet');
    }

    /**
     * Generate custom report based on user configuration
     */
    public function generateCustomReport(array $config): array
    {
        $reportType = $config['report_type'];
        $dateRangeType = $config['date_range_type'];

        // Get date range based on type
        $dateRange = $this->getDateRange($config);
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        // Base query for bookings
        $bookingsQuery = ForexBooking::whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->with(['user', 'targetAccount', 'initiatedBy']);

        // Apply filters
        if (!empty($config['transaction_type']) && $config['transaction_type'] !== 'both') {
            $bookingsQuery->where('transaction_type', $config['transaction_type']);
        }

        if (!empty($config['currency']) && $config['currency'] !== 'both') {
            $bookingsQuery->where('currency', $config['currency']);
        }

        if (!empty($config['client_type'])) {
            $bookingsQuery->where('client_type', $config['client_type']);
        }

        if (!empty($config['status'])) {
            $bookingsQuery->where('status', $config['status']);
        }

        // Amount range filters
        if (!empty($config['min_amount'])) {
            $bookingsQuery->where('amount', '>=', $config['min_amount']);
        }

        if (!empty($config['max_amount'])) {
            $bookingsQuery->where('amount', '<=', $config['max_amount']);
        }

        // Markup range filters
        if (!empty($config['min_markup'])) {
            $bookingsQuery->where('markup_percentage', '>=', $config['min_markup']);
        }

        if (!empty($config['max_markup'])) {
            $bookingsQuery->where('markup_percentage', '<=', $config['max_markup']);
        }

        // Apply sorting
        $sortBy = $config['sort_by'] ?? 'created_at';
        $sortDirection = $config['sort_direction'] ?? 'desc';
        $bookingsQuery->orderBy($sortBy, $sortDirection);

        // Apply limit if specified
        if (!empty($config['limit_results'])) {
            $bookingsQuery->limit($config['limit_results']);
        }

        $bookings = $bookingsQuery->get();

        // Apply grouping if specified
        if (!empty($config['group_by'])) {
            $bookings = $this->applyGrouping($bookings, $config['group_by']);
        }

        // Generate report based on type
        switch ($reportType) {
            case 'cbn_focused':
                return $this->generateCbnFocusedReport($bookings, $config, $startDate, $endDate);
            case 'volume':
                return $this->generateVolumeReport($bookings, $config, $startDate, $endDate);
            case 'performance':
                return $this->generatePerformanceReport($bookings, $config, $startDate, $endDate);
            case 'buy_transactions':
                return $this->generateBuyTransactionsReport($bookings, $config, $startDate, $endDate);
            case 'sell_transactions':
                return $this->generateSellTransactionsReport($bookings, $config, $startDate, $endDate);
            default:
                return $this->generateBookingsReport($bookings, $config, $startDate, $endDate);
        }
    }

    /**
     * Get date range based on configuration
     */
    private function getDateRange(array $config): array
    {
        switch ($config['date_range_type']) {
            case 'daily':
                $date = $config['specific_date'];
                return ['start' => $date, 'end' => $date];

            case 'weekly':
                return ['start' => $config['start_date'], 'end' => $config['end_date']];

            case 'monthly':
                $month = $config['month'];
                $year = $config['year'];
                $startDate = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
                $endDate = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));
                return ['start' => $startDate, 'end' => $endDate];

            case 'custom':
                return ['start' => $config['start_date'], 'end' => $config['end_date']];

            default:
                return ['start' => date('Y-m-01'), 'end' => date('Y-m-d')];
        }
    }

    /**
     * Generate CBN-focused report (excludes parallel rates)
     */
    private function generateCbnFocusedReport($bookings, $config, $startDate, $endDate): array
    {
        // Calculate CBN-specific metrics
        $completedBookings = $bookings->where('status', 'completed');
        $buyingTransactions = $bookings->where('transaction_type', 'buying');
        $sellingTransactions = $bookings->where('transaction_type', 'selling');

        $summary = [
            'total_bookings' => $bookings->count(),
            'completed_bookings' => $completedBookings->count(),
            'pending_bookings' => $bookings->where('status', 'pending')->count(),
            'cancelled_bookings' => $bookings->where('status', 'cancelled')->count(),

            // Amount breakdowns
            'total_amount_usd' => $bookings->where('currency', 'USD')->sum('amount'),
            'total_amount_ngn' => $bookings->where('currency', 'NGN')->sum('amount'),
            'completed_amount_usd' => $completedBookings->where('currency', 'USD')->sum('amount'),
            'completed_amount_ngn' => $completedBookings->where('currency', 'NGN')->sum('amount'),

            // CBN Rate Analysis
            'cbn_rate_range' => [
                'min' => $bookings->min('cbn_rate'),
                'max' => $bookings->max('cbn_rate'),
                'avg' => round($bookings->avg('cbn_rate'), 2),
                'current' => $bookings->sortByDesc('created_at')->first()?->cbn_rate ?? 0,
            ],

            // Markup Analysis (CBN-focused)
            'markup_analysis' => [
                'total_markup_amount' => $completedBookings->sum('markup_amount'),
                'average_markup_percentage' => round($bookings->avg('markup_percentage'), 2),
                'min_markup_percentage' => $bookings->min('markup_percentage'),
                'max_markup_percentage' => $bookings->max('markup_percentage'),
                'markup_revenue_usd' => $completedBookings->where('currency', 'USD')->sum('markup_amount'),
                'markup_revenue_ngn' => $completedBookings->where('currency', 'NGN')->sum('markup_amount'),
            ],

            // CBN Total Analysis
            'cbn_totals' => [
                'total_cbn_value' => $bookings->sum('cbn_total'),
                'completed_cbn_value' => $completedBookings->sum('cbn_total'),
                'buying_cbn_total' => $buyingTransactions->sum('cbn_total'),
                'selling_cbn_total' => $sellingTransactions->sum('cbn_total'),
            ],

            // Transaction Type Breakdown
            'transaction_breakdown' => [
                'buying_count' => $buyingTransactions->count(),
                'selling_count' => $sellingTransactions->count(),
                'buying_percentage' => $bookings->count() > 0 ? round(($buyingTransactions->count() / $bookings->count()) * 100, 2) : 0,
                'selling_percentage' => $bookings->count() > 0 ? round(($sellingTransactions->count() / $bookings->count()) * 100, 2) : 0,
            ],

            // Compliance Metrics
            'compliance_metrics' => [
                'completion_rate' => $bookings->count() > 0 ? round(($completedBookings->count() / $bookings->count()) * 100, 2) : 0,
                'average_processing_time' => $this->calculateAverageProcessingTime($completedBookings),
                'cbn_rate_stability' => $this->calculateRateStability($bookings),
                'markup_consistency' => $this->calculateMarkupConsistency($bookings),
            ],
        ];

        // Add advanced statistics if requested
        if (!empty($config['include_advanced_stats'])) {
            $summary['advanced_statistics'] = $this->calculateAdvancedStatistics($bookings, $config);
        }

        return [
            'report_type' => 'cbn_focused',
            'title' => 'CBN Compliance & Rate Analysis Report',
            'subtitle' => 'Regulatory Compliance Report - Excludes Parallel Market Data',
            'period' => $startDate . ' to ' . $endDate,
            'summary' => $summary,
            'bookings' => $bookings,
            'config' => $config,
            'compliance_notes' => [
                'This report focuses exclusively on CBN official rates and approved markup percentages.',
                'Parallel market rates are excluded from all calculations for regulatory compliance.',
                'All amounts are calculated using official CBN exchange rates only.',
                'Markup percentages reflect approved spreads over CBN rates.',
            ],
        ];
    }

    /**
     * Generate transaction volume report
     */
    private function generateVolumeReport($bookings, $config, $startDate, $endDate): array
    {
        $buyTransactions = $bookings->where('transaction_type', 'buying');
        $sellTransactions = $bookings->where('transaction_type', 'selling');
        $completedBookings = $bookings->where('status', 'completed');
        $completedBuying = $completedBookings->where('transaction_type', 'buying');
        $completedSelling = $completedBookings->where('transaction_type', 'selling');

        // Calculate volume metrics
        $totalVolumeUSD = $bookings->where('currency', 'USD')->sum('amount');
        $totalVolumeNGN = $bookings->where('currency', 'NGN')->sum('amount');
        $completedVolumeUSD = $completedBookings->where('currency', 'USD')->sum('amount');
        $completedVolumeNGN = $completedBookings->where('currency', 'NGN')->sum('amount');

        // Calculate daily/hourly volume patterns
        $volumeByDay = $this->calculateVolumeByPeriod($bookings, 'day');
        $volumeByHour = $this->calculateVolumeByPeriod($bookings, 'hour');
        $volumeByWeek = $this->calculateVolumeByPeriod($bookings, 'week');

        // Calculate client volume distribution
        $clientVolumeDistribution = $this->calculateClientVolumeDistribution($bookings);

        // Calculate transaction size distribution
        $transactionSizeDistribution = $this->calculateTransactionSizeDistribution($bookings);

        $summary = [
            // Total Volume Metrics
            'total_volume_usd' => $totalVolumeUSD,
            'total_volume_ngn' => $totalVolumeNGN,
            'completed_volume_usd' => $completedVolumeUSD,
            'completed_volume_ngn' => $completedVolumeNGN,
            'pending_volume_usd' => $totalVolumeUSD - $completedVolumeUSD,
            'pending_volume_ngn' => $totalVolumeNGN - $completedVolumeNGN,

            // Buy/Sell Volume Breakdown
            'buy_volume_usd' => $buyTransactions->where('currency', 'USD')->sum('amount'),
            'buy_volume_ngn' => $buyTransactions->where('currency', 'NGN')->sum('amount'),
            'sell_volume_usd' => $sellTransactions->where('currency', 'USD')->sum('amount'),
            'sell_volume_ngn' => $sellTransactions->where('currency', 'NGN')->sum('amount'),

            // Completed Buy/Sell Volume
            'completed_buy_volume_usd' => $completedBuying->where('currency', 'USD')->sum('amount'),
            'completed_buy_volume_ngn' => $completedBuying->where('currency', 'NGN')->sum('amount'),
            'completed_sell_volume_usd' => $completedSelling->where('currency', 'USD')->sum('amount'),
            'completed_sell_volume_ngn' => $completedSelling->where('currency', 'NGN')->sum('amount'),

            // Transaction Counts
            'total_transactions' => $bookings->count(),
            'completed_transactions' => $completedBookings->count(),
            'buy_count' => $buyTransactions->count(),
            'sell_count' => $sellTransactions->count(),
            'completed_buy_count' => $completedBuying->count(),
            'completed_sell_count' => $completedSelling->count(),

            // Average Transaction Sizes
            'average_transaction_size_usd' => $bookings->where('currency', 'USD')->avg('amount'),
            'average_transaction_size_ngn' => $bookings->where('currency', 'NGN')->avg('amount'),
            'average_buy_size_usd' => $buyTransactions->where('currency', 'USD')->avg('amount'),
            'average_sell_size_usd' => $sellTransactions->where('currency', 'USD')->avg('amount'),

            // Volume Percentages
            'buy_volume_percentage' => $totalVolumeUSD > 0 ? round(($buyTransactions->where('currency', 'USD')->sum('amount') / $totalVolumeUSD) * 100, 2) : 0,
            'sell_volume_percentage' => $totalVolumeUSD > 0 ? round(($sellTransactions->where('currency', 'USD')->sum('amount') / $totalVolumeUSD) * 100, 2) : 0,
            'completion_rate_by_volume' => $totalVolumeUSD > 0 ? round(($completedVolumeUSD / $totalVolumeUSD) * 100, 2) : 0,

            // Peak Performance Metrics
            'peak_daily_volume' => $volumeByDay->max('total_volume_usd'),
            'peak_hourly_volume' => $volumeByHour->max('total_volume_usd'),
            'average_daily_volume' => $volumeByDay->avg('total_volume_usd'),
            'volume_growth_rate' => $this->calculateVolumeGrowthRate($volumeByDay),

            // Client Distribution
            'top_clients_by_volume' => $clientVolumeDistribution->take(10),
            'client_concentration_ratio' => $this->calculateClientConcentrationRatio($clientVolumeDistribution),

            // Transaction Size Analysis
            'transaction_size_distribution' => $transactionSizeDistribution,
            'median_transaction_size' => $this->calculateMedianTransactionSize($bookings),
            'large_transaction_threshold' => 10000, // $10,000 USD
            'large_transactions_count' => $bookings->where('amount', '>', 10000)->count(),
            'large_transactions_volume' => $bookings->where('amount', '>', 10000)->sum('amount'),

            // Time-based Analysis
            'volume_by_day' => $volumeByDay,
            'volume_by_hour' => $volumeByHour,
            'volume_by_week' => $volumeByWeek,
            'busiest_day' => $volumeByDay->sortByDesc('total_volume_usd')->first(),
            'busiest_hour' => $volumeByHour->sortByDesc('total_volume_usd')->first(),
        ];

        return [
            'report_type' => 'volume',
            'title' => 'Transaction Volume Analysis Report',
            'subtitle' => 'Comprehensive Volume Analytics with NGN and USD Breakdowns',
            'period' => $startDate . ' to ' . $endDate,
            'summary' => $summary,
            'bookings' => $bookings,
            'buy_transactions' => $buyTransactions,
            'sell_transactions' => $sellTransactions,
            'config' => $config,
        ];
    }

    /**
     * Generate monthly performance report
     */
    private function generatePerformanceReport($bookings, $config, $startDate, $endDate): array
    {
        $completedBookings = $bookings->where('status', 'completed');
        $buyingTransactions = $completedBookings->where('transaction_type', 'buying');
        $sellingTransactions = $completedBookings->where('transaction_type', 'selling');

        // Calculate performance metrics
        $totalRevenue = $completedBookings->sum('markup_amount');
        $totalVolume = $completedBookings->sum('amount');
        $totalCustomerPayments = $completedBookings->sum('customer_payment_amount');

        // Calculate performance by time periods
        $performanceByDay = $this->calculatePerformanceByPeriod($completedBookings, 'day');
        $performanceByWeek = $this->calculatePerformanceByPeriod($completedBookings, 'week');
        $performanceByMonth = $this->calculatePerformanceByPeriod($completedBookings, 'month');

        // Calculate client performance metrics
        $clientPerformance = $this->calculateClientPerformanceMetrics($completedBookings);

        // Calculate efficiency metrics
        $efficiencyMetrics = $this->calculateEfficiencyMetrics($bookings, $completedBookings);

        // Calculate profitability metrics
        $profitabilityMetrics = $this->calculateProfitabilityMetrics($completedBookings);

        $summary = [
            // Revenue Metrics
            'total_revenue' => $totalRevenue,
            'revenue_usd' => $completedBookings->where('currency', 'USD')->sum('markup_amount'),
            'revenue_ngn' => $completedBookings->where('currency', 'NGN')->sum('markup_amount'),
            'average_revenue_per_transaction' => $completedBookings->count() > 0 ? $totalRevenue / $completedBookings->count() : 0,
            'revenue_growth_rate' => $this->calculateRevenueGrowthRate($performanceByDay),

            // Revenue by Transaction Type
            'revenue_by_type' => [
                'buying' => $buyingTransactions->sum('markup_amount'),
                'selling' => $sellingTransactions->sum('markup_amount'),
                'buying_percentage' => $totalRevenue > 0 ? round(($buyingTransactions->sum('markup_amount') / $totalRevenue) * 100, 2) : 0,
                'selling_percentage' => $totalRevenue > 0 ? round(($sellingTransactions->sum('markup_amount') / $totalRevenue) * 100, 2) : 0,
            ],

            // Volume and Transaction Metrics
            'total_transactions' => $completedBookings->count(),
            'total_volume_processed' => $totalVolume,
            'average_transaction_size' => $completedBookings->avg('amount'),
            'total_customer_payments' => $totalCustomerPayments,

            // Conversion and Efficiency Metrics
            'conversion_rate' => $bookings->count() > 0 ? round(($completedBookings->count() / $bookings->count()) * 100, 2) : 0,
            'completion_rate_by_volume' => $bookings->sum('amount') > 0 ? round(($completedBookings->sum('amount') / $bookings->sum('amount')) * 100, 2) : 0,
            'average_processing_time' => $this->calculateAverageProcessingTime($completedBookings),
            'efficiency_score' => $efficiencyMetrics['overall_efficiency'],

            // Markup Performance
            'markup_performance' => [
                'average_markup_percentage' => round($completedBookings->avg('markup_percentage'), 2),
                'min_markup_percentage' => $completedBookings->min('markup_percentage'),
                'max_markup_percentage' => $completedBookings->max('markup_percentage'),
                'markup_consistency' => $this->calculateMarkupConsistency($completedBookings),
                'optimal_markup_range' => $this->calculateOptimalMarkupRange($completedBookings),
            ],

            // Profitability Analysis
            'profitability_metrics' => $profitabilityMetrics,
            'profit_margin' => $totalCustomerPayments > 0 ? round(($totalRevenue / $totalCustomerPayments) * 100, 2) : 0,
            'revenue_per_dollar_processed' => $totalVolume > 0 ? round($totalRevenue / $totalVolume, 4) : 0,

            // Client Performance
            'client_metrics' => [
                'total_unique_clients' => $completedBookings->pluck('client_name')->unique()->count(),
                'average_revenue_per_client' => $clientPerformance['average_revenue_per_client'],
                'top_revenue_clients' => $clientPerformance['top_clients']->take(10),
                'client_retention_indicators' => $clientPerformance['retention_indicators'],
            ],

            // Time-based Performance
            'performance_trends' => [
                'daily_performance' => $performanceByDay,
                'weekly_performance' => $performanceByWeek,
                'monthly_performance' => $performanceByMonth,
                'best_performing_day' => $performanceByDay->sortByDesc('revenue')->first(),
                'best_performing_week' => $performanceByWeek->sortByDesc('revenue')->first(),
            ],

            // Operational Efficiency
            'operational_metrics' => $efficiencyMetrics,
            'peak_performance_indicators' => [
                'highest_daily_revenue' => $performanceByDay->max('revenue'),
                'highest_daily_volume' => $performanceByDay->max('volume'),
                'most_transactions_day' => $performanceByDay->max('transaction_count'),
                'best_conversion_rate_day' => $performanceByDay->max('conversion_rate'),
            ],

            // Forecasting Indicators
            'forecasting_metrics' => [
                'revenue_trend' => $this->calculateTrendDirection($performanceByDay, 'revenue'),
                'volume_trend' => $this->calculateTrendDirection($performanceByDay, 'volume'),
                'efficiency_trend' => $this->calculateTrendDirection($performanceByDay, 'efficiency'),
                'projected_monthly_revenue' => $this->projectMonthlyRevenue($performanceByDay),
            ],
        ];

        return [
            'report_type' => 'performance',
            'title' => 'Performance Analytics Report',
            'subtitle' => 'Comprehensive Performance Analysis with Revenue and Efficiency Metrics',
            'period' => $startDate . ' to ' . $endDate,
            'summary' => $summary,
            'bookings' => $bookings,
            'completed_bookings' => $completedBookings,
            'config' => $config,
        ];
    }

    /**
     * Generate buy transactions report (NGN to USD)
     */
    private function generateBuyTransactionsReport($bookings, $config, $startDate, $endDate): array
    {
        $buyTransactions = $bookings->where('transaction_type', 'buying');

        $summary = [
            'total_buy_transactions' => $buyTransactions->count(),
            'completed_buy_transactions' => $buyTransactions->where('status', 'completed')->count(),
            'total_usd_bought' => $buyTransactions->where('currency', 'USD')->sum('amount'),
            'total_ngn_spent' => $buyTransactions->where('currency', 'NGN')->sum('customer_payment_amount'),
            'average_buy_amount' => $buyTransactions->avg('amount'),
            'total_markup_earned' => $buyTransactions->sum('markup_amount'),
        ];

        return [
            'report_type' => 'buy_transactions',
            'title' => 'Buy Transactions Report (NGN to USD)',
            'period' => $startDate . ' to ' . $endDate,
            'summary' => $summary,
            'bookings' => $buyTransactions,
            'config' => $config,
        ];
    }

    /**
     * Generate sell transactions report (USD to NGN)
     */
    private function generateSellTransactionsReport($bookings, $config, $startDate, $endDate): array
    {
        $sellTransactions = $bookings->where('transaction_type', 'selling');

        $summary = [
            'total_sell_transactions' => $sellTransactions->count(),
            'completed_sell_transactions' => $sellTransactions->where('status', 'completed')->count(),
            'total_usd_sold' => $sellTransactions->where('currency', 'USD')->sum('amount'),
            'total_ngn_paid' => $sellTransactions->where('currency', 'NGN')->sum('customer_payment_amount'),
            'average_sell_amount' => $sellTransactions->avg('amount'),
            'total_markup_earned' => $sellTransactions->sum('markup_amount'),
        ];

        return [
            'report_type' => 'sell_transactions',
            'title' => 'Sell Transactions Report (USD to NGN)',
            'period' => $startDate . ' to ' . $endDate,
            'summary' => $summary,
            'bookings' => $sellTransactions,
            'config' => $config,
        ];
    }

    /**
     * Generate general bookings report
     */
    private function generateBookingsReport($bookings, $config, $startDate, $endDate): array
    {
        $summary = [
            'total_bookings' => $bookings->count(),
            'completed_bookings' => $bookings->where('status', 'completed')->count(),
            'pending_bookings' => $bookings->where('status', 'pending')->count(),
            'cancelled_bookings' => $bookings->where('status', 'cancelled')->count(),
            'total_amount_usd' => $bookings->where('currency', 'USD')->sum('amount'),
            'total_amount_ngn' => $bookings->where('currency', 'NGN')->sum('amount'),
            'total_revenue' => $bookings->where('status', 'completed')->sum('markup_amount'),
        ];

        return [
            'report_type' => 'bookings',
            'title' => 'Bookings Report',
            'period' => $startDate . ' to ' . $endDate,
            'summary' => $summary,
            'bookings' => $bookings,
            'config' => $config,
        ];
    }

    /**
     * Export custom report to specified format
     */
    public function exportCustomReport(array $reportData, array $config, string $format)
    {
        switch ($format) {
            case 'excel':
                return $this->exportCustomReportToExcel($reportData, $config);
            case 'pdf':
                return $this->exportCustomReportToPdf($reportData, $config);
            case 'csv':
                return $this->exportCustomReportToCsv($reportData, $config);
            default:
                throw new \InvalidArgumentException('Unsupported export format: ' . $format);
        }
    }

    /**
     * Export custom report to Excel
     */
    private function exportCustomReportToExcel(array $reportData, array $config)
    {
        // Implementation for Excel export would go here
        // For now, return a simple response
        return response()->json(['message' => 'Excel export not yet implemented']);
    }

    /**
     * Export custom report to PDF
     */
    private function exportCustomReportToPdf(array $reportData, array $config)
    {
        // Implementation for PDF export would go here
        // For now, return a simple response
        return response()->json(['message' => 'PDF export not yet implemented']);
    }

    /**
     * Export custom report to CSV
     */
    private function exportCustomReportToCsv(array $reportData, array $config)
    {
        // Implementation for CSV export would go here
        // For now, return a simple response
        return response()->json(['message' => 'CSV export not yet implemented']);
    }

    /**
     * Apply grouping to bookings collection
     */
    private function applyGrouping($bookings, string $groupBy)
    {
        switch ($groupBy) {
            case 'status':
                return $bookings->groupBy('status');
            case 'transaction_type':
                return $bookings->groupBy('transaction_type');
            case 'client_type':
                return $bookings->groupBy('client_type');
            case 'currency':
                return $bookings->groupBy('currency');
            case 'date':
                return $bookings->groupBy(function ($booking) {
                    return $booking->created_at->format('Y-m-d');
                });
            case 'week':
                return $bookings->groupBy(function ($booking) {
                    return $booking->created_at->format('Y-W');
                });
            case 'month':
                return $bookings->groupBy(function ($booking) {
                    return $booking->created_at->format('Y-m');
                });
            case 'target_account':
                return $bookings->groupBy('target_account_id');
            default:
                return $bookings;
        }
    }

    /**
     * Calculate advanced statistics for report data
     */
    private function calculateAdvancedStatistics($bookings, array $config): array
    {
        $stats = [];

        if (!empty($config['include_totals'])) {
            $stats['totals'] = [
                'total_amount' => $bookings->sum('amount'),
                'total_customer_payment' => $bookings->sum('customer_payment_amount'),
                'total_markup' => $bookings->sum('markup_amount'),
                'total_records' => $bookings->count(),
            ];
        }

        if (!empty($config['include_averages'])) {
            $stats['averages'] = [
                'avg_amount' => $bookings->avg('amount'),
                'avg_markup_percentage' => $bookings->avg('markup_percentage'),
                'avg_customer_payment' => $bookings->avg('customer_payment_amount'),
            ];
        }

        if (!empty($config['include_min_max'])) {
            $stats['ranges'] = [
                'amount_range' => [
                    'min' => $bookings->min('amount'),
                    'max' => $bookings->max('amount'),
                ],
                'markup_range' => [
                    'min' => $bookings->min('markup_percentage'),
                    'max' => $bookings->max('markup_percentage'),
                ],
            ];
        }

        if (!empty($config['include_percentages'])) {
            $total = $bookings->count();
            $stats['percentages'] = [
                'completed_percentage' => $total > 0 ? ($bookings->where('status', 'completed')->count() / $total) * 100 : 0,
                'pending_percentage' => $total > 0 ? ($bookings->where('status', 'pending')->count() / $total) * 100 : 0,
                'cancelled_percentage' => $total > 0 ? ($bookings->where('status', 'cancelled')->count() / $total) * 100 : 0,
            ];
        }

        if (!empty($config['include_conversion_rates'])) {
            $totalBookings = $bookings->count();
            $completedBookings = $bookings->where('status', 'completed')->count();
            $stats['conversion_rate'] = $totalBookings > 0 ? ($completedBookings / $totalBookings) * 100 : 0;
        }

        return $stats;
    }

    /**
     * Save report configuration as template
     */
    public function saveReportTemplate(array $config, string $name, string $description = '', bool $isPublic = false, ?int $createdBy = null): ForexReportTemplate
    {
        return ForexReportTemplate::create([
            'name' => $name,
            'description' => $description,
            'report_type' => $config['report_type'],
            'configuration' => $config,
            'is_public' => $isPublic,
            'created_by' => $createdBy,
        ]);
    }

    /**
     * Load report configuration from template
     */
    public function loadReportTemplate(int $templateId): array
    {
        $template = ForexReportTemplate::findOrFail($templateId);
        $template->incrementUsage();

        return $template->configuration;
    }

    /**
     * Get available report templates
     */
    public function getReportTemplates(bool $publicOnly = false): \Illuminate\Database\Eloquent\Collection
    {
        $query = ForexReportTemplate::query();

        if ($publicOnly) {
            $query->public();
        }

        return $query->popular()->get();
    }

    /**
     * Calculate average processing time for completed bookings
     */
    private function calculateAverageProcessingTime($completedBookings): float
    {
        if ($completedBookings->count() === 0) {
            return 0;
        }

        $totalHours = 0;
        $count = 0;

        foreach ($completedBookings as $booking) {
            if ($booking->completed_at && $booking->created_at) {
                $hours = $booking->created_at->diffInHours($booking->completed_at);
                $totalHours += $hours;
                $count++;
            }
        }

        return $count > 0 ? round($totalHours / $count, 2) : 0;
    }

    /**
     * Calculate CBN rate stability (coefficient of variation)
     */
    private function calculateRateStability($bookings): float
    {
        $rates = $bookings->pluck('cbn_rate')->filter()->values();

        if ($rates->count() < 2) {
            return 100; // Perfect stability if only one rate or no rates
        }

        $mean = $rates->avg();
        $variance = $rates->map(function ($rate) use ($mean) {
            return pow($rate - $mean, 2);
        })->avg();

        $standardDeviation = sqrt($variance);
        $coefficientOfVariation = $mean > 0 ? ($standardDeviation / $mean) * 100 : 0;

        // Return stability percentage (100 - coefficient of variation)
        return round(max(0, 100 - $coefficientOfVariation), 2);
    }

    /**
     * Calculate markup consistency
     */
    private function calculateMarkupConsistency($bookings): float
    {
        $markups = $bookings->pluck('markup_percentage')->filter()->values();

        if ($markups->count() < 2) {
            return 100; // Perfect consistency if only one markup or no markups
        }

        $mean = $markups->avg();
        $variance = $markups->map(function ($markup) use ($mean) {
            return pow($markup - $mean, 2);
        })->avg();

        $standardDeviation = sqrt($variance);
        $coefficientOfVariation = $mean > 0 ? ($standardDeviation / $mean) * 100 : 0;

        // Return consistency percentage (100 - coefficient of variation)
        return round(max(0, 100 - $coefficientOfVariation), 2);
    }

    /**
     * Create default CBN-focused templates
     */
    public function createCbnFocusedTemplates(): void
    {
        $templates = [
            [
                'name' => 'CBN Daily Compliance Report',
                'description' => 'Daily CBN compliance report with official rates and markup analysis',
                'report_type' => 'cbn_focused',
                'configuration' => [
                    'report_type' => 'cbn_focused',
                    'date_range_type' => 'daily',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'transaction_type', 'currency',
                        'amount', 'cbn_rate', 'markup_percentage', 'customer_rate',
                        'cbn_total', 'markup_amount', 'status', 'created_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'transaction_type' => 'Type',
                        'currency' => 'Currency',
                        'amount' => 'Amount',
                        'cbn_rate' => 'CBN Rate',
                        'markup_percentage' => 'Markup %',
                        'customer_rate' => 'Final Rate',
                        'cbn_total' => 'CBN Total',
                        'markup_amount' => 'Markup Revenue',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'sort_by' => 'created_at',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true,
                    'include_averages' => true,
                    'include_advanced_stats' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'CBN Weekly Summary',
                'description' => 'Weekly CBN rate analysis and markup performance summary',
                'report_type' => 'cbn_focused',
                'configuration' => [
                    'report_type' => 'cbn_focused',
                    'date_range_type' => 'weekly',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'amount', 'cbn_rate',
                        'markup_percentage', 'markup_amount', 'status', 'created_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'amount' => 'Amount',
                        'cbn_rate' => 'CBN Rate',
                        'markup_percentage' => 'Markup %',
                        'markup_amount' => 'Revenue',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'group_by' => 'date',
                    'sort_by' => 'created_at',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_growth_rates' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'CBN Monthly Compliance Report',
                'description' => 'Comprehensive monthly CBN compliance and regulatory report',
                'report_type' => 'cbn_focused',
                'configuration' => [
                    'report_type' => 'cbn_focused',
                    'date_range_type' => 'monthly',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'client_type', 'transaction_type',
                        'currency', 'amount', 'cbn_rate', 'markup_percentage', 'customer_rate',
                        'cbn_total', 'markup_amount', 'status', 'created_at', 'completed_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'client_type' => 'Client Type',
                        'transaction_type' => 'Transaction',
                        'currency' => 'Currency',
                        'amount' => 'Amount',
                        'cbn_rate' => 'CBN Rate',
                        'markup_percentage' => 'Markup %',
                        'customer_rate' => 'Final Rate',
                        'cbn_total' => 'CBN Total',
                        'markup_amount' => 'Revenue',
                        'status' => 'Status',
                        'created_at' => 'Created',
                        'completed_at' => 'Completed'
                    ],
                    'group_by' => 'transaction_type',
                    'sort_by' => 'amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_percentages' => true,
                    'include_conversion_rates' => true,
                    'include_advanced_stats' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'CBN Rate Analysis Only',
                'description' => 'Pure CBN rate analysis without parallel market data',
                'report_type' => 'cbn_focused',
                'configuration' => [
                    'report_type' => 'cbn_focused',
                    'date_range_type' => 'custom',
                    'selected_fields' => [
                        'booking_reference', 'amount', 'cbn_rate', 'markup_percentage',
                        'customer_rate', 'cbn_total', 'created_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'amount' => 'Amount',
                        'cbn_rate' => 'CBN Official Rate',
                        'markup_percentage' => 'Approved Markup %',
                        'customer_rate' => 'Customer Rate',
                        'cbn_total' => 'CBN Calculation',
                        'created_at' => 'Date'
                    ],
                    'sort_by' => 'cbn_rate',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_min_max' => true,
                    'include_averages' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'CBN Markup Performance',
                'description' => 'CBN-based markup performance and revenue analysis',
                'report_type' => 'cbn_focused',
                'configuration' => [
                    'report_type' => 'cbn_focused',
                    'date_range_type' => 'monthly',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'transaction_type', 'amount',
                        'cbn_rate', 'markup_percentage', 'markup_amount', 'customer_payment_amount', 'status'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'transaction_type' => 'Type',
                        'amount' => 'Amount',
                        'cbn_rate' => 'CBN Rate',
                        'markup_percentage' => 'Markup %',
                        'markup_amount' => 'Markup Revenue',
                        'customer_payment_amount' => 'Customer Payment',
                        'status' => 'Status'
                    ],
                    'group_by' => 'status',
                    'sort_by' => 'markup_amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_percentages' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Transaction Volume Analytics',
                'description' => 'Comprehensive volume analysis with NGN/USD breakdowns and client distribution',
                'report_type' => 'volume',
                'configuration' => [
                    'report_type' => 'volume',
                    'date_range_type' => 'monthly',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'transaction_type', 'currency',
                        'amount', 'customer_total', 'status', 'created_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'transaction_type' => 'Type',
                        'currency' => 'Currency',
                        'amount' => 'Amount',
                        'customer_total' => 'Total',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'group_by' => 'transaction_type',
                    'sort_by' => 'amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_volume_analytics' => true,
                    'include_client_distribution' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Performance Analytics Dashboard',
                'description' => 'Advanced performance metrics with revenue analysis and efficiency tracking',
                'report_type' => 'performance',
                'configuration' => [
                    'report_type' => 'performance',
                    'date_range_type' => 'monthly',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'transaction_type', 'amount',
                        'markup_percentage', 'markup_amount', 'customer_payment_amount',
                        'status', 'created_at', 'completed_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'transaction_type' => 'Type',
                        'amount' => 'Amount',
                        'markup_percentage' => 'Markup %',
                        'markup_amount' => 'Revenue',
                        'customer_payment_amount' => 'Payment',
                        'status' => 'Status',
                        'created_at' => 'Created',
                        'completed_at' => 'Completed'
                    ],
                    'group_by' => 'status',
                    'sort_by' => 'markup_amount',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_performance_analytics' => true,
                    'include_efficiency_metrics' => true,
                    'include_forecasting' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Daily Volume Tracker',
                'description' => 'Daily transaction volume tracking with hourly breakdowns',
                'report_type' => 'volume',
                'configuration' => [
                    'report_type' => 'volume',
                    'date_range_type' => 'daily',
                    'selected_fields' => [
                        'booking_reference', 'transaction_type', 'currency', 'amount',
                        'customer_total', 'status', 'created_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'transaction_type' => 'Type',
                        'currency' => 'Currency',
                        'amount' => 'Amount',
                        'customer_total' => 'Total',
                        'status' => 'Status',
                        'created_at' => 'Time'
                    ],
                    'group_by' => 'hour',
                    'sort_by' => 'created_at',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_counts' => true
                ],
                'is_public' => true
            ],
            [
                'name' => 'Weekly Performance Summary',
                'description' => 'Weekly performance summary with efficiency and profitability metrics',
                'report_type' => 'performance',
                'configuration' => [
                    'report_type' => 'performance',
                    'date_range_type' => 'weekly',
                    'selected_fields' => [
                        'booking_reference', 'client_name', 'amount', 'markup_percentage',
                        'markup_amount', 'status', 'created_at'
                    ],
                    'field_labels' => [
                        'booking_reference' => 'Reference',
                        'client_name' => 'Client',
                        'amount' => 'Amount',
                        'markup_percentage' => 'Markup %',
                        'markup_amount' => 'Revenue',
                        'status' => 'Status',
                        'created_at' => 'Date'
                    ],
                    'group_by' => 'date',
                    'sort_by' => 'created_at',
                    'sort_direction' => 'desc',
                    'include_totals' => true,
                    'include_averages' => true,
                    'include_growth_rates' => true
                ],
                'is_public' => true
            ]
        ];

        foreach ($templates as $template) {
            ForexReportTemplate::updateOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }

    /**
     * Calculate volume by time period
     */
    private function calculateVolumeByPeriod($bookings, string $period): \Illuminate\Support\Collection
    {
        return $bookings->groupBy(function ($booking) use ($period) {
            switch ($period) {
                case 'hour':
                    return $booking->created_at->format('Y-m-d H:00');
                case 'day':
                    return $booking->created_at->format('Y-m-d');
                case 'week':
                    return $booking->created_at->format('Y-W');
                case 'month':
                    return $booking->created_at->format('Y-m');
                default:
                    return $booking->created_at->format('Y-m-d');
            }
        })->map(function ($periodBookings, $periodKey) {
            $usdBookings = $periodBookings->where('currency', 'USD');
            $ngnBookings = $periodBookings->where('currency', 'NGN');

            return [
                'period' => $periodKey,
                'total_volume_usd' => $usdBookings->sum('amount'),
                'total_volume_ngn' => $ngnBookings->sum('amount'),
                'transaction_count' => $periodBookings->count(),
                'buy_volume_usd' => $usdBookings->where('transaction_type', 'buying')->sum('amount'),
                'sell_volume_usd' => $usdBookings->where('transaction_type', 'selling')->sum('amount'),
                'average_transaction_size' => $periodBookings->avg('amount'),
                'completed_transactions' => $periodBookings->where('status', 'completed')->count(),
                'completion_rate' => $periodBookings->count() > 0 ?
                    round(($periodBookings->where('status', 'completed')->count() / $periodBookings->count()) * 100, 2) : 0,
            ];
        })->values();
    }

    /**
     * Calculate client volume distribution
     */
    private function calculateClientVolumeDistribution($bookings): \Illuminate\Support\Collection
    {
        return $bookings->groupBy('client_name')->map(function ($clientBookings, $clientName) {
            $usdVolume = $clientBookings->where('currency', 'USD')->sum('amount');
            $ngnVolume = $clientBookings->where('currency', 'NGN')->sum('amount');

            return [
                'client_name' => $clientName,
                'total_volume_usd' => $usdVolume,
                'total_volume_ngn' => $ngnVolume,
                'transaction_count' => $clientBookings->count(),
                'average_transaction_size' => $clientBookings->avg('amount'),
                'total_revenue' => $clientBookings->where('status', 'completed')->sum('markup_amount'),
                'client_type' => $clientBookings->first()->client_type ?? 'Unknown',
            ];
        })->sortByDesc('total_volume_usd')->values();
    }

    /**
     * Calculate transaction size distribution
     */
    private function calculateTransactionSizeDistribution($bookings): array
    {
        $ranges = [
            'small' => ['min' => 0, 'max' => 1000, 'label' => '$0 - $1,000'],
            'medium' => ['min' => 1000, 'max' => 5000, 'label' => '$1,000 - $5,000'],
            'large' => ['min' => 5000, 'max' => 10000, 'label' => '$5,000 - $10,000'],
            'xlarge' => ['min' => 10000, 'max' => 50000, 'label' => '$10,000 - $50,000'],
            'xxlarge' => ['min' => 50000, 'max' => PHP_INT_MAX, 'label' => '$50,000+'],
        ];

        $distribution = [];
        foreach ($ranges as $key => $range) {
            $rangeBookings = $bookings->where('amount', '>=', $range['min'])
                                    ->where('amount', '<', $range['max']);

            $distribution[$key] = [
                'label' => $range['label'],
                'count' => $rangeBookings->count(),
                'volume' => $rangeBookings->sum('amount'),
                'percentage' => $bookings->count() > 0 ?
                    round(($rangeBookings->count() / $bookings->count()) * 100, 2) : 0,
                'average_size' => $rangeBookings->avg('amount'),
                'revenue' => $rangeBookings->where('status', 'completed')->sum('markup_amount'),
            ];
        }

        return $distribution;
    }

    /**
     * Calculate volume growth rate
     */
    private function calculateVolumeGrowthRate($volumeByDay): float
    {
        if ($volumeByDay->count() < 2) {
            return 0;
        }

        $sortedData = $volumeByDay->sortBy('period');
        $firstPeriod = $sortedData->first();
        $lastPeriod = $sortedData->last();

        if ($firstPeriod['total_volume_usd'] == 0) {
            return 0;
        }

        return round((($lastPeriod['total_volume_usd'] - $firstPeriod['total_volume_usd']) / $firstPeriod['total_volume_usd']) * 100, 2);
    }

    /**
     * Calculate client concentration ratio
     */
    private function calculateClientConcentrationRatio($clientDistribution): float
    {
        $totalVolume = $clientDistribution->sum('total_volume_usd');
        if ($totalVolume == 0) {
            return 0;
        }

        $topFiveVolume = $clientDistribution->take(5)->sum('total_volume_usd');
        return round(($topFiveVolume / $totalVolume) * 100, 2);
    }

    /**
     * Calculate median transaction size
     */
    private function calculateMedianTransactionSize($bookings): float
    {
        $amounts = $bookings->pluck('amount')->sort()->values();
        $count = $amounts->count();

        if ($count == 0) {
            return 0;
        }

        if ($count % 2 == 0) {
            return ($amounts[$count / 2 - 1] + $amounts[$count / 2]) / 2;
        } else {
            return $amounts[floor($count / 2)];
        }
    }

    /**
     * Calculate performance by time period
     */
    private function calculatePerformanceByPeriod($bookings, string $period): \Illuminate\Support\Collection
    {
        return $bookings->groupBy(function ($booking) use ($period) {
            switch ($period) {
                case 'day':
                    return $booking->created_at->format('Y-m-d');
                case 'week':
                    return $booking->created_at->format('Y-W');
                case 'month':
                    return $booking->created_at->format('Y-m');
                default:
                    return $booking->created_at->format('Y-m-d');
            }
        })->map(function ($periodBookings, $periodKey) {
            $revenue = $periodBookings->sum('markup_amount');
            $volume = $periodBookings->sum('amount');
            $transactionCount = $periodBookings->count();

            return [
                'period' => $periodKey,
                'revenue' => $revenue,
                'volume' => $volume,
                'transaction_count' => $transactionCount,
                'average_revenue_per_transaction' => $transactionCount > 0 ? $revenue / $transactionCount : 0,
                'average_markup_percentage' => $periodBookings->avg('markup_percentage'),
                'efficiency' => $volume > 0 ? round(($revenue / $volume) * 100, 4) : 0,
                'conversion_rate' => 100, // All bookings in this collection are completed
            ];
        })->values();
    }

    /**
     * Calculate client performance metrics
     */
    private function calculateClientPerformanceMetrics($completedBookings): array
    {
        $clientMetrics = $completedBookings->groupBy('client_name')->map(function ($clientBookings, $clientName) {
            $revenue = $clientBookings->sum('markup_amount');
            $volume = $clientBookings->sum('amount');

            return [
                'client_name' => $clientName,
                'revenue' => $revenue,
                'volume' => $volume,
                'transaction_count' => $clientBookings->count(),
                'average_transaction_size' => $clientBookings->avg('amount'),
                'average_markup_percentage' => $clientBookings->avg('markup_percentage'),
                'client_type' => $clientBookings->first()->client_type ?? 'Unknown',
                'first_transaction' => $clientBookings->min('created_at'),
                'last_transaction' => $clientBookings->max('created_at'),
            ];
        })->sortByDesc('revenue');

        $totalRevenue = $clientMetrics->sum('revenue');
        $totalClients = $clientMetrics->count();

        return [
            'top_clients' => $clientMetrics,
            'average_revenue_per_client' => $totalClients > 0 ? $totalRevenue / $totalClients : 0,
            'retention_indicators' => [
                'repeat_clients' => $clientMetrics->where('transaction_count', '>', 1)->count(),
                'repeat_client_percentage' => $totalClients > 0 ?
                    round(($clientMetrics->where('transaction_count', '>', 1)->count() / $totalClients) * 100, 2) : 0,
                'average_transactions_per_client' => $clientMetrics->avg('transaction_count'),
            ],
        ];
    }

    /**
     * Calculate efficiency metrics
     */
    private function calculateEfficiencyMetrics($allBookings, $completedBookings): array
    {
        $totalBookings = $allBookings->count();
        $completedCount = $completedBookings->count();
        $averageProcessingTime = $this->calculateAverageProcessingTime($completedBookings);

        // Calculate efficiency scores
        $completionEfficiency = $totalBookings > 0 ? ($completedCount / $totalBookings) * 100 : 0;
        $timeEfficiency = $averageProcessingTime > 0 ? max(0, 100 - ($averageProcessingTime / 24) * 10) : 100; // Penalty for longer processing
        $volumeEfficiency = $this->calculateVolumeEfficiency($completedBookings);

        return [
            'completion_efficiency' => round($completionEfficiency, 2),
            'time_efficiency' => round($timeEfficiency, 2),
            'volume_efficiency' => round($volumeEfficiency, 2),
            'overall_efficiency' => round(($completionEfficiency + $timeEfficiency + $volumeEfficiency) / 3, 2),
            'average_processing_time_hours' => $averageProcessingTime,
            'fast_completion_rate' => $this->calculateFastCompletionRate($completedBookings),
            'efficiency_trend' => $this->calculateEfficiencyTrend($allBookings),
        ];
    }

    /**
     * Calculate profitability metrics
     */
    private function calculateProfitabilityMetrics($completedBookings): array
    {
        $totalRevenue = $completedBookings->sum('markup_amount');
        $totalVolume = $completedBookings->sum('amount');
        $totalCustomerPayments = $completedBookings->sum('customer_payment_amount');

        return [
            'total_profit' => $totalRevenue,
            'profit_per_transaction' => $completedBookings->count() > 0 ? $totalRevenue / $completedBookings->count() : 0,
            'profit_margin_percentage' => $totalCustomerPayments > 0 ? round(($totalRevenue / $totalCustomerPayments) * 100, 2) : 0,
            'revenue_per_dollar_volume' => $totalVolume > 0 ? round($totalRevenue / $totalVolume, 4) : 0,
            'high_profit_transactions' => $completedBookings->where('markup_amount', '>', 100)->count(),
            'profit_concentration' => $this->calculateProfitConcentration($completedBookings),
            'profitability_by_size' => $this->calculateProfitabilityByTransactionSize($completedBookings),
        ];
    }

    /**
     * Calculate optimal markup range
     */
    private function calculateOptimalMarkupRange($completedBookings): array
    {
        $markupGroups = $completedBookings->groupBy(function ($booking) {
            $markup = $booking->markup_percentage;
            if ($markup < 1) return '0-1%';
            if ($markup < 2) return '1-2%';
            if ($markup < 3) return '2-3%';
            if ($markup < 4) return '3-4%';
            if ($markup < 5) return '4-5%';
            return '5%+';
        });

        $optimalRange = null;
        $maxRevenue = 0;

        foreach ($markupGroups as $range => $bookings) {
            $revenue = $bookings->sum('markup_amount');
            if ($revenue > $maxRevenue) {
                $maxRevenue = $revenue;
                $optimalRange = $range;
            }
        }

        return [
            'optimal_range' => $optimalRange,
            'optimal_range_revenue' => $maxRevenue,
            'range_analysis' => $markupGroups->map(function ($bookings, $range) {
                return [
                    'range' => $range,
                    'count' => $bookings->count(),
                    'revenue' => $bookings->sum('markup_amount'),
                    'average_transaction_size' => $bookings->avg('amount'),
                ];
            })->values(),
        ];
    }

    /**
     * Calculate revenue growth rate
     */
    private function calculateRevenueGrowthRate($performanceByDay): float
    {
        if ($performanceByDay->count() < 2) {
            return 0;
        }

        $sortedData = $performanceByDay->sortBy('period');
        $firstPeriod = $sortedData->first();
        $lastPeriod = $sortedData->last();

        if ($firstPeriod['revenue'] == 0) {
            return 0;
        }

        return round((($lastPeriod['revenue'] - $firstPeriod['revenue']) / $firstPeriod['revenue']) * 100, 2);
    }

    /**
     * Calculate trend direction
     */
    private function calculateTrendDirection($data, string $metric): string
    {
        if ($data->count() < 3) {
            return 'insufficient_data';
        }

        $values = $data->pluck($metric)->values();
        $recentValues = $values->slice(-3); // Last 3 data points

        $increasing = 0;
        $decreasing = 0;

        for ($i = 1; $i < $recentValues->count(); $i++) {
            if ($recentValues[$i] > $recentValues[$i - 1]) {
                $increasing++;
            } elseif ($recentValues[$i] < $recentValues[$i - 1]) {
                $decreasing++;
            }
        }

        if ($increasing > $decreasing) {
            return 'increasing';
        } elseif ($decreasing > $increasing) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }

    /**
     * Project monthly revenue
     */
    private function projectMonthlyRevenue($performanceByDay): float
    {
        if ($performanceByDay->count() < 7) {
            return 0;
        }

        $recentDays = $performanceByDay->sortBy('period')->slice(-7);
        $averageDailyRevenue = $recentDays->avg('revenue');
        $daysInMonth = 30; // Approximate

        return round($averageDailyRevenue * $daysInMonth, 2);
    }

    /**
     * Calculate volume efficiency
     */
    private function calculateVolumeEfficiency($completedBookings): float
    {
        $totalVolume = $completedBookings->sum('amount');
        $totalRevenue = $completedBookings->sum('markup_amount');

        if ($totalVolume == 0) {
            return 0;
        }

        $revenuePerDollar = $totalRevenue / $totalVolume;
        $benchmarkRevenuePerDollar = 0.02; // 2% benchmark

        return min(100, ($revenuePerDollar / $benchmarkRevenuePerDollar) * 100);
    }

    /**
     * Calculate fast completion rate
     */
    private function calculateFastCompletionRate($completedBookings): float
    {
        $fastCompletions = 0;
        $totalCompletions = 0;

        foreach ($completedBookings as $booking) {
            if ($booking->completed_at && $booking->created_at) {
                $totalCompletions++;
                $hours = $booking->created_at->diffInHours($booking->completed_at);
                if ($hours <= 4) { // Fast completion within 4 hours
                    $fastCompletions++;
                }
            }
        }

        return $totalCompletions > 0 ? round(($fastCompletions / $totalCompletions) * 100, 2) : 0;
    }

    /**
     * Calculate efficiency trend
     */
    private function calculateEfficiencyTrend($allBookings): string
    {
        $dailyEfficiency = $allBookings->groupBy(function ($booking) {
            return $booking->created_at->format('Y-m-d');
        })->map(function ($dayBookings) {
            $completed = $dayBookings->where('status', 'completed')->count();
            $total = $dayBookings->count();
            return $total > 0 ? ($completed / $total) * 100 : 0;
        })->values();

        return $this->calculateTrendDirection(collect($dailyEfficiency->map(function ($efficiency, $index) {
            return ['period' => $index, 'efficiency' => $efficiency];
        })), 'efficiency');
    }

    /**
     * Calculate profit concentration
     */
    private function calculateProfitConcentration($completedBookings): array
    {
        $totalProfit = $completedBookings->sum('markup_amount');
        $topTransactions = $completedBookings->sortByDesc('markup_amount')->take(10);
        $topTransactionsProfit = $topTransactions->sum('markup_amount');

        return [
            'top_10_transactions_profit' => $topTransactionsProfit,
            'concentration_percentage' => $totalProfit > 0 ? round(($topTransactionsProfit / $totalProfit) * 100, 2) : 0,
            'top_transactions' => $topTransactions->map(function ($booking) {
                return [
                    'booking_reference' => $booking->booking_reference,
                    'profit' => $booking->markup_amount,
                    'volume' => $booking->amount,
                    'markup_percentage' => $booking->markup_percentage,
                ];
            })->values(),
        ];
    }

    /**
     * Calculate profitability by transaction size
     */
    private function calculateProfitabilityByTransactionSize($completedBookings): array
    {
        $sizeRanges = [
            'small' => ['min' => 0, 'max' => 1000],
            'medium' => ['min' => 1000, 'max' => 5000],
            'large' => ['min' => 5000, 'max' => 10000],
            'xlarge' => ['min' => 10000, 'max' => PHP_INT_MAX],
        ];

        $profitability = [];
        foreach ($sizeRanges as $size => $range) {
            $rangeBookings = $completedBookings->where('amount', '>=', $range['min'])
                                             ->where('amount', '<', $range['max']);

            $totalRevenue = $rangeBookings->sum('markup_amount');
            $totalVolume = $rangeBookings->sum('amount');

            $profitability[$size] = [
                'transaction_count' => $rangeBookings->count(),
                'total_revenue' => $totalRevenue,
                'total_volume' => $totalVolume,
                'revenue_per_dollar' => $totalVolume > 0 ? round($totalRevenue / $totalVolume, 4) : 0,
                'average_markup_percentage' => $rangeBookings->avg('markup_percentage'),
            ];
        }

        return $profitability;
    }
}
