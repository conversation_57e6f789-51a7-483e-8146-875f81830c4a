<?php

require 'vendor/autoload.php';

use App\Models\ForexAccount;

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Current Forex Account Balances:\n\n";

$accounts = ForexAccount::all();

foreach($accounts as $account) {
    echo "Account: {$account->account_name} ({$account->currency_code})\n";
    echo "  Balance: " . number_format($account->balance, 2) . "\n";
    echo "  Pending: " . number_format($account->pending_balance, 2) . "\n";
    echo "  Available: " . number_format($account->available_balance, 2) . "\n";
    echo "  Status: " . ($account->is_active ? 'Active' : 'Inactive') . "\n";
    echo "---\n";
}
?>
