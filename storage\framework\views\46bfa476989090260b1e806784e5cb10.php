<!-- Modal -->
<div class="modal fade" id="loginAsUserModal" tabindex="-1" role="dialog" aria-labelledby="loginAsUserModalLabel" data-bs-backdrop="static"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="loginAsUserModalLabel">
                    <i class="bi bi-box-arrow-in-right me-1"></i> <?php echo app('translator')->get('Confirm Account Login'); ?>
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="get" action="" class="loginAccountAction" enctype="multipart/form-data" target="_blank">
                <div class="modal-body">
                    <?php echo app('translator')->get('Are you sure you want to proceed with logging into this account?'); ?>
                </div>
                <div class="modal-footer pt-0 border-top-0">
                    <button type="button" class="btn btn-white btn-sm" data-bs-dismiss="modal"><?php echo app('translator')->get('No'); ?></button>
                    <button type="submit" class="btn btn-primary btn-sm"><?php echo app('translator')->get('Yes'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- End Modal -->

<?php $__env->startPush('script'); ?>
<script>
    "use strict";
    $(document).ready(function() {
        // Initialize the modal properly to avoid backdrop errors
        var loginModal = document.getElementById('loginAsUserModal');
        if (loginModal) {
            // Ensure Bootstrap modal is properly initialized
            var modalInstance = new bootstrap.Modal(loginModal, {
                backdrop: 'static',
                keyboard: false
            });
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Herd\currency\resources\views/admin/user_management/components/login_as_user.blade.php ENDPATH**/ ?>