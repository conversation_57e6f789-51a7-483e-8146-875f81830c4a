@extends('admin.layouts.app')
@section('page-title')
    @lang($pageTitle)
@endsection

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb breadcrumb-no-gutter">
                            <li class="breadcrumb-item">
                                <a class="breadcrumb-link" href="{{ route('admin.forex.reports.index') }}">
                                    @lang('Forex Reports')
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">@lang('Performance Analytics')</li>
                        </ol>
                    </nav>
                    <h1 class="page-header-title">{{ $reportData['title'] }}</h1>
                    <p class="page-header-text">{{ $reportData['subtitle'] ?? '' }}</p>
                    <p class="page-header-text">@lang('Report Period'): {{ $reportData['period'] }}</p>
                </div>
                <div class="col-sm-auto">
                    <div class="btn-group" role="group">
                        <a class="btn btn-outline-secondary" href="{{ route('admin.forex.reports.index') }}">
                            <i class="bi-arrow-left me-1"></i> @lang('Back to Reports')
                        </a>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                            <i class="bi-printer me-1"></i> @lang('Print')
                        </button>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi-download me-1"></i> @lang('Export')
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                    <i class="bi-file-earmark-excel me-2"></i> @lang('Excel')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                    <i class="bi-file-earmark-pdf me-2"></i> @lang('PDF')
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                    <i class="bi-file-earmark-text me-2"></i> @lang('CSV')
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Performance Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Performance Overview')</h4>
                        <span class="badge bg-soft-success text-success">
                            <i class="bi-graph-up me-1"></i> @lang('Performance Analytics')
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">${{ number_format($reportData['summary']['total_revenue'], 2) }}</span>
                                        <span class="d-block text-muted">@lang('Total Revenue')</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['total_transactions']) }}</span>
                                        <span class="d-block text-muted">@lang('Completed Transactions')</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['conversion_rate'], 1) }}%</span>
                                        <span class="d-block text-muted">@lang('Conversion Rate')</span>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="media">
                                    <div class="media-body">
                                        <span class="d-block h4 mb-0">{{ number_format($reportData['summary']['efficiency_score'], 1) }}%</span>
                                        <span class="d-block text-muted">@lang('Efficiency Score')</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Analysis -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Revenue Breakdown')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['revenue_by_type']['buying'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Buying Revenue')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['revenue_by_type']['selling'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Selling Revenue')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['revenue_by_type']['buying_percentage'], 1) }}%</span>
                                    <span class="d-block text-muted small">@lang('Buying Share')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['revenue_by_type']['selling_percentage'], 1) }}%</span>
                                    <span class="d-block text-muted small">@lang('Selling Share')</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">@lang('Revenue Growth Rate')</span>
                                <span class="badge bg-soft-{{ $reportData['summary']['revenue_growth_rate'] >= 0 ? 'success' : 'danger' }} text-{{ $reportData['summary']['revenue_growth_rate'] >= 0 ? 'success' : 'danger' }}">
                                    {{ number_format($reportData['summary']['revenue_growth_rate'], 1) }}%
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Profitability Metrics')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Profit Margin')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['profit_margin'], 2) }}%</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Revenue per Transaction')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['average_revenue_per_transaction'], 2) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Revenue per Dollar Processed')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['revenue_per_dollar_processed'], 4) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('High Profit Transactions')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['profitability_metrics']['high_profit_transactions']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Markup Performance -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Markup Performance')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['markup_performance']['average_markup_percentage'], 2) }}%</span>
                                    <span class="d-block text-muted small">@lang('Average Markup')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['markup_performance']['markup_consistency'], 1) }}%</span>
                                    <span class="d-block text-muted small">@lang('Consistency')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['markup_performance']['min_markup_percentage'], 2) }}%</span>
                                    <span class="d-block text-muted small">@lang('Min Markup')</span>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h6 mb-0">{{ number_format($reportData['summary']['markup_performance']['max_markup_percentage'], 2) }}%</span>
                                    <span class="d-block text-muted small">@lang('Max Markup')</span>
                                </div>
                            </div>
                        </div>
                        @if(isset($reportData['summary']['markup_performance']['optimal_markup_range']['optimal_range']))
                            <div class="mt-3 pt-3 border-top">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Optimal Range')</span>
                                    <span class="badge bg-soft-success text-success">{{ $reportData['summary']['markup_performance']['optimal_markup_range']['optimal_range'] }}</span>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Operational Efficiency')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Completion Efficiency')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['operational_metrics']['completion_efficiency'], 1) }}%</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Time Efficiency')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['operational_metrics']['time_efficiency'], 1) }}%</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Average Processing Time')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['average_processing_time'], 1) }}h</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Fast Completion Rate')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['operational_metrics']['fast_completion_rate'], 1) }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Performance -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Client Metrics')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Total Unique Clients')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['client_metrics']['total_unique_clients']) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Average Revenue per Client')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['client_metrics']['average_revenue_per_client'], 2) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Repeat Clients')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['client_metrics']['client_retention_indicators']['repeat_clients']) }}</span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Client Retention Rate')</span>
                                    <span class="fw-bold">{{ number_format($reportData['summary']['client_metrics']['client_retention_indicators']['repeat_client_percentage'], 1) }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Performance Trends')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Revenue Trend')</span>
                                    <span class="badge bg-soft-{{ $reportData['summary']['forecasting_metrics']['revenue_trend'] === 'increasing' ? 'success' : ($reportData['summary']['forecasting_metrics']['revenue_trend'] === 'decreasing' ? 'danger' : 'secondary') }} text-{{ $reportData['summary']['forecasting_metrics']['revenue_trend'] === 'increasing' ? 'success' : ($reportData['summary']['forecasting_metrics']['revenue_trend'] === 'decreasing' ? 'danger' : 'secondary') }}">
                                        {{ ucfirst($reportData['summary']['forecasting_metrics']['revenue_trend']) }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Volume Trend')</span>
                                    <span class="badge bg-soft-{{ $reportData['summary']['forecasting_metrics']['volume_trend'] === 'increasing' ? 'success' : ($reportData['summary']['forecasting_metrics']['volume_trend'] === 'decreasing' ? 'danger' : 'secondary') }} text-{{ $reportData['summary']['forecasting_metrics']['volume_trend'] === 'increasing' ? 'success' : ($reportData['summary']['forecasting_metrics']['volume_trend'] === 'decreasing' ? 'danger' : 'secondary') }}">
                                        {{ ucfirst($reportData['summary']['forecasting_metrics']['volume_trend']) }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Efficiency Trend')</span>
                                    <span class="badge bg-soft-{{ $reportData['summary']['forecasting_metrics']['efficiency_trend'] === 'increasing' ? 'success' : ($reportData['summary']['forecasting_metrics']['efficiency_trend'] === 'decreasing' ? 'danger' : 'secondary') }} text-{{ $reportData['summary']['forecasting_metrics']['efficiency_trend'] === 'increasing' ? 'success' : ($reportData['summary']['forecasting_metrics']['efficiency_trend'] === 'decreasing' ? 'danger' : 'secondary') }}">
                                        {{ ucfirst($reportData['summary']['forecasting_metrics']['efficiency_trend']) }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-muted">@lang('Projected Monthly Revenue')</span>
                                    <span class="fw-bold">${{ number_format($reportData['summary']['forecasting_metrics']['projected_monthly_revenue'], 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Peak Performance Indicators -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-header-title">@lang('Peak Performance Indicators')</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['peak_performance_indicators']['highest_daily_revenue'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Highest Daily Revenue')</span>
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">${{ number_format($reportData['summary']['peak_performance_indicators']['highest_daily_volume'], 2) }}</span>
                                    <span class="d-block text-muted small">@lang('Highest Daily Volume')</span>
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['peak_performance_indicators']['most_transactions_day']) }}</span>
                                    <span class="d-block text-muted small">@lang('Most Transactions (Day)')</span>
                                </div>
                            </div>
                            <div class="col-lg-3 col-sm-6 mb-3">
                                <div class="text-center">
                                    <span class="d-block h5 mb-0">{{ number_format($reportData['summary']['peak_performance_indicators']['best_conversion_rate_day'], 1) }}%</span>
                                    <span class="d-block text-muted small">@lang('Best Conversion Rate (Day)')</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Revenue Clients -->
        @if($reportData['summary']['client_metrics']['top_revenue_clients']->count() > 0)
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-header-title">@lang('Top Revenue Clients')</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>@lang('Client Name')</th>
                                            <th>@lang('Revenue')</th>
                                            <th>@lang('Volume')</th>
                                            <th>@lang('Transactions')</th>
                                            <th>@lang('Avg Transaction Size')</th>
                                            <th>@lang('Avg Markup %')</th>
                                            <th>@lang('Client Type')</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reportData['summary']['client_metrics']['top_revenue_clients']->take(10) as $client)
                                            <tr>
                                                <td>{{ $client['client_name'] }}</td>
                                                <td>${{ number_format($client['revenue'], 2) }}</td>
                                                <td>${{ number_format($client['volume'], 2) }}</td>
                                                <td>{{ number_format($client['transaction_count']) }}</td>
                                                <td>${{ number_format($client['average_transaction_size'], 2) }}</td>
                                                <td>{{ number_format($client['average_markup_percentage'], 2) }}%</td>
                                                <td>
                                                    <span class="badge bg-soft-info text-info">{{ ucfirst($client['client_type']) }}</span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('script')
    <script>
        'use strict';
        
        function exportReport(format) {
            // Implementation for exporting performance reports
            alert('Export functionality will be implemented for ' + format + ' format');
        }
    </script>
@endpush
